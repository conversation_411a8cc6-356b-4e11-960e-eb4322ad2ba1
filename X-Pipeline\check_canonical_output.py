#!/usr/bin/env python3
"""
Simple script to examine the canonical JSON output.
"""

import json

def main():
    """Check the canonical JSON output file."""
    
    try:
        with open('canonical_output_test.json', 'r') as f:
            data = json.load(f)
        
        print("📊 Canonical JSON file structure:")
        for section, content in data.items():
            if isinstance(content, dict) and 'data' in content:
                print(f"   {section}: {len(content['data'])} records")
                
                # Show sample data for fixed shunt and AC line
                if section == 'fixed_shunt' and content['data']:
                    print(f"      First fixed shunt record: {content['data'][0]}")
                elif section == 'ac_line' and content['data']:
                    print(f"      First AC line record: {content['data'][0][:5]}...")
        
        print("\n🔍 Key findings:")
        fixed_shunt_count = len(data.get('fixed_shunt', {}).get('data', []))
        ac_line_count = len(data.get('ac_line', {}).get('data', []))
        branch_exists = 'branch' in data
        
        print(f"✅ Fixed shunt records: {fixed_shunt_count}")
        print(f"✅ AC line records: {ac_line_count}")
        print(f"✅ Branch section removed (not in canonical): {not branch_exists}")
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")

if __name__ == "__main__":
    main() 