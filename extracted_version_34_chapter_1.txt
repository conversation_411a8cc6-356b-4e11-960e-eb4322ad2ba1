Extracted text from Documentation/docs_34/DataFormat.pdf
Version: Version 34
Section: Chapter 1
Text length: 419399 characters
================================================================================


--- Page 1 ---
Data Formats Reference Manual 
PSS®E 34.8.0
September 2020

--- Page 2 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
iiCopyright © 1997 - 2020 Siemens Industry, Inc., Siemens Power Technologies International
Information in this manual and any software described herein is confidential and subject to change without notice and does not repre-
sent a commitment on the part of Siemens Industry, Inc., Siemens Power Technologies International. The software described in this
manual is furnished under a license agreement or nondisclosure agreement and may be used or copied only in accordance with the
terms of the agreement. No part of this manual may be reproduced or transmitted in any form or by any means, electronic or mechanical,
including photocopying, for any purpose other than the purchaser’s personal use, without the express written permission of Siemens
Industry, Inc., Siemens Power Technologies International.
PSS®E high-performance transmission planning software is a registered trademark of Siemens Industry, Inc., Siemens Power Technolo-
gies International in the United States and other countries.
The Windows 7 and Windows 10® operating systems, the Visual C++® development system, Microsoft Office Excel® and Microsoft Visual
Studio® are registered trademarks of Microsoft Corporation in the United States and other countries.
Intel® Visual Fortran Compiler for Windows is a trademark of Intel Corporation in the United States and other countries.
The PythonTM programming language is a trademark of the Python Software Foundation.
Other names may be trademarks of their respective owners.
--- Page 3 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
iiiTable of Contents
Power Flow Data Contents  ................................................................................................................ 1
Overview  ................................................................................................................................. 1
Extended Bus Names  ................................................................................................................ 1
Default Values  .......................................................................................................................... 2
Q Record  .................................................................................................................................. 2
Case Identification Data  ............................................................................................................ 4
System-Wide Data  .................................................................................................................... 5
GENERAL Record  ............................................................................................................... 5
GAUSS Record  .................................................................................................................. 5
NEWTON Record  ............................................................................................................... 6
ADJUST Record  ................................................................................................................. 6
TYSL Record  ..................................................................................................................... 7
SOLVER Record  ................................................................................................................. 7
RATING Record  ................................................................................................................. 8
Bus Data  .................................................................................................................................. 8
Bus Data Notes  ................................................................................................................. 9
Load Data  ................................................................................................................................ 9
Load Data Notes  ............................................................................................................. 11
Constant Power Load Characteristic  ................................................................................. 11
Constant Current Load Characteristic  ............................................................................... 12
Fixed Bus Shunt Data  .............................................................................................................. 13
Fixed Shunt Data Notes  ................................................................................................... 14
Generator Data  ....................................................................................................................... 14
Reactive Power Limits  ..................................................................................................... 17
Modeling of Generator Step-Up Transformers (GSU)  .......................................................... 18
Multiple Machine Plants  .................................................................................................. 19
Non-Transformer Branch Data  .................................................................................................. 20
Zero Impedance Lines  ..................................................................................................... 23
System Switching Device Data  ................................................................................................. 24
Transformer Data  .................................................................................................................... 25
Three-Winding Transformer Notes  .................................................................................... 38
Example Two-Winding Transformer Data Records  ............................................................. 38
Example Three-Winding  Transformer Data Records  ............................................................ 39
Two Winding Transformer Vector Groups  .......................................................................... 40
Three Winding Transformer Vector Groups  ........................................................................ 41
Clock Positions and Phase Angles specified in Transformer Power Flow Data  ........................ 41
CC=11  ............................................................................................................................ 42
CC=12  ............................................................................................................................ 42
CC=13  ............................................................................................................................ 42
CC=14  ............................................................................................................................ 42
CC=15  ............................................................................................................................ 43
CC=16  ............................................................................................................................ 43
CC=17  ............................................................................................................................ 43
CC=18  ............................................................................................................................ 43
Areas, Zones and Owners  ....................................................................................................... 44
Area Interchange Data  ............................................................................................................ 46
Area Interchange Data Notes  ........................................................................................... 47
Two-Terminal DC Transmission Line Data  .................................................................................. 47
--- Page 4 ---
Data Formats Reference Manual
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
ivTwo-Terminal DC Line Data Notes  .................................................................................... 50
Voltage Source Converter (VSC) DC Transmission Line Data  ........................................................ 51
VSC DC Line Data Notes  .................................................................................................. 53
Transformer Impedance Correction Tables  ................................................................................ 54
Impedance Correction Table Notes  ................................................................................... 55
Multi-Terminal DC Transmission Line Data  ................................................................................ 56
Multi-Terminal DC Line Notes  .......................................................................................... 61
Multi-Section Line Grouping Data  ............................................................................................ 62
Multi-Section Line Example  ............................................................................................. 63
Multi-Section Line Notes  ................................................................................................. 63
Zone Data  .............................................................................................................................. 64
Zone Data Notes  ............................................................................................................. 64
Interarea Transfer Data  ............................................................................................................ 64
Interarea Transfer Data Notes  .......................................................................................... 65
Owner Data  ........................................................................................................................... 65
FACTS Device Data  .................................................................................................................. 66
FACTS Device Notes  ........................................................................................................ 69
Switched Shunt Data  .............................................................................................................. 70
Switched Shunt Notes  ..................................................................................................... 73
Switched Shunt Example  ............................................................................................... 74
GNE Device Data  .................................................................................................................... 75
Induction Machine Data  .......................................................................................................... 76
Machine Electrical Data  ................................................................................................... 79
Load Mechanical Data  ..................................................................................................... 80
Substation Data  ...................................................................................................................... 81
Substation Data Record  ................................................................................................... 81
Node Data  ...................................................................................................................... 81
Node Data Notes  .................................................................................................... 82
Station Switching Device Data  ......................................................................................... 82
Equipment Terminal Data  ................................................................................................ 83
Load Terminal Data  ................................................................................................. 83
Fixed Shunt Terminal Data  ...................................................................................... 84
Machine Terminal Data  ........................................................................................... 84
Branch and two winding Transformer Terminal Data  .................................................. 85
Three winding Transformer Terminal Data  ................................................................ 85
Switched Shunt Terminal Data  ................................................................................. 86
Induction Machine Terminal Data  ............................................................................ 86
Two-terminal DC Line Terminal Data  ........................................................................ 86
Voltage Source Converter (VSC) DC Line Terminal Data  .............................................. 87
Multi-terminal DC Line Terminal Data  ....................................................................... 87
FACTS Device Terminal Data  .................................................................................... 88
End of Data Indicator  .............................................................................................................. 88
Sequence Data File  ......................................................................................................................... 89
Overview  ............................................................................................................................... 89
Change Code  .......................................................................................................................... 90
System Wide Data  .................................................................................................................. 90
Generator Sequence Data  ....................................................................................................... 91
Load Sequence Data  ............................................................................................................... 94
Zero Sequence Non-Transformer Branch Data  ........................................................................... 95
Zero Sequence Mutual Impedance Data  ................................................................................... 96
Zero Sequence Transformer Data  ............................................................................................. 98
--- Page 5 ---
Data Formats Reference Manual
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
vZero Sequence Switched Shunt Data  ...................................................................................... 105
Zero Sequence Fixed Shunt Data  ........................................................................................... 106
Induction Machine Sequence Data  ......................................................................................... 106
Non-Conventional Source Fault Contribution Data  ................................................................... 108
Application Notes - Transformers in the Zero Sequence  ........................................................... 109
Auto Transformer Equivalent Circuit  ............................................................................... 110
Two Winding Transformer Zero Sequence Networks  ........................................................ 111
CC=1 and CC=11  .................................................................................................. 111
CC=2 and CC=12  .................................................................................................. 112
CC=3 and CC=13  .................................................................................................. 114
CC=4 and CC=14  ................................................................................................. 116
CC=5 and CC=15  .................................................................................................. 117
CC=6 and CC=16  .................................................................................................. 118
CC=7 and CC=17  .................................................................................................. 120
CC=8 and CC=18  .................................................................................................. 121
CC=9 and CC=19  .................................................................................................. 123
CC=20  ................................................................................................................. 125
CC=21  .................................................................................................................. 127
CC=22  .................................................................................................................. 128
Three Winding Transformer Zero Sequence Network  ........................................................ 129
CC=1 and CC=11 (511)  ......................................................................................... 130
CC=2 and CC=12 (113)  ......................................................................................... 131
CC=3 and CC=13 (313)  ......................................................................................... 132
CC=4 and CC=14 (333)  ......................................................................................... 133
CC=5 and CC=15 (121)  ......................................................................................... 134
CC=6 and CC=16 (111)  ......................................................................................... 135
CC=17  .................................................................................................................. 136
CC=18  .................................................................................................................. 138
Optimal Power Flow Data Contents  ................................................................................................ 139
Overview  .............................................................................................................................. 139
Change Code  ........................................................................................................................ 140
Bus Voltage Constraint Data  .................................................................................................. 140
Bus Voltage Attribute Record  ......................................................................................... 140
Adjustable Bus Shunt Data  .................................................................................................... 142
Bus Load Data  ...................................................................................................................... 143
Bus Load Record  .......................................................................................................... 143
Adjustable Bus Load Table Data  ............................................................................................. 144
Adjustable Bus Load Table Record  ................................................................................. 144
Generator Dispatch Data  ....................................................................................................... 145
Generator Dispatch Data Record  ................................................................................... 145
Active Power Dispatch Data  ................................................................................................... 146
Active Power Dispatch Record  ........................................................................................ 146
Generation Reserve Data  ....................................................................................................... 147
Generation Reserve Record  ........................................................................................... 147
Generation Reactive Capability Data  ....................................................................................... 148
Generation Reactive Capability Record  ........................................................................... 148
Adjustable Branch Reactance Data  ......................................................................................... 149
Adjustable Branch Reactance Record  ............................................................................. 149
Piece-wise Linear Cost Data  ................................................................................................... 150
Piece-wise Linear Cost Record  ....................................................................................... 151
Piece-wise Quadratic Cost Data  .............................................................................................. 151
--- Page 6 ---
Data Formats Reference Manual
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
viPiece-wise Quadratic Cost Record  ................................................................................. 152
Polynomial and Exponential Cost Table  .................................................................................. 153
Polynomial and Exponential Record  .............................................................................. 153
Period Reserve Constraint Data  .............................................................................................. 154
Period Reserve Constraint Record  .................................................................................. 155
Branch Flow Constraint Data  ................................................................................................. 156
Branch Flow Constraint Record  ..................................................................................... 156
Interface Flow Constraint Data  .............................................................................................. 158
Linear Constraint Dependency Data  ....................................................................................... 160
Linear Constraint Dependency Record  ........................................................................... 160
GIC Data File Contents  .................................................................................................................. 164
Overview  .............................................................................................................................. 164
File Identification Data  .......................................................................................................... 165
Substation Data  .................................................................................................................... 165
Bus Substation Data  .............................................................................................................. 167
Transformer Data  .................................................................................................................. 167
Fixed Bus Shunt Data  ............................................................................................................ 171
Branch Data  ......................................................................................................................... 171
User Earth Model Data  .......................................................................................................... 172
Switched Shunt Data  ............................................................................................................ 173
Two-Terminal DC Data  ........................................................................................................... 174
VSC DC Data  ........................................................................................................................ 175
Multi-Terminal DC Data  ......................................................................................................... 176
FACTS Device Data  ................................................................................................................ 176
Load Data  ............................................................................................................................ 177
Harmonics Data File Contents  ........................................................................................................ 179
Overview  .............................................................................................................................. 179
File Identification Data  .......................................................................................................... 180
Impedance Characteristics Data  ............................................................................................. 180
Voltage Sources Data  ............................................................................................................ 181
Current Sources Data  ............................................................................................................ 182
Load Data  ............................................................................................................................ 183
Generator Data  ..................................................................................................................... 184
Non-Transformer Branch Data  ................................................................................................ 185
Transformer Data  .................................................................................................................. 186
Two-Terminal DC Data  ........................................................................................................... 188
VSC DC Data  ........................................................................................................................ 189
FACTS Device Data  ................................................................................................................ 189
Induction Machine (Asynchronous machine) Data  ................................................................... 191
Machine Capability Curve Data File  ................................................................................................ 193
Overview  .............................................................................................................................. 193
Change Code Data  ................................................................................................................ 193
Capability Curve Data  ............................................................................................................ 193
IEC Data File  ................................................................................................................................. 196
Overview  .............................................................................................................................. 196
File Identification Data  .......................................................................................................... 196
GSU, Equivalent Generator and Motor Data  ............................................................................ 197
Transformer Nameplate Winding MVA Data  ............................................................................ 198
Induction Machine Data  ........................................................................................................ 198
Wind Power Station Asynchronous Generator Data  .................................................................. 199
Wind Power Station Doubly Fed Asynchronous Generator Data  ................................................. 199
--- Page 7 ---
Data Formats Reference Manual
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
viiWind and Photovoltaic Power Station Generator with Full Size Converter Data  ........................... 200
--- Page 8 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
viiiList of Figures
Constant Power Load Characteristics  ................................................................................................ 12
Constant Current Load Characteristics  .............................................................................................. 13
Implicit GSU Configuration – Specified as Part of the Generator  ........................................................ 18
Explicit GSU Configuration– Specified Separately from the Generator  ................................................. 19
Multiple Generators at a Single Plant  ............................................................................................... 20
Data Set for the Multiple Generators in Figure 1-6  ........................................................................... 20
Two and Three-winding Transformer Configurations Related to Data Records  ....................................... 26
Two-Winding Transformer Positive Sequence Connections  ............................................................... 105
Three-Winding Transformer Positive Sequence Connections  ............................................................. 105
Induction machine sequence networks  ........................................................................................... 108
Non-conventional Source Fault Contribution Characteristics  ............................................................. 108
Auto-transformer Equivalent Circuit  ............................................................................................... 110
YNyn transformer zero sequence network  ...................................................................................... 111
YNyn with neutral impedance transformer zero sequence network  ................................................... 111
YNd transformer zero sequence network  ........................................................................................ 112
Znyn, Zny, or ZNd transformer zero sequence network  .................................................................... 112
YNy core type transformer zero sequence network  .......................................................................... 113
Dyn transformer zero sequence network  ........................................................................................ 114
YNzn, Yzn or Dzn transformer zero sequence network  ..................................................................... 114
Yyn core type transformer zero sequence network  .......................................................................... 115
Yy, Yd, Dy, Dd, Yyn or YNy transformer zero sequence network  ........................................................ 116
Ya ungrounded auto transformer zero sequence network  ................................................................ 116
CC=5 or CC=15 zero sequence network  .......................................................................................... 117
YNd transformer with Zigzag or YNd earthing transformer on winding 2 side zero sequence network  ... 118
YNzn or Dzn core type transformer zero sequence network  ............................................................. 119
Dyn transformer with Zigzag or YNd earthing transformer on winding 1 side zero sequence network  ... 120
ZNyn or ZNd core type transformer zero sequence network  ............................................................. 120
YNa core type auto transformer zero sequence network  .................................................................. 121
YNa core type auto transformer zero sequence network  .................................................................. 123
YNyn with or without neutral impedance core type transformer zero sequence network  ..................... 125
YNa auto transformer zero sequence network  ................................................................................. 127
Ya ungrounded core type auto transformer zero sequence network  .................................................. 128
YNynyn with magnetising impedance modelled transformer zero sequence network  .......................... 130
YNynd transformer zero sequence network  .................................................................................... 131
Dynd transformer zero sequence network  ...................................................................................... 132
Ddd, Ddy, Dyd, Dyy, Ydd, Ydy, Yyd or Yyy transformer zero sequence network  ................................... 133
Dynd auto transformer zero sequence network  ............................................................................... 134
YNynyn transformer zero sequence network  ................................................................................... 135
Ynad (grounded) auto transformer zero sequence network  .............................................................. 136
Equivalent zero sequnce network for a transformer with two externally available neutrals and 0o phase shift
between windings 1 and 2  ............................................................................................................ 136
Yad (ungrounded) auto transformer zero sequence network  ............................................................ 138
Capability Curve Example for savnw.sav Case  ................................................................................. 195
--- Page 9 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
ixList of Tables
Examples of Two Winding Transformer Vector Groups  ....................................................................... 40
--- Page 10 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1Chapter 1
Power Flow Data Contents
1.1. Overview
The input stream to activity READ consists of 23 groups of records, with each group containing a particular
type of data required in power flow work (refer to Figure 1-1 ). The end of each category of data, except
the Case Identification Data , is indicated by a record specifying a value of zero; the end of the system-wide,
FACTS device, DC line, and GNE device data categories may alternatively be indicated with a record specifying
a NAME value with blanks. The optimal power flow problem typically consists of several components: one or
more objectives, a set of available system controls and any number of system constraints. The purpose of this
chapter is to present the available controls and constraints for the optimal power flow problem statement.
The sections within this chapter are presented in the order in which the data categories must appear within
the OPF Raw Data File. The format of the OPF Raw Data File itself is outlined in Figure 3-1. Each section of
this chapter fully describes the data elements associated with each data model. Specific information on the
use of the data input facilities can be found in Section 14.8 Data Input and Storage.
1.2. Extended Bus Names
On its Bus Data  record, each bus is assigned a bus number and a 12 character alphanumeric name. When
the bus names  input option of activity READ is enabled, data fields designating buses on load, fixed shunt,
generator, non-transformer branch, transformer, area, two-terminal dc line, VSC dc line, multi-terminal dc
line, multi-section line, FACTS device, switched shunt, GNE device, and induction machine, data records may
be specified as either extended bus names enclosed in single quotes or as bus numbers.
The requirements for specifying an extended bus name are:
•The extended name of a bus is a concatenation of its 12 character alphanumeric name and its Breaker
and Switch base voltage.
•It must be enclosed in single quotes.
•The 12 character bus name, including any trailing blanks , must be the first 12 characters of the extended
bus name.
•The bus base voltage in kV follows the 12 character bus name. Up to 6 characters may be used.
•For those data fields for which a sign is used to indicate a modeling attribute, a minus sign may be specified
between the leading single quote and the first character of the 12 character bus name.
Thus, valid forms of an extended bus name include ' aaaaaaaaaaaavvvvvv ' and ' aaaaaaaaaaaavvv '. For
those data fields cited in (4) above, ' -aaaaaaaaaaaavvvvvv ' and '- aaaaaaaaaaaavvv ' are also valid forms
of extended bus names.
As an example, consider a 345 kV bus with the name ERIE BLVD. The following are all valid forms of Its
extended bus name:
’ERIE BLVD 345.0’ ’ERIE BLVD 345’ ’ERIE BLVD 345’
--- Page 11 ---
Power Flow Data Contents
 Default Values
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
2The following is not a valid form of its extended bus name because the three tailing blanks of its bus name
are not all included before the base voltage:
’ERIE BLVD 345’
1.3. Default Values
All data is read in free format with data items separated by a comma or one or more blanks; [Tab]  delimited
data items are not recommended.
Because there are default values for many of the data items specified in the Power Flow Raw Data File, you
can include only the specific information you need. For example, if bus 99 is a 345 kV Type 1 bus assigned
to zone 3, the Bus Data  record in the file could be:
99,,345,,,3
This is equivalent to specifying the data record:
99,’ ’,345.0,1,1,3,1,1.0,0.0
If, in addition, you name the bus ERIE BLVD, the minimum data line would be:
99,’ERIE BLVD’,345,,,3
1.4. Q Record
Generally, specifying a data record with a Q in column one is used to indicate that no more data records are
to be supplied to activity READ. This end of data input indicator is permitted anywhere in the Power Flow
Raw Data File except  where activity READ is expecting one of the following:
•one of the three Case Identification Data  records
•the second or subsequent records of the four-record block defining a two-winding transformer
•the second or subsequent records of the five-record block defining a three-winding transformer
•the second or third record of the three-record block defining a two-terminal dc transmission line
•the second or third record of the three-record block defining a VSC dc transmission line
•the second or subsequent records of the series of data records defining a multi-terminal dc transmission
line
•the second or subsequent records of the series of data records defining a GNE device
Case Identification Data
System-Wide Data
Bus Data
Load Data
--- Page 12 ---
Power Flow Data Contents
 Q Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
3Fixed Bus Shunt Data
Generator Data
Non-Transformer Branch Data
System Switching Device Data
Transformer Data
Area Interchange Data
Two-Terminal DC Transmission Line Data
Voltage Source Converter (VSC) DC Transmission Line Data
Transformer Impedance Correction Tables
Multi-Terminal DC Transmission Line Data
Multi-Section Line Grouping Data
Zone Data
Interarea Transfer Data
Owner Data
FACTS Device Data
Switched Shunt Data
GNE Device Data
Induction Machine Data
Substation Data
Q Record
Power Flow Raw Data Input Structure
Each substation block data consists of following records.
--- Page 13 ---
Power Flow Data Contents
 Case Identification Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
4Substation Data Record
Node Data
Station Switching Device Data
Equipment Terminal Data
1.5. Case Identification Data
Case identification data consists of three data records. The first record contains six items of data as follows:
IC, SBASE, REV, XFRRAT, NXFRAT, BASFRQ
IC New case flag:
0 for base case input (i.e., clear the working case before adding data to it)
1 to add data to the working case
IC = 0 by default
SBASE System MVA base
SBASE = 100.0 by default
REV PSSE revision number
REV = current revision by default
XFRRAT Units of transformer ratings (refer to Transformer Data ). The transformer percent load-
ing units program option setting (refer to Saved Case Specific Option Settings) is set
according to thisdata value.
XFRRAT <= 0 for MVA
XFRRAT > 0 for current expressed as MVA
XFRRAT = present transformer percent loading program option setting by default (refer
to activity OPTN).
NXFRAT Units of ratings of non-transformer branches (refer to Non-Transformer Branch Data ).
The non-transformer branch percent loading units program option setting (refer to
Saved Case Specific Option Settings) is set according to this data value
NXFRAT <= 0 for MVA
NXFRAT > 0 for current expressed as MVA
NXFRAT = present non-transformer branch percent loading program option setting by
default (refer to activity OPTN).
BASFRQ System base frequency in Hertz. The base frequency program option setting (refer to
Saved Case Specific Option Settings) is set to this data value. BASFRQ = present base
frequency program option setting value by default (refer to activity OPTN).
--- Page 14 ---
Power Flow Data Contents
 System-Wide Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
5When current ratings are being specified, ratings are entered as:
MVA rated = √ 3 x E base x Irated x 10-6
where:
Ebase Is the branch or transformer winding voltage base in volts.
Irated Is the rated phase current in amps
The next two records each contain a line of text to be associated with the case as its case title. Each line may
contain up to 60 characters, which are entered in columns 1 through 60.
1.6. System-Wide Data
Through the system-wide data category, data that pertains to the case as a whole (rather than to individual
equipment items) may be included in the Power Flow Raw Data File to allow convenient transfer of it with
the case. Records may be included that define:
•power flow solution parameters
•descriptions of rating sets
•information on the most recent power flow solution attempt
Generally, each record specified in the System-Wide Data category begins with a NAME that defines the type
of data specified on the record. The formats of the various records are described in the following paragraphs.
1.6.1. GENERAL Record
The GENRAL record begins with the name GENERAL and contains solution parameters used by all of the
power flow solution methods. Using keyword input, any or all of the following solution parameters may be
specified:
•THRSHZ (the zero impedance line threshold tolerance)
•PQBRAK (the constant power load characteristic voltage breakpoint)
•BLOWUP (the largest voltage change threshold)
Those solution parameters that are specified may be entered in any order. The following is an example of
the GENERAL record:
GENERAL, THRSHZ=0.0001, PQBRAK=0.7, BLOWUP=5.0
1.6.2. GAUSS Record
The GAUSS record begins with the name GAUSS and contains solution parameters used by the Gauss-Seidel
power flow solution methods (SOLV and MSLV). Using keyword input, any or all of the following solution
parameters may be specified:
•ITMX (the maximum number of iterations)
•ACCP (real component voltage change acceleration factor)
--- Page 15 ---
Power Flow Data Contents
 NEWTON Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
6•ACCQ (imaginary component voltage change acceleration factor)
•ACCM (type 1 bus complex voltage change acceleration factor in MSLV)
•TOL (voltage magnitude change convergence tolerance)
Those solution parameters that are specified may be entered in any order. The following is an example of
the GAUSS record:
GAUSS, ITMX=100, ACCP=1.6, ACCQ=1.6, ACCM=1.0, TOL=0.0001
1.6.3. NEWTON Record
The NEWTON record begins with the name NEWTON and contains solution parameters used by the New-
ton-Raphson power flow solution methods (FDNS, FNSL and NSOL). Using keyword input, any or all of the
following solution parameters may be specified:
•ITMXN (the maximum number of iterations)
•ACCN (voltage magnitude setpoint change acceleration factor at voltage controlled buses)
•TOLN (mismatch convergence tolerance)
•VCTOLQ (controlled bus reactive power mismatch convergence tolerance)
•VCTOLV (controlled bus voltage error convergence tolerance)
•DVLIM (maximum votlage magnitude change that may be applied on any iteration)
•NDVFCT (non-divergent solution improvement factor)
Those solution parameters that are specified may be entered in any order. The following is an example of
the NEWTON record:
NEWTON, ITMXN=20, ACCN=1.0, TOLN=0.1, VCTOLQ=0.1, VCTOLV=0.00001, DVLIM=0.99,
NDVFCT=0.99
1.6.4. ADJUST Record
The ADJUST record begins with the name ADJUST and contains solution parameters used by the automatic
adjustment functions of the Gauss-Seidel and Newton-Raphson power flow solution methods. Using keyword
input, any or all of the following solution parameters may be specified:
•ADJTHR (automatic adjustment threshold tolerance)
•ACCTAP (tap movement deceleration factor)
•TAPLIM (maximum tap ratio change on any iteration)
•SWVBND (percent of voltage band switched shunts with voltage violations that are adjusted on any iter-
ation)
•MXTPSS (maximum number of tap and/or switched shunt adjustment cycles)
--- Page 16 ---
Power Flow Data Contents
 TYSL Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
7•MXSWIM (maximum number of induction machine state switchings)
Those solution parameters that are specified may be entered in any order. The following is an example of
the ADJUST record:
ADJUST, ADJTHR=0.005, ACCTAP=1.0, TAPLIM=0.05, SWVBND=100.0, MXTPSS=99,
MXSWIM=10
1.6.5. TYSL Record
The TYSL record begins with the name TYSL and contains solution parameters used by the balanced switch-
ing network solution (TYSL). Using keyword input, any or all of the following solution parameters may be
specified:
•ITMXTY (the maximum number of iterations)
•ACCTY (voltage change acceleration factor)
•TOLTY (voltage magnitude change convergence tolerance)
Those solution parameters that are specified may be entered in any order. The following is an example of
the TYSL record:
TYSL, ITMXTY=20, ACCTY=1.0, TOLTY=0.00001
1.6.6. SOLVER Record
The SOLVER record begins with the name SOLVER and identifies the power flow solution method and solution
options used in the last power flow solution attempt.
Following the name SOLVER is the name of the solution method (either FDNS, FNSL, NSOL, SOLV or MSLV).
Then, using keyword input, any or all of the following solution option selections may be specified:
•ACTAPS (the ac tap adjustment code)
•AREAIN (the area interchange adjustment code)
•PHSHFT (the phase shift adjustment code)
•DCTAPS (the dc tap adjustment code)
•SWSHNT (the switched shunt adjustment code)
•FLATST (the flat start code)
•VARLIM (the reactive power limit application code)
•NONDIV (the non-divergent solution code)
Those solution options that are specified may be entered in any order. The following is an example of the
SOLVER record:
SOLVER, FNSL, ACTAPS=1, AREAIN=0, PHSHFT=0, DCTAPS=1, SWSHNT=1, FLATST=0, VAR-
LIM=0, NONDIV=0
--- Page 17 ---
Power Flow Data Contents
 RATING Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
81.6.7. RATING Record
The RATING record begins with the name RATING and specifies a six-character name and a 32-character
description associated with a specified rating set.
Following the name RATING are the following data items:
•the number of the rating set (1 through 12),
•a quoted string that contains the rating’s name (this is used as a column heading in several reports), and
•a quoted string that contains the rating’s description.
If no RATING record is specified for rating set "n", its name is set to "RATEn" and its description is set to "RATING
SET n".
The following is an example of the RATING record:
RATING, 3, "STEMER", "Short term summer emergency"
System wide data input is terminated with a record specifying a value of zero.
1.7. Bus Data
Each network bus to be represented in PSSE is introduced by reading a bus data record. Each bus data record
has the following format:
I, 'NAME', BASKV, IDE, AREA, ZONE, OWNER, VM, VA, NVHI, NVLO, EVHI, EVLO
I Bus number (1 through 999997). No default allowed.
NAME Alphanumeric identifier assigned to bus I. NAME may be up to twelve characters and
may contain any combination of blanks, uppercase letters, numbers and special char-
acters, but the first character must not  be a minus sign. NAME must  be enclosed in
single or double quotes if it contains any blanks or special characters.
NAME = twelve blanks by default
BASKV Bus base voltage; entered in kV
BASKV = 0.0 by default
IDE Bus type code:
•1 - for a load bus or passive node (no generator boundarycondition)
•2 - for a generator or plant bus (either voltage regulating or fixed Mvar)
•3 - for a swing bus
•4 - for a disconnected (isolated) bus
IDE = 1 by default
AREA Area number (1 through 9999).
AREA = 1 by default
--- Page 18 ---
Power Flow Data Contents
 Bus Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
9ZONE Zone number (1 through 9999).
ZONE = 1 by default
OWNER Owner number (1 through 9999).
OWNER = 1 by default
VM Bus voltage magnitude; entered in pu.
VM = 1.0 by default
VA Bus voltage phase angle; entered in degrees.
VA = 0.0 by default
NVHI Normal voltage magnitude high limit; entered in pu.
NVHI = 1.1 by default
NVLO Normal voltage magnitude low limit, entered in pu.
NVLO = 0.9 by default
EVHI Emergency voltage magnitude high limit; entered in pu.
EVHI = 1.1 by default
EVLO Emergency voltage magnitude low limit; entered in pu.
EVLO = 0.9 by default
Bus data input is terminated with a record specifying a bus number of zero.
1.7.1. Bus Data Notes
VM and VA need to be set to their actual solved case values only when the network, as entered into the
working case via activity READ, is to be considered solved as read in. Otherwise, unless some better estimate
of the solved voltage and/or phase angle is available, VM and VA may be omitted (and therefore set to their
default values; see Default Values ).
1.8. Load Data
Each network bus at which load is to be represented must be specified in at least one load data record.
Multiple loads may be represented at a bus by specifying more than one load data record for the bus, each
with a different load identifier.
Each load at a bus can be a mixture of loads with three different characteristics: the Constant Power Load
Characteristic , the Constant Current Load Characteristic , and the constant admittance load characteristic. For
additional information on load characteristic modeling, refer to Load , activities CONL  and RCNL , Modeling
Load Characteristics  and Basic Load Characteristics .
Each load data record has the following format:
--- Page 19 ---
Power Flow Data Contents
 Load Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
10I,ID,STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT,DGENP,DGENQ,DGENM
I Bus number, or extended bus name enclosed in single quotes (refer to  Extended Bus
Names ).
No default allowed.
ID One or two-character uppercase non-blank alphanumeric load identifier used to dis-
tinguish among multiple loads at bus I. It is recommended that, at buses for which a
single load is present, the load be designated as having the load identifier '1'.
ID = '1' by default
STATUS Load status of one for in-service and zero for out-of-service.
STATUS = 1 by default
AREA Area to which the load is assigned (1 through 9999). By default, AREA is the area to
which bus I is assigned (refer to Bus Data ).
ZONE Zone to which the load is assigned (1 through 9999). By default, ZONE is the zone to
which bus I is assigned (refer to Bus Data ).
PL Active power component of constant MVA load; entered in MW.
PL = 0.0 by default
QL Reactive power component of constant MVA load; entered in Mvar.
QL = 0.0 by default
IP Active power component of constant current load; entered in MW at one per unit
voltage.
IP = 0.0 by default
IQ Reactive power component of constant current load; entered in Mvar at one per unit
voltage.
IQ = 0.0 by default
YP Active power component of constant admittance load; entered in MW at one per unit
voltage.
YP = 0.0 by default
YQ Reactive power component of constant admittance load; entered in Mvar at one per
unit voltage. YQ is a negative quantity for an inductive load and positive for a capacitive
load.
YQ = 0.0 by default
OWNER Owner to which the load is assigned (1 through 9999). By default, OWNER is the owner
to which bus I is assigned (refer to Bus Data ).
SCALE Load scaling flag of one for a scalable load and zero for a fixed load (refer to SCAL ).
SCALE = 1 by default
INTRPT Interruptible load flag of one for an interruptible load for zero for a non interruptible
load.
INTRPT = 0 by default
--- Page 20 ---
Power Flow Data Contents
 Load Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
11DGENP Distributed Generation active power component; entered in units of MW.
DGENP = 0.0 by default
DGENQ Distributed Generation reactive power component; entered in units of MVAR.
DGENQ = 0.0 by default
DGENM Distributed Generation operation mode; 0 = distributed generation on feeder is OFF,
1 = distributed generation on feeder is ON.
DGENM = 0 by default
Load data input is terminated with a record specifying a bus number of zero.
1.8.1. Load Data Notes
The area, zone, and owner assignments of loads are used for area, zone, and owner totaling purposes (e.g.,
in activities AREA , OWNR , and ZONE ) and for load scaling and conversion purposes. They may differ from
those of the bus to which they are connected. The area and zone assignments of loads may optionally be
used during area and zone interchange calculations (refer to Area Interchange Control  and activities AREA ,
ZONE , TIES, TIEZ, INTA , and INTZ ).
1.8.2. Constant Power Load Characteristic
The constant power characteristic holds the load power values, and also, the distributed generation power
values, constant as long as the bus voltage exceeds a value specified by the solution parameter PQBRAK. The
constant power characteristic assumes an elliptical current-voltage characteristic of the corresponding load
current for voltages below this threshold. Figure 1-2  depicts this characteristic for PQBRAK values of 0.6, 0.7,
and 0.8 pu. The user may modify the value of PQBRAK using the [Solution Parameters]  GUI (refer to PSSE
GUI Users Guide , Section 11.1.1, Boundary Conditions).
--- Page 21 ---
Power Flow Data Contents
 Constant Current Load Characteristic
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
12
Figure 1.1. Constant Power Load Characteristics
1.8.3. Constant Current Load Characteristic
The constant current characteristic holds the load current constant as long as the bus voltage exceeds 0.5
pu, and assumes an elliptical current-voltage characteristic as shown in Figure 1-3  for voltages below 0.5 pu.
--- Page 22 ---
Power Flow Data Contents
 Fixed Bus Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
13
Figure 1.2. Constant Current Load Characteristics
1.9. Fixed Bus Shunt Data
Each network bus at which fixed bus shunt is to be represented must be specified in at least one fixed bus
shunt data record. Multiple fixed bus shunts may be represented at a bus by specifying more than one fixed
bus shunt data record for the bus, each with a different shunt identifier.
Each fixed bus shunt data record has the following format:
--- Page 23 ---
Power Flow Data Contents
 Fixed Shunt Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
14I, ID, STATUS, GL, BL
I Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ).
No default allowed
ID One- or two-character uppercase non-blank alphanumeric shunt identifier used to dis-
tinguish among multiple shunts at bus I. It is recommended that, at buses for which
a single shunt is present, the shunt be designated as having the shunt identifier '1'.
ID = '1' by default
STATUS Shunt status of one for in-service and zero for out-of-service.
STATUS = 1 by default
GL Activecomponent of shunt admittance to ground; entered in MW at one per unit volt-
age. GL should not include any resistive impedance load, which is entered as part of
load data.
GL = 0.0 by default
BL Reactive component of shunt admittance to ground; entered in Mvar at one per unit
voltage. BL should not include any reactive impedance load, which is entered as part
of load data; line charging and line connected shunts, which are entered as part of
non-transformer branch data; transformer magnetizing admittance, which is entered
as part of transformer data; or switched shunt admittance, which is entered as part
of switched shunt data. BL is positive for a capacitor, and negative for a reactor or an
inductive load.
BL = 0.0 by default
Fixed bus shunt data input is terminated with a record specifying a bus number of zero.
1.9.1. Fixed Shunt Data Notes
The area, zone, and owner assignments of the bus to which the shunt is connected are used for area, zone,
and owner totaling purposes (e.g., in activities AREA , OWNR , and ZONE ); refer to Section 12.7, “Summarizing
Area Totals”  through Section 12.12, “Summarizing Zone-to-Zone Interchange” ) and for shunt scaling purpos-
es (refer to SCAL ).
The admittance specified in the data record can represent a shunt capacitor or a shunt reactor (both with
or without a real component) or a shunt resistor. It must not represent line connected admittance, switched
shunts, loads, line charging or transformer magnetizing impedance, all of which are entered in other data
categories.
1.10. Generator Data
Each network bus to be represented as a generator or plant bus in PSSE must be specified in a generator
data record. In particular, each bus specified in the bus data input with a Type code of 2 or 3 must have a
generator data record entered for it.
--- Page 24 ---
Power Flow Data Contents
 Generator Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
15
Each generator has a single line data record with the following format:
I,ID,PG,QG,QT,QB,VS,IREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,                                                    O1,F1,...,O4,F4,WMOD,WPF,NREG
I Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ).
No default allowed
ID One- or two-character uppercase non-blank alphanumeric machine identifier used to
distinguish among multiple machines at bus I. It is recommended that, at buses for
which a single machine is present, the machine be designated as having the machine
identifier '1'.
ID = '1' by default
PG Generator active power output; entered in MW.
PG = 0.0 by default
QG Generator reactive power output; entered in Mvar. QG needs to be entered only if the
case, as read in, is to be treated as a solved case.
QG = 0.0 by default
QT Maximum generator reactive power output; entered in Mvar. For fixed output gen-
erators (i.e., nonregulating), QT must be equal to the fixed Mvar output. For infeed
machines (WMOD=4), QT is not used in powerflow calculations. The reactive power
output of infeed machines is held constant at QG.
QT = 9999.0 by default
QB Minimum generator reactive power output; entered in Mvar. For fixed output gener-
ators, QB must be equal to the fixed Mvar output. For infeed machines (WMOD=4),
QB is not used in powerflow calculations. The reactive power output infeed machines
is held constant at QB = -9999.0 by default.
VS Regulated voltage setpoint; entered in pu.
--- Page 25 ---
Power Flow Data Contents
 Generator Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
16VS = 1.0 by default
IREG Bus number, or extended bus name enclosed in single quotes, of the bus for which
voltage is to be regulated by this plant to the value specified by VS. If IREG specifies
a remote bus (i.e., a bus other than bus I), bus IREG must be a Type 1 or 2 bus (if it is
other than a Type 1 or 2 bus, bus I regulates its own voltage to the value specified by
VS). IREG may be entered as zero if the plant is to regulate its own voltage. If bus I is
a Type 3 (swing) bus, IREG must not specify a remote bus.
IREG = 0 by default
MBASE Total MVA base of the units represented by this machine; entered in MVA. This quantity
is not needed in normal power flow and equivalent construction work, but is required
for switching studies, fault analysis, and dynamic simulation.
MBASE = system base MVA by default
ZR,ZX Complex machine impedance, ZSORCE; entered in pu on MBASE base. This data is not
needed in normal power flow and equivalent construction work, but is required for
switching studies, fault analysis, and dynamic simulation. For dynamic simulation, this
impedance must be set equal to the unsaturated subtransient impedance for those
generators to be modeled by subtransient level machine models, and to unsaturated
transient impedance for those to be modeled by classical or transient level models.
For short-circuit studies, the saturated subtransient or transient impedance should be
used.
ZR = 0.0 and ZX = 1.0 by default
RT,XT Step-up transformer impedance, XTRAN; entered in pu on MBASE base. XTRAN should
be entered as zero if the step-up transformer is explicitly modeled as a network branch
and bus I is the terminal bus.
RT + jXT = 0.0 by default
GTAP Step-up transformer off-nominal turns ratio; entered in pu on a system base. GTAP is
used only if XTRAN is non-zero.
GTAP = 1.0 by default
STAT Machine status of one for in-service and zero for out-of-service;
STAT = 1 by default
RMPCT Percent of the total Mvar required to hold the voltage at the bus controlled by bus
I that are to be contributed by the generation at bus I; RMPCT must be positive. RM-
PCT is needed only if there is more than one local or remote setpoint mode voltage
controlling device (plant, switched shunt, FACTS device shunt element, or VSC dc line
converter) controlling the voltage at bus IREG.
RMPCT = 100.0 by default
PT Maximum generator active power output; entered in MW.
PT = 9999.0 by default
PB Minimum generator active power output; entered in MW.
PB = -9999.0 by default
--- Page 26 ---
Power Flow Data Contents
 Reactive Power Limits
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
17Oi Owner number (1 through 9999). Each machine may have up to four owners. By de-
fault, O1 is the owner to which bus I is assigned (refer to Bus Data ) and O2, O3, and
O4 are zero.
Fi Fraction of total ownership assigned to owner Oi; each Fi must be positive. The Fi
values are normalized such that they sum to 1.0 before they are placed in the working
case. By default, each Fi is 1.0.
WMOD Machine control mode; WMOD is used to indicate whether a machine is a convention-
al or a non-conventional machine (e.g. renewables, infeed) machine, and, if it is, the
type of reactive power limits to be imposed. Non-conventional machines are renew-
ables (e.g., wind, PV etc.) and infeed machines (for definition of infeed machines, see
description below of WNMOD=4)
0 - a conventional machine (e.g. synchronous machines).
1 - renewable type machine for which reactive power limits are specified by QT and QB.
2 - renewable type machine for which reactive power limits are determined from the
machine’s active power output and WPF; limits are of equal magnitude and opposite
sign
3 - renewable type machine with a fixed reactive power setting determined from the
machine’s active power output and WPF; when WPF is positive, the machine’s reactive
power has the same sign as its active power; when WPF is negative, the machine’s
reactive power has the opposite sign of its active power.
4 - infeed type machine. An infeed type machine is one for which the machine reactive
power (QG) is held constant. The QT and QB limits values are not used and are for
information only. QG value has to be between QT and QB.
WMOD = 0 by default
WPF Power factor used in calculating reactive power limits or output when WMOD is 2 or 3.
WPF = 1.0 by default
NREG A node number of bus IREG. The bus section of bus IREG to which node NREG is con-
nected is the bus section for which voltage is to be regulated by this plant to the value
specified by VS. If bus IREG is not in a substation, NREG must be specified as 0.
NREG = 0 by default
Generator data input is terminated with a record specifying a bus number of zero.
1.10.1. Reactive Power Limits
In specifying reactive power limits for voltage controlling plants (i.e., those with unequal reactive power
limits), the use of very narrow var limit bands is discouraged. The Newton-Raphson based power flow solu-
tions require that the difference between the controlling equipment's high and low reactive power limits
be greater than 0.002 pu for all setpoint mode voltage controlling equipment (0.2 Mvar on a 100 MVA sys-
tem base). It is recommended that voltage controlling plants have Mvar ranges substantially wider than this
minimum permissible range.
For additional information on generator modeling in power flow solutions, refer to refer to Section 6.3.12,
“Generation”  and Section 6.3.18, “AC Voltage Control” .
--- Page 27 ---
Power Flow Data Contents
 Modeling of Generator Step-Up Trans-
formers (GSU)
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
181.10.2. Modeling of Generator Step-Up Transformers (GSU)
Before setting-up the generator data, it is important to understand the two methods by which a generator
and its associated GSU are represented.
The Implicit Method
•The transformer data is included on the generator data record.
•The transformer is not explicitly represented as a transformer branch.
•The generator terminal bus is not explicitly represented.
Figure 1-4  shows that bus K is the Type 2 bus. This is the bus at which the generator will regulate/control
voltage unless the user specifies a remote bus.
Figure 1.3.  Implicit GSU Configuration – Specified as Part of the Generator
The Explicit Method
In this method, the transformer data is not specified with the generator data. It is entered separately (see
Transformer Data ) in a transformer branch data block.
In Figure 1-5 , there is an additional bus to represent the generator terminal. This is the Type 2 bus where the
generator will regulate/control voltage unless the user specifies a remote bus.
--- Page 28 ---
Power Flow Data Contents
 Multiple Machine Plants
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
19
Figure 1.4. Explicit GSU Configuration– Specified Separately from the Generator
1.10.3. Multiple Machine Plants
If a generating plant has several units, they can be represented separately even if they are connected to the
same Type 2 bus. When two or more machines are to be separately modeled at a plant, their data may be
introduced into the working case using one of two approaches.
A generator data record may be entered in activities READ , TRSQ , or RDCH  for each of the machines to be
represented, with machine powers, power limits, impedance data, and step-up transformer data for each
machine specified on separate generator data records. The plant power output and power limits are taken
as the sum of the corresponding quantities of the in-service machines at the plant. The values specified for
VS, IREG, and RMPCT, which are treated as plant quantities rather than individual machine quantities, must
be identical  on each of these generator data records.
Alternatively, a single generator record may be specified in activities READ, TREA, or RDCH with the plant
total power output, power limits, voltage setpoint, remotely regulated bus, and percent of contributed Mvar
entered. Impedance and step-up transformer data may be omitted. The PSSE power flow activities may be
used and then, any time prior to beginning switching study, fault analysis, or dynamic simulation work,
activity MCRE  may be used to introduce the individual machine impedance and step-up transformer data;
activity MCRE also apportions the total plant loading among the individual machines.
As an example, Figure 1-6  shows three Type 2 buses, each having two connected units. For generators 1
through 4, the GSU is explicitly represented while for generators 5 and 6 the GSU is implicitly represented.
Figure 1-7  shows the generator data records corresponding to Figure 1-6 .
The separate transformer data records for the explicitly represented transformers from buses 1238 and 1239
to bus 1237 are not included in Figure 1-7 .
--- Page 29 ---
Power Flow Data Contents
 Non-Transformer Branch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
20
Figure 1.5. Multiple Generators at a Single Plant
Figure 1.6.  Data Set for the Multiple Generators in Figure 1-6
1.11. Non-Transformer Branch Data
Each ac network branch to be represented in PSSE as a non-transformer branch is introduced by reading a
non-transformer branch data record.
Branches to be modeled as transformers are not specified in this data category; rather, they are specified
in Transformer Data .
When specifying a non-transformer branch between buses I and J with circuit identifier CKT, if a two-winding
transformer between buses I and J with a circuit identifier of CKT is already present in the working case, it
is replaced (i.e., the transformer is deleted from the working case and the newly specified branch is then
added to the working case).
In PSSE, the basic transmission line model is an Equivalent Pi connected between network buses. Figure 1-8
shows the required parameter data where the equivalent Pi is comprised of:
•A series impedance (R + jX).
•Two admittance branches (jB ch/2) representing the line's capacitive admittance (line charging).
--- Page 30 ---
Power Flow Data Contents
 Non-Transformer Branch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
21•Two admittance branches (G + jB) for shunt equipment units (e.g., reactors) that are connected to and
switched with the line.
To represent shunts connected to buses, that shunt data should be entered in fixed shunt and/or switched
shunt data records.
Transmission Line Equivalent Pi Model
Each non-transformer branch data record has the following format:
I,J,CKT,R,X,B,'NAME',RATE1...RATE12,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,...,O4,F4
I Branch from bus number, or extended bus name enclosed in single quotes (refer to
Extended Bus Names ).
No default allowed
J Branch to bus number, or extended bus name enclosed in single quotes.
CKT One- or two-character uppercase non-blank alphanumeric branch circuit identifier; the
first character of CKT must  not be an ampersand ( ); refer to  Multi-Section Line Group-
ing Data .
If the first character of CKT is greater than sign (>), the branch buses I and J belong to
the same substation in GIC data (see GIC Bus Substation ). Unless it is a breaker, switch,
or branch in GIC data substation, it is recommended that single circuit branches be
designated as having the circuit identifier '1'.
CKT = '1' by default
R Branch resistance; entered in pu. A value of R must be entered for each branch.
X Branch reactance; entered in pu. A non-zero value of X must be entered for each
branch. Refer to Zero Impedance Lines  for details on the treatment of branches as zero
impedance lines.
B Total branch charging susceptance; entered in pu.
B = 0.0 by default
--- Page 31 ---
Power Flow Data Contents
 Non-Transformer Branch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
22NAME Alphanumeric identifier assigned to the branch. NAME may be up to forty characters
and may contain any combination of blanks, uppercase letters, numbers and special
characters. NAME must be enclosed in single or double quotes if it contains any blanks
or special characters.
NAME must  be unique within all non-transformer and transformer branches.
NAME is blank by default
RATEn nth rating; entered in either MVA or current expressed as MVA, according to the value
specified for NXFRAT specified on the first data record (refer to Case Identification ).
Each RATEn = 0.0 (bypass check for this branch; this branch will not be included in any
examination of circuit loading) by default. Refer to activity RATE.
When specified in units of current expressed as MVA, ratings are entered as:
MVA rated = √ 3 x E base x Irated x 10-6
where:
Ebase is the base line-to-line voltage in volts of the buses to which the terminal of the
branch is connected
Irated is the branch rated phase current in amperes.
GI,BI Complex admittance of the line shunt at the bus I end of the branch; entered in pu. BI
is negative for a line connected reactor and positive for line connected capacitor.
GI + jBI = 0.0 by default
GJ,BJ Complex admittance of the line shunt at the bus J end of the branch; entered in pu.
BJ is negative for a line connected reactor nd positive for line connected capacitor.
GJ + jBJ = 0.0 by default
ST Branch status of one for in-service and zero for out-of-service;
ST = 1 by default
MET Metered end flag;
•<= 1 to designate bus I as the metered end
•>= 2 to designate bus J as the metered end
MET = 1 by default.
PSSE assigns losses to non-metered end of the branch.
LEN Line length; entered in user-selected units.
LEN = 0.0 by default.
Oi Owner number (1 through 9999). Each branch may have up to four owners. By default,
O1 is the owner to which bus I is assigned (refer to Bus Data ) and O2, O3, and O4
are zero.
--- Page 32 ---
Power Flow Data Contents
 Zero Impedance Lines
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
23Fi Fraction of total ownership assigned to owner Oi; each Fi must be positive. The Fi
values are normalized such that they sum to 1.0 before they are placed in the working
case.
Each Fi is 1.0 by default
Non-transformer branch data input is terminated with a record specifying a from bus number of zero.
The branch is treated as series compensated line when its R=0 and X is < 0.
1.11.1. Zero Impedance Lines
PSSE provides for the treatment of bus ties, jumpers, breakers, switches, and other low impedance branches
as zero impedance lines. For a branch to be treated as a zero impedance line, it must have the following
characteristics:
•Its resistance must be zero.
•Its magnitude of reactance must be less than or equal to the zero impedance line threshold tolerance,
THRSHZ.
•It must be a non-transformer branch.
During network solutions, buses connected by such lines are treated as the same bus, thus having identical
bus voltages. At the completion of each solution, the loadings on zero impedance lines are determined.
When obtaining power flow solutions, zero impedance line flows, as calculated at the end of the solution, are
preserved with the working case and are available to the power flow solution reporting activities. Similarly,
in activity SCMU , the positive, negative, and zero sequence branch currents on zero impedance lines are
determined and preserved, and are subsequently available to activity SCOP . In the ACCC , as well as activity
ASCC  and in the linearized network analysis activities, zero impedance line results are calculated and reported
as needed.
The remainder of this section contains points to be noted, and restrictions to be observed, in using zero
impedance lines.
Branch impedances may not be specified as identically zero; a non-zero reactance must be specified for all
branches, and those meeting the criteria above are treated as zero impedance lines.
The zero impedance line threshold tolerance, THRSHZ, may be changed using the category of solution pa-
rameter data via activity CHNG  or the [Solution Parameters]  dialog. Setting THRSHZ to zero disables zero
impedance line modeling, and all branches are represented with their specified impedances.
A zero impedance line may not have a transformer in parallel with it. Although not required, it is recommend-
ed that no other in-service lines exist in parallel with a zero impedance line.
A zero impedance line may have non-zero values of line charging and/or line connected shunts. This allows,
for example, a low impedance cable to be modeled as a zero impedance line.
When more than two buses are connected together by zero impedance lines in a loop arrangement, there is
no unique solution to the flows on the individual zero impedance lines that form the loop. In this case, the
reactances specified for these branches is used in determining the zero impedance line flows.
It is important to note that buses connected together by zero impedance lines are treated as a single bus by
the power flow solution activities. Hence, equipment controlling the voltages of multiple buses in a zero im-
--- Page 33 ---
Power Flow Data Contents
 System Switching Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
24pedance connected group of buses must have coordinated voltage schedules (i.e., the same voltage setpoint
should be specified for each of the voltage controlling devices). Activity CNTB  recognizes this condition in
scanning for conflicting voltage objectives, and activity REGB  may be used to generate a regulated bus report.
Similarly, if multiple voltage controlling devices are present in a group of buses connected together by zero
impedance lines, the power flow solution activities handle the boundary condition as if they are all connected
to the same bus (refer to  Setpoint Voltage Control ).
In fault analysis activities, a branch treated as a zero impedance line in the positive sequence is treated in
the same manner in the zero sequence, regardless of its zero sequence branch impedance. Zero sequence
mutual couplings involving a zero impedance line are ignored in the fault analysis solution activities.
1.12. System Switching Device Data
Breakers and switches can be represented by system switching devices in PSSE. System switching devices are
set to represent breakers or switches by setting the STYPE data element described below.
Most activities do not honor the system switching devices. System switching devices are treated as zero
impedance lines if they have characteristics of zero impedance lines; otherwise, they are treated as regular
non-transformer branches.
System switching devices are recognized in Substation Reliability Assessment (refer to Section 6.16, Calcu-
lating Substation Reliability) and activity DFAX. Substation Reliability Assessment simulates operations of
breakers to isolate faults in a substation and manual switching to restore the service to supply loads. Dis-
tribution Factor File setup activity can process automatic commands to operate and monitor breakers and
switches in Contingency Description Data File and Monitored Element Data File respectively.
As mentioned in the section Zero Impedance Lines , PSSE is able to handle a loop arrangement consisting of
zero impedance lines so that users can build a fully detailed bus/breaker model for any bus configuration,
such as a ring bus configuration. When adding a system switching device into a network model, connectivity
nodes where the terminals of a transmission line connect to the terminals of the system switching device
must be added as well. This will change a bus branch configuration which is widely used in planning studies
to a detailed bus breaker configuration and lead to a tremendous increase in number of buses. In such cases
as this, the use of the the use of the extensive substation modeling capabilities introduced in PSSE 34 is
recommended.
I,J,CKT,X,RATE1...RATE12,STATUS,NSTATUS,METERED,STYPE,NAME
I From bus number.
No default allowed
J To bus number.
No default allowed
CKT Two-character uppercase non-blank alphanumeric switching device identifier.
CKT = '1' by default
X Branch reactance; entered in pu, must be less than ZTHRES
RATEn nth rating; entered in either MVA or current expressed as MVA, according to the value
specified for NXFRAT specified on the first data record (refer to Case Identification
Data ).
--- Page 34 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
25Each RATEn = 0.0 (bypass check for this branch; this branch will not be included in any
examination of circuit loading) by default. Refer to activity RATE.
STATUS 1 for close, 0 for open
NSTATUS Normal service status, 1 for normally open and 0 for normally close
METERD Metered end
STYPE Switching device type
1 - Generic connector
2 - Circuit breaker
3 - Disconnect switch
NAME System switching device name
System Switching Device data input is terminated with a record specifying a from bus number of zero.
1.13. Transformer Data
Each ac transformer to be represented in PSSE is introduced through transformer data record blocks that
specify all the data required to model transformers in power flow calculations, with one exception. That
exception is an optional set of ancillary data, transformer impedance correction tables, which define the
manner in which transformer impedance changes as off-nominal turns ratio or phase shift angle is adjusted.
Those data records are described in Transformer Impedance Cor .
Both two-winding and three-winding transformers are specified in transformer data record blocks. Two-wind-
ing transformers require a block of four data records. Three-winding transformers require five data records.
t1:
t2:t = t 1 / t2; transformer turns ratio
winding 1 turns ratio in kV or pu on
bus voltage base or winding volt-
age base
winding 2 turns ratio in kV or pu on
bus voltage base or winding volt-
age base
Figure 1-9  shows the transformer winding configurations.
--- Page 35 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
26
Figure 1.7. Two and Three-winding Transformer Configurations Related to Data
Records
The five record transformer data block for three-winding transformers has the following format:
I,J,K,CKT,CW,CZ,CM,MAG1,MAG2,NMETR,’NAME’,STAT,O1,F1,...,O4,F4,VECGRP,ZCOD
R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR
WINDV1,NOMV1,ANG1,RATE11...RATE121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,                                                     NTP1,TAB1,CR1,CX1,CNXA1,NODE1
WINDV2,NOMV2,ANG2,RATE12...RATE122,COD2,CONT2,RMA2,RMI2,VMA2,VMI2,                                                     NTP2,TAB2,CR2,CX2,CNXA2,NODE2
WINDV3,NOMV3,ANG3,RATE13...RATE123,COD3,CONT3,RMA3,RMI3,VMA3,VMI3,                                                     NTP3,TAB3,CR3,CX3,CNXA3,NODE3
The four-record transformer data block for two-winding transformers is a subset of the data required for
three-winding transformers and has the following format:
I,J,K,CKT,CW,CZ,CM,MAG1,MAG2,NMETR,’NAME’,STAT,O1,F1,...,O4,F4,VECGRP
R1-2,X1-2,SBASE1-2
WINDV1,NOMV1,ANG1,RATE11...RATE121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,                                                     NTP1,TAB1,CR1,CX1,CNXA1,NODE1
WINDV2,NOMV2
Control parameters for the automatic adjustment of transformers and phase shifters are specified on the
third record of the two-winding transformer data block, and on the third through fifth records of the three-
winding transformer data block. All transformers are adjustable and the control parameters may be specified
either at the time of raw data input or subsequently via activity CHNG or the transformer [Spreadsheets] . Any
two-winding transformer and any three-winding transformer winding for which no control data is provided
has default data assigned to it; the default data is such that the two-winding transformer or three-winding
transformer winding is treated as locked.
Refer to Transformer Sequence Numbers and Three-Winding Transformer Notes  for additional details on the
three-winding transformer model used in PSSE.
When specifying a two-winding transformer between buses I and J with circuit identifier CKT, if a nontrans-
former branch between buses I and J with a circuit identifier of CKT is already present in the working case, it
--- Page 36 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
27is replaced (i.e., the nontransformer branch is deleted from the working case and the newly specified two-
winding transformer is then added to the working case).
All data items on the first record are specified for both two- and three-winding transformers except for ZCOD,
which is specified only for three-winding transformers.
I The bus number, or extended bus name enclosed in single quotes (refer to Extended
Bus Names ), of the bus to which Winding 1 is connected. The transformer's magne-
tizing admittance is modeled on Winding 1. Winding 1 is the only winding of a two-
winding transformer for which tap ratio or phase shift angle may be adjusted by the
power flow solution activities; any winding(s) of a three-winding transformer may be
adjusted.
No default is allowed
J The bus number, or extended bus name enclosed in single quotes, of the bus to which
Winding 2 is connected.
No default is allowed
K The bus number, or extended bus name enclosed in single quotes, of the bus to which
Winding 3 is connected. Zero is used to indicate that no third winding is present (i.e.,
that a two-winding rather than a three-winding transformer is being specified).
K = 0 by default.
CKT One- or two-character uppercase non-blank alphanumeric transformer circuit identi-
fier; the first character of CKT must not  be an ampersand ( ), at sign ( @ ), or asterisk
( * ); refer to Multi-Section Line Grouping Data  and Section 6.15.2, Outage Statistics
Data File Contents.
CKT = '1' by default.
CW The winding data I/O code defines the units in which the turns ratios WINDV1, WINDV2
and WINDV3 are specified (the units of RMAn and RMIn are also governed by CW when
|CODn| is 1 or 2):
•1 for off-nominal turns ratio in pu of winding bus base voltage
•2 for winding voltage in kV
•3 for off-nominal turns ratio in pu of nominal winding voltage, NOMV1, NOMV2
and NOMV3
CW = 1 by default.
CZ The impedance data I/O code defines the units in which the winding impedances R1-2,
X1-2, R2-3, X2-3, R3-1 and X3-1 are specified:
•1 for resistance and reactance in pu on system MVA base and winding voltage base
•2 for resistance and reactance in pu on a specified MVA base and winding voltage
base
•3 for transformer load loss in watts and impedance magnitude in pu on a specified
MVA base and winding voltage base
--- Page 37 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
28In specifying transformer leakage impedances, the base voltage values are always the
nominal winding voltages that are specified on the third, fourth and fifth records of
the transformer data block (NOMV1, NOMV2 and NOMV3). If the default NOMVn is
not specified, it is assumed to be identical to the winding n bus base voltage.
CZ = 1 by default.
CM The magnetizing admittance I/O code defines the units in which MAG1 and MAG2 are
specified:
•1 for complex admittance in pu on system MVA base and Winding 1 bus voltage base
•2 for no load loss in watts and exciting current in pu on Winding 1 to two MVA base
(SBASE1-2) and nominal Winding 1 voltage, NOMV1.
CM = 1 by default.
MAG1, MAG2 The transformer magnetizing admittance connected to ground at bus I.
When CM is 1, MAG1 and MAG2 are the magnetizing conductance and susceptance,
respectively, in pu on system MVA base and Winding 1 bus voltage base. When a non-
zero MAG2 is specified, it should be entered as a negative quantity.
When CM is 2, MAG1 is the no load loss in watts and MAG2 is the exciting current
in pu on Winding 1 to two MVA base (SBASE1-2) and nominal Winding 1 voltage
(NOMV1). For three-phase transformers or three-phase banks of single phase trans-
formers, MAG1 should specify the three-phase no-load loss. When a non-zero MAG2
is specified, it should be entered as a positive quantity.
MAG1 = 0.0 and MAG2 = 0.0 by default.
NMETR The nonmetered end code of either 1 (for the Winding 1 bus) or 2 (for the Winding 2
bus). In addition, for a three-winding transformer, 3 (for the Winding 3 bus) is a valid
specification of NMETR.
NMETR = 2 by default
NAME Alphanumeric identifier assigned to the transformer. NAME may be up to forty char-
acters and may contain any combination of blanks, uppercase letters, numbers and
special characters. NAME must  be enclosed in single or double quotes if it contains
any blanks or special characters.
NAME must  be unique within all non-transformer and transformer branches.
NAME is blank by default
STAT Transformer status of one for in-service and zero for out-of-service.
In addition, for a three-winding transformer, the following values of STAT provide for
one winding out-of-service with the remaining windings in-service:
•2 for only Winding 2 out-of-service
•3 for only Winding 3 out-of-service
•4 for only Winding 1 out-of-service
--- Page 38 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
29STAT = 1 by default
Oi An owner number (1 through 9999). Each transformer may have up to four owners.
By default, O1 is the owner to which bus I is assigned and O2, O3, and O4 are zero.
Fi The fraction of total ownership assigned to owner Oi; each Fi must be positive. The Fi
values are normalized such that they sum to 1.0 before they are placed in the working
case.
Each Fi is 1.0 by default
VECGRP Alphanumeric identifier specifying vector group based on transformer winding con-
nections and phase angles. VECGRP value is used for information purpose only.
VECGRP is 12 blanks by default
ZCOD Method to be used in deriving actual transformer impedances in applying transformer
impedance adjustment tables:
•0 apply impedance adjustment factors to winding impedances
•1 apply impedance adjustment factors to bus-to-bus impedances
ZCOD = 0 by default
ZCOD value is used only for three winding transformers. It is not used for two winding
transformers.
For three winding transformers, winding impedances are the equivalent T-model im-
pedances Z1, Z2 and Z3; and the bus-to-bus impedances are impedances Z12, Z23
and Z31.
For three winding transformers and bus-to-bus impedance correction factors, only one
of the three windings must be adjustable (only one of COD1, COD2 and COD3 can be
non-zero).
The first three data items on the second record are read for both two- and three-winding transformers; the
remaining data items are used only  for three-winding transformers:
R1-2, X1-2 The measured impedance of the transformer between the buses to which its first and
second windings are connected.
When CZ is 1, they are the resistance and reactance, respectively, in pu on system MVA
base and winding voltage base.
When CZ is 2, they are the resistance and reactance, respectively, in pu on Winding 1
to 2 MVA base (SBASE1-2) and winding voltage base.
When CZ is 3, R1-2 is the load loss in watts, and X1-2 is the impedance magnitude
in pu on Winding 1 to 2 MVA base (SBASE1-2) and winding voltage base. For three-
phase transformers or three-phase banks of single phase transformers, R1-2 should
specify the three-phase load loss.
R1-2 = 0.0 by default, but no default is allowed for X1-2.
SBASE1-2 The Winding 1 to 2 three-phase base MVA of the transformer. SBASE1-2 = SBASE (the
system base MVA) by default.
--- Page 39 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
30R2-3, X2-3 The measured impedance of a three-winding transformer between the buses to which
its second and third windings are connected; ignored for a two-winding transformer.
When CZ is 1, they are the resistance and reactance, respectively, in pu on system MVA
base and winding voltage base.
When CZ is 2, they are the resistance and reactance, respectively, in pu on Winding 2
to 3 MVA base (SBASE2-3) and winding voltage base.
When CZ is 3, R2-3 is the load loss in watts, and X2-3 is the impedance magnitude
in pu on Winding 2 to 3 MVA base (SBASE2-3) and winding voltage base. For three-
phase transformers or three-phase banks of single phase transformers, R2-3 should
specify the three-phase load loss.
R2-3 = 0.0 by default, but no default is allowed for X2-3.
SBASE2-3 The Winding 2 to 3 three-phase base MVA of a three-winding transformer; ignored for
a two-winding transformer. SBASE2-3 = SBASE (the system base MVA) by default.
R3-1, X3-1 The measured impedance of a three-winding transformer between the buses to which
its third and first windings are connected; ignored for a two-winding transformer.
When CZ is 1, they are the resistance and reactance, respectively, in pu on system MVA
base and winding voltage base.
When CZ is 2, they are the resistance and reactance, respectively, in pu on Winding 3
to 1 MVA base (SBASE3-1) and winding voltage base.
When CZ is 3, R3-1 is the load loss in watts, and X3-1 is the impedance magnitude
in pu on Winding 3 to 1 MVA base (SBASE3-1) and winding voltage base. For three-
phase transformers or three-phase banks of single phase transformers, R3-1 should
specify the three-phase load loss.
R3-1 = 0.0 by default, but no default is allowed for X3-1.
SBASE3-1 The Winding 3 to 1 three-phase base MVA of a three-winding transformer; ignored for
a two-winding transformer.
SBASE3-1 = SBASE (the system base MVA) by default.
VMSTAR The voltage magnitude at the hidden star point bus; entered in pu.
VMSTAR = 1.0 by default.
ANSTAR The bus voltage phase angle at the hidden star point bus; entered in degrees.
ANSTAR = 0.0 by default
All data items on the third record are read for both two- and three-winding transformers:
WINDV1 When CW is 1, WINDV1 is the Winding 1 off-nominal turns ratio in pu of Winding 1
bus base voltage; WINDV1 = 1.0 by default.
When CW is 2, WINDV1 is the actual Winding 1 voltage in kV; WINDV1 is equal to the
base voltage of bus I by default.
--- Page 40 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
31When CW is 3, WINDV1 is the Winding 1 off-nominal turns ratio in pu of nominal Wind-
ing 1 voltage, NOMV1; WINDV1 = 1.0 by default.
NOMV1 The nominal (rated) Winding 1 voltage base in kV, or zero to indicate that nominal
Winding 1 voltage is assumed to be identical to the base voltage of bus I. NOMV1 is
used in converting magnetizing data between physical units and per unit admittance
values when CM is 2. NOMV1 is used in converting tap ratio data between values in
per unit of nominal Winding 1 voltage and values in per unit of Winding 1 bus base
voltage when CW is 3.
NOMV1 = 0.0 by default
ANG1 The winding one phase shift angle in degrees. For a two-winding transformer, ANG1
is positive when the winding one bus voltage leads the winding two bus voltage; for a
three-winding transformer, ANG1 is positive when the winding one bus voltage leads
the T (or star) point bus voltage. ANG1 must be greater than -180.0º and less than or
equal to +180.0º. ANG1 = 0.0 by default.
RATEn1 Winding 1’s twelve three-phase ratings, entered in either MVA or current expressed
as MVA, according to the value specified for XFRRAT specified on the first data record
(refer to Case Identification Data ). Each RATEn1 = 0.0 (bypass loading limit check for
this transformer winding) by default.
COD1 The transformer control mode for automatic adjustments of the Winding 1 tap or
phase shift angle during power flow solutions:
•0 - for fixed tap and fixed phase shift
•±1 - for voltage control
•±2 - for reactive power flow control
•±3 - for active power flow control
•±4 - for control of a dc line quantity (valid only for two-winding transformers)
•±5 - for asymmetric active power flow control
If the control mode is entered as a positive number, automatic adjustment of this trans-
former winding is enabled when the corresponding adjustment is activated during
power flow solutions; a negative control mode suppresses the automatic adjustment
of this transformer winding.
COD1 = 0 by default.
CONT1 The bus number, or extended bus name enclosed in single quotes (refer to Extended
Bus Names ), of the bus for which voltage is to be controlled by the transformer turns
ratio adjustment option of the power flow solution activities when COD1 is 1. CONT1
should be non-zero only for voltage controlling transformer windings.
CONT1 may specify a bus other than I, J, or K; in this case, the sign of CONT1 defines
the location of the controlled bus relative to the transformer winding. If CONT1 is
entered as a positive number, or a quoted extended bus name, the ratio is adjusted
as if bus CONT1 is on the Winding 2 or Winding 3 side of the transformer; if CONT1
is entered as a negative number, or a quoted extended bus name with a minus sign
--- Page 41 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
32preceding the first character, the ratio is adjusted as if bus |CONT1| is on the Winding
1 side of the transformer.
CONT1 = 0 by default.
RMA1, RMI1 When |COD1| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Off-nominal turns ratio in pu of Winding 1 bus base voltage when |COD1| is 1 or 2
and CW is 1; RMA1 = 1.1 and RMI1 = 0.9 by default.
•Actual Winding 1 voltage in kV when |COD1| is 1 or 2 and CW is 2. No default is
allowed.
•Off-nominal turns ratio in pu of nominal Winding 1 voltage (NOMV1) when |COD1|
is 1 or 2 and CW is 3; RMA1 = 1.1 and RMI1 = 0.9 by default.
•Phase shift angle in degrees when |COD1| is 3 or 5. No default is allowed.
Not used when |COD1| is 0 or 4; RMA1 = 1.1 and RMI1 = 0.9 by default.
VMA1, VMI1 When |COD1| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Voltage at the controlled bus (bus |CONT1|) in pu when |COD1| is 1. VMA1 = 1.1
and VMI1 = 0.9 by default.
•Reactive power flow into the transformer at the Winding 1 bus end in Mvar when
|COD1| is 2. No default is allowed.
•Active power flow into the transformer at the Winding 1 bus end in MW when |
COD1| is 3 or 5. No default is allowed.
Not used when |COD1| is 0 or 4.
VMA1 = 1.1 and VMI1 = 0.9 by default.
NTP1 The number of tap positions available; used when COD1 is 1 or 2. NTP1 must be be-
tween 2 and 9999.
NTP1 = 33 by default.
TAB1 The number of a transformer impedance correction table if this transformer winding’s
impedance is to be a function of either off-nominal turns ratio or phase shift angle
(refer to Transformer Impedance Correction Tables ), or 0 if no transformer impedance
correction is to be applied to this transformer winding. TAB1 = 0 by default.
For three winding transformers, these impedance correction factors are applied to the
equivalent T-model impedance Z1 when ZCOD=0 and to the bus-to-bus impedance
Z12 when ZCOD=1.
CR1, CX1 The load drop compensation impedance for voltage controlling transformers entered
in pu on system base quantities; used when COD1 is 1.
CR1 + j CX1 = 0.0 by default
--- Page 42 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
33CNXA1 Winding connection angle in degrees; used when COD1 is 5. There are no restrictions
on the value specified for CNXA1; if it is outside of the range from -90.0 to +90.0,
CNXA1 is normalized to within this range.
CNXA1 = 0.0 by default.
NODE1 A node number of bus CONT1. The bus section of bus CONT1 to which node NODE1 is
connected is the bus section for which voltage is to be controlled by the transformer
turns ratio adjustment option of the power flow solution activities when COD1 is 1.
NODE1 should be non-zero only for voltage controlling transformer windings. If bus
CONT1 is not in a substation, NODE1 must be specified as 0.
NODE1 = 0 by default.
The first two data items on the fourth record are read for both two- and three-winding transformers; the
remaining data items are used only  for three-winding transformers:
WINDV2 When CW is 1, WINDV2 is the Winding 2 off-nominal turns ratio in pu of Winding 2
bus base voltage; WINDV2 = 1.0 by default.
When CW is 2, WINDV2 is the actual Winding 2 voltage in kV; WINDV2 is equal to the
base voltage of bus J by default.
When CW is 3, WINDV2 is the Winding 2 off-nominal turns ratio in pu of nominal Wind-
ing 2 voltage, NOMV2; WINDV2 = 1.0 by default.
NOMV2 The nominal (rated) Winding 2 voltage base in kV, or zero to indicate that nominal
Winding 2 voltage is assumed to be identical to the base voltage of bus J. NOMV2 is
used in converting tap ratio data between values in per unit of nominal Winding 2
voltage and values in per unit of Winding 2 bus base voltage when CW is 3.
NOMV2 = 0.0 by default.
ANG2 The winding two phase shift angle in degrees. ANG2 is ignored for a two-winding
transformer. For a three-winding transformer, ANG2 is positive when the winding two
bus voltage leads the T (or star) point bus voltage. ANG2 must be greater than -180.0º
and less than or equal to +180.0º.
ANG2 = 0.0 by default.
RATEn2 Winding 2’s twelve three-phase ratings, entered in either MVA or current expressed
as MVA, according to the value specified for XFRRAT specified on the first data record
(refer to Case Identification Data ). Each RATEn2 = 0.0 (bypass loading limit check for
this transformer winding) by default.
COD2 The transformer control mode for automatic adjustments of the Winding 2 tap or
phase shift angle during power flow solutions:
•0 - for fixed tap and fixed phase shift
•±1 - for voltage control
•±2 - for reactive power flow control
•±3 - for active power flow control
•±5 - for asymmetric active power flow control
--- Page 43 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
34If the control mode is entered as a positive number, automatic adjustment of this trans-
former winding is enabled when the corresponding adjustment is activated during
power flow solutions; a negative control mode suppresses the automatic adjustment
of this transformer winding.
COD2 = 0 by default.
CONT2 The bus number, or extended bus name enclosed in single quotes (refer to Extended
Bus Names ), of the bus for which voltage is to be controlled by the transformer turns
ratio adjustment option of the power flow solution activities when COD2 is 1. CONT2
should be non-zero only for voltage controlling transformer windings.
CONT2 may specify a bus other than I, J, or K; in this case, the sign of CONT2 defines
the location of the controlled bus relative to the transformer winding. If CONT2 is
entered as a positive number, or a quoted extended bus name, the ratio is adjusted
as if bus CONT2 is on the Winding 1 or Winding 3 side of the transformer; if CONT2
is entered as a negative number, or a quoted extended bus name with a minus sign
preceding the first character, the ratio is adjusted as if bus |CONT2| is on the Winding
2 side of the transformer.
CONT2 = 0 by default.
RMA2, RMI2 When |COD2| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Off-nominal turns ratio in pu of Winding 2 bus base voltage when |COD2| is 1 or 2
and CW is 1; RMA2 = 1.1 and RMI2 = 0.9 by default.
•Actual Winding 2 voltage in kV when |COD2| is 1 or 2 and CW is 2. No default is
allowed.
•Off-nominal turns ratio in pu of nominal Winding 2 voltage (NOMV2) when |COD2|
is 1 or 2 and CW is 3; RMA2 = 1.1 and RMI2 = 0.9 by default.
•Phase shift angle in degrees when |COD2| is 3 or 5. No default is allowed.
Not used when |COD2| is 0.
RMA2 = 1.1 and RMI2 = 0.9 by default.
VMA2, VMI2 When |COD2| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Voltage at the controlled bus (bus |CONT2|) in pu when |COD2| is 1. VMA2 = 1.1
and VMI2 = 0.9 by default.
•Reactive power flow into the transformer at the Winding 2 bus end in Mvar when
|COD2| is 2. No default is allowed.
•Active power flow into the transformer at the Winding 2 bus end in MW when |
COD2| is 3 or 5. No default is allowed.
Not used when |COD2| is 0.
VMA2 = 1.1 and VMI2 = 0.9 by default
--- Page 44 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
35NTP2 The number of tap positions available; used when COD2 is 1 or 2. NTP2 must be be-
tween 2 and 9999.
NTP2 = 33 by default
TAB2 The number of a transformer impedance correction table if this transformer winding’s
impedance is to be a function of either off-nominal turns ratio or phase shift angle
(refer to Transformer Impedance Correction Tables ), or 0 if no transformer impedance
correction is to be applied to this transformer winding.
TAB2 = 0 by default.
For three winding transformers, these impedance correction factors are applied to the
equivalent T-model impedance Z2 when ZCOD=0 and to the bus-to-bus impedance
Z23 when ZCOD=1.
CR2, CX2 The load drop compensation impedance for voltage controlling transformers entered
in pu on system base quantities; used when COD2 is 1.
CR2 + j CX2 = 0.0 by default.
CNXA2 Winding connection angle in degrees; used when COD2 is 5. There are no restrictions
on the value specified for CNXA2; if it is outside of the range from -90.0 to +90.0,
CNXA2 is normalized to within this range.
CNXA2 = 0.0 by default.
NODE2 A node number of bus CONT2. The bus section of bus CONT2 to which node NODE2 is
connected is the bus section for which voltage is to be controlled by the transformer
turns ratio adjustment option of the power flow solution activities when COD2 is 1.
NODE2 should be non-zero only for voltage controlling transformer windings. If bus
CONT2 is not in a substation, NODE2 must be specified as 0.
NODE2 = 0 by default.
The fifth data record is specified only for three-winding transformers:
WINDV3 When CW is 1, WINDV3 is the Winding 3 off-nominal turns ratio in pu of Winding 3
bus base voltage; WINDV3 = 1.0 by default.
When CW is 2, WINDV3 is the actual Winding 3 voltage in kV; WINDV3 is equal to the
base voltage of bus K by default.
When CW is 3, WINDV3 is the Winding 3 off-nominal turns ratio in pu of nominal Wind-
ing 3 voltage, NOMV3; WINDV3 = 1.0 by default.
NOMV3 The nominal (rated) Winding 3 voltage base in kV, or zero to indicate that nominal
Winding 3 voltage is assumed to be identical to the base voltage of bus K. NOMV3
is used in converting tap ratio data between values in per unit of nominal Winding 3
voltage and values in per unit of Winding 3 bus base voltage when CW is 3.
NOMV3 = 0.0 by default
ANG3 The winding three phase shift angle in degrees. ANG3 is positive when the winding
three bus voltage leads the T (or star) point bus voltage. ANG3 must be greater than
-180.0º and less than or equal to +180.0º.
--- Page 45 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
36ANG3 = 0.0 by default
RATEn3 Winding 3’s twelve three-phase ratings, entered in either MVA or current expressed
as MVA, according to the value specified for XFRRAT specified on the first data record
(refer to Case Identification Data ).
Each RATEn3 = 0.0 (bypass loading limit check for this transformer winding) by default.
COD3 The transformer control mode for automatic adjustments of the Winding 3 tap or
phase shift angle during power flow solutions:
•0 - for fixed tap and fixed phase shift
•±1 - for voltage control
•±2 - for reactive power flow control
•±3 - for active power flow control
•±5 - for asymmetric active power flow control
If the control mode is entered as a positive number, automatic adjustment of this trans-
former winding is enabled when the corresponding adjustment is activated during
power flow solutions; a negative control mode suppresses the automatic adjustment
of this transformer winding.
COD3 = 0 by default
CONT3 The bus number, or extended bus name enclosed in single quotes (refer to Extended
Bus Names ), of the bus for which voltage is to be controlled by the transformer turns
ratio adjustment option of the power flow solution activities when COD3 is 1. CONT3
should be non-zero only for voltage controlling transformer windings.
CONT3 may specify a bus other than I, J, or K; in this case, the sign of CONT3 defines
the location of the controlled bus relative to the transformer winding. If CONT3 is
entered as a positive number, or a quoted extended bus name, the ratio is adjusted
as if bus CONT3 is on the Winding 1 or Winding 2 side of the transformer; if CONT3
is entered as a negative number, or a quoted extended bus name with a minus sign
preceding the first character, the ratio is adjusted as if bus |CONT3| is on the Winding
3 side of the transformer.
CONT3 = 0 by default
RMA3, RMI3 When |COD3| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Off-nominal turns ratio in pu of Winding 3 bus base voltage when |COD3| is 1 or 2
and CW is 1; RMA3 = 1.1 and RMI3 = 0.9 by default.
•Actual Winding 3 voltage in kV when |COD3| is 1 or 2 and CW is 2. No default is
allowed.
•Off-nominal turns ratio in pu of nominal Winding 3 voltage (NOMV3) when |COD3|
is 1 or 2 and CW is 3; RMA3 = 1.1 and RMI3 = 0.9 by default.
•Phase shift angle in degrees when |COD3| is 3 or 5. No default is allowed.
--- Page 46 ---
Power Flow Data Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
37Not used when |COD3| is 0.
RMA3 = 1.1 and RMI3 = 0.9 by default
VMA3, VMI3 When |COD3| is 1, 2, 3, or 5, the upper and lower limits, respectively, of one of the
following:
•Voltage at the controlled bus (bus |CONT3|) in pu when |COD3| is 1. VMA3 = 1.1
and VMI3 = 0.9 by default.
•Reactive power flow into the transformer at the Winding 3 bus end in Mvar when
|COD3| is 2. No default is allowed.
•Active power flow into the transformer at the Winding 3 bus end in MW when |
COD3| is 3 or 5. No default is allowed.
Not used when |COD3| is 0.
VMA3 = 1.1 and VMI3 = 0.9 by default
NTP3 The number of tap positions available; used when COD3 is 1 or 2. NTP3 must be be-
tween 2 and 9999.
NTP3 = 33 by default
TAB3 The number of a transformer impedance correction table if this transformer winding’s
impedance is to be a function of either off-nominal turns ratio or phase shift angle
(refer to Transformer Impedance Correction Tables ), or 0 if no transformer impedance
correction is to be applied to this transformer winding.
TAB3 = 0 by default
For three winding transformers, these impedance correction factors are applied to the
equivalent T-model impedance Z3 when ZCOD=0 and to the bus-to-bus impedance
Z31 when ZCOD=1.
CR3, CX3 The load drop compensation impedance for voltage controlling transformers entered
in pu on system base quantities; used when COD3 is 1.
CR3 + j CX3 = 0.0 by default
CNXA3 Winding connection angle in degrees; used when COD3 is 5. There are no restrictions
on the value specified for CNXA3; if it is outside of the range from -90.0 to +90.0,
CNXA3 is normalized to within this range.
CNXA3 = 0.0 by default
NODE3 A node number of bus CONT3. The bus section of bus CONT3 to which node NODE3 is
connected is the bus section for which voltage is to be controlled by the transformer
turns ratio adjustment option of the power flow solution activities when COD3 is 1.
NODE3 should be non-zero only for voltage controlling transformer windings. If bus
CONT3 is not in a substation, NODE3 must be specified as 0.
NODE3 = 0 by default.
Transformer data input is terminated with a record specifying a Winding 1 bus number of zero.
--- Page 47 ---
Power Flow Data Contents
 Three-Winding Transformer Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
381.13.1. Three-Winding Transformer Notes
The transformer data record blocks described in Transformer Data  provide for the specification of both two-
winding transformers and three-winding transformers. A three-winding transformer is modeled in PSSE as
a grouping of three two-winding transformers, where each of these two-winding transformers models one
of the windings. While most of the three-winding transformer data is stored in the two-winding transformer
data arrays, it is accessible for reporting and modification only as three-winding transformer data.
In deriving winding impedances from the measured impedance data input values, one winding with a small
impedance, in many cases negative, often results. In the extreme case, it is possible to specify a set of mea-
sured impedances that themselves do not individually appear to challenge the precision limits of typical
power system calculations, but which result in one winding impedance of nearly (or identically) 0.0. Such
data could result in precision difficulties, and hence inaccurate results, when processing the system matrices
in power flow and short circuit calculations.
Whenever a set of measured impedance results in a winding reactance that is identically 0.0, a warning
message is printed by the three-winding transformer data input or data changing function, and the winding’s
reactance is set to the zero impedance line threshold tolerance (or to 0.0001 if the zero impedance line
threshold tolerance itself is 0.0). Whenever a set of measured impedances results in a winding impedance for
which magnitude is less than 0.00001, a warning message is printed. As with all warning and error messages
produced during data input and data modification phases of PSSE, the user should resolve the cause of the
message (e.g., was correct input data specified?) and use engineering judgement to resolve modeling issues
(e.g., is this the best way to model this transformer or would some other modeling be more appropriate?).
Activity BRCH may be used to detect the presence of branch reactance magnitudes less than a user-specified
threshold tolerance; its use is always recommended whenever the user begins power system analysis work
using a new or modified system model.
1.13.2.  Example Two-Winding Transformer Data Records
Figure1-10  shows the data records for a 50 MVA, 138/34.5 kV two-winding transformer connected to system
buses with nominal voltages of 134 kV and 34.5 kV, and sample data on 100 MVA system base and winding
voltage bases of 134 kV and 34.5 kV.
Example of 2-Winding Transformer:
Data Formats
I,J,K,CKT,CW,CZ,CM,MAG1,MAG2,NMETR,’NAME’,STAT,O1,F1,...,O4,F4,VECGRP
R1-2,X1-2,SBASE1-2
WINDV1,NOMV1,ANG1,RATE11...RATE121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,                                                     NTP1,TAB1,CR1,CX1,CNXA1,NODE1
WINDV2,NOMV2
Data
6150, 6151, 0, ’1’, 1, 1, 1, 0.0, 0.0, 2, ’TWO-WINDINGS’, 1, 5, 1.0
0.0, 0.30, 100.0
--- Page 48 ---
Power Flow Data Contents
 Example Three-Winding  Transformer
Data Records
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
391.0299, 0.0, 0.0, 50.0, 60.0, 75.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6151, 1.1, 0.9, 1.025, 1.0, 33, 0, 0.0, 0.0
1.0, 0.0
Sample Data for Two-Winding Transformer
1.13.3. Example Three-Winding  Transformer Data Records
Figure 1-11  shows the data records for a 300 MVA, 345/138/13.8 kV three-winding transformer connected
to system buses with nominal voltages of 345 kV, 138 kV and 13.8 kV, respectively, and sample data on 100
MVA system base and winding base voltages of 345 kV, 138 kV and 13.8 kV.
Example of 3-Winding Transformer:
Data Formats
I,J,K,CKT,CW,CZ,CM,MAG1,MAG2,NMETR,’NAME’,STAT,O1,F1,...,O4,F4,VECGRP,ZCOD
R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR
WINDV1,NOMV1,ANG1,RATE11...RATE121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,                                                     NTP1,TAB1,CR1,CX1,CNXA1,NODE1
WINDV2,NOMV2,ANG2,RATE12...RATE122,COD2,CONT2,RMA2,RMI2,VMA2,VMI2,                                                     NTP2,TAB2,CR2,CX2,CNXA2,NODE2
WINDV3,NOMV3,ANG3,RATE13...RATE123,COD3,CONT3,RMA3,RMI3,VMA3,VMI3,                                                     NTP3,TAB3,CR3,CX3,CNXA3,NODE3
Data
3001, 3002, 3000, ’1’, 1, 1, 1, 0.0, 0.0, 2, ’THREEWINDING’, 1, 5, 1.0
0.003, 0.03, 100.0, 0.001, 0.03, 100.0, 0.001, 0.035, 100.0, 1.025, 0.0
1.00, 0.0, 0.0, 300, 400, 600, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3001, 1.1, 0.9, 1.04, 1.0, 33, 0, 0.0, 0.0, 0.0
1.02, 0.0, 0.0, 300, 400, 600
1.00, 0.0, 0.0, 50, 60, 75
--- Page 49 ---
Power Flow Data Contents
 Two Winding Transformer Vector
Groups
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
40
Sample Data for Three-Winding Transformer
1.13.4. Two Winding Transformer Vector Groups
Table 1-1, Examples of Two Winding Transformer Vector Groups  shows examples of two winding transformer
vector groups, corresponding phase angles and connection codes. A different winding clock position can be
used by appropriately specifying the phase angle ANG1.
Table 1.1. Examples of Two Winding Transformer Vector Groups
Vector Group PSSE Phase
Angle (ANG1)Transformer
TypeConnection
Code (CC)Transformer
TypeConnection
Code (CC)
YNyn0 0 shell 11 core 20
YNyn6 180 shell 11 core 20
YNd1 30 shell 12
YNd11 -30 shell 12
YNd5 150 shell 12
YNd7 -150 shell 12
ZNd0 0 shell 12 core 17
ZNd1 30 shell 12 core 17
ZNd6 180 shell 12 core 17
ZNd7 -150 shell 12 core 17
ZNyn1 30 shell 12 core 17
ZNyn11 -30 shell 12 core 17
ZNyn5 150 shell 12 core 17
ZNyn7 -150 shell 12 core 17
ZNy1 30 shell 12 core 17
ZNy11 -30 shell 12 core 17
ZNy5 150 shell 12 core 17
ZNy7 -150 shell 12 core 17
Dyn1 30 shell 13
Dyn11 -30 shell 13
Dyn5 150 shell 13
Dyn7 -150 shell 13
--- Page 50 ---
Power Flow Data Contents
 Three Winding Transformer Vector
Groups
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
41Dzn0 0 shell 13 core 16
Dzn1 30 shell 13 core 16
Dzn6 180 shell 13 core 16
Dzn7 -150 shell 13 core 16
YNzn1 30 shell 13 core 16
YNzn11 -30 shell 13 core 16
YNzn5 150 shell 13 core 16
YNzn7 -150 shell 13 core 16
Yzn1 30 shell 13 core 16
Yzn11 -30 shell 13 core 16
Yzn5 150 shell 13 core 16
Yzn7 -150 shell 13 core 16
Dd0 0 shell 14
Dd6 180 shell 14
Dy1 30 shell 14
Dy11 -30 shell 14
Dy5 150 shell 14
Dy7 -150 shell 14
Yd1 30 shell 14
Yd11 -30 shell 14
Yd5 150 shell 14
Yd7 -150 shell 14
YNy0 0 shell 14 core 12
YNy6 180 shell 14 core 12
Yyn0 0 shell 14 core 13
Yyn6 180 shell 14 core 13
Yy0 0 shell 14
Yy6 180 shell 14
YNa0 0 core 18 or 19 shell 21
Ya0 0 core 22 shell 14
1.13.5. Three Winding Transformer Vector Groups
Table 5-1, Branch Parameter Data Check Options shows examples of three winding transformer vector
groups, corresponding phase angles and connection codes. A different winding clock position can be used
by appropriately specifying the phase angles ANG1, ANG2 and ANG3.
Vector groups are specified forming combinations of allowed winding connections and clock positions for
various transformer connection codes.
1.13.6. Clock Positions and Phase Angles specified in Transformer Power
Flow Data
Clock Position Phase Angles (ANG1/ANG2/ANG3)
--- Page 51 ---
Power Flow Data Contents
 CC=11
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
420 0
6 180
1 -30
5 -150
7 150
11 30
1.13.7. CC=11
Allowed Winding Configurations Allowed Clock Positions
Winding 1 YN 0, 6
Winding 2 yn 0, 6
Winding 3 yn 0, 6
Examples YN0yn6yn0, ANG1=0, ANG2=180, ANG3=0
YN0yn0yn0, ANG1=0, ANG2=0, ANG3=0
1.13.8. CC=12
Allowed Winding Configurations Allowed Clock Positions
Winding 1 YN 0, 6
Winding 2 yn 0, 6
y 0, 6 Winding 3
d 1, 5, 7, 11
Examples YN0yn6d5, ANG1=0, ANG2=180, ANG3=-150
YN6yn0y0 ANG1=180, ANG2=0, ANG3=0
1.13.9. CC=13
Allowed Winding Configurations Allowed Clock Positions
Winding 1 D 1, 5, 7, 11
Winding 2 yn 0, 6
Winding 3 d 1, 5, 7, 11
Examples D1YN0d1, ANG1=-30, ANG2=0, ANG3=0
D5YN0d5, ANG1=-150, ANG2=0, ANG3=-150
1.13.10. CC=14
Allowed Winding Configurations Allowed Clock Positions
Y 0, 6 Winding 1
D 1, 5, 7, 11
Winding 2 y 0, 6
--- Page 52 ---
Power Flow Data Contents
 CC=15
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
43Allowed Winding Configurations Allowed Clock Positions
d 1, 5, 7, 11
y 0, 6 Winding 3
d 1, 5, 7, 11
Examples Y6d11d7, ANG1=180, ANG2=30, ANG3=150
D1d1y6, ANG1=-30, ANG2=-30, ANG3=180
1.13.11. CC=15
Allowed Winding Configurations Allowed Clock Positions
Winding 1* D 1, 5, 7, 11
Winding 2 yn 0, 6
Winding 3* d 1, 5, 7, 11
Examples D1yn0d1, ANG1=-30, ANG2=0, ANG3=-30
D1yn6d11, ANG1= -30, ANG2=180, ANG3=30
* Note: Windings 1 and 3 form auto-transformer. So their clock positions are always identical.
1.13.12. CC=16
Allowed Winding Configurations Allowed Clock Positions
Winding 1 YN 0, 6
Winding 2 yn 0, 6
Winding 3 yn 0, 6
Examples YN0yn0yn0, ANG1=0, ANG2=0, ANG3=0
YN6yn0yn6, ANG1=180, ANG2=0, ANG3=180
1.13.13. CC=17
Allowed Winding Configurations Allowed Clock Positions
Winding 1
Winding 2YNa 0
Winding 3 d 1, 5, 7, 11
Examples YNa0d1, ANG1=0, ANG2=0, ANG3=-30
YNa0d7, ANG1=0, ANG2=0, ANG3=150
1.13.14. CC=18
Allowed Winding Configurations Allowed Clock Positions
Winding 1 Ya 0
--- Page 53 ---
Power Flow Data Contents
 Areas, Zones and Owners
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
44Allowed Winding Configurations Allowed Clock Positions
Winding 2
Winding 3 d 1, 5, 7, 11
Examples Ya0d5, ANG1=0, ANG2=0, ANG3=-150
Ya0d11, ANG1=0, ANG2=0, ANG3=30
1.14. Areas, Zones and Owners
In the analysis of large scale power systems for both planning and operations purposes, it is often convenient
to be able to restrict the processing or reporting of PSSE functions to one or more subsets of the complete
power system model. PSSE provides three groupings of network elements which may be used for these
purposes: areas, zones, and owners.
Areas are commonly used to designate sections of the network that represent control areas between which
there are scheduled flows. PSSE provides for the identification of areas and their schedules. Alternatively, the
network can be subdivided between utility companies or any other subdivisions useful for specific analyses.
Each ac bus, load, and induction machine, as well as each dc bus of each multi-terminal dc line, is assigned
to an area.
Assigning buses to specific zones allows an additional subdivision of the network to facilitate analyses and
documentation. While PSSE provides documentation of zone interchange, it provides no analytical facility to
schedule interchange between zones. Each ac bus, load, and induction machine as well as each dc bus of
each multi-terminal dc line, is assigned to a zone.
Although areas cannot overlap other areas and zones cannot overlap other zones, areas and zones can over-
lap each other.
Figure 1-12  shows a system subdivided into three areas and three zones, each with a unique name. Notice
the following:
•An area does not have to be contiguous. Area #1 covers two separate parts of the network.
•Zone #1 lies entirely in Area #1.
•Zone #2 lies partly in Area #1 and partly in Area #4.
•Zone #3 lies partly in Area 4 and Area 2.
--- Page 54 ---
Power Flow Data Contents
 Areas, Zones and Owners
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
45
Overlapping Areas and Zones
Assigning ownership attributes to buses and other equipment allows an additional subdivision of the network
for analysis and documentation purposes. PSSE provides neither analytical facility to schedule interchange
between owners, nor documentation of owner interchange. Each of the following power system elements
is assigned to a single owner:
•ac bus
•load
•induction machine
•dc bus of a multi-terminal dc line
•FACTS device
•GNE device
Each of the following elements may have up to four owners:
•synchronous machine
•non-transformer branch
--- Page 55 ---
Power Flow Data Contents
 Area Interchange Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
46•two-winding and three-winding transformer
•VSC dc line
Area, zone and owner assignments are established at the time the network element is introduced into the
working case, either as specified by the user or to a documented default value. Assignments may be modified
either through the standard power flow data modification functions (refer to Section 5.9, Changing Service
Status and Power Flow Parametric Data) or via activities ARNM, OWNM and ZONM.
Additional Information
See also: Section 4.8, Subsystem Selection Section 4.9, Subsystem Reporting Adjusting Net Inter-
change Area Interchange Control Area Interchange Data  Interarea Transfer Data  Owner Data  Zone
Data  Bus Data  Load Data  Generator Data  Non-Transformer Branch Data  Transformer Data  Volt-
age Source Converter (VSC) DC Transmission Line Data  Multi-Terminal DC Transmission Line Data
Induction Machine Data  FACTS Device Data
1.15. Area Interchange Data
Area identifiers and interchange control parameters are specified in area interchange data records. Data for
each interchange area may be specified either at the time of raw data input or subsequently via activity CHNG
or the area [Spreadsheet] . Each area interchange data record has the following format:
I, ISW, PDES, PTOL, 'ARNAME'
I Area number (1 through 9999).
No default allowed.
ISW Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the area slack bus for area interchange control. The bus must  be a gener-
ator (Type 2) bus in the specified area. Any area containing a system swing bus (Type
3) must have  either that swing bus or a bus number of zero specified for its area slack
bus number. Any area with an area slack bus number of zero is considered a floating
area by the area interchange control option of the power flow solution activities.
ISW = 0 by default.
PDES Desired net interchange leaving the area (export); entered in MW. PDES must be spec-
ified such that is consistent with the area interchange definition implied by the area
interchange control code (tie lines only, or tie lines and loads) to be specified during
power flow solutions (refer to Section 6.3.20, Automatic Adjustments and Area Inter-
change Control).
PDES = 0.0 by default.
PTOL Interchange tolerance bandwidth; entered in MW.
PTOL = 10.0 by default
ARNAME Alphanumeric identifier assigned to area I. ARNAME may be up to twelve characters
and may contain any combination of blanks, uppercase letters, numbers and special
--- Page 56 ---
Power Flow Data Contents
 Area Interchange Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
47characters. ARNAME must  be enclosed in single or double quotes if it contains any
blanks or special characters.
ARNAME is twelve blanks by default
Area interchange data input is terminated with a record specifying an area number of zero.
1.15.1. Area Interchange Data Notes
All buses (ac and dc), loads and induction machines can be assigned to an area. The area number is entered
as part of the data records for the buses, loads, and induction machines (see Areas, Zones and Owners , Bus
Data , Load Data  and Multi-Terminal DC Transmission Line Data ).
Area interchange is a required net export of power from, or net import of power to, a specific area. This does
not imply that the power is destined to be transferred to or from any other specific area. To specify transfers
between specific pairs of areas, see Interarea Transfer Data .
Each bus in the PSSE working case may be designated as residing in an interchange area, for purposes of
both interchange control and selective output and other processing. When the interchange control option
is enabled during a power flow solution, each interchange area for which an area slack bus is specified has
the active power output of its area slack bus modified such that the desired net interchange for the area falls
within a desired band. Refer to Area Interchange Control for further discussion on this option of the power
flow solution activities.
1.16. Two-Terminal DC Transmission Line Data
The two-terminal dc transmission line model is used to simulate either a point-to-point system with rectifier
and inverter separated by a bipolar or mono-polar transmission system or a back-to-back system where the
rectifier and inverter are physically located at the same site and separated only by a short bus-bar.
The data requirements fall into three groups:
•Control parameters and set-points
•Converter transformers
•The dc line characteristics
Each two-terminal dc transmission line to be represented in PSSE is introduced by reading three consecutive
data records. Each set of dc line data records has the following format:
'NAME',MDC,RDC,SETVL,VSCHD,VCMOD,RCOMP,DELTI,METER,DCVMIN,CCCITMX,CCCACC
IPR,NBR,ANMXR,ANMNR,RCR,XCR,EBASR,TRR,TAPR,TMXR,TMNR,STPR,ICR,                                                                IFR,ITR,IDR,XCAPR,NDR
IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,                                                                IFI,ITI,IDI,XCAPI,NDI
--- Page 57 ---
Power Flow Data Contents
 Two-Terminal DC Transmission Line Da-
ta
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
48The first of the three dc line data records defines the following line quantities and control parameters:
NAME The non-blank alphanumeric identifier assigned to this dc line. Each two-terminal dc
line must  have a unique NAME. NAME may be up to twelve characters and may con-
tain any combination of blanks, uppercase letters, numbers and special characters.
NAME must  be enclosed in single or double quotes if it contains any blanks or special
characters.
No default allowed
MDC Control mode: 0 for blocked, 1 for power, 2 for current.
MDC = 0 by default
RDC The dc line resistance; entered in ohms.
No default allowed
SETVL Current (amps) or power (MW) demand. When MDC is one, a positive value of SETVL
specifies desired power at the rectifier and a negative value specifies desired inverter
power.
No default allowed
VSCHD Scheduled compounded dc voltage; entered in kV.
No default allowed
VCMOD Mode switch dc voltage; entered in kV. When the inverter dc voltage falls below this
value and the line is in power control mode (i.e., MDC = 1), the line switches to current
control mode with a desired current corresponding to the desired power at scheduled
dc voltage.
VCMOD = 0.0 by default
RCOMP Compounding resistance; entered in ohms. Gamma and/or TAPI is used to attempt to
hold the compounded voltage (VDCI + DCCUR*RCOMP) at VSCHD. To control the in-
verter end dc voltage VDCI, set RCOMP to zero; to control the rectifier end dc voltage
VDCR, set RCOMP to the dc line resistance, RDC; otherwise, set RCOMP to the appro-
priate fraction of RDC.
RCOMP = 0.0 by default
DELTI Margin entered in per unit of desired dc power or current. This is the fraction by which
the order is reduced when ALPHA is at its minimum and the inverter is controlling the
line current.
DELTI = 0.0 by default
METER Metered end code of either R (for rectifier) or I (for inverter).
METER = I by default
DCVMIN Minimum compounded dc voltage; entered in kV. Only used in constant gamma op-
eration (i.e., when ANMXI = ANMNI) when TAPI is held constant and an ac transformer
tap is adjusted to control dc voltage (i.e., when IFI, ITI, and IDI specify a two-winding
transformer).
DCVMIN = 0.0 by default
--- Page 58 ---
Power Flow Data Contents
 Two-Terminal DC Transmission Line Da-
ta
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
49CCCITMX Iteration limit for capacitor commutated two-terminal dc line Newton solution proce-
dure.
CCCITMX = 20 by default
CCCACC Acceleration factor for capacitor commutated two-terminal dc line Newton solution
procedure.
CCCACC = 1.0 by default
The second of the three dc line data records defines rectifier end data quantities and control parameters:
IPR Rectifier converter bus number, or extended bus name enclosed in single quotes (refer
to Extended Bus Names ).
No default allowed
NBR Number of bridges in series (rectifier).
No default allowed
ANMXR Nominal maximum rectifier firing angle; entered in degrees.
No default allowed
ANMNR Minimum steady-state rectifier firing angle; entered in degrees.
No default allowed
RCR Rectifier commutating transformer resistance per bridge; entered in ohms.
No default allowed
XCR Rectifier commutating transformer reactance per bridge; entered in ohms.
No default allowed
EBASR Rectifier primary base ac voltage; entered in kV.
No default allowed
TRR Rectifier transformer ratio.
TRR = 1.0 by default
TAPR Rectifier tap setting. TAPR = 1.0 by default.
If no two-winding transformer is specified by IFR, ITR, and IDR, TAPR is adjusted to
keep alpha within limits; otherwise, TAPR is held fixed and this transformer’s tap ratio
is adjusted. The adjustment logic assumes that the rectifier converter bus is on the
Winding 2 side of the transformer. The limits TMXR and TMNR specified here are used;
except for the transformer control mode flag (COD1 of Transformer Data ), the ac tap
adjustment data is ignored.
TMXR Maximum rectifier tap setting.
TMXR = 1.5 by default
TMNR Minimum rectifier tap setting.
TMNR = 0.51 by default
--- Page 59 ---
Power Flow Data Contents
 Two-Terminal DC Line Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
50STPR Rectifier tap step; must be positive.
STPR = 0.00625 by default
ICR Bus number of the rectifier commutating bus (rectifier firing angle measuring bus),
or extended bus name enclosed in single quotes (refer to Extended Bus Names ). The
firing angle and angle limits used inside the dc model are adjusted by the difference
between the phase angles at this bus and the ac/dc interface (i.e., the converter bus,
IPR).
ICR = 0 by default
IFR Winding 1 side from bus number, or extended bus name enclosed in single quotes, of
a two-winding transformer.
IFR = 0 by default
ITR Winding 2 side to bus number, or extended bus name enclosed in single quotes, of a
two-winding transformer.
ITR = 0 by default
IDR Circuit identifier; the branch described by IFR, ITR, and IDR must  have been entered
as a two-winding transformer; an ac transformer may control at most only one dc
converter.
IDR = '1' by default
XCAPR Commutating capacitor reactance magnitude per bridge; entered in ohms.
XCAPR = 0.0 by default
NDR A node number of bus ICR. The bus section of bus ICR to which node NDR is connected
is the bus section used as the rectifier commutating bus. If bus ICR is not in a substa-
tion, NDR must be specified as 0.
NDR = 0 by default
Data on the third of the three dc line data records contains the inverter quantities corresponding to the
rectifier quantities specified on the second record described above. The significant difference is that the
control angle ALFA for the rectifier is replaced by the control angle GAMMA for the inverter.
IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,                                                                IFI,ITI,IDI,XCAPI,NDI
DC line data input is terminated with a record specifying a blank dc line name or a dc line name of ’0’.
1.16.1. Two-Terminal DC Line Data Notes
The steady-state two-terminal dc line model used in power flow analysis establishes the initial steady state
for dynamic analysis.
DC line converter buses, IPR and IPI, may be Type 1, 2, or 3 buses. Generators, loads, fixed and switched
shunt elements, induction machines other dc line converters, FACTS device sending ends, and GNE devices
are permitted at converter buses.
When either XCAPR > 0.0 or XCAPI > 0.0, the two-terminal dc line is treated as capacitor commutated. Capac-
itor commutated two-terminal dc lines preclude the use of a remote ac transformer as commutation trans-
--- Page 60 ---
Power Flow Data Contents
 Voltage Source Converter (VSC) DC
Transmission Line Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
51former tap and remote commutation angle buses at either converter. Any data provided in these fields are
ignored for capacitor commutated two-terminal dc lines.
For additional information on dc line modeling in power flow solutions, refer to Section 6.3.17, DC Lines.
1.17. Voltage Source Converter (VSC) DC Transmission
Line Data
The voltage source converter (VSC) two-terminal dc transmission line model is used to simulate either a
point-to-point system or a back-to-back system using voltage source converters.
Each voltage source converter (VSC) dc line to be represented in PSSE is introduced by reading a set of three
consecutive data records. Each set of VSC dc line data records has the following format:
'NAME', MDC, RDC, O1, F1, ... O4, F4
IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,                                                             MAXQ,MINQ,VSREG,RMPCT,NREG
IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,                                                             MAXQ,MINQ,VSREG,RMPCT,NREG
The first of the three VSC dc line data records defines the following line quantities and control parameters:
NAME The non-blank alphanumeric identifier assigned to this dc line. Each VSC dc line must
have a unique NAME. NAME may be up to twelve characters and may contain any
combination of blanks, uppercase letters, numbers and special characters. NAME must
be enclosed in single or double quotes if it contains any blanks or special characters.
No default allowed.
MDC Control mode:
•0 - for out-of-service
•1 - for in-service
MDC = 1 by default.
RDC The dc line resistance; entered in ohms. RDC must be positive.
No default allowed
Oi An owner number (1 through 9999). Each VSC dc line may have up to four owners.
By default, O1 is 1, and O2, O3 and O4 are zero.
Fi The fraction of total ownership assigned to owner Oi; each Fi must be positive. The Fi
values are normalized such that they sum to 1.0 before they are placed in the working
case.
Each Fi is 1.0 by default
--- Page 61 ---
Power Flow Data Contents
 Voltage Source Converter (VSC) DC
Transmission Line Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
52The remaining two data records define the converter buses (converter 1 and converter 2), along with their
data quantities and control parameters:
IBUS Converter bus number, or extended bus name enclosed in single quotes (refer to Ex-
tended Bus Names ).
No default allowed
TYPE Code for the type of converter dc control:
•0 - for converter out-of-service
•1 - for dc voltage control
•2 -for MW control
When both converters are in-service, exactly one converter of each VSC dc line must
be TYPE 1.
No default allowed
MODE Converter ac control mode:
•1 - for ac voltage control
•2 - for fixed ac power factor
MODE = 1 by default
DCSET Converter dc setpoint. For TYPE = 1, DCSET is the scheduled dc voltage on the dc side
of the converter bus; entered in kV. For TYPE = 2, DCSET is the power demand, where a
positive value specifies that the converter is feeding active power into the ac network
at bus IBUS, and a negative value specifies that the converter is withdrawing active
power from the ac network at bus IBUS; entered in MW.
No default allowed
ACSET Converter ac setpoint. For MODE = 1, ACSET is the regulated ac voltage setpoint; en-
tered in pu. For MODE = 2, ACSET is the power factor setpoint.
ACSET = 1.0 by default
ALOSS, BLOSS Coefficients of the linear equation used to calculate converter losses:
KW conv loss  = ALOSS + (I dc * BLOSS)
ALOSS is entered in kW. BLOSS is entered in kW/amp.
ALOSS = BLOSS = 0.0 by default
MINLOSS Minimum converter losses; entered in kW.
MINLOSS = 0.0 by default
SMAX Converter MVA rating; entered in MVA. SMAX = 0.0 to allow unlimited converter MVA
loading.
SMAX = 0.0 by default
--- Page 62 ---
Power Flow Data Contents
 VSC DC Line Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
53IMAX Converter ac current rating; entered in amps. IMAX = 0.0 to allow unlimited converter
current loading. If a positive IMAX is specified, the base voltage assigned to bus IBUS
must be positive.
IMAX = 0.0 by default
PWF Power weighting factor fraction (0.0 <= PWF <= 1.0) used in reducing the active power
order and either the reactive power order (when MODE is 2) or the reactive power lim-
its (when MODE is 1) when the converter MVA or current rating is violated. When PWF
is 0.0, only the active power is reduced; when PWF is 1.0, only the reactive power is
reduced; otherwise, a weighted reduction of both active and reactive power is applied.
PWF = 1.0 by default
MAXQ Reactive power upper limit; entered in Mvar. A positive value of reactive power indi-
cates reactive power flowing into the ac network from the converter; a negative value
of reactive power indicates reactive power withdrawn from the ac network. Not used
if MODE = 2.
MAXQ = 9999.0 by default
MINQ Reactive power lower limit; entered in Mvar. A positive value of reactive power indi-
cates reactive power flowing into the ac network from the converter; a negative value
of reactive power indicates reactive power withdrawn from the ac network. Not used
if MODE = 2.
MINQ = -9999.0 by default
VSREG Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the bus for which voltage is to be regulated by this converter to the value
specified by ACSET. If VSREG specifies a remote bus (i.e., a bus other than bus IBUS),
bus VSREG must be a Type 1 or 2 bus (if it is other than a Type 1 or 2 bus, bus IBUS
regulates its own voltage to the value specified by ACSET). VSREG may be entered as
zero if the converter is to regulate its own voltage. Not used if MODE = 2.
VSREG = 0 by default
RMPCT Percent of the total Mvar required to hold the voltage at the bus controlled by bus
IBUS that are to be contributed by this VSC converter; RMPCT must be positive. RM-
PCT is needed only if there is more than one local or remote setpoint mode voltage
controlling device (plant, switched shunt, FACTS device shunt element, or VSC dc line
converter) controlling the voltage at bus VSREG.
RMPCT = 100.0 by default
NREG A node number of bus VSREG. The bus section of bus VSREG to which node NREG is
connected is the bus section for which voltage is to be regulated by this converter
to the value specified by ACSET. If bus VSREG is not in a substation, NREG must be
specified as 0.
NREG = 0 by default
VSC dc line data input is terminated with a record specifying a blank dc line name or a dc line name of ’0’.
1.17.1. VSC DC Line Data Notes
Each VSC dc line converter bus must have the following characteristics:
--- Page 63 ---
Power Flow Data Contents
 Transformer Impedance Correction Ta-
bles
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
54•It must be a Type 1 or 2 bus. Generators, loads, fixed and switched shunt elements, induction machines,
other dc line converters, FACTS device sending ends, and GNE devices are permitted at converter buses.
•It must not have the terminal end of a FACTS device connected to the same bus.
•It must not be connected by a zero impedance line to another bus that violates any of the above restrictions.
In specifying reactive power limits for converters that control ac voltage (i.e., those with unequal reactive
power limits where the MODE is 1), the use of very narrow var limit bands is discouraged. The Newton-Raph-
son based power flow solutions require that the difference between the controlling equipment's high and
low reactive power limits be greater than 0.002 pu for all setpoint mode voltage controlling equipment (0.2
Mvar on a 100 MVA system base). It is recommended that voltage controlling VSC converters have Mvar
ranges substantially wider than this minimum permissible range.
For interchange and loss assignment purposes, the dc voltage controlling converter is assumed to be the
non-metered end of each VSC dc line. As with other network branches, losses are assigned to the subsystem
of the non-metered end, and flows at the metered ends are used in interchange calculations.
For additional information on dc line modeling in power flow solutions, refer to Section 6.3.17 DC Lines.
1.18. Transformer Impedance Correction Tables
Transformer impedance correction tables are used to model a change of transformer impedance as off-nom-
inal turns ratio or phase shift angle is adjusted. Data for each table may be specified either at the time of raw
data input, or subsequently via activity CHNG or the impedance table [Spreadsheet] .
Each impedance correction table may have up to 99 points. The scaling factors are complex numbers; the
imaginary components of these factors may be specified as 0.0. Points are specified six per line in the im-
pedance correction table data block. As many records as are needed (with six points per record) are entered.
End of data for a table is specified by specifying an additional point with the three values defining the point
all specified as 0.0.
Each transformer impedance correction table data block has the following format:
I, T1, Re(F1), Im(F1), T2, Re(F2), Im(F2), ... T6, Re(F6), Im(F6)
T7, Re(F7), Im(F7), T8, Re(F8), Im(F8), ... T12, Re(F12), Im(F12)
.
.
Tn, Re(Fn), Im(Fn), 0.0, 0.0, 0.0
I Impedance correction table number (1 through the maximum number of impedance
correction tables at the current size level; refer to Table 3-1, Standard Maximum PSS®E
Program Capacities).
No default allowed
Ti Either off-nominal turns ratio in pu of the controlling windings bus voltage base or
phase shift angle in degrees.
--- Page 64 ---
Power Flow Data Contents
 Impedance Correction Table Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
55Ti = 0.0 by default
Fi Complex scaling factor by which transformer nominal impedance is to be multiplied to
obtain the actual transformer impedance for the corresponding T i. Fi = (0.0+j0.0) by
default. The impedances used in calculation of F i should be expressed in percent or pu
on on winding voltage base at specified tap position Ti and MVA base used in power
flow data. This is the same base as per CZ of power flow data but winding voltage
at tap Ti.
Transformer impedance correction data input is terminated with a record specifying a table number of zero.
Example: Transformer Test Data, MVA=50, Nominal Tap position=10, winding voltage=132 kV,
Impedance=123.1118 ohms/phase
Tap Position Winding kV Z12 Data (ohms/phase) Base Z (ohms) PU Z
1 112.20 81.7016 251.7768 0.3245
10 132.0 123.1118 346.48 0.3532
19 151.8 178.8155 460.8648 0.3880
Corresponding Impedance Correction Table data would be as below (CZ=2).
Ti Fi
0.85=112.2/132.0 0.9187=0.3245/0.3532
1.0=132.0/132.0 1.0=0.3532/0.3532
1.15=151.8/132.0 1.098277=0.3880/0.3532
1.18.1. Impedance Correction Table Notes
The T i values on a transformer impedance correction table record block must all be either tap ratios or phase
shift angles. They must be entered in strictly ascending order; i.e., for each i, T i+1>Ti. Each F i entered must be
non-zero. For each table, at least 2 sets of values must be specified (plus the additional end-of-table point
of zeros), and up to 99 may be entered. For a graphical view of a correction table for which the imaginary
component of each scaling factor is 0.0, see Figure 1-13 .
The T i values for tables that are a function of tap ratio (rather than phase shift angle) in pu of the controlling
winding bus voltage base.
A transformer impedance is assigned to an impedance correction table either on the third, fourth or fifth
record of the transformer data record block of activities READ, TREA, RDCH (refer to Trans ), or via activity
CHNG or the two-winding and three-winding transformer [Spreadsheets] . Each table may be shared among
many transformer impedances. If the first T in a table is less than 0.5 or the last T entered is greater than
1.5, T is assumed to be the phase shift angle and each transformer impedance dependent on the table is
treated as a function of phase shift angle. Otherwise, the transformer impedances dependent on the table
are made sensitive to off-nominal turns ratio.
For three-winding transformers, a data item associated with the transformer (ZCOD) indicates whether im-
pedance adjustment is to be applied to winding impedances or to the bus-to-bus impedances. If applied to
winding impedances, the input to the table look-up is the winding tap ratio or phase shift angle, as appro-
priate, and the scaling factor is applied to the winding’s nominal impedance.
--- Page 65 ---
Power Flow Data Contents
 Multi-Terminal DC Transmission Line
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
56When impedance adjustment is applied to the bus-to-bus impedances of a three-winding transformer, the
following requirements must be met:
•exactly one winding of the transformer must be automatically adjustable.
•at least one winding must have an impedance correction table assigned to it.
•the impedance correction table(s) must be sensitive to the quantity that is automatically adjustable (tap
ratio or phase shift angle, as appropriate).
In this case, the input to each of the tables is the appropriate quantity (tap ratio or phase shift angle) of the
adjustable winding. If an impedance correction table is specified for winding 1, impedance adjustment is
applied to Z1-2; if an impedance correction table is specified for winding 2, impedance adjustment is applied
to Z2-3; and if an impedance correction table is specified for winding 3, impedance adjustment is applied
to Z3-1.
The power flow case stores both a nominal and actual impedance for each transformer winding imped-
ance. The value of transformer impedance entered in activities READ, TREA, RDCH, CHNG, or the transformer
[Spreadsheets]  is taken as the nominal value of impedance. Each time the complex tap setting of a trans-
former is changed, either automatically by the power flow solution activities or manually by the user, and
the modified quantity is an input to the table look-up function of any impedance correction table associated
with the transformer, actual transformer impedances are redetermined if appropriate. First, the scaling fac-
tor is established from the appropriate table by linear interpolation; then nominal impedance is multiplied
by the scaling factor to determine actual impedance. An appropriate message is printed any time the actual
impedance is modified.
Typical Impedance Correction Factor Curve
1.19. Multi-Terminal DC Transmission Line Data
PSSE allows the representation of up to 12 converter stations on one multi-terminal dc line. The dc network of
each multi-terminal dc line may consist of up to 20 dc network buses connected together by up to 20 dc links.
--- Page 66 ---
Power Flow Data Contents
 Multi-Terminal DC Transmission Line
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
57
Each multi-terminal dc transmission line to be represented in PSSE is introduced by reading a series of data
records. Each set of multi-terminal dc line data records begins with a record that defines the number of
converters, number of dc buses and number of dc links as well as related bus numbers and the control mode.
Following this first record there are subsequent records for each converter, each dc bus, and each dc link.
Each set of multi-terminal dc line data records begins with a record of system definition data in the following
format:
'NAME', NCONV, NDCBS, NDCLN, MDC, VCONV, VCMOD,VCONVN
NAME The non-blank alphanumeric identifier assigned to this dc line. Each multi-terminal
dc line must  have a unique NAME. NAME may be up to twelve characters and may
contain any combination of blanks, uppercase letters, numbers and special characters.
NAME must  be enclosed in single or double quotes if it contains any blanks or special
characters.
No default allowed
NCONV Number of ac converter station buses in multi-terminal dc line I.
No default allowed
NDCBS Number of dc buses in multi-terminal dc line I (NCONV NDCBS).
No default allowed
NDCLN Number of dc links in multi-terminal dc line I.
No default allowed
MDC Control mode:
•0 - for blocked
•1 - for power control
•2 - for current control
MDC = 0 by default
VCONV Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the ac converter station bus that controls dc voltage on the positive pole
of multi-terminal dc line I. Bus VCONV must  be a positive pole inverter.
No default allowed
VCMOD Mode switch dc voltage; entered in kV. When any inverter dc voltage magnitude falls
below this value and the line is in power control mode (i.e., MDC = 1), the line switch-
es to current control mode with converter current setpoints corresponding to their
desired powers at scheduled dc voltage.
VCMOD = 0.0 by default
--- Page 67 ---
Power Flow Data Contents
 Multi-Terminal DC Transmission Line
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
58VCONVN Bus number, or extended bus name enclosed in single quotes, of the ac converter
station bus that controls dc voltage on the negative pole of multi-terminal dc line
I. If any negative pole converters are specified (see below), bus VCONVN must be a
negative pole inverter. If the negative pole is not being modeled, VCONVN must  be
specified as zero.
VCONVN = 0 by default
This data record is followed by NCONV converter records of the following format:
IB,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD
IB ac converter bus number, or extended bus name enclosed in single quotes (refer to
Extended Bus Names ).
No default allowed
N Number of bridges in series.
No default allowed.
ANGMX Nominal maximum ALPHA or GAMMA angle; entered in degrees.
No default allowed
ANGMN Minimum steady-state ALPHA or GAMMA angle; entered in degrees.
No default allowed
RC Commutating resistance per bridge; entered in ohms.
No default allowed
XC Commutating reactance per bridge; entered in ohms.
No default allowed
EBAS Primary base ac voltage; entered in kV.
No default allowed
TR Actual transformer ratio.
TR = 1.0 by default
TAP Tap setting.
TAP = 1.0 by default
TPMX Maximum tap setting.
TPMX = 1.5 by default
TPMN Minimum tap setting.
TPMN = 0.51 by default
TSTP Tap step; must be a positive number.
TSTP = 0.00625 by default
SETVL Converter setpoint. When IB is equal to VCONV or VCONVN, SETVL specifies the sched-
uled dc voltage magnitude, entered in kV, across the converter. SETVL is positive for
--- Page 68 ---
Power Flow Data Contents
 Multi-Terminal DC Transmission Line
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
59positive pole inverter and negative for negative pole inverter. For other converter bus-
es, SETVL contains the converter current (amps) or power (MW) demand; a positive
value of SETVL indicates that bus IB is a rectifier, and a negative value indicates an
inverter.
No default allowed
DCPF Converter participation factor. When the order at any rectifier in the multi-terminal
dc line is reduced, either to maximum current or margin, the orders at the remaining
converters on the same pole are modified according to their DCPFs to:
SETVL + (DCPF/SUM)*R
where SUM is the sum of the DCPFs at the unconstrained converters on the same pole
as the constrained rectifier, and R is the order reduction at the constrained rectifier.
DCPF = 1. by default
MARG Rectifier margin entered in per unit of desired dc power or current. The converter or-
der reduced by this fraction, (1.-MARG)*SETVL, defines the minimum order for this
rectifier. MARG is used only at rectifiers.
MARG = 0.0 by default
CNVCOD Converter code. A positive value or zero must be entered if the converter is on the
positive pole of multi-terminal dc line I. A negative value must be entered for negative
pole converters.
CNVCOD = 1 by default
These data records are followed by NDCBS dc bus records of the following format:
IDC, IB, AREA, ZONE, 'DCNAME', IDC2, RGRND, OWNER
IDC dc bus number (1 to NDCBS). The dc buses are used internally within each multi-ter-
minal dc line and must  be numbered 1 through NDCBS.
No default allowed
IB ac converter bus number, or extended bus name enclosed in single quotes (refer to Ex-
tended Bus Names ), or zero. Each converter station bus specified in a converter record
must be specified as IB in exactly one dc bus record. DC buses that are connected on-
ly to other dc buses by dc links and not to any ac converter buses must have a zero
specified for IB. A dc bus specified as IDC2 on one or more other dc bus records must
have a zero specified for IB on its own dc bus record.
IB = 0 by default
AREA Area number (1 through 9999).
AREA = 1 by default
ZONE Zone number (1 through 9999).
ZONE = 1 by default
DCNAME Alphanumeric identifier assigned to dc bus IDC. DCNAME may be up to twelve char-
acters and may contain any combination of blanks, uppercase letters, numbers and
--- Page 69 ---
Power Flow Data Contents
 Multi-Terminal DC Transmission Line
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
60special characters. DCNAME must  be enclosed in single or double quotes if it contains
any blanks or special characters.
DCNAME is twelve blanks by default
IDC2 Second dc bus to which converter IB is connected, or zero if the converter is connected
directly to ground. For voltage controlling converters, this is the dc bus with the lower
dc voltage magnitude and SETVL specifies the voltage difference between buses IDC
and IDC2. For rectifiers, dc buses should be specified such that power flows from bus
IDC2 to bus IDC. For inverters, dc buses should be specified such that power flows from
bus IDC to bus IDC2. IDC2 is ignored on those dc bus records that have IB specified
as zero.
IDC2 = 0 by default
RGRND Resistance to ground at dc bus IDC; entered in ohms. During solutions RGRND is used
only for those dc buses specified as IDC2 on other dc bus records.
RGRND = 0.0 by default
OWNER Owner number (1 through 9999).
OWNER = 1 by default
These data records are followed by NDCLN dc link records of the following format:
IDC, JDC, CKT, MET, RDC, LDC
IDC Branch from bus dc bus number.
No default allowed
JDC Branch to bus dc bus number.
No default allowed
CKT One-character uppercase alphanumeric branch circuit identifier. It is recommended
that single circuit branches be designated as having the circuit identifier '1'.
CKT = '1' by default
MET Metered end flag:
•<= 1 - to designate bus IDC as the metered end
•>= 2 - to designate bus JDC as the metered end
MET = 1 by default
RDC dc link resistance, entered in ohms.
No default allowed
LDC dc link inductance, entered in mH. LDC is not used by the power flow solution activities
but is available to multi-terminal dc line dynamics models.
LDC = 0.0 by default
Multi-terminal dc line data input is terminated with a record specifying a dc line number of zero.
--- Page 70 ---
Power Flow Data Contents
 Multi-Terminal DC Line Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
611.19.1. Multi-Terminal DC Line Notes
The following points should be noted in specifying multi-terminal dc line data:
•Conventional two-terminal (refer to Two-Terminal DC Transmission Line Data ) and multi-terminal dc lines
are stored separately in PSSE working memory. Therefore, there may simultaneously exist, for example, a
two-terminal dc line identified as dc line ABC along with a multi-terminal line for which the name is ABC.
•Multi-terminal lines should have at least three converter terminals; conventional dc lines consisting of two
terminals should be modeled as two-terminal lines (refer to Two-Terminal DC Transmission Line Data ).
•AC converter buses may be Type 1, 2, or 3 buses. Generators, loads, fixed and switched shunt elements,
induction machines, other dc line converters, FACTS device sending ends, and GNE devices are permitted
at converter buses.
•Each multi-terminal dc line is treated as a subnetwork of dc buses and dc links connecting its ac converter
buses. For each multi-terminal dc line, the dc buses must be numbered 1 through NDCBS.
•Each ac converter bus must be specified as IB on exactly one dc bus record; there may be dc buses con-
nected only to other dc buses by dc links but not to any ac converter bus.
•AC converter bus IB may be connected to a dc bus IDC, which is connected directly to ground. IB is specified
on the dc bus record for dc bus IDC; the IDC2 field is specified as zero.
•Alternatively, ac converter bus IB may be connected to two dc buses IDC and IDC2, the second of which
is connected to ground through a specified resistance. IB and IDC2 are specified on the dc bus record for
dc bus IDC; on the dc bus record for bus IDC2, the ac converter bus and second dc bus fields (IB and IDC2,
respectively) must be specified as zero and the grounding resistance is specified as RGRND.
•The same dc bus may be specified as the second dc bus for more than one ac converter bus.
•All dc buses within a multi-terminal dc line must be reachable from any other point within the dc subnet-
work.
•The area numbers assigned to dc buses and the metered end designations of dc links are used in calculating
area interchangeand assigning losses in activities AREA, INTA, TIES, and SUBS as well as in the interchange
control option of the power flow solution activities. Similarly, the zone assignments and metered end
specifications are used in activities ZONE, INTZ, TIEZ, and SUBS.
•Section 5.7.2 Reading RDCH Data Files Created by Previous Releases of PSS®E describes the specification of
NCONV, NDCBS and NDCLN when specifying changes to an existing multi-terminal dc line in activity RDCH.
For additional information on dc line modeling in power flow solutions, refer to Section 6.3.17, DC Lines.
A multi-terminal layout is shown in Figure 1-14 . There are 4 convertors, 5 dc buses and 4 dc links.
--- Page 71 ---
Power Flow Data Contents
 Multi-Section Line Grouping Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
62
Multi-Terminal DC Network
1.20. Multi-Section Line Grouping Data
Transmission lines commonly have a series of sections with varying physical structures. The section might
have different tower configurations, conductor types and bundles, or various combinations of these. The
physical differences can result in the sections having different resistance, reactance and charging.
A transmission line with several distinct sections can be represented as one multisection line group.
Each multi-section line grouping to be represented in PSSE is introduced by reading a multi-section line group-
ing data record. Each multi-section line grouping data record has the following format:
I, J, ID, MET, DUM1, DUM2, ... DUM9
I From bus number, or extended bus name enclosed in single quotes (refer to Extended
Bus Names ).
No default allowed
J To bus number, or extended bus name enclosed in single quotes.
No default allowed
ID Two-character upper case alphanumeric multi-section line grouping identifier. The
first character must  be an ampersand (' ').
ID = '1' by default
MET Metered end flag:
--- Page 72 ---
Power Flow Data Contents
 Multi-Section Line Example
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
63•<= 1 - to designate bus I as the metered end
•>= 2 - to designate bus J as the metered end
MET = 1 by default
DUM i Bus numbers, or extended bus names enclosed in single quotes (refer to Extended
Bus Names ), of the dummy buses connected by the branches that comprise this mul-
ti-section line grouping.
No defaults allowed
Multi-section line grouping data input is terminated with a record specifying a from bus number of zero.
1.20.1. Multi-Section Line Example
The DUM i values on each record define the branches connecting bus I to bus J, and are entered so as to trace
the path from bus I to bus J. Specifically, for a multi-section line grouping consisting of three line sections
(and hence two dummy buses):
The path from I to J is defined by the following branches:
I D1 C1
D1 D2 C2
D2 J C3
If this multi-section line grouping is to be assigned the line identifier 1, the corresponding multi-section line
grouping data record is given by:
I J 1 1 D1 D2
1.20.2. Multi-Section Line Notes
Up to 10 line sections (and hence 9 dummy buses) may be defined in each multi-section line grouping. A
branch may be a line section of at most one multi-section line grouping.
Each dummy bus must have exactly two branches connected to it, both of which must be members of the
same multi-section line grouping. A multi-section line dummy bus may not be a converter bus of a dc trans-
mission line. A FACTS control device may not be connected to a multi-section line dummy bus.
The status of line sections and type codes of dummy buses are set such that the multi-section line is treated
as a single entity with regards to its service status.
When the multi-section line reporting option is enabled (refer to Section 3.3.3, Program Run-Time Option
Settings and activity OPTN), several power flow reporting activities such as POUT and LOUT do not tabulate
conditions at multi-section line dummy buses. Accordingly, care must be taken in interpreting power flow
output reports when dummy buses are other than passive nodes (e.g., if load or generation is present at a
dummy bus).
--- Page 73 ---
Power Flow Data Contents
 Zone Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
641.21. Zone Data
Zone identifiers are specified in zone data records. Zone names may be specified either at the time of raw data
input or subsequently via activity CHNG or the zone [Spreadsheet] . Each zone data record has the following
format:
I, 'ZONAME'
I Zone number (1 through 9999).
No default allowed
ZONAME Alphanumeric identifier assigned to zone I. ZONAME may be up to twelve characters
and may contain any combination of blanks, uppercase letters, numbers and special
characters. ZONAME must  be enclosed in single or double quotes if it contains any
blanks or special characters.
ZONAME is twelve blanks by default
Zone data input is terminated with a record specifying a zone number of zero.
1.21.1. Zone Data Notes
All buses (ac and dc), loads, and induction machines can be assigned to a zone. The zone number is entered
as part of the data records for the buses, loads, and induction machines (see Areas, Zones and Owners , Bus
Data , Load Data  and Multi-Terminal DC Transmission Line Data ).
The use of zones enables the user to develop reports and to check results on the basis of zones and, conse-
quently, be highly specific when reporting and interpreting analytical results.
1.22. Interarea Transfer Data
The PSSE user has the ability to assign each bus, load, and induction machine to an area (see Bus Data ,
Load Data , Multi-Terminal DC Transmission Line Data , Area Interchange Data  and Areas, Zones and Owners ).
Furthermore, the user can schedule active power transfers between pairs of areas.
These active power transfers are specified in interarea transfer data records. Each interarea transfer data
record has the following format:
ARFROM, ARTO, TRID, PTRAN
ARFROM From area number (1 through 9999).
No default allowed
ARTO To area number (1 through 9999).
No default allowed
--- Page 74 ---
Power Flow Data Contents
 Interarea Transfer Data Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
65TRID Single-character (0 through 9 or A through Z) upper case interarea transfer identifier
used to distinguish among multiple transfers between areas ARFROM and ARTO.
TRID = '1' by default
PTRAN MW comprising this transfer. A positive PTRAN indicates that area ARFROM is selling
to area ARTO.
PTRAN = 0.0 by default
Interarea transfer data input is terminated with a record specifying a from area number of zero.
1.22.1. Interarea Transfer Data Notes
Following the completion of interarea transfer data input, activity READ generates an alarm for any area for
which at least one interarea transfer is present and where the sum of transfers differs from its desired net
interchange, PDES (refer to Area Interchange Data ).
1.23. Owner Data
PSSE allows the user to identify which organization or utility actually owns a facility, a piece of equipment
or a load. Buses (ac and dc), loads, induction machines, FACTS devices, and GNE devices have provision for
an owner, while machines, ac branches, and VSC dc lines can have up to four different owners. Ownership
is specified as part of the data records for these network elements (see Bus Data , Load Data , FACTS Device
Data , Generator Data , Non-Transformer Branch Data , Transformer Data , Voltage Source Converter (VSC) DC
Transmission Line Data , Multi-Ter , and GNE Device Data ).
The use of the ownership attribute enables the user to develop reports and to check results on the basis of
ownership and, consequently, be highly specific when reporting and interpreting analytical results.
Owner identifiers are specified in owner data records. Owner names may be specified either at the time of
raw data input or subsequently via activity CHNG or the owner [Spreadsheet] . Each owner data record has
the following format:
I, 'OWNAME'
I Owner number (1 through 9999).
No default allowed
OWNAME Alphanumeric identifier assigned to owner I. OWNAME may be up to twelve characters
and may contain any combination of blanks, uppercase letters, numbers and special
characters. OWNAME must  be enclosed in single or double quotes if it contains any
blanks or special characters.
OWNAME is twelve blanks by default
--- Page 75 ---
Power Flow Data Contents
 FACTS Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
66Owner data input is terminated with a record specifying an owner number of zero.
1.24. FACTS Device Data
There are a variety of Flexible AC Transmission System (FACTS) devices currently available. These include
shunt devices, such as the Static Compensator (STATCOM), series devices such as the Static Synchronous
Series Compensator (SSSC), combined devices such as the Unified Power Flow Controller (UPFC), and parallel
series devices such as the Interline Power Flow Controller (IPFC).
PSSE accepts data for all of these devices through one generic set of data records. Each FACTS device to
be represented in PSSE is specified in FACTS device data records. Each FACTS device data record has the
following format:
’NAME’,I,J,MODE,PDES,QDES,VSET,SHMX,TRMX,VTMN,VTMX,VSMX,IMX,LINX,                                     RMPCT,OWNER,SET1,SET2,VSREF,FCREG,’MNAME’,NREG
NAME The non-blank alphanumeric identifier assigned to this FACTS device. Each FACTS de-
vice must  have a unique NAME. NAME may be up to twelve characters and may con-
tain any combination of blanks, uppercase letters, numbers and special characters.
NAME must  be enclosed in single or double quotes if it contains any blanks or special
characters.
No default allowed
I Sending end bus number, or extended bus name enclosed in single quotes (refer to
Extended Bus Names ).
No default allowed
J Terminal end bus number, or extended bus name enclosed in single quotes; 0 for a
STATCON.
J = 0 by default
MODE Controlmode:
For a STATCON (i.e., a FACTS devices with a shunt element but no series element), J
must be 0 and MODE must be either 0 or 1):
•0 - out-of-service (i.e., shunt link open)
•1 - shunt link operating
For a FACTS device with a series element (i.e., J is not 0), MODE may be:
•0 - out-of-service (i.e., series and shunt links open)
--- Page 76 ---
Power Flow Data Contents
 FACTS Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
67•1 - series and shunt links operating
•2 - series link bypassed (i.e., like a zero impedance line) and shunt link operating
as a STATCON
•3 - series and shunt links operating with series link at constant series impedance
•4 - series and shunt links operating with series link at constant series voltage
•5 - master device of an IPFC with P and Q setpoints specified; another FACTS device
must be designated as the slave device (i.e., its MODE is 6 or 8) of this IPFC
•6 - slave device of an IPFC with P and Q setpoints specified; the FACTS device spec-
ified in MNAME must be the master device (i.e., its MODE is 5 or 7) of this IPFC.
The Q setpoint is ignored as the master device dictates the active power exchanged
between the two devices.
•7 - master device of an IPFC with constant series voltage setpoints specified; another
FACTS device must be designated as the slave device (i.e., its MODE is 6 or 8) of
this IPFC
•8 - slave device of an IPFC with constant series voltage setpoints specified; the FACTS
device specified in MNAME must be the master device (i.e., its MODE is 5 or 7) of
this IPFC. The complex V d + jV q setpoint is modified during power flow solutions to
reflect the active power exchange determined by the master device
MODE = 1 by default
PDES Desired active power flow arriving at the terminal end bus; entered in MW.
PDES = 0.0 by default
QDES Desired reactive power flow arriving at the terminal end bus; entered in MVAR.
QDES = 0.0 by default
VSET Voltage setpoint at the sending end bus; entered in pu.
VSET = 1.0 by default
SHMX Maximum shunt current at the sending end bus; entered in MVA at unity voltage.
SHMX = 9999.0 by default
TRMX Maximum bridge active power transfer; entered in MW.
TRMX = 9999.0 by default
VTMN Minimum voltage at the terminal end bus; entered in pu.
VTMN = 0.9 by default
VTMX Maximum voltage at the terminal end bus; entered in pu.
VTMX = 1.1 by default
VSMX Maximum series voltage; entered in pu.
VSMX = 1.0 by default
--- Page 77 ---
Power Flow Data Contents
 FACTS Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
68IMX Maximum series current, or zero for no series current limit; entered in MVA at unity
voltage.
IMX = 0.0 by default
LINX Reactance of the dummy series element used during power flow solutions; entered
in pu.
LINX = 0.05 by default
RMPCT Percent of the total Mvar required to hold the voltage at the bus controlled by the shunt
element of this FACTS device that are to be contributed by the shunt element; RMPCT
must be positive. RMPCT is needed only if there is more than one local or remote
setpoint mode voltage controlling device (plant, switched shunt, FACTS device shunt
element, or VSC dc line converter) controlling the voltage at bus FCREG.
RMPCT = 100.0 by default
OWNER Owner number (1 through 9999).
OWNER = 1 by default
SET1, SET2 If MODE is 3, resistance and reactance respectively of the constant impedance, entered
in pu; if MODE is 4, the magnitude (in pu) and angle (in degrees) of the constant series
voltage with respect to the quantity indicated by VSREF; if MODE is 7 or 8, the real (V d)
and imaginary (V q) components (in pu) of the constant series voltage with respect to
the quantity indicated by VSREF; for other values of MODE, SET1 and SET2 are read,
but not saved or used during power flow solutions.
SET1 = 0.0 and SET2 = 0.0 by default
VSREF Series voltage reference code to indicate the series voltage reference of SET1 and SET2
when MODE is 4, 7 or 8:
•0 - for sending end voltage
•1 - for series current
VSREF = 0 by default
FCREG Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the bus for which voltage is to be regulated by the shunt element of this
FACTS device to the value specified by VSET. If FCREG specifies a remote bus (i.e., a bus
other than bus I), bus FCREG must be a Type 1 or 2 bus (if it is other than a Type 1 or 2
bus, the shunt element regulates voltage at the sending end bus to the value specified
by VSET). FCREG may be entered as zero if the shunt element is to regulate voltage at
the sending end bus and must  be zero if the sending end bus is a Type 3 (swing) bus.
FCREG = 0 by default
MNAME The name of the FACTS device that is the IPFC master device when this FACTS device
is the slave device of an IPFC (i.e., its MODE is specified as 6 or 8). MNAME must  be
enclosed in single or double quotes if it contains any blanks or special characters.
MNAME is blank by default
NREG A node number of bus FCREG. The bus section of bus FCREG to which node NREG is
connected is the bus section for which voltage is to be regulated by the shunt element
--- Page 78 ---
Power Flow Data Contents
 FACTS Device Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
69to the value specified by VSET. If bus FCREG is not in a substation, NREG must be
specified as 0.
NREG = 0 by default
FACTS device data input is terminated with a record specifying a FACTS device number of zero.
1.24.1. FACTS Device Notes
PSSE’s FACTS device model contains a shunt element that is connected between the sending end bus and
ground, and a series element connected between the sending and terminal end buses.
A static synchronous condenser (STATCON) or static compensator (STATCOM) is modeled by a FACTS device
for which the terminal end bus is specified as zero (i.e., the series element is disabled).
A unified power flow controller (UPFC) has both the series and shunt elements active, and allows for the
exchange of active power between the two elements (i.e., TRMX is positive). A static synchronous series
compensator (SSSC) is modeled by setting both the maximum shunt current limit (SHMX) and the maximum
bridge active power transfer limit (TRMX) to zero (i.e., the shunt element is disabled).
An Interline Power Flow Controller (IPFC) is modeled by using two series FACTS devices. One device of this
pair must be assigned as the IPFC master device by setting its control mode to 5 or 7; the other must be
assigned as its companion IPFC slave device by setting its control mode to 6 or 8 and specifying the name
of the master device in its MNAME. In an IPFC, both devices have a series element but no shunt element.
Therefore, both devices typically have SHMX set to zero, and VSET of both devices is ignored. Conditions at
the master device define the active power exchange between the two devices. TRMX of the master device
is set to the maximum active power transfer between the two devices, and TRMX of the slave device is set
to zero.
Figure 1-15  shows the PSSE FACTS control device model with its various setpoints and limits.
Each FACTS sending end bus must be a Type 1 or 2 bus, and each terminal end bus must be a Type 1 bus. Refer
to Section 6.3.16, FACTS Devices and Section 6.3.18, AC Voltage Control for other topological restrictions
and for details on the handling of FACTS devices during the power flow solution activities.
--- Page 79 ---
Power Flow Data Contents
 Switched Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
70
FACTS Control Device Setpoints and Limits
1.25. Switched Shunt Data
Automatically switched shunt devices may be represented on a network bus.

--- Page 80 ---
Power Flow Data Contents
 Switched Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
71The switched shunt elements at a bus may consist entirely of blocks of shunt reactors (each B i is a negative
quantity), entirely of blocks of capacitor banks (each B i is a positive quantity), or of both reactors and capac-
itors.
Each network bus to be represented in PSSE with switched shunt admittance devices must have a switched
shunt data record specified for it. The switched shunts are represented with up to eight blocks of admittance,
each one of which consists of up to nine steps of the specified block admittance. Each switched shunt data
record has the following format:
I,MODSW,ADJM,STATUS,VSWHI,VSWLO,SWREG,RMPCT,’RMIDNT’,BINIT,                                                                N1,B1,N2,B2,...N8,B8,NREG
I Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ).
No default allowed
MODSW Control mode:
•0 - locked
•1 - discrete adjustment, controlling voltage locally or at bus SWREG
•2 - continuous adjustment, controlling voltage locally or at bus SWREG
•3 - discrete adjustment, controlling the reactive power output of the plant at bus
SWREG
•4 - discrete adjustment, controlling the reactive power output of the VSC dc line
converter at bus SWREG of the VSC dc line for which the name is specified as RMIDNT
•5 - discrete adjustment, controlling the admittance setting of the switched shunt
at bus SWREG
•6 - discrete adjustment, controlling the reactive power output of the shunt element
of the FACTS device for which the name is specified as RMIDNT
MODSW = 1 by default
ADJM Adjustment method:
•0 - steps and blocks are switched on in input order, and off in reverse input order;
this adjustment method was the only method available prior to PSSE-32.0.
•1 - steps and blocks are switched on and off such that the next highest (or lowest,
as appropriate) total admittance is achieved.
ADJM = 0 by default
STATUS Initial switched shunt status of one for in-service and zero for out-of-service.
STATUS = 1 by default
VSWHI When MODSW is 1 or 2, the controlled voltage upper limit; entered in pu.
When MODSW is 3, 4, 5 or 6, the controlled reactive power range upper limit; entered
in pu of the total reactive power range of the controlled voltage controlling device.
--- Page 81 ---
Power Flow Data Contents
 Switched Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
72VSWHI is not used when MODSW is 0.
VSWHI = 1.0 by default
VSWLO When MODSW is 1 or 2, the controlled voltage lower limit; entered in pu.
When MODSW is 3, 4, 5 or 6, the controlled reactive power range lower limit; entered
in pu of the total reactive power range of the controlled voltage controlling device.
VSWLO is not used when MODSW is 0.
VSWLO = 1.0 by default
SWREG Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the bus for which voltage or connected equipment reactive power output
is controlled by this switched shunt.
When MODSW is 1 or 2, SWREG specifies the bus for which voltage is regulated. If
SWREG specifies a remote bus (i.e., a bus other than bus I), bus SWREG must be a
Type 1 or 2 bus (if it is other than a Type 1 or 2 bus, the switched shunt controls its
own voltage). SWREG may be entered as 0 if the switched shunt is to regulate its own
voltage.
When MODSW is 3, SWREG specifies the Type 2 or 3 bus where plant reactive power
output is to be regulated by this switched shunt. Set SWREG to I if the switched shunt
and the plant that it controls are connected to the same bus.
When MODSW is 4, SWREG specifies the converter bus of a VSC dc line where converter
reactive power output is to be regulated by this switched shunt. Set SWREG to I if the
switched shunt and the VSC dc line converter that it controls are connected to the
same bus.
When MODSW is 5, SWREG specifies the remote bus to which the switched shunt for
which the admittance setting is to be regulated by this switched shunt is connected.
SWREG is not used when MODSW is 0 or 6.
SWREG = 0 by default
RMPCT Percent of the total Mvar required to hold the voltage at the bus controlled by bus I that
are to be contributed by this switched shunt; RMPCT must be positive. RMPCT is need-
ed only if there is more than one local or remote setpoint mode voltage controlling
device (plant, switched shunt, FACTS device shunt element, or VSC dc line converter)
controlling the voltage at bus SWREG. Only used if MODSW = 1 or 2.
RMPCT = 100.0 by default
RMIDNT When MODSW is 4, the name of the VSC dc line where the converter bus is specified in
SWREG. When MODSW is 6, the name of the FACTS device where the shunt element’s
reactive output is to be controlled. RMIDNT is not used for other values of MODSW.
RMIDNT is a blank name by default
BINIT Initial switched shunt admittance; entered in Mvar at unity voltage.
BINIT = 0.0 by default
--- Page 82 ---
Power Flow Data Contents
 Switched Shunt Notes
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
73Ni Number of steps for block i (0 Ni 9). The first zero value of N i or B i is interpreted as the
end of the switched shunt blocks for bus I.
Ni = 0 by default
Bi Admittance increment for each of N i steps in block i; entered in Mvar at unity voltage.
Bi = 0.0 by default
NREG A node number of bus SWREG. The bus section of bus SWREG to which node NREG
is connected is the bus section for which voltage or connected equipment reactive
power output is controlled by this switched shunt. If bus SWREG is not in a substation,
NREG must be specified as 0.
NREG = 0 by default
Switched shunt data input is terminated with a record specifying a bus number of zero.
1.25.1. Switched Shunt Notes
BINIT needs to be set to its actual solved case value only when the network, as entered into the working case
via activity READ, is to be considered solved as read in, or when the device is to be treated as locked (i.e.,
MODSW is set to zero or switched shunt adjustment is disabled during power flow solutions).
The switched shunt elements at a bus may consist entirely of reactors (each B i is a negative quantity) or
entirely of capacitor banks (each B i is a positive quantity). In these cases, when ADJM is zero, the shunt blocks
are specified in the order in which they are switched on the bus; when ADJM is one, the shunt blocks may
be specified in any order.
The switched shunt devices at a bus may be comprised of a mixture of reactors and capacitors. In these cases,
when ADJM is zero, the reactor blocks are specified first in the order in which they are switched on, followed
by the capacitor blocks in the order in which they are switched on; when ADJM is one, the reactor blocks are
specified first in any order, followed by the capacitor blocks in any order.
In specifying reactive power limits for setpoint mode voltage controlling switched shunts (i.e., those with
MODSW of 1 or 2), the use of a very narrow admittance range is discouraged. The Newton-Raphson based
power flow solutions require that the difference between the controlling equipment's high and low reactive
power limits be greater than 0.002 pu for all setpoint mode voltage controlling equipment (0.2 Mvar on a
100 MVA system base). It is recommended that voltage controlling switched shunts have admittance ranges
substantially wider than this minimum permissible range.
When MODSW is 3, 4, 5 or 6, VSWLO and VSWHI define a restricted band of the controlled device’s reactive
power range. They are specified in pu of the total reactive power range of the controlled device (i.e., the
plant QMAX - QMIN when MODSW is 3, MAXQ - MINQ of a VSC dc line converter when MODSW is 4, SNiBi
- SNjBj when MODSW is 5 where i are those switched shunt blocks for which Bi is positive and j are those
for which Bi is negative, and 2.*SHMX of the shunt element of the FACTS device, reduced by the current
corresponding to the bridge active power transfer when a series element is present, when MODSW is 6).
VSWLO must be greater than or equal to 0.0 and less than VSWHI, and VSWHI must be less than or equal to
1.0. That is, the following relationship must be honored:
0.0 VSWLO VSWHI 1.0
The reactive power band for switched shunt control is calculated by applying VSWLO and VSWHI to the re-
active power band extremes of the controlled plant or VSC converter. For example, with MINQ of -50.0 pu
--- Page 83 ---
Power Flow Data Contents
 Switched Shunt Example
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
74and MAXQ of +50.0 pu, if VSWLO is 0.2 pu and VSWHI is 0.75 pu, then the reactive power band defined by
VSWLO and VSWHI is:
-50.0 + 0.2*(50.0 - (-50.0)) = -50.0 + 0.2*100.0 = -50.0 + 20.0 = -30.0 Mvar
through:
-50.0 + 0.75*(50.0 - (-50.0)) = -50.0 + 0.75*100.0 = -50.0 + 75.0 = +25.0 Mvar
The switched shunt admittance is kept in the working case and reported in output tabulations separately
from the fixed bus shunt, which is entered on the fixed bus shunt data record (refer to Fixed Bus Shunt Data ).
Refer to Section 6.3.15, Switched Shunt Devices and Section 6.3.17, DC Lines and Switched Shunt Adjust-
ment for details on the handling of switched shunts during power flow solutions.
It is recommended that data records for switched shunts for which the control mode is 5 (i.e., they control
the setting of other switched shunts) be grouped together following all other switched shunt data records.
This practice will eliminate any warnings of no switched shunt at the specified remote bus simply because
the remote bus switched shunt record has not as yet been read.
1.25.2.  Switched Shunt Example
Figure 1-16  shows the data record that may be specified to match the combination of switched elements on
Bus 791. Note that the quantity shown as Load is entered as Load Data , and the fixed bus shunt indicated
as B SHUNT and G SHUNT is entered as Fixed Bus Shunt Data .
Example Data Record for Combination of Switched Shunts
--- Page 84 ---
Power Flow Data Contents
 GNE Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
751.26. GNE Device Data
PSSE accepts data for Generic Network Element (GNE) devices that are modeled in BOSL ".mac" or ".xmac"
files. Each instance of a GNE device to be represented in PSSE is specified in a GNE device data record block.
Each GNE device data record block has the following format:
’NAME’,’MODEL’,NTERM, BUS1, ...,BUSNTERM,NREAL,NINTG,NCHAR,
STATUS, OWNER,NMETR
REAL1, ..., REALmin(10,NREAL)
INTG1, ..., INTGmin(10,NINTG)
CHAR1, ..., CHARmin(10,NCHAR)
NAME The non-blank alphanumeric identifier assigned to this GNE device. Each GNE device
instance must  have a unique NAME. NAME may be up to twelve characters and may
contain any combination of blanks, uppercase letters, numbers and special characters.
NAME must  be enclosed in single or double quotes if it contains any blanks or special
characters.
No default allowed
MODEL The name of the BOSL model. NAME is the root name of the ".mac" or ".xmac" file
containing the BOSL model.
No default allowed
NTERM The number of buses to which this instance of the model is connected. NTERM may
be either 1 or 2 for a variable admittance model, and must be 1 for a variable power
model and a variable current model.
NTERM = 1 by default
BUSi Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ).
No default allowed
NREAL Number of floating point data items required by model MODEL. NREAL must be iden-
tical to the number required by the ".mac" or ".xmac" file.
NREAL = 0 by default
NINTG Number of buses required in calculating the inputs required by model MODEL. NINTG
must be identical to the number required by the ".mac" or .".xmac" file.
NINTG = 0 by default
NCHAR Number of two-character identifiers (e.g., machine identifiers, circuit identifiers, etc.)
required in calculating the inputs required by model MODEL. NCHAR must be identical
to the number required by the ".mac" or ".xmac" file.
NINTG = 0 by default
STATUS Device status of one for in-service and zero for out-of-service.
STATUS = 1 by default
--- Page 85 ---
Power Flow Data Contents
 Induction Machine Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
76OWNER Owner to which the device is assigned (1 through 9999). By default, OWNER is the
owner to which BUS1 is assigned (refer to Bus Data ).
NMETR Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ), of the non-metered end bus. NMETR is used for GNE devices with NTERM > 1.
NMETR = BUSNTERM by default
REAL I NREAL floating point data items required by model MODEL.
Data items are entered 10 per line, with as many lines as required to supply NREAL
data items. If NREAL is 0, no record is specified.
REAL I = 0.0 by default
INTG I NINTG bus numbers or extended bus names required by model MODEL. INTG I = BUS1
by default.
Data items are entered 10 per line, with as many lines as required to supply NINTG
data items. If NINTG is 0, no record is specified.
INTG I = BUS1 by default
CHAR I NCHAR two-character identifiers required by model MODEL.
Data items are entered 10 per line, with as many lines as required to supply NCHAR
data items. If NCHAR is 0, no record is specified.
CHAR I = '1' by default
GNE device data input is terminated with a record specifying a blank GNE device name or a GNE device name
of ’0’.
GNE devices are not recognized in all forms of analysis available in PSS®E. For example, they are ignored
in the fault analysis activities. Those analysis functions from which they are excluded print an appropriate
message if any in-service GNE devices are present in the working case.
1.27. Induction Machine Data
Each network bus at which an induction machine is to be represented must be specified in at least one
induction machine data record. Multiple induction machines may be represented at a bus by specifying more
than one induction machine data record for the bus, each with a different machine identifier.
Each induction machine data record has the following format:
I,ID,STATUS,SCODE,DCODE,AREA,ZONE,OWNER,TCODE,BCODE,MBASE,RATEKV,PCODE,          PSET,H,A,B,D,E,RA,XA,XM,R1,X1,R2,X2,X3,E1,SE1,E2,SE2,IA1,IA2,XAMULT
I Bus number, or extended bus name enclosed in single quotes (refer to Extended Bus
Names ).
No default allowed
ID One- or two-character uppercase non-blank alphanumeric machine identifier used to
distinguish among multiple induction machines at bus I. It is recommended that, at
buses for which a single induction machine is present, it be designated as having the
machine identifier '1'.
--- Page 86 ---
Power Flow Data Contents
 Induction Machine Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
77ID = '1' by default
STATUS Machine status of 1 for in-service and 0 for out-of-service.
STATUS = 1 by default
SCODE Machine standard code:
•1 - for NEMA
•2 - for IEC
SCODE = 1 by default
DCODE Machine design code. Following are allowed machine design codes:
•0 - for Custom design with equivalent circuit reactances specified
•1 - for NEMA Design A
•2 - for NEMA Design B / IEC Design N
•3 - for NEMA Design C / IEC Design H
•4 - for NEMA Design D
•5 - for NEMA Design E
DCODE = 2 by default
AREA Area to which the induction machine is assigned (1 through 9999). By default, AREA
is the area to which bus I is assigned (refer to Bus Data ).
ZONE Zone to which the induction machine is assigned (1 through 9999). By default, ZONE
is the zone to which bus I is assigned (refer to Bus Data ).
OWNER Owner to which the induction machine is assigned (1 through 9999). By default, OWN-
ER is the owner to which bus I is assigned (refer to Bus Data ).
TCODE Type of mechanical load torque variation:
•1 - for the simple power law
•2 - for the WECC model
TCODE = 1 by default
BCODE Machine base power code:
•1 - for 1 for mechanical power (MW) output of the machine
•2 - for apparent electrical power (MVA) drawn by the machine
BCODE = 1 by default
MBASE Machine base power; entered in MW or MVA. This value is specified according to
BCODE, and could be either the mechanical rating of the machine or the electrical
input. It is necessary only that the per unit values entered for the equivalent circuit
parameters match the base power.
--- Page 87 ---
Power Flow Data Contents
 Induction Machine Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
78MBASE = system base MVA by default
RATEKV Machine rated voltage; entered in kV line-to-line, or zero to indicate that machine rated
voltage is assumed to be identical to the base voltage of bus I.
RATEKV = 0.0 by default
PCODE Scheduled power code:
•1 - for mechanical power (MW) output of the machine
•2 - for electrical real power (MW) drawn by the machine
PCODE = 1 by default
PSET Scheduled active power for a terminal voltage at the machine of 1.0 pu of the machine
rated voltage; entered in MW. This value is specified according to PCODE, and is either
the mechanical power output of the machine or the real electrical power drawn by
the machine. The sign convention used is that PSET specifies power supplied to the
machine:
A positive value of electrical power means that the machine is operating as a motor;
similarly, a positive value of mechanical power output means that the machine is dri-
ving a mechanical load and operating as a motor.
No default allowed.
H Machine inertia; entered in per unit on MBASE base.
H = 1.0 by default
A, B, D, E Constants that describe the variation of the torque of the mechanical load with speed.
If TCODE is 1 (simple power law model), only D is used; if TCODE is 2 (WECC model),
all of these constants are used.
A = B = D = E = 1.0 by default.
RA Armature resistance, R a (> 0.0); entered in per unit on the power base MBASE and
voltage base RATEKV.
RA = 0.0 by default
XA Armature leakage reactance, X a (> 0.0); entered in per unit on the power base MBASE
and voltage base RATEKV.
XA = 0.0 by default
XM Unsaturated magnetizing reactance, X m (> 0.0); entered in per unit on the power base
MBASE and voltage base RATEKV.
XM = 2.5 by default
R1 Resistance of the first rotor winding ("cage"), r 1 (> 0.0); entered in per unit on the
power base MBASE and voltage base RATEKV.
R1 = 999.0 by default
X1 Reactance of the first rotor winding ("cage"), X 1 (>0.0); entered in per unit on the
power base MBASE and voltage base RATEKV.
--- Page 88 ---
Power Flow Data Contents
 Machine Electrical Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
79X1 = 999.0 by default
R2 Resistance of the second rotor winding ("cage"), r 2 (> 0.0); entered in per unit on the
power base MBASE and voltage base RATEKV.
R2 = 999.0 by default
X2 Reactance of the second rotor winding ("cage"), X 2 (>0.0); entered in per unit on the
power base MBASE and voltage base RATEKV.
X2 = 999.0 by default
X3 Third rotor reactance, X 3 (> 0.0); entered in per unit on the power base MBASE and
voltage base RATEKV.
X3 = 0.0 by default
E1 First terminal voltage point from the open circuit saturation curve, E 1 (> 0.0); entered
in per unit on RATEKV base.
E1 = 1.0 by default
SE1 Saturation factor at terminal voltage E1, S(E 1).
SE1 = 0.0 by default
E2 Second terminal voltage point from the open circuit saturation curve, E 2 (> 0.0); en-
tered in per unit on RATEKV base.
E2 = 1.2 by default
SE2 Saturation factor at terminal voltage E2, S(E 2).
SE2 = 0.0 by default
IA1,IA2 Stator currents in PU specifying saturation of the stator leakage reactance, XA.
IA1 =IA2 = 0.0 by default
XAMULT Multiplier for the saturated value. Allowed value 0 to 1.0.
XAMULT = 1.0 by default
Induction machine data input is terminated with a record specifying a bus number of zero.
1.27.1. Machine Electrical Data
The positive sequence steady state equivalent circuit for the induction machine is shown in Figure 1-17 .
--- Page 89 ---
Power Flow Data Contents
 Load Mechanical Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
80
Induction Machine Equivalent Circuit
The machine model is described by eight electrical elements: three resistive and five inductive. Values are
specified in per unit on the base power, MBASE, and rated voltage, RATEKV, which are also specified on the
data record.
The left side of the circuit is the machine armature: r a is the armature resistance and X a is the armature leak-
age reactance. The armature and the rotor are linked through the magnetizing reactance X m; the unsaturated
value of the mutual reactance is specified.
The rotor is described by two parallel resistance and reactance branches, r 1, X1 and r 2, X2, that represent the
"cages" or windings in the rotor. To model a single cage machine, the resistance and reactance of the second
of these parallel branches must both  be specified as 999.0; i.e., to model a single cage machine, specify r 2
= X 2 = 999.0 on the data record.
The reactance X 3 is included to allow a more general model.
The mutual reactance X m saturates. The saturation curve is for the induction machine operating with no load.
Two points on the saturation curve must be specified. These are normally chosen such that E 1 is near the
"knee" of the saturtion curve and E 2 is near its ceiling. Saturation is neglected if E 1 * S(E 1) = 0.0; therefore,
to neglect saturation, specify either E1 or SE1 as 0.0.
If a non-zero machine design code (DCODE) value is specified, all data items from RA to the end of the record
are ignored, and pre-programmed machine electrical and saturation data values are assigned to the machine.
If you wish to modify any of these data items after they have been assigned, you may change the machine
design code to 0 (custom).
1.27.2. Load Mechanical Data
Five data items (TCODE, A, B, D and E) are used to describe how the torque of the mechanical load varies with
speed. When TCODE is 1, a simple power law is applied that uses the constant specified as D. The equation is

--- Page 90 ---
Power Flow Data Contents
 Substation Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
81where T load0 is the load torque and S 0 is the slip at a terminal voltage of 1.0 pu.
The WECC model applies the following equations:
Tload = Tload0  [A(1-s) 2+B(1-s)+D(1-s)E+C 0] WECC Model
C0=1-A(1-s 0) 2-B(1-s 0)+D(1-s 0)E
1.28. Substation Data
Each substation to be represented in PSS®E is introduced by reading a substation data record block. Each
substation block consists of:
•A substation data record
•Several node data records
•Several substation switching device data records
•Several equipment terminal data records
1.28.1. Substation Data Record
The first record in each substation block is a substation data record. Exactly one such record is specified in
each substation block. Each substation data record has the following format:
IS, NAME, LATI, LONG, SGR
IS Substation number (1 through 99999).
No default allowed
NAME Substation name, NAME may be up to forty characters and may contain any combi-
nation of blanks, uppercase letters, numbers and special characters. NAME must be
enclosed in single or double quotes if it contains any blanks or special characters.
NAME is forty blanks by default
LATI Substation latitude in degrees (-90.0 to 90.0).
It is positive for North and negative for South, 0.0 by default
LONG Substation longitude in degrees. (-180.0 to 180.0).
It is positive for East and negative for West, 0.0 by default
SRG Substation grounding DC resistance in ohms.
Substation grounding DC resistance in ohms 0.1 ohms by default
Substation data input is terminated with a record specifying a substation number of zero.
1.28.2. Node Data
The substation data record is followed by substation node data. Each node in the substation is specified in
a node data record. Each node data record has the following format:
--- Page 91 ---
Power Flow Data Contents
 Station Switching Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
82NI, NAME, I, STATUS, VM, VA
NI Node number (1 through 999).
No default allowed
NAME Node name, NAME may be up to 40 characters and may contain any combination of
blanks, uppercase letters, numbers and special characters. NAME must be enclosed in
single or double quotes if it contains any blanks or special characters.
NAME is 40 blanks by default
I Electrical Bus number (1 through 999997) in bus branch model. The electrical bus
represents this node NI and others that are connected by closed switching devices.
No default allowed
STATUS Node status. One for in-service and zero for out-of-service.
STATUS = 1 by default
VM Node voltage magnitude; entered in pu.
VM = -1.0 by default
VA Node voltage phase angle; entered in degrees.
VA = 0.0 by default
Node data input for this substation is terminated with a record specifying a node number of zero.
Node Data Notes
VM and VA need to be set to their actual solved case values only under the following conditions:
•The network, as entered into the working case via activity READ, is to be considered solved as read in
•Substation bus “I” is represented in the bus branch model by multiple bus sections
The voltage of each bus representing one of the bus sections is set to the node voltage corresponding to the
bus section number (e.g., if bus 154 is represented by the bus sections154-1 and 154-4, the voltage at bus
154-1 is set to the voltage specified for node 1 and the voltage at bus 154-4 is set to the voltage specified
for node 4).
Otherwise, unless some better estimate of the solved voltage and/or phase angle is available, VM and VA
may be omitted. In this case, the voltage at each of the bus sections is set to the voltage specified for bus “I”.
1.28.3. Station Switching Device Data
The node data records are followed by substation switching device data. Each station switching device in the
substation is specified in a station switching device data record. Each station switching device data record
has the following format:
NI,NJ,CKT,NAME,TYPE,STATUS,NSTAT,X,RATE1,RATE2,RATE3
NI From node number (1 through 999). The from node must be in the sub- station IS.
No default allowed
--- Page 92 ---
Power Flow Data Contents
 Equipment Terminal Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
83NJ To node number (1 through 999). The to node must be in the substation IS.
No default allowed
CKT Two-character uppercase non-blank alphanumeric switching device identifier.
CKT = '1' by default
NAME Switching device name, NAME may be up to 40 characters and may contain any com-
bination of blanks, uppercase letters, numbers and special characters. NAME must be
enclosed in single or double quotes if it contains any blanks or special characters.
NAME is 40 blanks by default
TYPE Switching device type
1 - Generic connector
2 - Circuit breaker
3 - Disconnect switch
STATUS Switching device status. One for close and zero for open.
STATUS = 1 by default
NSTAT Switching device normal status. One for close and zero for open.
NSTAT = 1 by default
X Switching device reactance; entered in pu. A non-zero value of X must be entered for
each switching device.
X = 0.0001 by default
RATE1 First rating; entered in either MVA or current expressed as MVA.
RATE2 Second rating; entered in either MVA or current expressed as MVA.
RATE3 Third rating; entered in either MVA or current expressed as MVA.
Station switching device data input for this substation is terminated with a record specifying a from node
number of zero.
1.28.4. Equipment Terminal Data
The substation switching device data records are followed by equipment terminal data. Equipment items
that are connected to substation buses are specified in equipment terminal data records. The following para-
graphs describe the record formats of the various types of terminal equipment that may be specified. Within
the set equipment terminal data records specified for a substation, records maybe specified in any order.
Equipment terminal data input for this substation is terminated with a record specifying a bus number of zero.
Load Terminal Data
Each load terminal data record has the following format:
I, NI, 'L', ID
I Bus number.
--- Page 93 ---
Power Flow Data Contents
 Equipment Terminal Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
84No default allowed
NI Node number (1 through 999). If the electrical bus has node breaker model, in other
words it represents a set of nodes in a substation, the node must be one node in the
set and indicates the connections of the load within the substation. If the electrical
bus has no node breaker model, it is zero.
No default allowed
L Single character ‘L’ to indicate the record contains terminal information for a Load.
ID One or two-character uppercase non-blank alphanumeric load identifier used to dis-
tinguish among multiple loads at bus I. It is recommended that, at buses for which a
single load is present, the load be designated as having the load identifier '1'.
ID = '1' by default
Fixed Shunt Terminal Data
Each fixed shunt terminal data record has the following format:
I, NI, 'F', ID
I Bus number.
No default allowed
NI Node number (1 through 999). If the electrical bus has node breaker model, in other
words it represents a set of nodes in a substation, the node must be one node in the
set and indicates the connections of the shunt within the substation. If the electrical
bus has no node breaker model, it is zero.
No default allowed
F Single character ‘F’ to indicate the record contains terminal information for a Fixed
Shunt.
ID One or two-character uppercase non-blank alphanumeric shunt identifier used to dis-
tinguish among multiple shunts at bus I. It is recommended that, at buses for which
a single shunt is present, the shunt be designated as having the shunt identifier '1'.
ID = '1' by default
Machine Terminal Data
Each machine terminal data record has the following format:
I, NI, 'M', ID
I Bus number.
No default allowed
NI Node number (1 through 999). If the electrical bus has node breaker model, in other
words it represents a set of nodes in a substation, the node must be one node in the set
and indicates the connections of the machine within the substation. If the electrical
bus has no node breaker model, it is zero.
--- Page 94 ---
Power Flow Data Contents
 Equipment Terminal Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
85No default allowed
M Single character ‘M’ to indicate the record contains terminal information for a Machine.
ID One or two-character uppercase non-blank alphanumeric machine identifier used to
distinguish among multiple machines at bus I. It is recommended that, at buses for
which a single machine is present, the machine be designated as having the machine
identifier '1'.
ID = '1' by default
Branch and two winding Transformer Terminal Data
Each non-transformer branch and two-winding transformer terminal data record has the following format:
I,NI,'B',J,CKT  or: I,NI,'2',J,CKT
I From bus number.
No default allowed
NI From node number (1 through 999). If the electrical from bus has node breaker model,
in other words it represents a set of nodes in a substation, the from node must be one
node in the set and indicates the connections of branch within the substation. If the
electrical from bus has no node breaker model, it is zero.
No default allowed
'B' or 2 Specifies that the record contains terminal information for a non-transformer or two-
winding transformer.
J To bus number.
No default allowed
CKT One or two-character uppercase non-blank alphanumeric switching device identifier.
CKT = '1' by default
Three winding Transformer Terminal Data
Each three winding transformer terminal data record has the following format:
I, NI, '3', J, K, CKT
I From bus number.
No default allowed
NI From node number (1 through 999). If the electrical from bus I has node breaker mod-
el, in other words it represents a set of nodes in a substation, the from node must be
one node in the set and indicates the connections of branch within the substation. If
the electrical from bus has no node breaker model, it is zero.
No default allowed
3 Single character ‘3’ to indicate the record contains terminal information for a three-
winding transformer.
J To bus number.
--- Page 95 ---
Power Flow Data Contents
 Equipment Terminal Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
86No default allowed
K To bus number.
No default allowed
CKT One or two-character uppercase non-blank alphanumeric switching device identifier.
CKT = '1' by default
Switched Shunt Terminal Data
Each switched shunt terminal data record has the following format:
I, NI, 'S'
I Bus number.
No default allowed
NI Node number (1 through 999). If the electrical bus has node breaker model, in other
words it represents a set of nodes in a substation, the node must be one in the set and
indicates the connections of the switched shunt within the substation. If the electrical
bus has no node breaker model, it is zero.
No default allowed
S Single character ‘S’ to indicate the record contains terminal information for a switched
shunt.
Induction Machine Terminal Data
Each induction machine terminal data record has the following format:
I, NI, 'I', ID
I Bus number.
No default allowed
NI Node number (1 through 999). Must be one of the nodes of the bus within the sub-
station to which the induction machine is to be connected to. If the bus has no node
breaker model, it is zero.
No default allowed
I Single character ‘I’ to indicate the record contains terminal information for an induction
machine.
ID One or two-character uppercase non-blank alphanumeric machine identifier used to
distinguish among multiple machines at bus I. It is recommended that, at buses for
which a single machine is present, the machine be designated as having the machine
identifier '1'.
ID = '1' by default
Two-terminal DC Line Terminal Data
Each two-terminal DC line terminal data record has the following format:
--- Page 96 ---
Power Flow Data Contents
 Equipment Terminal Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
87I, NI, 'D', "NAME'
I Rectifier or Inverter converter bus number.
No default allowed
NI Node number (1 through 999). Must be one of the nodes of the bus within the sub-
station to which one end of the two-terminal dc line is to be connected to. If the elec-
trical bus has no node breaker model, it is zero.
No default allowed
D Single character ‘D’ to indicate that the record contains terminal information for a two-
terminal dc line connection.
NAME The non-blank alphanumeric identifier of the two-terminal dc line.
No default allowed
Voltage Source Converter (VSC) DC Line Terminal Data
Each VSC DC line terminal data record has the following format:
I, NI, 'V', "NAME'
I Converter bus number.
No default allowed
NI Node number (1 through 999). Must be one of the nodes of a converter bus within a
substation to which one end of the VSC dc line is to be connected to. If the bus has
no node breaker model, it is zero.
No default allowed
V Single character ‘V’ to indicate that the record contains terminal information for a VSC
dc line connection.
NAME The non-blank alphanumeric identifier of the VSC dc line.
No default allowed
Multi-terminal DC Line Terminal Data
Each multi-terminal DC line terminal data record has the following format:
I, NI, 'N', "NAME'
I AC converter station bus number.
No default allowed
NI Node number (1 through 999). Must be one of the nodes of the ac converter station
bus within the substation to which the multi-terminal dc line is to be connected to. If
the bus has no node breaker model, it is zero.
No default allowed
N Single character ‘N’ to indicate that the record contains terminal information for a mul-
ti-terminal dc line connection.
--- Page 97 ---
Power Flow Data Contents
 End of Data Indicator
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
88NAME The non-blank alphanumeric identifier of the multi-terminal dc line.
No default allowed
FACTS Device Terminal Data
Each FACTS device terminal data record has the following format:
I, NI, 'A', "NAME'
I Sending or terminal end bus number of the FACTS device.
No default allowed
NI Node number (1 through 999). Must be one of the nodes of the ac converter station
bus within the substation to which the FACTS device is to be connected to. If the bus
has no node breaker model, it is zero.
No default allowed
A Single character ‘A’ to indicate that the record contains terminal information for a FAC-
TS device connection.
NAME The non-blank alphanumeric identifier of the FACTS device.
No default allowed
1.29. End of Data Indicator
It is good practice to end the Power Flow Raw Data File with a Q Record . Then, if new data categories are
introduced in a point release of PSS ®E, no modification of the file is required.
--- Page 98 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
89Chapter 2
Sequence Data File
2.1. Overview
The input stream to activity RESQ is a Sequence Data File containing 11 groups of records with each group
specifying a particular type of sequence data required for fault analysis work (see Figure 2-1 ). Any piece of
equipment for which sequence data is to be entered in activity RESQ must  be represented as power flow data
in the working case. That is, activity RESQ will not accept data for a bus, generator, branch, switched shunt
or fixed shunt not contained in the working case.
All data is read in free format with data items separated by a comma or one or more blanks. Each category
of data except the change code is terminated by a record specifying an I value of zero. Termination of all
data is indicated by a value of Q.
Change Code
System Wide Data
Generator Sequence Data
Load Sequence Data
Zero Sequence Non-Transformer Branch Data
Zero Sequence Mutual Impedance Data
Zero Sequence Transformer Data
Zero Sequence Switched Shunt Data
Zero Sequence Fixed Shunt Data
Induction Machine Sequence Data
Non-Conventional Source Fault Contribution Data
Q Record
Sequence Data Input Structure
--- Page 99 ---
Sequence Data File
 Change Code
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
902.2. Change Code
The first record in the Sequence Data File contains two data items as follows:
IC, REV
IC IC = 0 indicates the initial input of sequence data for the network contained in the
working case. All buses, generators, branches, switched shunts and fixed shunts for
which no data record is entered in a given category of data have the default values
assigned for those data items.
IC = 1 indicates change case input of sequence data for the network contained in the
working case. All buses, generators, branches, switched shunts and fixed shunts for
which no data record is entered in a given category of data have those data items
unchanged; i.e., they are not set to the default values.
IC=0 by default.
REV PSSE revision number. REV = Current revision by default.
The use of the change case mode in activity RESQ is identical to its use in activity READ: for the addition
of equipment to the working case (e.g., to add a zero sequence mutual coupling to the working case). It
is not valid to set IC to one for the initial execution of activity RESQ for the network in the working case;
in this case, an appropriate message is printed and activity RESQ continues its execution as if IC had been
specified as zero.
2.3. System Wide Data
Through the system-wide data category, data that pertains to the case as a whole (rather than to individual
equipment items) may be included in the Sequence Data File to allow convenient transfer of it with the case.
Records may be included that define:
•Short Circuit Output Report Format
•Metal Var Oxide (MOV) Iteration Options
•Short Circuit Model
Generally, each record specified in the System-Wide Data category begins with a NAME that defines the type
of data specified on the record. The formats of the various records are described in the following paragraphs.
RPTFORMAT Record
The Short Circuit Output Report Format record begins with the name RPTFORMAT and contains unit and co-
ordinates option for reporting current, voltage and Thevenin impedance. Using keyword input, any or all of
the following report format parameters may be specified:
AMPOUT = 0, Current and Voltages in PU) (default)
= 1, Current in Amperes and Voltages in kV
POLROU = 0, Currents and Voltages in Rectangular co-ordinates (default)
--- Page 100 ---
Sequence Data File
 Generator Sequence Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
91= 1, Currents and Voltages in Polar co-ordinates
AMPOUTZ = 0, Thevenin Impedance in PU (default)
= 1, Thevenin Impedance in ohms
POLROUZ = 0, Thevenin Impedance in Rectangular co-ordinates (default)
= 1, Thevenin Impedance in Polar co-ordinates
Those RPTFORMAT parameters that are specified may be entered n any order. The following is an example
of this record:
RPTFORMAT, AMPOUT=0, POLROU=0, AMPOUTZ=0, POLROUZ=0
MOV Record
The Metal Var Oxide (MOV) Iteration Options record begins with the name MOV and contains linearized MOV
model iteration parameters used by short circuit calculation methods. Using keyword input, any or all of the
following MOV iteration parameters may be specified:
ITERATIONS Maximum number of iterations (default=20)
TOLERANCE Tolerance (default=0.01)
MOVALPHA Acceleration factor (default=0.3)
Those parameters that are specified may be entered in any order. The following is an example of this record:
MOV, ITERATIONS=20, TOLERANCE=0.01, MOVALPHA=0.3
SCMODEL Record
The Short Circuit Model Options record begins with the name SCMODEL and contains fault analysis modeling
option setting. Using keyword input, SCMODEL parameters may be specified:
SCNRML = 0, center tapped two-phase modeling
= 1, Normal three-phase modeling (default)
The following is an example of this record:
SCMODEL, SCNRML=1
2.4. Generator Sequence Data
Each network bus to be represented as a generator bus (i.e., as a current source) in the unbalanced analysis
activities must have sequence generator impedances entered into the PSSE working case for all in-service
machines at the bus.
Each generator sequence impedance data record has the following format:
--- Page 101 ---
Sequence Data File
 Generator Sequence Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
92I, ID, ZRPOS, ZXPPDV, ZXPDV, ZXSDV, ZRNEG, ZXNEGDV, ZR0, ZX0DV, CZG, ZRG, ZXG,
REFDEG
I Bus number; bus I must be present in the working case as a generator bus.
ID One or two character machine identifier of the machine bus I for which the data is
specified by this record.
ID = '1' by default
ZRPOS Generator positive sequence resistance; entered in pu on machine base (i.e., on bus
voltage base and MBASE).
ZRPOS = ZR (source resistance in raw data) by default.
ZXPPDV Generator positive sequence saturated subtransient reactance; entered in pu on ma-
chine base (i.e., on bus voltage base and MBASE).
ZXPPDV = ZX (source reactance in raw data) by default
ZXPDV Generator positive sequence saturated transient reactance; entered in pu on machine
base (i.e., on bus voltage base and MBASE).
ZXPDV = ZXPPDV by default
ZXSDV Generator positive sequence saturated synchronous reactance; entered in pu on ma-
chine base (i.e., on bus voltage base and MBASE).
ZXSDV = XPPDV by default.
ZRNEG Generator negative sequence resistance; entered in pu on machine base (i.e., on bus
voltage base and MBASE).
ZRNEG = ZRPOS by default
ZXNEGDV Generator negative sequence saturated reactance; entered in pu on machine base
(i.e., on bus voltage base and MBASE).
ZXNEGDV = ZXPPDV by default
ZR0 Generator zero sequence resistance; entered in pu on machine base (i.e., on bus volt-
age base and MBASE).
ZR0 = ZRPOS by default
ZX0DV Generator zero sequence saturated reactance; entered in pu on machine base (i.e., on
bus voltage base and MBASE).
ZX0DV = ZXPPDV by default
CZG Units of grounding impedance (ZRG and ZXG) values, = 1 for pu (on bus voltage base
and MBASE), = 2 for Ohms
ZRG Generator grounding resistance; entered in pu on machine base (i.e., on bus voltage
base and MBASE) when CZG=1 or in ohms when CZG=2.
ZRG = 0.0 by default
ZXG Generator grounding reactance; entered in pu on machine base (i.e., on bus voltage
base and MBASE) when CZG=1 or in ohms when CZG=2.
ZXG = 0.0 by default
--- Page 102 ---
Sequence Data File
 Generator Sequence Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
93REFDEG Generator Reference Angle; entered in degrees.
This angle is used only when fault calculations with for "FLAT" voltage profile is select-
ed.
REFDEG = 0.0 by default
Throughout this Manual, the complex positive sequence generator impedance used in fault analysis will be
referred to as ZPOS. The real component of ZPOS is always ZRPOS. Its imaginary component is either ZXPPDV,
ZXPDV or ZXSDV, according to the selection made in selecting the fault analysis calculation activity. Similarly,
the negative sequence generator impedance, ZNEG, is ZRNEG + j ZXNEGDV, and the zero sequence generator
impedance, ZZERO, is ZR0 + j ZX0DV.
During the initial input of sequence data (i.e., IC = 0 on the first data record), any machine for which no
data record of this category is entered has its positive sequence resistance set to ZR (the real component
of ZSORCE), and all three positive sequence reactances set to ZX (the imaginary component of ZSORCE).
ZSORCE is the generator impedance entered in activities READ, Section Activity, TREA, RDCH, and MCRE; it is
used in switching studies and dynamic simulations (refer to Generator Data).
In subsequent executions of activity RESQ (i.e., IC = 1 on the first data record), any machine for which no data
record of this category is entered has its positive sequence generator impedance values unchanged. Note
that the generator positive sequence impedance values entered in activity RESQ for fault analysis purposes
is not necessarily the same as the generator impedance (ZSORCE) used in dynamics, and that it does not
overwrite ZSORCE. That is, the two different sets of positive sequence impedance data are specified in the
working case simultaneously at different locations.
During the initial input of sequence data (i.e., IC = 0 on the first data record), any machine for which no data
record of this category is entered has its negative sequence generator impedance ZNEG set equal to ZRPOS
+ j ZXPPDV. In subsequent executions of activity RESQ (i.e., IC = 1 on the first data record), any machine for
which no data record of this category is entered has its negative sequence generator impedance unchanged.
For those machines at which the step-up transformer is represented as part of the generator data (i.e., XTRAN
is non-zero), ZZERO is not used and, in the fault analysis activities, the step-up transformer is assumed to be
a delta wye transformer. Refer to Modeling of Generator Step-Up Transformers (GSU).
For those machines that do not include the step-up transformer as part of the generator data (i.e., XTRAN
is zero), a zero sequence impedance of zero results in the machine being treated as an open circuit in the
zero sequence.
During the initial input of sequence data (i.e., IC = 0 on the first data record), any machine for which no data
record of this category is entered has its zero sequence generator impedance ZZERO set equal to ZRPOS + j
ZXPPDV. In subsequent executions of activity RESQ (i.e., IC = 1 on the first data record), any machine for which
no data record of this category is entered has its zero sequence generator impedance unchanged.Generator
sequence impedance data input is terminated with a record specifying a bus number of zero.
Figure 2-2  shows generator representation in sequence networks.
--- Page 103 ---
Sequence Data File
 Load Sequence Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
94
Figure - Generator Sequence Networks
PSS®E calculates pu grounding impedance from ohm as below:
2.5. Load Sequence Data
Exceptional negative sequence loads (i.e., loads that, in the negative sequence, differ from the positive se-
quence loads) and zero sequence loads are entered into the working case in load sequence data records in
the Sequence Data File. Each load negative and zero sequence data record has the following format:
I, ID, PNEG, QNEG, GRDFLG, PZERO, QZERO
I Bus number, bus I must be present in the working case.
ID One or two character load identifier of the load at bus I for which the data is specified
by this record.
ID = '1' by default
PNEG Active component of negative sequence load; entered in MW at one per unit voltage.
If PNEG=0 or is not specified, PNEG = positive sequence load MW.
QNEG Reactive component of negative sequence load; entered in MVAR at one per unit volt-
age. If QNEG=0 or is not specified, QNEG = positive sequence load MVAR. QNEG is
specified as negative MVAR for lagging (reactive) power factor loads.
GRDFLG Grounding flag; 1 for grounded loads and 0 for ungrounded loads.
GRDFLG=0 by default
PZERO Active component of zero sequence load; entered in MW at 1 pu voltage. If PZERO is
non-zero and GRDFLG=1, PZERO is modelled. If GRDFLG=0, PZERO is ignored.
PZERO=0 by default
QZERO Reactive component of zero sequence load; entered in MVAR at 1 pu voltage. If QZE-
RO is non-zero and GRDFLG=1, QZERO is modelled. If GRDFLG=0, QZERO is ignored.
QZERO is specified as negative MVAR for lagging (reactive) power factor loads.
QZERO = 0.0 by default
--- Page 104 ---
Sequence Data File
 Zero Sequence Non-Transformer
Branch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
95For any bus where no load sequence data record is specified, or PNEG and QNEG are both specified as zero,
the load elements are assumed to be equal in the positive and negative sequence networks. For any bus
where no load sequence data record is specified, or PZERO and QZERO are both specified as zero, or GRDFLG
is specified as zero (i.e., an ungrounded load), no load component is represented in the zero sequence.
The user is advised to exercise caution in specifying negative and zero sequence loads. In the fault analysis
calculations, constant power and constant current loads are converted to constant admittance at the pre-
fault voltage. Further, when positive sequence loads are changed, either directly by the user or by activities
such as SCAL, it may be appropriate to change previously specified negative and zero sequence loads. It is
the user’s responsibility to ensure that the positive sequence loading data, as contained in the working case,
is coordinated with the specified negative and zero sequence load data.
Load sequence data input is terminated with a record specifying a bus number of zero.
2.6. Zero Sequence Non-Transformer Branch Data
Zero sequence non-transformer branch parameters are entered into the working case in zero sequence non-
transformer branch data records in the Sequence Data File. Each zero sequence branch data record has the
following format:
I, J, CKT, RLINZ, XLINZ, BCHZ, GI, BI, GJ, BJ, IPR, SCTYP
I Bus number of one end of the branch.
J Bus number of the other end of the branch.
CKT One- or two-character branch circuit identifier; a non-transformer branch with circuit
identifier CKT between buses I and J must be in the working case.
CKT = '1' by default
RLINZ Zero sequence branch resistance; entered in pu on system base MVA and bus voltage
base.
RLINZ = 0.0 by default
XLINZ Zero sequence branch reactance; entered in pu on system base MVA and bus voltage
base. Any branch for which RLINZ and XLINZ are both 0.0 is treated as open in the zero
sequence network. XLINZ must be negative for a series capacitor branch.
XLINZ = 0.0 by default
BCHZ Total zero sequence branch charging susceptance; entered in pu.
BCHZ = 0.0 by default
GI,BI Complex zero sequence admittance of the line connected shunt at the bus I end of
the branch; entered in pu.
GI + jBI = 0.0 by default
GJ,BJ Complex zero sequence admittance of the line connected shunt at the bus J end of
the branch; entered in pu.
GJ + jBJ = 0.0 by default
IPR MOV rated current for a series capacitor branch; entered in kA. It must be positive.
IPR = 0.0 by default
--- Page 105 ---
Sequence Data File
 Zero Sequence Mutual Impedance Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
96SCTYP MOV Protection Mode
•0 for normal branch (i.e., not a MOV protected branch)
•1 for MOV Protection enabled
•2 for MOV Protection disabled
•3 for Spark Gap Protection enabled (information only, not used in any calculations)
SCTPY=0 by default
The zero sequence network is assumed to be a topological subset of the positive sequence network. That is,
it may have a branch in every location where the positive sequence network has a branch, and may not have
a branch where the positive sequence network does not have a branch. The zero sequence network does not
need to have a branch in every location where the positive sequence network has a branch.
A branch treated as a zero impedance line in the positive sequence (refer to Zero Impedance Lines) is treated
in the same manner in the zero sequence, regardless of its specified zero sequence impedance.
During the initial input of sequence data (i.e., IC = 0 on the first data record), any non-transformer branch
for which no data record of this category is entered is treated as open in the zero sequence network (i.e., the
zero sequence impedance is set to zero). In subsequent executions of activity RESQ (i.e., IC = 1 on the first
data record), any branch for which no data record of this category is entered has its zero sequence branch
data unchanged.
Zero sequence branch data input is terminated with a record specifying a from bus number of zero.
2.7. Zero Sequence Mutual Impedance Data
Data describing mutual couplings between branches in the zero sequence network are entered into the
working case in zero sequence mutual impedance data records in the Sequence Data File. Each zero sequence
mutual impedance data record has the following format:
I, J, CKT1, K, L, CKT2, RM, XM, BIJ1, BIJ2, BKL1, BKL2
I Bus number of one end of the first branch
J Bus number of the other end of the first branch
CKT1 One- or two-character branch circuit identifier of the first branch; a non-transformer
branch with circuit identifier ICKT1 between buses I and J must be in the working case.
CKT1 = '1' by default
K Bus number of one end of the second branch
L Bus number of the other end of the second branch
CKT2 One- or two-character branch circuit identifier of the second branch; a non-trans-
former branch with circuit identifier ICKT2 between buses K and L must be in the
working case.
CKT2 = '1' by default
RM,XM Branch-to-branch mutual impedance coupling circuit CKT1 from bus I to bus J with
circuit CKT2 from bus K to bus L; entered in pu.
--- Page 106 ---
Sequence Data File
 Zero Sequence Mutual Impedance Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
97No default is allowed
BIJ1 Starting location of the mutual coupling along circuit CKT1 from bus I to bus J relative
to the bus I end of the branch; entered in per unit of total line length.
BIJ1 = 0.0 by default
BIJ2 Ending location of the mutual coupling along circuit CKT1 from bus I to bus J relative
to the bus I end of the branch; entered in per unit of total line length.
BIJ2 = 1.0 by default
BKL1 Starting location of the mutual coupling along circuit CKT2 from bus K to bus L relative
to the bus K end of the branch; entered in per unit of total line length.
BKL1 = 0.0 by default
BKL2 Ending location of the mutual coupling along circuit CKT2 from bus K to bus L relative
to the bus K end of the branch; entered in per unit of total line length.
BKL2 = 1.0 by default
The following rules must be observed in specifying mutual impedance data:
•The maximum number of zero sequence mutual couplings that may be entered at the standard size levels
of PSSE is defined in Table 3-1 Standard Maximum PSS®E Program Capacities.
•The polarity of a mutual coupling is determined by the ordering of the bus numbers (I,J,K,L) in the data
record. The dot convention applies, with the from buses (I and K) specifying the two dot ends of the
coupled branches.
•RM+jXM specifies the circuit-to-circuit mutual impedance, given the polarity implied by I and K.
•The geographical B factors are required only if one or both of the two mutually coupled lines is to be
involved in an unbalance part way down the line, and only part of the length of one or both of the lines
is involved in the coupling. (Note that the default values of the B factors result in the entire length of the
first line coupled to the entire length of the second line.)
•The values of the B factors must be between zero and one inclusive; they define the portion of the line
involved in the coupling.
•BIJ1 must be less than BIJ2, and BKL1 must be less than BKL2.
•Mutuals involving transformers or zero impedance lines are ignored by the fault analysis solution activities.
The following figure schematically illustrates a mutual coupling with BIJ1 = 0.0, BIJ2 = 0.4, BKL1 = 0.0 and
BKL2 = 1.0 (the first 40% of the first line coupled with the entire second line).
--- Page 107 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
98
As a second example, BIJ1 = 0.6, BIJ2 = 1.0, BKL1 = 0.0 and BKL2 = 0.6 (last 40% of the first line coupled
with the first 60% of the second line) might be depicted as follows:
Zero sequence mutual impedance data input is terminated with a record specifying a from bus number of
zero.
2.8. Zero Sequence Transformer Data
Zero sequence transformer parameters are entered into the working case in zero sequence transformer data
records in the Sequence Data File. Each transformer data record has one of the following formats:
For two-winding transformers:
I,J,K,CKT,CZ0,CZG,CC,RG1,XG1,R01,X01,RG2,XG2,R02,X02,RNUTRL,XNUTRL
For three-winding transformers:
I,J,K,CKT,CZ0,CZG,CC,RG1,XG1,R01,X01,RG2,XG2,R02,X02,
RG3,XG3,R03,X03,RNUTRL,XNUTRL
Notations used in Zero Sequence Networks
--- Page 108 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
99Z01=R01+jX01 Z02=R02+jX02 Z03=R03+jX03
Zg1=RG1+jXG1 Zg2=RG2+jXG2 Zg3=RG3+jXG3
Znutrl=RNUTRL+jXNUTRL
I Bus number of the bus to which a winding of the transformer is connected.
J Bus number of the bus to which another winding of the transformer is connected.
K Bus number of the bus to which another winding of the transformer is connected. Zero
is used to indicate that no third winding is present (i.e., that a two-winding transformer
is being specified).
K = 0 by default
CKT One- or two-character transformer circuit identifier; a transformer with circuit identi-
fier CKT between buses I and J (and K if K is non-zero) must be in the working case.
CKT = '1' by default
CZ0 The non-grounding impedance data I/O code defines the units in which the impedance
values Z01, Z02 and Z03 are specified. In specifying these impedances, the winding
base voltage values are always the nominal winding voltages (NOMV1, NOMV2 and
NOMV3) that are specified on the third, fourth and fifth records of the Transformer
Data block in the Power Flow Raw Data File; see Transformer Data for more details.
If no value for NOMVn is specified, the winding "n" voltage base is assumed to be
identical to the winding "n" bus base voltage.
Legacy Connection Codes
For those connection codes that existed prior to PSS®E-33, CZ0 must be specified as
1. For two-winding transformers, these are connection codes 1 through 9; for three-
winding transformers, these are connection codes 1 through 6, as well as the three
digit connection codes where each digit refers to one of the two-winding connection
codes 1 through 7 that is to be applied to one of the three windings of the transformer.
For all two-winding transformers with legacy connection codes, Z01 is specified in
per unit on system MVA base and winding voltage base. Only for two-winding trans-
formers with connection code 9, Z02 is specified in per unit on system MVA base and
winding 2 voltage base.
For all three-winding transformers with legacy connection codes, Z01, Z02 and Z03
are specified in per unit on system MVA base and winding "n" voltage base.
Connection Codes Introduced in PSS®E-33
For all two-digit connection codes for two- and three-winding transformers, CZ0 may
be specified as one of the following values:
1 for per unit on system MVA base and winding "n" voltage base
2 for per unit on a specified MVA base and winding "n" voltage base
These are the same units dictated by CZ values 1 and 2 on the transformer data record
of the Power Flow Raw Data File.
--- Page 109 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
100CZG The grounding impedance data I/O code defines the units in which the impedance val-
ues Zg1, Zg2, Zg3 and Znutrl are specified. In specifying these impedances, the wind-
ing base voltage values are always the nominal winding voltages (NOMV1, NOMV2
and NOMV3) that are specified on the third, fourth and fifth records of the Transformer
Data block in the Power Flow Raw Data File. If no value for NOMVn is specified, the
winding "n" voltage base is assumed to be identical to the winding "n" bus base volt-
age.
Legacy Connection Codes
For those connection codes that existed prior to PSS®E-33, CZG must be specified as
1. For two-winding transformers, these are connection codes 1 through 9; for three-
winding transformers, these are connection codes 1 through 6, as well as the three
digit connection codes.
For two-winding transformers with legacy connection codes 2, 3 and 9, Zg1 is speci-
fied in per unit on system MVA base and winding voltage base. For two-winding trans-
formers with connection codes 5 through 8, Zg1 is specified in per unit on system
MVA base and winding voltage base. For two-winding transformers with connection
code 8, Zg2 is specified in per unit on system MVA base and winding 2 voltage base.
For three-winding transformers with legacy connection code 1, Zg1 is specified in per
unit on system MVA base and winding 1 voltage base. For three-winding transformers
with legacy connection code 5, Zg2 is specified in per unit on system MVA base and
winding 2 voltage base.
Connection Codes Introduced in PSS®E-33
For all two-digit connection codes for two- and three-winding transformers, CZG may
be specified as one of the following values:
1 for per unit on system MVA base and winding voltage base
2 for per unit on a specified MVA base and winding voltage base
For three winding transformers, Zg1 is on SBASE12, Zg2 on SBASE23, Zg3 on SBASE31,
and Znutrl on SABSE12.
3 for ohms
For CZG values of 1 and 2, these are the same units dictated by CZ values 1 and 2 on
the transformer data record of the Power Flow Raw Data File
CC Winding connection code indicating the connections and ground paths to be used in
modeling the transformer in the zero sequence network.
For a two-winding transformer, valid values are 1 through 9 and 11 through 23. They
define the following zero sequence connections that are shown in Section Two Wind-
ing Transformer Zero Sequence Network Diagrams and Connection Codes.
1, 11 series path, no ground path.
2, 12 no series path, ground path on Winding 1 side.
--- Page 110 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1013, 13 no series path, ground path on Winding 2 side.
4, 14 no series or ground paths.
5, 15 series path, ground path on Winding 2 side (normally only used as part of a
three-winding transformer).
6, 16 no series path, ground path on Winding 1 side, earthing transformer on Winding
2 side.
7, 17 no series path, earthing transformer on Winding 1 side, ground path on Winding
2 side.
8, 18 series path, ground path on each side.
9, 19 series path on each side, ground path at the junction point of the two series
paths.
20 series path on each side, ground path at the junction point of the two series paths;
wye grounded - wye grounded core type transfromer
21 series path, no ground path; wye grounded - wye grounded non core type auto
transfromer
22 series path, no ground path; wye - wye ungrounded core type auto transfromer
For a three-winding transformer, CC may be specified as a three digit number, each
digit of which is 1 through 7; the first digit applies to Winding 1, the second to Wind-
ing 2, and the third to Winding 3, where the winding connections correspond to the
first seven two-winding transformer connections defined above and shown in Section
5.5.6, Three Winding Transformer Zero Sequence Network Diagrams and Connection
Codes.
Alternatively, several common zero sequence three-winding transformer connection
combinations may be specified using the single digit values 1 through 6. These are
defined the zero sequence transformer connections.
The following ' single digit three-winding connection codes are available, where the
connection codes of the three two-winding transformers comprising the three-wind-
ing transformer are shown in parenthesis in winding number order:
1, 11 series path in all three windings, Winding 1 ground path at the star point bus
(5-1-1).
2, 12 series path in Windings 1 and 2, Winding 3 ground path at the star point bus
(1-1-3).
3, 13 series path in Winding 2, ground paths from windings one and three at the star
point bus (3-1-3).
4, 14 no series paths, ground paths from all three windings at the star point bus
(3-3-3).
--- Page 111 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1025, 15 series path in windings one and three, ground path at the Winding 2 side bus
(1-2-1).
6, 16 series path in all three windings, no ground path (1-1-1).
17 series path in Windings 1 and 2, Winding 3 ground path at the star point bus; wye
grounded - wye grounded - delta auto transfromer
18 series path in Windings 1 and 2, no ground path in Winding 3;wye - wye - delta
ungrounded neutral auto transfromer
Section 5.5.3, Transformers in the Zero Sequence, includes examples of the proper
specification of CC and the remaining transformer data items for several types of trans-
formers.
CC = 14 by default
RG1, XG1 Zero sequence grounding impedance on for an impedance grounded transformer.
This data is specified in units specified by CZG.
Refer  zero sequence network diagrams  for each connection code for specifying this
value.
RG1 = 0.0 and XG1 = 0.0 by default
From two winding transformers connection codes ZG1=RG1+jXG1 is interpreted as:
•ZG1 is grounding impedance of winding 1 when CC>9
•ZG1 is grounding impedance of winding 1 when CC=2
•ZG1 is grounding impedance of winding 2 when CC=3
•ZG1 is grounding impedance at winding 2 bus when CC=5,6
•ZG1 is grounding impedance at winding 1 bus when CC=7,8
•ZG1 is grounding impedance at star point of zero-sequence network T-model when
CC=9
From three winding transformers connection codes ZG1=RG1+jXG1 is interpreted as:
•ZG1 is grounding impedance of winding 1 when CC are two digits, like, CC=12
•ZG1 is grounding impedance at star point of zero-sequence network T-model when
CC=1 or User Code Digit=5
•ZG1 is grounding impedance of winding 1 when CC=2 or user code digit is 2.
•ZG1 is grounding impedance of winding 2 when CC=3 or user code digit is 3.
R01, X01 Refer zero sequence network diagram for each connection code for specifying this
value. This value could be:
--- Page 112 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
103•Two winding transformer: Z01 is equal to the transformer's zero sequence leakage
impedance. Z01 is equal to the transformer's positive sequence impedance by de-
fault.
•Three winding transformers and connection codes CC=11 and higher: Z01 is equal
to the transformer's winding 1 to winding 2 zero sequence impedance. Z01 is equal
to the transformer's winding 1 to winding 2 positive sequence impedance by de-
fault.
•For three winding transformers and connection codes CC=1 through 9 and User
Code: Z01 is equal to the transformer's winding 1 star-circuit equivalent zero se-
quence impedance. is equal to the transformer's winding 1 star-circuit equivalent
positive sequence impedance by default.
This data is specified in units specified by CZ0.
RG2, XG2 Zero sequence grounding impedance on winding 2 for an impedance grounded trans-
former. This data is applicable for connection codes CC=11 and higher.
This data is specified in units specified by CZG.
Refer zero sequence network diagram for each connection code for specifying this
value.
RG2 = 0.0 and XG2 = 0.0 by default
R02, X02 Refer zero sequence network diagram for each connection code for specifying this
value. This value could be:
•For two winding transformer:
Refer zero sequence network diagram for each connection code for specifying Z02
value. R02 = 0.0 and X02 = 0.0 by default.
•For three winding transformer and connection codes CC=11 and higher:
Z02 is equal to the transformer's winding 2 to winding 3 zero sequence impedance.
Z02 is equal to the transformer's winding 2 to winding 3 positive sequence imped-
ance by default.
•For three winding transformer and connection codes CC=1 through 9 and User
Code: Z02 is equal to the transformer's winding 2 star-circuit equivalent zero se-
quence impedance. Z02 is equal to the transformer's winding 2 star-circuit equiva-
lent positive sequence impedance by default.
This data is specified in units specified by CZ0.
RG3, XG3 Zero sequence grounding impedance on winding 3 for an impedance grounded trans-
former. This data is applicable for connection codes CC=11 and higher. This data is
specified in units specified by CZG.
Refer zero sequence network diagram for each connection code for specifying this
value. RG3 = 0.0 and XG3 = 0.0 by default.
--- Page 113 ---
Sequence Data File
 Zero Sequence Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
104R03, X03 Refer zero sequence network diagram for each connection code for specifyingthis val-
ue. This value could be:
•For three winding transformer and connection codes CC=11 and higher: Z03 is equal
to the transformer's winding 3 to winding 1 zero sequence impedance.
Z03 is equal to the transformer's winding 3 to winding 1 positive sequence impedance
by default.
•For three winding transformer and connection codes CC=1 through 9: Z03 is equal
to the transformer's winding 3 star-circuit equivalent zero sequence impedance.
Z03 is equal to the transformer's winding 3 star-circuit equivalent positive sequence
impedance by default. This data is specified in units specified by CZ0.
RNUTRL, XNUTRL Zero sequence common neutral grounding impedance. This data is applicable for con-
nection codes CC=11 and higher.
This data is specified in units specified by CZG.
Refer zero sequence network diagram for each connection code for specifying this
value.
RNUTRL = 0.0 and XNUTRL = 0.0 by default.
Refer Sections 5.5.4, 5.5.5 and 5.5.6 for transformer winding connections, zero sequence network diagrams
and connection codes.
In specifying zero sequence impedances for three-winding transformers, note that winding  impedances are
required, and that the zero sequence impedances return to the default value of the positive sequence wind-
ing impedances. Recall that, in specifying positive sequence data for three-winding transformers (refer to
Transformer Data), measured impedances between pairs of buses to which the transformer is connected,
not winding impedances, are required. PSSE converts the measured bus-to-bus impedances to winding im-
pedances that are subsequently used in building the network matrices. Activities LIST and EXAM tabulate
both sets of positive sequence impedances.
Recall that the service status of a three-winding transformer may be specified such that two of its windings
are in-service and the remaining winding is out-of-service (refer to Transformer Data). Recall also that data
for the three windings of a three-winding transformer is stored in the working case as three two-winding
transformers (refer to Three-Winding Transformer Notes). Ri + jXi is stored with the two-winding transformer
containing winding i’s data; RG + jXG is stored with the two-winding transformer containing the data of the
winding at which it is applied.
Placing one winding of a three-winding transformer out-of-service may require a change to the zero sequence
data of the two windings that remain in-service. As the fault analysis calculation functions construct the zero
sequence admittance matrix, when a three-winding transformer with one winding out-of-service is encoun-
tered, all data pertaining to the out-of-service winding (i.e., pertaining to the two-winding transformer con-
taining the data of the out-of-service winding) is ignored. Thus, any zero sequence series and ground paths
resulting from the impedances and connection code of the out-of-service winding are excluded from the
zero sequence admittance matrix. It is the user’s responsibility to ensure that the zero sequence impedances
and connection codes of the two in-service windings result in the appropriate zero sequence modeling of
the transformer.
--- Page 114 ---
Sequence Data File
 Zero Sequence Switched Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
105Specification of the transformer connection code along with the impedances entered here enables the fault
analysis activities to correctly model the zero sequence transformer connections, including the ground ties
and open series branch created by certain grounded transformer windings. If no connection code is entered
for a transformer, all windings are assumed to be open. Section 5.5.3, Transformers in the Zero Sequence
gives additional details on the treatment of transformers in the zero sequence network, including examples
of specifying data for several types of transformers.
During the initial input of sequence data (i.e., IC = 0 on the first data record), any transformer for which no
data record of this category is entered has it zero sequence winding impedance(s) set to the same value(s)
as its positive sequence winding impedance(s). In subsequent executions of activity RESQ (i.e., IC = 1 on the
first data record), any transformer for which no data record of this category is entered has its zero sequence
transformer data unchanged.
Zero sequence transformer data input is terminated with a record specifying a from bus number of zero.
Figure 2.1. Two-Winding Transformer Positive Sequence Connections
Figure 2.2. Three-Winding Transformer Positive Sequence Connections
2.9. Zero Sequence Switched Shunt Data
Zero sequence shunt admittances for switched shunts are entered into the working case in zero sequence
switched shunt data records in the Sequence Data File. Each switched shunt data record has the following
format:
I, ID, BZ1, BZ2, ... BZ8
I Bus number; bus I must be present in the working case with positive sequence
switched shunt data.
ID One or two character identifier of the switched shunt at bus I for which the data is
specified by this record.
--- Page 115 ---
Sequence Data File
 Zero Sequence Fixed Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
106ID = '1' by default
BZi Zero sequence admittance increment for each of the steps in block i; entered in MVAR
at 1 pu unit voltage.
BZ i = 0.0 by default
Data specified on zero sequence switched shunt data records must be coordinated with the corresponding
positive sequence data (refer to Switched Shunt Data). The number of blocks and the number of steps in
each block are taken from the positive sequence data.
Activity RESQ generates an alarm for any block for which any of the following applies:
•The positive sequence admittance is positive and the zero sequence admittance is negative.
•The positive sequence admittance is negative and the zero sequence admittance is positive.
•The positive sequence admittance is zero and the zero sequence admittance is non-zero.
The zero sequence admittance switched on at a bus is determined from the bus positive sequence value,
with the same number of blocks and steps in each block switched on.
Zero sequence switched shunt data input is terminated with a record specifying a bus number of zero.
2.10. Zero Sequence Fixed Shunt Data
Zero sequence fixed shunts are entered into the working case in zero sequence fixed shunt data records in
the Sequence Data File. Each zero sequence fixed shunt data record has the following format:
I, ID, GSZERO, BSZERO
I Bus number; bus I must be present in the working case.
ID One or two character identifier of the fixed shunt at bus I for which the data is specified
by this record.
ID = '1' by default
GSZERO Active component of zero sequence admittance to ground to represent this fixed shunt
at bus I; entered in MW at 1 pu voltage.
BSZERO Reactive component of zero sequence admittance to ground to represent this fixed
shunt at bus I; entered in MVAR at 1 pu voltage.
For any fixed shunt for which either no such data record is specified or GSZERO and BSZERO are both speci-
fied as 0.0, no zero sequence ground path is modeled for this fixed shunt. The zero sequence ground tie cre-
ated by a grounded transformer winding is automatically added to whatever zero sequence fixed shunt and
shunt load is specified at the bus when the transformer winding connection code data for the transformer
is specified (refer to Zero Sequence Transformer Data ).
Zero sequence fixed shunt data input is terminated with a record specifying a bus number of zero.
2.11. Induction Machine Sequence Data
Each zero sequence induction machine data has the following format:
--- Page 116 ---
Sequence Data File
 Induction Machine Sequence Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
107I, ID, CZG, GRDFLG, ILR2IR_SUB, R2X_SUB, ZR0, ZX0, ZRG, ZXG, ILR2IR_TRN, R2X_TRN,
ILR2IR_NEG, R2X_NEG
I Bus number; bus I must be present in the working case as an induction machine bus.
ID One or two character identifier of the induction machine at bus I for which the data
is specified by this record.
ID = '1' by default
CZG Units of grounding impedance (ZRG and ZXG) values, = 1 for pu (on bus voltage base
and MBASE), = 2 for Ohms
GRDFLG 1 for grounded machine, 0 for ungrounded machine (Most commonly, stator winding
is either delta connected or star connected with the neutral isolated.) GRDFLG=0 by
default.
ILR2IR_SUB Ratio of positive sequence subtransient locked rotor current to rated current.
R2X_SUB Ratio of positive sequence subtransient resistance to reactance. This is used only when
positive sequence impedance is calculated using ILR2IR_SUB.
R2X_SUB = 0.0 by default
ZR0 Zero sequence resistance; entered in pu on machine base (i.e., on bus voltage base
and MBASE).
ZR0 = 0.0 by default
ZX0 Zero sequence reactance; entered in pu on machine base (i.e., on bus voltage base
and MBASE). Induction machine is isolated in zero sequence if the stator winding is
either delta connected or star connected with the neutral isolated.
For a star connected stator winding with an earthed neutral, zero sequence imped-
ance is much smaller than motor starting impedance (subtransient or transient) and
does not vary with time. Induction machine zero sequence impedance can be assumed
equal to the stator ac resistance
ZX0 = ZXPPDV by default depending on generator impedance option
ZRG Grounding resistance; entered in pu on machine base (i.e., on bus voltage base and
MBASE) when CZG=1 or in ohms when CZG=2.
ZRG = 0.0 by default
ZXG Grounding reactance; entered in pu on machine base (i.e., on bus voltage base and
MBASE) when CZG=1 or in ohms when CZG=2.
ZXG = 0.0 by default
ILR2IR_TRN Ratio of positive sequence transient locked rotor current to rated current.
R2X_TRN Ratio of positive sequence transient resistance to reactance. This is used only when
positive sequence impedance is calculated using ILR2IR_TRN.
R2X_TRN = 0.0 by default
ILR2IR_NEG Ratio of negative sequence locked rotor current to rated current.
R2X_NEG Ratio of negative sequence resistance to reactance. This is used only when negative
sequence impedance is calculated using ILR2IR_NEG.
R2X_NEG = 0.0 by default
--- Page 117 ---
Sequence Data File
 Non-Conventional Source Fault Contri-
bution Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
108Application Notes:
Positive (ZP) and negative (ZN) sequence impedances are calculated as below.
1. When locked rotor current to rated current sequence data is provided, depending on activity "reactance"
option used:
2. When locked rotor current to rated current sequence data is not provided:
3. When ILR2IR_NEG and R2X_NEG are not provided, ZN=ZP.
Figure 2.3. Induction machine sequence networks
2.12. Non-Conventional Source Fault Contribution Data
The fault contribution from non-conventional sources like Type 3 and Type 4 Wind Generators and Photo
Voltaic Sources is very much dependent on the voltage source converters used, and is fundamentally differ-
ent from conventional synchronous fault contribution. In most of the machines cases, the converter design
and control determines the fault contribution. This fault current contribution is modeled as shown below.
Figure 2.4. Non-conventional Source Fault Contribution Characteristics
In order to accommodate large number of possibilities and still provide flexibility, the fault contribution data
is provided as a time dependent capability curve. Each data record has following format:
--- Page 118 ---
Sequence Data File
 Application Notes - Transformers in the
Zero Sequence
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
109I, ID, T1, C1P, C1Q, T2, C2P, C2Q, T3, C3P, C3Q, T4, C4P, C4Q, T5, C5P, C5Q,
T6, C6P, C6Q
I Bus number; bus I must be present in the working case as a generator bus.
No default is allowed
ID One or two character machine identifier of the generator bus I for which the data is
specified by this record.
ID = '1' by default
Ti Time in seconds
CiP The active component of the fault current contribution in pu on rated (nominal) cur-
rent and rated voltage base (1.0 pu) at time Ti
CiQ The reactive component of fault current contribution in pu on rated (nominal) current
and rated voltage base (1-0 pu) at time Ti. When supplying reactive power, specify
CiQ as positive.
Application Note - Refer Program Application Guide for technical details.
2.13. Application Notes - Transformers in the Zero
Sequence
The fault analysis activities of PSS®E handle the zero sequence representation of two- and three-winding
transformers automatically. Other nonstandard transformer types must be reduced to combinations of two-
winding transformers, three-winding transformers, and/or branches by the use of dummy buses and equiv-
alent circuits. Note again that the introduction of buses and branches needed for the modeling of nonstan-
dard transformers is accomplished by their addition to the positive sequence network via activities READ,
TREA, or RDCH, or the [Spreadsheet] .
Transformer zero sequence data is entered into the working case by means of zero sequence transformer data
records in the Sequence Data File. Transformers are represented in the zero sequence as per their connection
codes. The establishment of the connections and ground paths is handled automatically on the basis of the
impedances and connection code entered and the winding turns ratios.
Zero sequence transformer default data is such that the transformer appears as an open circuit in the zero
sequence network. Therefore, zero sequence data must be entered for all grounded transformers.
Connection codes do not  indicate the inherent phase shift due to the relative connection of delta and wye
windings. If this phase shift is to be represented, it must be specified in the positive sequence power flow
data.
Virtually any impedance grounded two-winding transformer may be modeled automatically by specifying
its winding and grounding impedances along with the appropriate connection code. Many three-winding
transformer configurations may be handled in a similar manner; others require the addition of 3Z g or other
impedances to one or more of the winding impedances.
The winding numbers specified in zero sequence network diagrams are not directly associated with the nom-
inal voltage levels of those windings. They are associated with corresponding winding connection only.
--- Page 119 ---
Sequence Data File
 Auto Transformer Equivalent Circuit
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1102.13.1. Auto Transformer Equivalent Circuit
The equivalent circuit impedance of an auto transformer can be determined from a short circuit test per-
formed as shown in Figure 2.5, “Auto-transformer Equivalent Circuit” .
Figure 2.5. Auto-transformer Equivalent Circuit
Auto transformer ohmic impedance measured across circuit 1 when circuit 2 is short circuited (winding im-
pedance) as shown in (b) and (c) is given by:
Auto transformer turns ratio are defined as:
--- Page 120 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1112.13.2. Two Winding Transformer Zero Sequence Networks
NOTE:  * in grounding impedance notations (like Z g1*) in zero seq/uence network diagrams means PSS®E will
automatically multiply that grounding impedance by a factor 3.
CC=1 and CC=11
Series Path, No Ground Path
Figure 2.6. YNyn transformer zero sequence network
Figure 2.7. YNyn with neutral impedance transformer zero sequence network
CC=11
Set:
•R01+jX01 = Z t0
•RG1+jXG1 = Z g1
•RG2+jXG2 = Z g2
•RNUTRL+jXNUTRL = Z nutrlPSS®E calculates pu Znutrl0 as below:
NOTE: When CC=1, data specified for Zg1 and Zg2 will be ignored. Hint instead use CC=11.
--- Page 121 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
112CC=2 and CC=12
No Series Path, Ground Path on Winding 1 side
Figure 2.8. YNd transformer zero sequence network
Figure 2.9. Znyn, Zny, or ZNd transformer zero sequence network
--- Page 122 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
113
Figure 2.10. YNy core type transformer zero sequence network
NOTE: When CC=2, data specified for Zg1 will be used as grounding impedance of winding 1.
--- Page 123 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
114CC=3 and CC=13
No Series Path, Ground Path on Winding 2 side
Figure 2.11. Dyn transformer zero sequence network
Figure 2.12. YNzn, Yzn or Dzn transformer zero sequence network
--- Page 124 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
115
Figure 2.13. Yyn core type transformer zero sequence network
NOTE: To make backward compatible, when CC=3, data specified for Zg1 will be used as grounding
impedance of winding 2. Hint instead use CC=13 and specify Zg2.
--- Page 125 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
116CC=4 and CC=14
No Series Path, No Ground Path
Figure 2.14. Yy, Yd, Dy, Dd, Yyn or YNy transformer zero sequence network
Figure 2.15. Ya ungrounded auto transformer zero sequence network
--- Page 126 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
117CC=5 and CC=15
Series Path, Ground Path on Winding 2 side
Figure 2.16. CC=5 or CC=15 zero sequence network
NOTE: To make backward compatible, when CC=5, data specified for Zg1 will be used as grounding
impedance at winding 2 side. Hint instead use CC=15 and specify Zg2.
--- Page 127 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
118CC=6 and CC=16
Wye grounded - delta with an earting transformer
No Series Path, Ground Path on Winding 1 side, Earthing transformer on Winding 2 Side
Figure 2.17. YNd transformer with Zigzag or YNd earthing transformer on winding 2
side zero sequence network
Note: Activity IECS applies impedance correction factors to transformer impedances. When main and
earthing transformers are modeled separately with CC=2, impedance correction factors are applied
to both main and earthing transformers. This is incorrect. The impedance correction factors should
not applied to earthing transformer. So model main and earthing transformers correctly using CC=6
or CC=16.
--- Page 128 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
119
Figure 2.18. YNzn or Dzn core type transformer zero sequence network
--- Page 129 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
120CC=7 and CC=17
Delta with an earting transformer - Wye grounded
No Series Path, Earting transformer on Winding 1 Side, Ground Path on Winding 2 side
Figure 2.19. Dyn transformer with Zigzag or YNd earthing transformer on winding 1
side zero sequence network
No Series Path, Ground Path on Winding 2 side through core magnetizing impedance
Figure 2.20. ZNyn or ZNd core type transformer zero sequence network
--- Page 130 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
121CC=8 and CC=18
Wye grounded - wye grounded three legged core type auto transformer
Series Path, Ground Path each side
Figure 2.21. YNa core type auto transformer zero sequence network
--- Page 131 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
122CC=8
Calculate:
Z10, Z20, ZM0 from equations as in CC=18
Set:
- R01+jX01 = Z M0
- RG1+jXG1 = Z 10
- RG2+jXG2 = Z 20
CC=18
Set:
- R01+jX01 = Z to
- R02+jX02 = Z phiC
- RG2+jXG2 = Z g2CC=18
PSS®E calculates pu values as:
Where:
ZphiC is the magnetising (exciting) impedance as measured on the Common Winding with series winding on
the same core open circuited and zero sequence voltage is applied to Terminal 2.
Refer auto transformer equivalent circuit represention description for definition of N and Z to.
--- Page 132 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
123CC=9 and CC=19
•Series Path on each side, Ground Path at the junction point of the two series paths
•Wye grouned - wye grounded three legged core type auto transformer
Figure 2.22. YNa core type auto transformer zero sequence network
--- Page 133 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
124CC=9
Calculate:
Z1S, Z2S, ZSG from equations as in CC=19
Set:
- R01+jX01 = Z 1S
- R02+jX02 = Z 2S
- RG1+jXG1 = Z SGCC=19
Set:
- R01+jX01 = Z to
- R02+jX02 = Z phiC
- RG2+jXG2 = Z g2
PSS®E calculates pu values as:
Where:
ZphiC is the magnetising (exciting) impedance as measured on the Common Winding with series winding on
the same core open circuited and zero sequence voltage is applied to Terminal 2.
Refer auto transformer equivalent circuit representation description for definition of N and Z CS.
--- Page 134 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
125CC=20
•Series Path on each side, Ground Path at the junction point of the two series paths
•Wye grouned - wye grounded core type transformer
Figure 2.23. YNyn with or without neutral impedance core type transformer zero
sequence network
--- Page 135 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
126Set:
- R01+jX01 = Zt0
- R02+jX02 = Zphi0
- RG1+jXG1 = Zg1
- RG2+jXG2 = Zg2
- RNUTRL+jXNUTRL = ZnutrlPSS®E calculates pu values as:
Where:
Zphi1 is the magnetising (exciting) impedance as measured on Winding 1 with winding 2 on the same core
open circuited and zero sequence voltage is applied to Winding 1.
Zphi2 is the magnetising (exciting) impedance as measured on Winding 2 with winding 2 on the same core
open circuited and zero sequence voltage is applied to Winding 2.
Zphi0 = Z phi1 or Z phi2, if Z phi1 = Z phi2
Zphi0 = 0.5(Z phi1 + Z phi2) if Z phi1 ≠ Z phi2
--- Page 136 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
127CC=21
Figure 2.24. YNa auto transformer zero sequence network
Set:
- R01+jX01 = Zto
- RG2+jXG2 = Zg2PSS®E calculates pu values as:
Refer auto transformer equivalent circuit represention description for definition of N and Z to.
--- Page 137 ---
Sequence Data File
 Two Winding Transformer Zero Se-
quence Networks
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
128CC=22
Wye - wye ungrounded core type auto transformer
Figure 2.25. Ya ungrounded core type auto transformer zero sequence network
Set:
- R01+jX01 = Z phiSPSS®E calculates pu values as:
Where:
ZphiS is the magnetising (exciting) impedance as measured on the Series Winding with all the other windings
on the same core open circuited and zero sequence voltage is applied to Terminal 1.
Refer auto transformer equivalent circuit representation description for definition of N.
--- Page 138 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1292.13.3. Three Winding Transformer Zero Sequence Network
Note:
Impedance Notations used in the Zero Sequence Networks:
Z12 0 = Zero sequence leakage impedance between winding 1 and winding 2
Z23 0 = Zero sequence leakage impedance between winding 2 and winding 3
Z31 0 = Zero sequence leakage impedance between winding 3 and winding 1
Zg1 = Winding 1 grounding impedance
Zg2 = Winding 2 grounding impedance
Zg3 = Winding 3 grounding impedance
Znutrl = Neutral grounding impedance
The three Winding transformer is modeled as T-model in zero sequence network. The impedances of this T-
model are calculated from transformer test report in-between winding impedances as below.
Zt10 = 0.5(Z 12 0 + Z 31 0 - Z23 0)
Zt20 = 0.5(Z 12 0 + Z 23 0 - Z31 0)
Zt30 = 0.5(Z230 + Z 31 0 - Z12 0)
where:
Zt1 0 = Zero sequence impedance between winding 1 and star point
Zt2 0 = Zero sequence impedance between winding 2 and star point
Zt3 0 = Zero sequence impedance between winding 3 and star point
The in-between winding impedances can be calculated from T-model impedances as below:
Z12 = Z 1 + Z 2
Z23 = Z 2 + Z 3
Z31 = Z 3 + Z 1
--- Page 139 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
130CC=1 and CC=11 (511)
Series path in all three Windings, Winding 1 ground path at star point bus
Figure 2.26. YNynyn with magnetising impedance modelled transformer zero
sequence network
CC=1
Calculate:
Z1 0, Z 2 0, Z3 0 from equations as in CC=11
Set (pu):
- R01+jX01 = Z 1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
- RG1+jXG1 = Z g1
Assigned:
- Z1 0 = R01+jX01
- Z2 0 = R02+jX02
- Z3 0 = R03+jX03
PSS®E automatically multiplies grounding imped-
ance by 3.
- Zg 0 = 3(RG1+jXG1)CC=11
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG1+jXG1 = Z g1
PSS®E calculates pu values as:
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Z t1 0
- Z2 0 = Z t2 0
- Z3 0 = Z t3 0
- Zg 0 = 3(RG1+jXG1)
--- Page 140 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
131CC=2 and CC=12 (113)
Series path in Windings 1 and 2, Winding 3 ground path at star point bus
(For YNad, refer CC=17)
Figure 2.27. YNynd transformer zero sequence network
CC=2 or 113
Calculate:
Zt1 0, Z20, Z30 from equations
as in CC=12
Set (pu):
- R01+jX01 = Z t1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
- RG1+jXG1 = Z g3
Assigned:
- Z10 = R01+jX01
- Z20 = R02+jX02
- Z30 = R03+jX03+ 3Z g3CC=12
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG1+jXG1 = Z g1
- RG2+jXG2 = Z g2
Calculated (pu):
(no Znutrl)
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Zt 1 0+ 3Z g1
- Z2 0 = Z t2 0 + 3Zg 2
- Z3 0 = Z t3 0Calculated (pu):
(with Znutrl)
Note: When CC=2 or CC=113, Z g3 assigned is a fictious grounding impedance derived from T-model.
--- Page 141 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
132CC=3 and CC=13 (313)
Series path in Windings 2, ground paths from Windings 1 and 3 at star point bus
Figure 2.28. Dynd transformer zero sequence network
CC=3 or CC=313
Calculate:
Z1 0, Z 2 0, Z3 0 from equations as in CC=13
Set (pu):
- R01+jX01 = Z 1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
- RG1+jXG1 = Z g1
Assigned:
- Z1 0 = R01+jX01 + 3Z g1
- Z2 0 = R02+jX02
- Z3 0 = R03+jX03CC=13
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG2+jXG2 = Z g2
Calculated (pu):
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Z t1 0
- Z2 0 = Z t2 0 + 3(RG2+jXG2)
- Z3 0 = Z t3 0
Note: When CC=3 or CC=313, Z g1 assigned is a fictious grounding impedance derived from T-model.
--- Page 142 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
133CC=4 and CC=14 (333)
No series paths, ground paths from all three Windings at the star point bus
Figure 2.29. Ddd, Ddy, Dyd, Dyy, Ydd, Ydy, Yyd or Yyy transformer zero sequence
network
CC=4
Calculate:
Z1 0, Z 2 0, Z3 0 from equations as in CC=14
Set (pu):
- R01+jX01 = Z 1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
Assigned:
- Z1 0 = R01+jX01
- Z2 0 = R02+jX02
- Z3 0 = R03+jX03CC=14
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
Calculated (pu):
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Z t1 0
- Z2 0 = Z t2 0
- Z3 0 = Z t3 0
--- Page 143 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
134CC=5 and CC=15 (121)
Series path in Windings 1 and 3, ground path at Winding 2 side bus
Figure 2.30. Dynd auto transformer zero sequence network
CC=5
Calculate:
Z1 0, Z 2 0, Z3 0 from equations as in CC=15
Set (pu):
- R01+jX01 = Z 1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
- RG2+jXG2 = Z g2
Assigned:
- Z1 0 = R01+jX01
- Z2 0 = R02+jX02
- Z3 0 = R03+jX03
Calculated (pu)
- Zg 0 = 3(RG2+jXG2)CC=15
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG2+jXG2 = Z g2
Calculated (pu):
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Z t1 0
- Z2 0 = Z t2 0
- Z3 0 = Z t3 0
- Zg 0 = 3(RG2+jXG2)
--- Page 144 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
135CC=6 and CC=16 (111)
Series path in all three Windings, no ground path
Figure 2.31. YNynyn transformer zero sequence network
CC=6
Calculate:
Z1 0, Z 2 0, Z3 0 from equations as
in CC=16
Set (pu):
- R01+jX01 = Z 1 0
- R02+jX02 = Z 2 0
- R03+jX03 = Z 3 0
Assigned:
- Z1 0 = R01+jX01
- Z2 0 = R02+jX02
- Z3 0 = R03+jX03CC=16
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG1+jXG1 = Z g1
- RG2+jXG2 = Z g2
- RG3+jXG3 = Z g3
Calculated (pu):
(without Znutrl)
- Zt1 0, Zt2 0, Zt3 0
- Z1 0 = Z t1 0 + 3 (RG1+jXG1)
- Z2 0 = Z t2 0 + 3 (RG2+jXG2)
- Z3 0 = Z t3 0 + 3 (RG3+jXG3)Calculated (pu):
(with Znutrl)
- Calculate Z t1 0, Zt2 0, Zt3 0

--- Page 145 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
136CC=17
Series path in Windings 1 and 2, Winding 3 ground path at star point bus
(For wye grounded - wye grounded - delta non auto transformer, refer CC=12)
Figure 2.32. Ynad (grounded) auto transformer zero sequence network
CC=17
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
- RG2+jXG2 = Z g2Calculated (pu):
For auto transformers with one/two neutral connections taken out, IEEE Std C57.12.90-2015 standard pro-
vides the test procedure and T-model impedance calculations from that test data.
Figure 2.33. Equivalent zero sequnce network for a transformer with two externally
available neutrals and 0o phase shift between windings 1 and 2
Following four test results are used to determine zero sequence network:
--- Page 146 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
137•Test 1 - Apply voltage winding 1 and its neutral. All other windings are open-circuited. The measured zero
sequence is represented by Z 1N0.
•Test 2 - Apply voltage winding 1 and its neutral. Short Winding 2 and its neutral. All other windings may
be open-circuited or shorted. The measured zero sequence is represented by Z 1Ns.
•Test 3 - Apply voltage winding 2 and its neutral. All other windings are open-circuited. The measured zero
sequence is represented by Z 2N0.
•Test 4 - Apply voltage winding 2 and its neutral. Short Winding 1 and its neutral. All other windings may
be open-circuited or shorted. The measured zero sequence is represented by Z 2Ns.
The zero sequence network T-model impedances are calculated as below.
Set PSSE seqence data as below.
•R01 + j X 01 = Z 120 = Z 1 + Z 2
•R02 + j X 02 = Z 230 = Z 2 + Z 3
•R03 + j X 03 = Z 310 = Z 3 + Z 1
--- Page 147 ---
Sequence Data File
 Three Winding Transformer Zero Se-
quence Network
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
138CC=18
Figure 2.34. Yad (ungrounded) auto transformer zero sequence network
Set:
- R01+jX01 = Z 12 0
- R02+jX02 = Z 23 0
- R03+jX03 = Z 31 0
Calculated (pu):
Calculate Z t1 0, Zt2 0, Zt3 0

--- Page 148 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
139Chapter 3
Optimal Power Flow Data Contents
3.1. Overview
The input stream to activity ROPF is a Optimal Power Flow Data File containing 17 groups of records with
each group specifying a particular type of OPF data or constraint definition required for OPF work (see Figure
3-1). Any piece of equipment for which OPF data is to be entered in activity ROPF must be represented as
power flow data in the working case. That is, activity ROPF will not accept data for a bus, generator, branch,
switched shunt or fixed shunt not contained in the working case.
All data is read in free format with data items separated by a comma or one or more blanks. Each category
of data except the change code is terminated by a record specifying an I value of zero. Termination of all
data is indicated by a value of Q.
Change Code
Bus Voltage Constraint Data
Adjustable Bus Shunt Data
Bus Load Data
Adjustable Bus Load Table Data
Generator Dispatch Data
Active Power Dispatch Data
Generation Reserve Data
Generation Reactive Capability Data
Adjustable Branch Reactance Data
Piece-wise Linear Cost Data
Piece-wise Quadratic Cost Data
Polynomial and Exponential Cost Table
--- Page 149 ---
Optimal Power Flow Data Contents
 Change Code
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
140Period Reserve Constraint Data
Branch Flow Constraint Data
Interface Flow Constraint Data
Linear Constraint Dependency Data
Optimal Power Flow Raw Data File Structure
3.2. Change Code
The OPF data modification code indicates whether a new set of optimal power flow data records are to be
loaded into the working case, or whether the existing optimal power flow data is to be modified or appended
with updated information. This value is only used within the OPF Raw Data File.
Within the OPF Raw Data File, this record contains one data field entered as follows:
ICODE
ICODE ICODE = 0. All data within the OPF Raw Data File is treated as new data and entered
into the working case. Any optimal power flow data that may have previously existed
within the working case is erased prior to the reading of the rest of the data records
contained within the OPF Raw Data File.
ICODE = 1. All data within the OPF Raw Data File is to supersede values that current-
ly exist in the working case. Any data records introduced through the OPF Raw Data
File which do not correspond to an existing record within the working case, are auto-
matically appended to the data records already within the current working case. Data
records which do correspond to an entry within the working case are simply updated
to reflect the new values.
3.3. Bus Voltage Constraint Data
OPF Bus Voltage Constraint records define lower and upper voltage limits at each bus existing within the
PSSE power flow data model. Constraints may only be applied to existing buses; no new buses may be added
through the Bus Voltage Constraint record.
By default, all buses within the working case automatically have OPF Bus Voltage Constraint records defined.
The OPF Bus Voltage records of out of service (Type 4) buses can be modified but the bus and all bus asso-
ciated models (voltage constraints, bus shunts, loads, etc.) will not be utilized by the optimal power flow
solution process.
3.3.1. Bus Voltage Attribute Record
The format for each OPF Bus Voltage Attribute record is:
BUS, VNMAX, VNMIN, VEMAX, VEMIN, LTYP, SLPEN
--- Page 150 ---
Optimal Power Flow Data Contents
 Bus Voltage Attribute Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
141When entered in the OPF Raw Data File each field must be separated by either a space or a comma. Any
blank fields must be delineated by commas. A bus value of zero (0) indicates that no further bus voltage
constraint records are to be processed.
Each bus voltage constraint record is uniquely identified by a bus identifier. The values for each record is
defined as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
a bus already defined within the power flow working case.
VNMAX The maximum bus voltage magnitude value, entered in pu. The normal and emer-
gency OPF bus voltage limits are independent of the normal and emergency bus volt-
age limits in the main network bus data. The OPF bus voltage limits may be initialized
to those of the network bus voltage limits through either the OPF bus and bus subsys-
tem spreadsheets, or through the OPF bus API commands.
VNMAX = 9999.0 by default
VNMIN The minimum bus voltage magnitude value, entered in pu.
VNMIN = -9999.0 by default
VEMAX The maximum emergency bus voltage magnitude value, entered in pu. To enforce
recognition of the minimum and maximum emergency voltage limits during the OPF
solution, select the Impose emergency bus voltage limits  solution option. Otherwise
the normal voltage limits, as entered above, will be utilized. Refer to Impose Emer-
gency Bus Voltage Limits for more information.
VEMAX = 9999.0 by default
VEMIN The minimum emergency bus voltage magnitude value, entered in pu.
VEMIN = -9999.0 by default
LTYP One of four limit types may be enforced during the OPF solution:
•Reporting only (0),Only report on violations of the bus voltage limits, taking no
action if the voltage falls outside of limits.
•Hard limit (1). Strictly enforce the specified bus voltage magnitude limits through
the use of barrier terms.
•Soft limit with a linear penalty (2). Permit bus voltages to go outside of their speci-
fied voltage magnitude limits, but penalize excursions linearly. The Soft limit penal-
ty weight, SLPEN , is used in conjunction with this penalty to indicate severity of
excursion.
•Soft limit with a quadratic penalty (3). Permit bus voltages to go outside of their
specified voltage limits, but penalize excursions along a quadratic curve. The Soft
limit penalty weight, SLPEN , is used in conjunction with this penalty to indicate
severity of excursion.
Refer to Section 14.7.2 Accommodating Inequality Constraints for more information
on the limit type options.
LTYP = 1 (Hard limit) by default
--- Page 151 ---
Optimal Power Flow Data Contents
 Adjustable Bus Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
142SLPEN The soft limit penalty weight value applied to either the linear or quadratic soft limit
penalty functions. The larger the number, the higher the penalty for voltage excursions
outside of limits.
SLPEN = 1.0 by default
3.4. Adjustable Bus Shunt Data
Adjustable Bus Shunt Records define candidate bus locations for shunt compensation. These records are
unique to the PSSE OPF but do impact bus shunts within the power flow data model after an OPF solution.
If a corresponding bus and bus shunt identifier is found, then the BINIT value will be updated with the new
OPF solution value; otherwise a new bus shunt will be added to the power flow network data. The switched
shunt data records defined within the PSSE power flow data model are not affected by the OPF Adjustable
Bus Shunt data.
The maximum and minimum var limits specified in the Adjustable Bus Shunt records are used in conjunction
with the Minimize Adjustable Bus Shunts objective function. Details of the adjustable bus shunt model can
be found in Section 14.6.2 Adjustable Bus Shunt.
An individual bus may have one or more adjustable bus shunts defined, each differentiated by a unique bus
shunt identifier.
The format for each Adjustable Bus Shunt record is:
BUS, ID, BINIT, BMAX, BMIN, BCOST, STATUS, CTYP, CTBL
When entering records in the OPF Raw Data File, each field must be separated by either a space or a com-
ma and any fields left blank must be delineated by commas. A bus value of zero indicates that no further
Adjustable Bus Shunt records are to be processed.
The bus number and shunt identifier uniquely identifies each Adjustable Bus Shunt record. The values for
each record are described as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
an existing bus within the power flow working case.
ID A one or two character identifier that uniquely identifies the bus shunt at the bus. If
this field is left blank, the bus shunt identifier will default to a value of '1'.
The bus number and bus shunt identifier may optionally correspond to a fixed shunt
record within the power flow network. If so, then the corresponding fixed shunt data
record will be updated after an OPF solution.
BINIT The initial additional shunt value, entered in Mvar at nominal voltage.
BINIT = 0.0 by default.
BMAX The maximum bus shunt limit, entered in Mvar. To define an initial fixed shunt com-
ponent, deployed at no cost, enter the desired value into the main power flow model
as a fixed bus shunt with the same bus number and shunt identifier.
BMAX = 0.0 by default.
BMIN The minimum bus shunt limit, entered in Mvar. Negative or positive shunt values may
be entered for the maximum and minimum bus shunt susceptance to indicate induc-
tors or capacitors respectively.
--- Page 152 ---
Optimal Power Flow Data Contents
 Bus Load Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
143BMIN = 0.0 by default.
BCOST The cost coefficient, entered in cost units per Mvar. This coefficient assigns a cost value
to each Mvar employed during the solution process.
For example, one application for the cost scale coefficient is to assign a relatively low
cost to an Adjustable Bus Shunt record representing an existing var installation, and a
high cost to an Adjustable Bus Shunt record representing a potentially new installation.
This higher cost may take into consideration the additional costs associated with the
purchase of new equipment and the labor required for installation. This setup ensures
that vars from the existing installation will likely be employed during solution before
any new vars are applied.
BCOST = 1.0 by default.
STATUS The status switch determines whether the specified bus shunt control should be con-
sidered active or not. Only in-service bus shunts are recognized as candidates for var
control.
•In-service (1)
•Out of service (0)
STATUS = 1 (In-service) by default.
CTYP Cost curve type. This value is not currently utilized by the program.
CTBL Cost curve table number. This value is not currently utilized by the program.
3.5. Bus Load Data
Each OPF Bus Load Data record points to an Adjustable Bus Load table ( Section 3.6 Adjustable Bus Load Table
Data ) that, in turn, defines load limits for use in load adjustment studies (i.e., load shedding, power transfer).
These records are used in conjunction with the Minimize Adjustable Bus Loads objective function.
By default, all bus loads within the working case are initialized with default OPF Bus Load data. When a new
bus load is added to the power flow network, a corresponding OPF Bus Load data record will automatically
be created with default values. These data values may be updated. Bus loads connected to buses that are out
of service can have their OPF Bus Load Data modified, but the load will not be acknowledged by the optimal
power flow solution process.
3.5.1.  Bus Load Record
The format for each OPF Bus Load record is as follows:
BUS, LOADID, LOADTBL
Within the OPF Raw Data File each field must be separated by either a space or a comma. A bus value of zero
indicates that no further adjustable bus load records are being entered.
The bus number and load identifier uniquely identify each adjustable bus load record. The values for the
record are described as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
an existing bus within the power flow working case.
--- Page 153 ---
Optimal Power Flow Data Contents
 Adjustable Bus Load Table Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
144LOADID A one or two character load identifier that uniquely identifies the load at the bus. If
left blank, a default bus load identifier of '1' is assumed.
LOADTBL The adjustable bus load table reference number, as presented in Section 3.6 Adjustable
Bus Load Table Data .
An adjustable bus load table number of zero indicates that the corresponding bus load
is not being utilized within any OPF Adjustable Bus Load models.
Multiple OPF bus load records may reference the same adjustable bus load table num-
ber.
3.6. Adjustable Bus Load Table Data
Adjustable Bus Load Table records define load scaling limits for use in load adjustment studies (load shedding,
power transfer). They are referenced by the OPF Bus Load records defined in Section 3.5 Bus Load Data  and
are used in conjunction with the Minimize Adjustable Bus Loads objective. Details of the load adjustment
model are covered in Section 14.6.3 Load Adjustment.
An Adjustable Bus Load Table must be defined before it can be referenced by an OPF Bus Load record. Not
all Adjustable Bus Load Tables however have to be referenced by an adjustable bus load record. Those tables
which are defined but not referenced are ignored during the OPF solution process. There may be up to 1000
Adjustable Bus Load Table records defined within the working case.
3.6.1.  Adjustable Bus Load Table Record
The format of each Adjustable Bus Load Table record is:
TBL, LM, LMMAX, LMMIN, LR, LRMAX, LRMIN, LDCOST, CTYP, STATUS, CTBL
When entering data in the OPF Raw Data File each field must be separated by either a space or a comma.
Any fields left blank must be delineated with commas. A load table value of zero indicates that no further
adjustable bus load table records are to be processed.
The adjustable bus load table number uniquely identifies each adjustable bus load table record. The values
for the record are defined as follows:
TBL Adjustable bus load table number is an integer number. A value less than four digits
in length is most suitable for reporting purposes.
LM Load multiplier. The initial load adjustment variable, as indicated by Yi in the load
adjustment model of Section 14.6.3 Load Adjustment.
LM = 1.0 by default
LMMAX The maximum load adjustment multiplier, used to establish an upper limit for the load
multiplier Y.
To represent a load shedding model, YMAX  should be between 0.0 and 1.0 and larger
than YMIN . For a load addition model, YMAX  should be greater than 1.0.
LMMAX = 1.0 by default
LMMIN The minimum load adjustment multiplier, used to establish a lower limit for the load
multiplier Y. This value should be less than or equal to the value defined for the max-
imum load multiplier.
--- Page 154 ---
Optimal Power Flow Data Contents
 Generator Dispatch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
145LMMIN = 1.0 by default
LR Load ratio multiplier. This value is not presently utilized by the program.
LRMAX Maximum load ratio multiplier. This value is not presently utilized by the program.
LRMIN Minimum load ratio multiplier. This value is not presently utilized by the program.
LDCOST Cost scale coefficient. The cost, in $/pu MW, assigned to each OPF bus load participat-
ing in this adjustable bus load group.
LDCOST = 1.0 by default
CTYP Cost curve type. This value is not presently utilized by the program.
STATUS The status switch determines whether the specified Adjustable Bus Load Table should
be considered active or not. Only in-service Adjustable Bus Load Tables and their as-
sociated OPF Bus Loads will be recognized as adjustable bus load candidates.
•In-service (1)
•Out of service (0)
STATUS = 1 (In-service) by default
CTBL Cost table cross-reference number. This value is not presently utilized by the program.
3.7. Generator Dispatch Data
Generator Dispatch Data records reference Active Power Dispatch Tables ( Section 3.8 Active Power Dispatch
Data ) which, in turn, reference Cost Curves ( Sections 3.12  to 3.14 ). These relationships, in conjunction with
the Minimize Fuel Cost objective, introduce active power controls for generator dispatch studies.
All or a portion of the generating unit’s capacity may be made available for dispatch. The active power dis-
patch model, including the minimum and maximum active power limits, is defined within the active power
dispatch table record, described in Section 3.8 Active Power Dispatch Data .
By default, all machines within the working case that do not already have generator dispatch data defined,
are initialized with default data. When a new generator is added to the power flow network, a corresponding
OPF Generator Dispatch record is automatically created with default values.
3.7.1.  Generator Dispatch Data Record
The format for each OPF Generator Dispatch data record is:
BUS, GENID, DISP, DSPTBL
When entering data in the OPF Raw Data File each field must be separated by either a space or a comma.
A bus value of zero indicates that no further generator dispatch records are being entered. Any blank fields
must be delineated by commas.
The bus number and machine identifier uniquely identifies each Generator Dispatch record. The values for
each Generator Dispatch data record are described as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
a bus already defined within the power flow working case.
GENID A one or two character machine identifier that uniquely identifies the machine at the
bus. If left blank, a default machine identifier of '1' is assumed.
--- Page 155 ---
Optimal Power Flow Data Contents
 Active Power Dispatch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
146DISP The fractional dispatch value of the machine's total active power output available for
participation in the active power dispatch control.
A value of 1.0 indicates that 100% of the current active power output at the machine
will be employed in the associated active power control.The sum of the dispatch frac-
tions for all of the generator dispatch records that reference the same active power
dispatch table should add up to 1.0 for typical applications.
DISP = 1.0 by default
DSPTBL The table number of the active power dispatch control table. Multiple generator dis-
patch records may reference the same active power dispatch table. An active power
dispatch table number of zero implies that the generator is not participating as an
active power control.
DSPTBL = 1 by default
3.8. Active Power Dispatch Data
Active Power Dispatch Table records define the maximum and minimum active power dispatch limits.
Each Active Power Dispatch Table references a Cost Curve ( Sections 3.12 - 3.14 ) that specifies the costs as-
sociated with dispatching generation between the defined active power limits. Active Power Dispatch Table
records in turn are referenced by Generator Dispatch records ( Section 3.7 Generator Dispatch Data ). The
combination of these data records are used in conjunction with the Minimize Fuel Cost objective to perform
dispatch studies.
3.8.1. Active Power Dispatch Record
The format for each Active Power Dispatch Table data record is:
TBL, PMAX, PMIN, FUELCOST, CTYP, STATUS, CTBL
When entering data in the OPF Raw Data File each field must be separated by either a space or a comma.
Any fields left blank must be delineated with commas. A table value of zero indicates that no further active
power dispatch table records are to be processed.
Each active power dispatch table is uniquely identified by a numerical identifier. The values of each record
are defined as follows:
TBL Active power dispatch table number is an integer number. A number less than four
digits is most suitable for reporting purposes.
PMAX The upper limit on the total amount of active power available for dispatch, specified
in MW.
PMAX = 9999.0 by default
PMIN The lower limit on the total amount of active power available for dispatch, specified
in MW.
PMIN = -9999.0 by default
FUELCOST Fuel cost scale coefficient. A value chosen such that when the product between this
value and the associated cost curve coordinate value produces a result that has cost
units of (cost units)/hour.
--- Page 156 ---
Optimal Power Flow Data Contents
 Generation Reserve Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
147As an example, if the cost curve table coordinate value has units of MBTU/hour, then
the fuel cost scale coefficient should be entered with units of (cost units)/MBTU.
FUELCOST = 1.0 by default
CTYP One of three cost curve models may be specified to represent the fuel dispatch curves
of the generator units.
1. Polynomial and exponential curve
2. Piece-wise linear curve
3. Piece-wise quadratic curve
CTYP = 1 (Polynomial and exponential curve) by default
STATUS The status switch indicates whether the active power dispatch record is an active con-
trol within the OPF problem statement or not. Only in-service active power dispatch
tables and their associated generators will be recognized as active power dispatch
candidates.
•1 - In-service
•0 - Out of service
STATUS = 1 (In-service) by default
CTBL The table number of the cost curve to employ. Multiple active power dispatch table
records may reference the same cost curve. A cost curve table number of zero indicates
that the active power dispatch record, along with its participating generators will not
be utilized within the OPF solution.
CTBL = 0 by default
3.9. Generation Reserve Data
Generation Reserve records define a generating unit’s MW output capability and ramp rate. These records
are used in conjunction with the Period Reserve Constraint records ( Section 3.15 Period Reserve Constraint
Data ) to introduce MW reserve constraints into the optimal power flow problem.
Generation Reserve records may be utilized by one or more generation Period Reserve Constraint records.
The period reserve constraint model, as described in Section 14.6.6 Generator Period Reserve, provides a
means of imposing a specified MW reserve within a certain time limit (i.e., 200 MW in 10 minutes) by the
participating generator reserve units.
3.9.1.  Generation Reserve Record
The format for each Generation Reserve record is:
BUS, GENID, RAMP, RTMWMAX
When entering records into the OPF Raw Data File, each field must be separated by either a space or a comma.
A bus number of zero indicates that no further generator reserve records are being entered. Any blank fields
must be delineated by commas.
--- Page 157 ---
Optimal Power Flow Data Contents
 Generation Reactive Capability Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
148The bus number and machine identifier uniquely identifies each Generation Reserve record. The values for
each record are defined as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
a bus defined within the power flow working case.
GENID A one or two character machine identifier that uniquely identifies the machine at the
bus. If left blank, a default machine identifier of '1' is assumed.
RAMP Unit ramp rate. The rate at which it takes the generator to reach its maximum MW
capability, specified in MW / minute.
RAMP = 9999.0 by default
RTMWMAX The maximum unit reserve contribution, specified in MW
RTMWMAX = 9999.0 by default
3.10. Generation Reactive Capability Data
Generation Reactive Capability records define the limits in the armature reaction and stator current magni-
tude.
Whereas the conventional generator model provides for constant reactive generation limits, the reactive ca-
pability model represents generator armature reaction (Efd) behind synchronous reactance (Xd). With limits
applied to armature reaction magnitude and stator current magnitude, the reactive power capability of the
unit is recognized in a manner which is independent of any assumptions in terminal voltage magnitude or
active power generation. This is further discussed in Section 14.6.5 Generator Reactive Capability.
3.10.1.  Generation Reactive Capability Record
The format for each Reactive Capability record is:
BUS, GENID, XD, ISMAX, PFLAG, PFLEAD, QLIMIT, STATUS
When entering records in the OPF Raw Data File each field must be separated by either a space or a comma
and any fields left blank must be delineated with commas. A bus number of zero indicates that no further
generator reactive capability records are being entered.
A bus number and machine identifier is used to uniquely identify each Generation Reactive Capability record.
The values for each record are defined as follows:
BUS A bus number between 1 and 999997. The specified bus number must correspond to
a bus already defined within the power flow working case.
GENID The one or two character machine identifier of a valid machine within the working
case. If this field is left blank, a default machine identifier of '1' will be assumed.
XD The direct axis synchronous reactance of the machine, entered in pu on machine base.
XD = 1.0 by default
ISMAX The generator stator current limit, entered in pu on machine base.
ISMAX = 1.0 by default
--- Page 158 ---
Optimal Power Flow Data Contents
 Adjustable Branch Reactance Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
149PFLAG Real value generator rated lagging power factor.
PFLAG = 1.0 by default
PFLEAD Real value generator rated leading power factor.
PFLEAD = 1.0 by default
QLIMIT The maximum reactive absorption limit at zero power factor, entered in pu on machine
base.
QLIMIT = 1.0 by default
STATUS Reactive Capability Limit Status:
•0 - Out-of-service. The program will employ reactive generation limits directly from
the power flow data.
•1 - Enabled. The generator is fully enabled with no reactive generation limits.
•2 - Enabled with +DEfd inhibited. The generator is in service and any increase in the
field voltage is inhibited.
•3 - Enabled with -DEfd inhibited. The generator is in service and any decrease in the
field voltage is inhibited.
•4 - Enabled with Efd fixed. The generator is in service with an invariant field voltage.
The limit status determines how the specified reactive capability record should be em-
ployed in the optimal power flow problem.
STATUS = 4 (Enabled with Efd fixed) by default
3.11. Adjustable Branch Reactance Data
Adjustable Branch Reactance records define the reactive compensation limits and associated costs of adding
series var compensation. The data records are used in conjunction with the Minimize Adjustable Branch Re-
actances objective to identify candidate branches for use in series var compensation studies. A full descrip-
tion of the adjustable branch reactance model is presented in Section 14.6.4 Adjustable Branch Reactance.
3.11.1.  Adjustable Branch Reactance Record
The format for each Adjustable Branch Reactance record is:
IBUS, JBUS, CKT, XMLT, XMLTMAX, XMLTMIN, XCOST, CTYP, STATUS, CTBL
Each field must be separated by either a space or a comma and any fields left blank must be delineated by
commas. An IBUS number of zero indicates that no further adjustable branch reactance records are being
entered.
The from bus, to bus and circuit identifier uniquely identify each Adjustable Branch Reactance record. The
values for each record are defined as follows:
IBUS The sending bus, specified by a number between 1 and 999997. The number must
correspond to a bus already contained within the power flow working case.
--- Page 159 ---
Optimal Power Flow Data Contents
 Piece-wise Linear Cost Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
150JBUS The receiving bus, specified by a number between 1 and 999997. The number must
correspond to a bus already contained within the power flow working case.
CKT The one or two character branch identifier of an existing branch between the from
bus and the to bus. If this field is left blank a default circuit identifier of '1' is assumed.
XMLT The multiplier applied to the current reactance of the branch to yield the initial series
compensation value. A value of 1.0 implies that the initial reactance will be the current
reactance of the branch as obtained from the working case.
XMLT = 1.0 by default
XMLTMAX The maximum multiplier value applied to the reactance of the branch. The calculated
value determines the upper limit on the amount of available branch reactance com-
pensation. It is specified as a fraction of the branch reactance. Values over 1.0 are al-
lowed for situations where a potential increase in reactance is desired.
XMLTMAX = 1.0 by default
XMLTMIN The minimum multiplier value applied to the reactance of the branch. The calculated
value determines the lower limit on the amount of available branch reactance com-
pensation. It is specified as a fraction of the branch reactance.
For example, if the minimum reactance multiplier is specified as 0.3 and the maximum
reactance multiplier is specified as 1.0 then 70% of the branch reactance is available
as compensation.
The minimum value cannot be less than 0.1 to ensure that compensation does not
exceed 90% of the branch impedance.
XMLTMIN = 1.0 by default
XCOST The adjustable branch reactance cost in cost units / pu ohms.
XCOST = 1.0 by default
CTYP This value is not presently utilized by the program
STATUS •1 - In-service
•0 - Out-of-service
The status determines whether the specified Adjustable Branch Reactance record
should be considered active or not. Only in-service Adjustable Branch Reactance
records are recognized as candidates for series var adjustment.
STATUS = 1 (In-service) by default
CTBL This value is not presently utilized by the program
3.12. Piece-wise Linear Cost Data
The Cost Curve data record provides essential information on the fuel cost characteristics of each participat-
ing generator unit. It is used specifically in conjunction with the Minimize Fuel Cost objective and the Active
Power Dispatch tables (  Section 3.8 Active Power Dispatch Data ) for generator dispatch analysis.
The Piece-wise Linear cost model defines a linear relation between a cost, in cost units (i.e., dollars, pounds,
etc.), and a particular control variable value. For example, an active power dispatch model may reference a
--- Page 160 ---
Optimal Power Flow Data Contents
 Piece-wise Linear Cost Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
151piece-wise linear cost curve in order to obtain the relative fuel cost for dispatching a participating generator
unit at a certain active power dispatch level.
3.12.1.  Piece-wise Linear Cost Record
The format for each Piece-wise Linear Cost Table data record is a multi-line record as follows:
LTBL, LABEL, NPAIRS, X 1, Y1...X N, YN
Each field must be separated by either a space or a comma and any fields left blank must be delineated by
commas.
The total number of pairs entered must equal the value specified for NPAIRS.
An LTBL number of zero indicates that no further piece-wise linear cost table records are to be processed.
Each Piece-wise Linear Cost Curve Table record is uniquely identified by a linear cost table number. The values
for each record are defined as follows:
LTBL The piece-wise linear cost table number is an integer number. A number less than four
digits in length is most suitable for reporting purposes.
Note that the same cost table number may be used for multiple cost curve tables,
provided that each table represents a different cost curve type (i.e., quadratic or poly-
nomial).
LABEL A descriptive label of the piece-wise linear cost table, containing at most, 12 charac-
ters. This label is strictly used for reporting purposes.
LABEL = '' by default
NPAIRS The number of cost pairs. The total number of xi, yi coordinate pairs being entered.
This value is only used when entering raw data records in the OPF Raw Data File format
or when using PSSE Automation commands.
NPAIRS = 0 by default
Coordinate Pairs The individual coordinate pairs. Each pair (X 1, Y1 through X N, YN) defines one segment
of the piece-wise linear cost curve.
•X1... X N The control variable value. In the typical situation where the cost curve
is representing fuel cost characteristics, this value would define the active power
generation, in MW.
•Y1... Y N The total cost or energy consumption. For the fuel cost model, this value
would typically be entered in cost units / hour.
The Piece-wise Linear Cost Table displays all Piece-wise Linear Cost Tables in the working case. The subsystem
filter has no effect on the list displayed. If there are no Piece-wise Linear Cost Tables in the working case,
the Tables list will be blank.
3.13. Piece-wise Quadratic Cost Data
The Cost Curve data record provides essential information on the fuel cost characteristics of each participat-
ing generator unit. It is used specifically in conjunction with the Minimize Fuel Cost objective and the Active
Power Dispatch tables (  Section 3.8 Active Power Dispatch Data ).
--- Page 161 ---
Optimal Power Flow Data Contents
 Piece-wise Quadratic Cost Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
152The Piece-wise Quadratic Cost Curve model presents the cost, in cost units (i.e., dollars, pounds, etc.), as a
quadratic function of a control variable value. For example, an active power dispatch model may reference
a piece-wise quadratic cost curve to obtain the relative fuel costs for dispatching a participating generator
unit at a certain active power dispatch level.
3.13.1.  Piece-wise Quadratic Cost Record
The format for each Piece-wise Quadratic Cost Table data record is a multi-line record as follows:
QTBL, LABEL, COST, NPAIRS, X 1, Y1... X N, YN
Each field of the data record must be separated by either a space or a comma, with any blank fields being
delineated by commas.
The total number of pairs entered must equal the value specified for NPAIRS.
A QTBL number of zero indicates that no further Piece-wise Quadratic Cost Table records are to be entered.
Each Piece-wise Quadratic Cost Curve Table is uniquely identified by a quadratic cost table number. The data
values for each record are defined as follows:
QTBL The piece-wise quadratic cost table number is an integer number. A number less than
four digits in length is most suitable for reporting purposes.
Note that the same cost table number may be used for multiple cost curve tables,
provided that each table represents a different cost curve type (i.e., quadratic or poly-
nomial).
LABEL A descriptive label of the piece-wise linear cost table, containing at most, 12 charac-
ters. This label is strictly used for reporting purposes.
LABEL = '' by default
COST The cost or energy integration constant used to calculate the total fuel cost.
When this value is used in conjunction with the active power dispatch table, it should
be defined in units which, when its product is taken with the fuel cost scale coefficient
defined in the active power dispatch table record, the resultant units are cost units /
hour. For example, if the fuel cost scale coefficient in the active power dispatch table
has units of $/MBTU, then the integration constant should be specified in units of
MBTU/hour.
COST = 0.0 by default
NPAIRS The number of cost pairs. The total number of xi, yi coordinate pairs being entered.
This value is only used when entering raw data records in the OPF Raw Data File format
or when using PSSE Automation commands.
NPAIRS = 0 by default
Coordinate Pairs The individual coordinate pairs. Each pair (X 1, Y1 through X N, YN) defines one segment
of the piece-wise quadratic cost curve.
•X1... X N The control variable value. In the typical situation where the cost curve
is representing fuel cost characteristics, this value would define the active power
generation, in MW.
--- Page 162 ---
Optimal Power Flow Data Contents
 Polynomial and Exponential Cost Table
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
153•Y1... Y N The incremental cost or energy consumption. For the fuel cost model, this
value would typically be entered in cost units / MW.
The Piece-wise Quadratic Cost Table editor displays in the editor all Piece-wise Quadratic Cost Curve Tables
in the working case. The subsystem filter has no effect on the list displayed. If no Piece-wise Quadratic Cost
Curve Tables exist in the working case, the editor will be blank.
3.14. Polynomial and Exponential Cost Table
The Cost Curve data records provide essential information on the fuel cost characteristics of each participat-
ing generator unit. It is used specifically in conjunction with the Minimize Fuel Cost objective and the Active
Power Dispatch tables (  Section 3.8 Active Power Dispatch Data ) for generator dispatch analysis.
The Polynomial and Exponential Cost Curve model describes the cost, in cost units (i.e., dollars, pounds,
etc.), as a polynomial equation in terms of a control variable value. Similar to the linear and quadratic cost
curve models, an active power dispatch model may reference a polynomial cost curve in order to obtain
the relative fuel cost to dispatch a participating generator unit at a certain active power dispatch level. The
following equation is employed:
Polynomial Cost Equation
3.14.1.  Polynomial and Exponential Record
The format for each Polynomial and Exponential Cost Curve Table record is:
PTBL, LABEL, COST, COSTLIN, COSTQUAD, COSTEXP, EXPN
Each field of the data record must be separated by either a space or a comma with blank fields delineated
by commas. A PTBL number of zero indicates that no further polynomial and exponential cost table records
are being entered.
--- Page 163 ---
Optimal Power Flow Data Contents
 Period Reserve Constraint Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
154Each Polynomial and Exponential Cost Curve Table record is uniquely identified by a polynomial and expo-
nential cost table number.
PTBL The plynomial and exponential cost table number is an integer number. A number less
than four digits in length is most suitable for reporting purposes.
Note that the same cost table number may be used for multiple cost curve tables, pro-
vided that each table represents a different cost curve type (i.e., linear or quadratic).
LABEL A descriptive label of the piece-wise linear cost table, containing at most, 12 charac-
ters. This label is strictly used for reporting purposes.
LABEL = '' by default
COST The cost or energy integration constant used to calculate the total fuel cost.
COST = 0.0 by default
COSTLIN The linear cost coefficient as indicated by A in the equation given in Figure 3-2 .
COSTLIN = 0.0 by default
COSTQUAD The quadratic cost coefficient as indicated by B in the equation given in Figure 3-2 .
COSTQUAD = 0.0 by default
COSTEXP The exponential cost coefficient as indicated by C in the equation given in Figure 3-2 .
COSTEXP = 0.0 by default
EXPN The scale factor value which may be applied to the exponent of the exponential term
as indicated by D in the equation given in Figure 3-2 .
EXPN = 0.0 by default
The values for the integration constant and each of the coefficients should be specified in units that will allow
them to be multiplied by a cost scale value. When the polynomial and exponential table is used in conjunction
with the active power dispatch table, the coefficients and integration constant should be defined in units
which, when a product is taken with the fuel cost scale coefficient defined in the active power dispatch table
record, the resulting value is in units of cost units / hour. For example, if the fuel cost scale coefficient in
the active power dispatch table has units of $/MBTU, then the integration constant should be specified in
units of MBTU / hour.
The Polynomial and Exponential Cost Table displays in the editor all Polynomial and Exponential Cost Curve
Tables that exist in the working case. The subsystem filter has no effect on the list displayed. If no Polynomial
and Exponential Cost Curve Tables exist in the working case, the editor will simply show a blank record.
3.15. Period Reserve Constraint Data
Period Reserve Constraint data records are used in conjunction with the Generation Reserve records (  Section
3.9 Generation Reserve Data ) to impose MW reserve limits.
The period reserve constraint model, as described in Section 14.6.6 Generator Period Reserve, defines a MW
reserve that must be met within a stated time limit (i.e., 200 MW in 10 minutes). Some or all of a group of
participating generator units may be deployed to meet this requirement.The maximum reserve contribution
in MWand the unit ramp rate in MW/minute are defined for each participating generator unit as part of
the generation reserve data presented in Section 3.9 Generation Reserve Data . The period reserve records
described here define the desired reserve limit and the time limit in which the reserve limit must be met.
--- Page 164 ---
Optimal Power Flow Data Contents
 Period Reserve Constraint Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1553.15.1.  Period Reserve Constraint Record
The format for each Period Reserve data record is a multi-line record as follows:
RSVID, MWLIMIT, T, STATUS
BUS, GENID
...
BUS, GENID
0
Each field of the data record must be separated by either a space or a comma and any fields left blank must
be delineated by commas. For each complete period reserve record entered, a single zero must be placed on
the line immediately following the last generator unit entered, or immediately after the main RSVID record
if no participating units are specified. A RSVID value of zero indicates that no further period reserve records
are being entered.
Each Period Reserve record is uniquely identified by a reserve identification number between one and fifteen.
The values for each record are defined as follows:
RSVID The Reserve identifier is a number between one and fifteen, inclusive.
MWLIMIT The reserve requirement, in MW.
If the sum of maximum reserves for all of the units participating in the Period Reserve
data record is less than the specified reserve limit, then the constraint cannot be sat-
isfied. The solution will terminate if this situation arises.
If the reserve limit is set to 0.0, the reserve constraint will not be employed as part of
the optimal power flow problem statement.
MWLIMIT = 0.0 by default
T The time constraint for which the reserve requirement must be fulfilled, in minutes.
T = 9999.0 by default
STATUS •1 - In-service
•0 - Out-of-service
The status switch indicates whether the specified period reserve record should be in-
cluded within the OPF problem statement. Only in-service period reserve records will
be included as a reserve constraint.
STATUS = 1 (In-service) by default
Participating Units A list of participating generator reserve units available to the period reserve constraint.
Each unit must already have a corresponding generator reserve data record defined.
Each participating unit is uniquely specified by the following identifiers:
•BUS - The bus number of the bus where the unit is located. When using the spread-
sheet, this value may need to be entered as a bus name, depending upon the input
mode currently in effect.
--- Page 165 ---
Optimal Power Flow Data Contents
 Branch Flow Constraint Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
156•GENID - The generator unit identifier of the participating generator. A default iden-
tifier of 1 is assumed if left blank.
The Period Reserve data editor displays all Period Reserve data records within the working case. The subsys-
tem filter has no effect on the list displayed. If no Period Reserve records exist in the working case, the editor
will be blank.
3.16. Branch Flow Constraint Data
Branch Flow Constraint records define upper and lower flow limits on selected non zero impedance branch-
es. Four different flow limits may be imposed: MW, MVar, MVA and Ampere. More than one branch flow
constraint type may be defined for the same branch.
3.16.1.  Branch Flow Constraint Record
The format of each Branch Flow Constraint record is:
IBUS, JBUS, CKT, BFID, FMAX, FMIN, EFMAX, EFMIN, FTYP, LTYP, LPEN, KBUS
Each field of the data record must be separated by either a space or a comma and any fields left blank must
be delineated by commas. An IBUS number of zero indicates that no further branch flow constraint records
are being entered.
The from bus, to bus, third bus (for three-winding transformers), circuit id and flow id uniquely identify each
Branch Flow Constraint record. The values for each record are defined as follows:
IBUS The sending bus, specified by a number from 1 through 999997. The number must
correspond to an existing bus within the power flow working case.
If a three-winding transformer is being specified, the from bus defines the winding for
which the flow constraint is being introduced.
JBUS The receiving bus, specified by a number from 1 through 999997. The number must
correspond to an existing bus within the power flow working case.
CKT A one or two character identifier used to differentiate between multiple connecting
lines between the from bus, to bus and third bus (if three-winding transformer). If this
field is left blank, a circuit identifier of '1' is assumed.
BFID A single character identifier to differentiate between multiple branch flow constraints
defined at the same branch. If this field is left blank, a flow identifier of '1' is assumed.
FMAX The maximum normal flow limit on the specified branch. Values are specified in phys-
ical units appropriate to the flow limit type being specified; Ampere constraints are
specified in MVA.
If the difference between the specified upper and lower branch flow limits is less than
0.0001 then the specified flow constraint is fixed  at the indicated limit.
This value, along with the minimum normal limit defined below, is used to define
one of two possible flow limits assigned to the branch. An alternate set of emergency
limits is defined below. By default, the normal flow limits are employed during the OPF
solution unless the Impose Emergency Branch Flow Limits solution option is selected.
FMAX = 0.0 by default
--- Page 166 ---
Optimal Power Flow Data Contents
 Branch Flow Constraint Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
157FMIN The minimum normal flow limit on the specified branch. Values are specified in phys-
ical units appropriate to the flow limit type being specified; Ampere constraints are
specified in MVA.
If the difference between the specified upper and lower branch flow limits is less than
0.0001 then specified flow constraint is treated as fixed  at the indicated limit.
FMIN = 0.0 by default
EFMAX The maximum emergency flow limit on the specified branch. This limit, in conjunction
with the minimum emergency limit, defines an optional alternate set of flow limits.
Values are specified in physical units appropriate to the flow limit type being specified;
Ampere constraints are specified in MVA.
To enforce recognition of the minimum and maximum emergency flow limits as op-
posed to the normal flow limits during the OPF solution, select the Impose Emergency
Branch Flow Limits solution option.
EFMAX = 0.0 by default
EFMIN The minimum emergency flow limit on the specified branch. This limit, in conjunction
with the maximum emergency limit, defines an optional alternate set of flow limits.
Values are specified in physical units appropriate to the flow limit type being specified;
Ampere constraints are specified in MVA.
If emergency limits are employed during the OPF solution and the difference between
the specified upper and lower emergency branch flow limits is less than 0.0001, then
the specified flow constraint is treated as fixed  at the indicated limit.
EFMIN = 0.0 by default
FTYP One of four different flow types specified for the constraint:
•1 - MW
•2 - MVar
•3 - MVA
•4 - Ampere (4)
FTYP is 4 (Ampere) by default
LTYP One of four constraint limit types enforced during the OPF solution:
•0 - Reporting only. Only report on violations of the specified branch flow limits,
taking no action if the branch flow falls outside of limits.
•1 - Hard limit. Strictly enforce the specified branch flow limits through the use of
barrier terms.
•2 - Soft limit with a linear penalty. Permit branch flows to go outside of their speci-
fied branch flow limits, but penalize excursions along a linear curve. The Soft limit
penalty weight , as defined below, is used in conjunction with this penalty to indi-
cate severity of excursion.
--- Page 167 ---
Optimal Power Flow Data Contents
 Interface Flow Constraint Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
158•3 - Soft limit with a quadratic penalty. Permit branch flow limit to go outside of
their specified flow limits, but penalize excursions along a quadratic curve. The Soft
limit penalty weight , as defined below, is used in conjunction with this penalty to
indicate severity of excursion.
Refer to Section 14.7.2 Accommodating Inequality Constraints for more information
on the limit type options.
LTYP is 1 (Hard limit) by default
LPEN The penalty weight value applied to either the linear or quadratic soft limit penalty
functions. The larger the number, the higher the penalty for branch flow excursions
outside of limits.
LPEN is 1.0 by default
KBUS The third bus of a three-winding transformer, specified by a number from 1 through
999997. The number must correspond to an existing bus within the power flow work-
ing case.
If a three-winding transformer is not being entered, this value is zero.
3.17. Interface Flow Constraint Data
Interface flow records introduce MW or MVar flow constraints across a defined interface. These limits are
only enforced when the Constrain Interface Flows option is enabled, otherwise they are ignored during the
OPF solution. In conjunction with both the interface flow constraint records and the directive to Constrain
Interface Flows, the Minimize Interface Flows objective may be employed to either minimize or maximize
flows across an interface.
An interface consists of a collection of branches that may include the tie lines between two areas, the flows
through a particular transmission corridor, or the collection of lines emanating from an area. Each interface
flow constraint record defines a set of branches included in the interface and the flow limits that are to
be imposed on that set during the optimization process. By default, the interface flow definitions are for
informational purposes only. They do not automatically introduce constraint equations or objective terms
in the optimization problem unless one or both of the corresponding Constrain Interface Flows or Minimize
Interface Flows options are enforced.
The format for each Interface Flow Constraint data record is a multi-line record as follows:
IFLWID, LABEL, FMAX, FMIN, FTYP, LTYP, LPEN
IBUS, JBUS, CKT, KBUS
IBUS, JBUS, CKT, KBUS
...
0
Each field of the data record must be separated by either a space or a comma, with any blank fields delineated
by commas. For each interface flow record entered, a single zero must be placed on the line immediately
following the last participating branch entered, or immediately after the main IFLWID record if no participat-
ing branches are specified.
--- Page 168 ---
Optimal Power Flow Data Contents
 Interface Flow Constraint Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
159An IFLWID value of zero indicates that no further interface flow records are to be processed.
Each Interface Flow Constraint record is uniquely identified by an interface flow identifier. The values for
each record are defined as follows:
IFLWID The interface flow identifier is an integer number. A value less than four digits in length
is most suitable for reporting purposes.
LABEL A string containing a maximum of 32 characters used to describe the interface. This
label is only used for reporting purposes.
LABEL is '' by default
FMAX The maximum flow limit across the interface, specified in the physical units appropri-
ate for the specified flow limit type defined below.
If the range between the maximum and minimum interface MW flow limits is less than
0.001, then the maximum interface flow limit is set to the average of the two interface
flow limit values plus 0.1.
FMAX is 0.0 by default
FMIN The minimum flow limit across the interface, specified in the physical units appropriate
for the specified flow limit type defined below.
If the range between the maximum and minimum interface MW flow limits is less than
0.001, then the minimum interface flow limit is set to the average of the two interface
flow limit values minus 0.1.
FMIN is 0.0 by default
FTYP One of two valid flow types:
•1 - MW
•2 - MVar
FTYP is 1 (MW) by default
LTYP One of four different limit types:
•0 - Reporting only. Only report on violations of the specified interface flow limits,
taking no action if the interface flow falls outside of limits.
•1 - Hard limit. Strictly enforce the specified interface flow limits through the use of
barrier terms.
•2 - Soft limit with a linear penalty. Permit interface flows to go outside of the speci-
fied interface flow limits, but penalize excursions along a linear curve. The Soft limit
penalty weight , as defined below, is used in conjunction with this penalty to indi-
cate severity of excursion.
•3 - Soft limit with a quadratic penalty. Permit interface flows to go outside of the
specified interface flow limits, but penalize excursions along a quadratic curve. The
Soft limit penalty weight , as defined below, is used in conjunction with this penalty
to indicate severity of excursion.
--- Page 169 ---
Optimal Power Flow Data Contents
 Linear Constraint Dependency Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
160Refer to Section 14.7.2 Accommodating Inequality Constraints for more information
on the limit type options.
LTYP is 1 (Hard limit) by default
LPEN The penalty weight value applied to either the linear or quadratic soft limit penalty
functions. The larger the number, the higher the penalty for interface flow excursions
outside of the defined interface flow limits.
LPEN is 1.0 by default
Participating Branch-
esA list of branches defining the interface. Each branch is individually specified by the
following identifiers:
•IBUS - From bus number. The sending bus number (1 through 999997). When using
the spreadsheet, this value may optionally be entered as a bus name, provided that
names  input mode is in effect.
•JBUS - To bus number. The receiving end bus number (1 through 999997). When
using the spreadsheet, this value may optionally be entered as a bus name, provided
that names  input mode is in effect.
•CKT - Circuit ID. The one or two character circuit identifier. If no circuit identifier is
entered, a default value of '1' is assumed.
•KBUS - Third bus number. The third bus number (1 through 999997) if a three wind-
ing transformer is specified; zero (0) otherwise. When using the spreadsheet, this
value may optionally be entered as a bus name, provided that names  input mode
is in effect.
The Interface Flow Constraint editor displays all Interface Flow Constraint records within the working case
in the data table. The subsystem filter has no effect on the list displayed. If there are no Interface Flow
Constraint records in the working case, then the editor window will be blank.
3.18. Linear Constraint Dependency Data
Linear Constraint Dependency records provide a way to introduce customized linear constraint equations into
the optimal power flow problem statement. Each dependency equation may be comprised of any number of
variables, selected from a group of previously defined records. Details of the linear constraint dependency
equation model are discussed in Section 14.6.8 Linear Constraint Dependency Equation.
3.18.1.  Linear Constraint Dependency Record
The format of each Linear Constraint Dependency data record is a multi-lined record as follows:
EQID, LABEL, SLKMAX, SLKMIN
VTYP, "ID FIELDS", COEFF
VTYP, "ID FIELDS", COEFF
...
0
--- Page 170 ---
Optimal Power Flow Data Contents
 Linear Constraint Dependency Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
161Each field of the data record must be separated by either a space or a comma, with any blank fields delineated
by commas. For each Linear Constraint Equation record entered, a single zero must be placed on the line
immediately following the last participating variable record entered, or immediately after the main EQID
record if no participating variables are specified.
An EQID value of zero indicates that no further linear constraint dependency records are to be processed.
Each Linear Constraint Dependency record is uniquely identified by a constraint equation identifier. The val-
ues for each field within the record are defined as follows:
EQID The linear constraint equation identifier is an integer number. A value less than four
digits in length is most suitable for reporting purposes.
LABEL A string containing a maximum of 32 characters used to describe the linear constraint
dependency equation being defined. This label is only used for reporting purposes.
LABEL is '' by default
SLKMAX The constraint equation maximum slack variable limit
SLKMAX is 0.0 by default.
SLKMIN The constraint equation minimum slack variable limit
SLKMIN is 0.0 by default.
VTYP A number (1 through 10) corresponding to the type of dependency variable being
added to the constraint equation. The numerical values associated with each variable
type are as follows:
1. voltage magnitude, in pu
2. voltage angle, in radians (degrees/57.29578)
3. active power generation, in per unit of reactive power based on system base(i.e.
400 MW limit base on a system base of 100 is entered as 4.0)
4. reactive power generation, in per unit of reactive power based on system base(i.e.
400 Mvar limit base on a system base of 100 is entered as 4.0)
5. transformer tap ratio, entered as the inverse of the tap ratio; or transformer phase
shift angle, in radians
6. branch flow, in per unit flow value based on system base
7. interface flow, in per unit flow value based on system base
8. adjustable bus shunt, in per unit Mvar value based on system base
9. switched shunt, in per unit Mvar value based on system base
10. load adjustment, entered in terms of the load multiplier (i.e. 0.8 for 80% of load
or 1.8 for 180% of load
"ID FIELDS" Depending upon the variable type code selected above, one or more identification
fields must be specified in order to uniquely identify the record to be employed as
the variable entry. The identification fields corresponding to each of the variable type
codes defined above, are as follows:
--- Page 171 ---
Optimal Power Flow Data Contents
 Linear Constraint Dependency Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1621. Bus number (1 through 999997)
2. Bus number (1 through 999997)
3. Active power dispatch table number
4. Bus number (1 through 999997)
Generator identifier [" 1"]
5. From bus number
To bus number
Circuit identifier [" 1"]
Third bus number, if a three-winding transformer is specified; placed after the
Coeff value [0]
6. From bus number
To bus number
Circuit identifier [" 1"]
Branch flow identifier ["1"]
Third bus number, if a three-winding transformer is specified; placed after the
Coeff value [0]
7. Interface flow identifier
8. Bus number (1 through 999997) Adjustable bus shunt identifier [" 1"]
9. Bus number (1 through 999997)
10. Adjustable bus load table number
Depending upon the variable type code selected above, one or more identification
fields must be specified in order to uniquely identify the record to be employed as
the variable entry. The identification fields corresponding to each of the variable type
codes defined above, are as follows:
When using the linear constraint equation table editor, bus identifiers may alternately
be entered as extended bus names instead of bus number, provided that the names
input mode option is in effect.
COEFF A real variable coefficient applied to the dependency variable specified above.
COEFF is 1.0 by default.
Any number of participating variables may be included in the linear constraint dependency equation. The
variable identifiers must correspond to a record that already exists within the working case.
--- Page 172 ---
Optimal Power Flow Data Contents
 Linear Constraint Dependency Record
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
163The Linear Constraint Dependency Equation data table displays in the editor all Linear Constraint Dependency
Equation records within the working case. The subsystem filter has no effect on the list displayed. If there
are no Linear Constraint Dependency Equation records in the working case, the editor window will be blank.
--- Page 173 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
164Chapter 4
GIC Data File Contents
4.1. Overview
The geomagnetic induced currents can flow through transformer winding and substation ground paths.
For power flow calculations and substation ground paths data is not required. So this data is not available
in power flow data. This additional data is provided in GIC data file (extension .gic). The accuracy of GIC
calculations will depend on the data provided in GIC data file.
Depending on the size of the power flow network studied, this data could be large. It is recommended to
create a GIC data template with one of the following ways and edit/modify that:
1. Using activity GIC GUI, GIC data file from Excel GIC data templates
•GIC > Excel template for GIC data file
•GIC > Create GIC data file from Excel template
2. Python module “gicdata” (See PSSE Application Program Interface (API) manual )
All data is read in free format with data items separated by a comma or one or more blanks. Each category
of data except the change code is terminated by a record specifying an I value of zero. Termination of all
data is indicated by a value of Q.
File Identification Data
Substation Data
Bus Substation Data
Transformer Data
Fixed Bus Shunt Data
Branch Data
User Earth Model Data
Switched Shunt Data
Two-Terminal DC Data
--- Page 174 ---
GIC Data File Contents
 File Identification Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
165VSC DC Data
Multi-Terminal DC Data
FACTS Device Data
Load Data
Q Record
GIC Data Input Structure
4.2. File Identification Data
This record contains only one data item which is specified as:
GICFILEVRSN=vernum
GICFILEVRSN GICFILEVRSN is the keyword to specify the GIC data file version number.
vernum is an integer value specifying GIC data file version number. No default allowed.
Allowed, GICFILEVRSN = 4
GICFILEVRSN = 5
Note: The first release of the GIC data file did not have this record and is treated as
file version 1
At PSSE version 35.0.0, network equipment location information is consolidated in one source which is base
case data. To faciliate that:
•The substation location information is moved from the GIC data file to the Base Case data (power flow
data).
•When GIC data file version 4 is read, "Single Bus" substation configuration is added to each bus-substation
data record and non Node-Breaker base case is converted to Node-Breaker base case.
•For this to work without any data conflicts, GIC data file version 4 is allowed to be read into base case that
has no Node-Breaker configurations.
4.3. Substation Data
Some text here.
Each substation data record has the following format:
GICFILEVRSN 4
I, NAME, UNIT, LATITUDE, LONGITUDE, RG, EARTHMDL, RGFLAG
--- Page 175 ---
GIC Data File Contents
 Substation Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
166GICFILEVRSN 5
I, RG, EARTHMDL, RGFLAG
I Substation number (1 through 999997)
No default allowed.
NAME Alphanumeric identifier assigned to substation I. NAME may be up to 40 characters
and may contain any combination of blanks, uppercase letters, numbers and special
characters, but the first character must not be a minus sign. NAME must be enclosed
in single or double quotes if it contains any blanks or special characters.
NAME = "" by default.
UNIT Unit for geophysical location (longitude and longitude) data
•0 - degrees
UNIT = 0 by default.
LATITUDE Substation latitude, positive for North and negative for South. When UNIT = 1, latitude
is specified in degrees.
No default allowed.
LONGITUDE Substation longitude, positive for East and negative for West. When UNIT = 1, longi-
tude is specified in degrees.
No default allowed.
RG Substation grounding DC resistance in ohms. If RG <= 0.0 or RG > = 99.0, it is assumed
that the substation is ungrounded.
RG = 0.1 by default.
EARTHMDL Name of the earth model. EARTHMDL may be up to 32 characters.
•When specified this earth model will be used in determining Benchmark GMD event
earth model scaling factor (beta) and Non-uniform GMD event calculations
•When not specified, the earth model specified in GIC API is used.
EARTHMDL = "" by default.
RGFLAG Method used to specify RG value. RGFLAG may be up to 40 characters. It is used for
informational purposes only.
Possible methods:
•"Assumed"
•"Measured"
•"Calculated"
•"Brief comment of your choice"
RGFLAG = "Assumed" by default.
--- Page 176 ---
GIC Data File Contents
 Bus Substation Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1674.4. Bus Substation Data
Each bus substation data record has the following format::
GICFILEVRSN 4
BUSNUM, SUBNUM
GICFILEVRSN 5
There is no bus substation data group.
BUSNUM Bus number. Bus BUSNUM must be present in the working case.
No default allowed.
SUBNUM Substation number (1 through 999997). This is the substation number to which bus
BUSNUM belongs to.
SUBNUM must be previously defined in “Substation Data” record group.
The following restrictions apply when assigning bus and its substation:
•Generally two buses connected by a transmission line (non-transformer branch) re-
side in two different substations. Exception to this would be short lines between two
buses of same substation. Those short branches are treated as zero length branches
with no GMD induced voltage in those.
•Two buses connected by a two winding transformer must be in same substation.
•Three buses connected by a three winding transformer must be in same substation.
•Two buses connected by zero impedance line must have same substation number.
No default allowed.
4.5. Transformer Data
The transformer specified by buses “I, J, K, CKT” must exist in the working case. Also the winding bus order
must be same as in the working case.
Note 1 : For two and three winding auto transformers WRI, WRJ and WRK could represent per phase dc
resistances of series winding (Rs) or common winding (Rc).
For example, as shown in figure below:
•Bus I is series winding bus, WRI=Rs
•Bus J is common winding bus, WRJ=Rc
--- Page 177 ---
GIC Data File Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
168
Note 2 : Figure below shows generic Phase Angle Regulator (PAR) connections where series unit has split
tap. It is represented as T model in GIC calculation DC network. The series unit is connected between Bus I
and Bus J. In GIC data file:
•WRI is dc resistance of series unit section connected to Bus I
•WRJ is dc resistance of series unit section connected to Bus J
•WRK is dc resistance of exciting unit
It is imperative that WRI, WRJ and WRK values are calculated and specified in GIC data file for correct modeling
of such a transformer.
Each transformer data record has the following format:
I, J, K, CKT, WRI, WRJ, WRK, GICBDI, GICBGJ, GICBDK, VECGRP, CORE, KFACTOR,
GRDWRI, GRDWRJ, GRDWRK, TMODEL
--- Page 178 ---
GIC Data File Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
169I The bus number of the bus to which Winding 1 is connected. It must be same Winding
1 bus for the same transformer in the working case.
No default allowed
J The bus number of the bus to which Winding 2 is connected. It must be same Winding
2 bus for the same transformer in the working case.
No default allowed
K The bus number of the bus to which Winding 3 is connected. It must be same Winding
3 bus for the same transformer in the working case.
K = 0 for two winding transformers. No default allowed for three winding transform-
ers.
CKT One- or two-character branch circuit identifier.
CKT = '1' by default
WRI DC resistance of Winding 1 in ohms/phase. When WRI is not specified, working case
resistance is used to determine WRI.
DC resistance of series unit of PAR (see Note 2).
WRI = 0.0 by default
WRJ DC resistance of Winding 2 in ohms/phase. When WRJ is not specified, working case
resistance is used to determine WRJ.
DC resistance of series unit of PAR (see Note 2).
WRJ = 0.0 by default
WRK DC resistance of Winding 3 in ohms/phase. For three-winding transformers, when WRK
is not specified, working case resistance is used to determine WRK.
DC resistance of exiciting unit of PAR (see Note 2).
WRK = 0.0 by default
GICBDI GIC blocking device in neutral of Winding 1
•0 - no GIC blocking device present
•1 - GIC blocking device present
For an auto-transformers, if either GICBDI = 1 or GICBDJ = 1, that auto-transformer is
treated as if it has GIC blocking device present.
GICBDI = 0 by default
GICBDJ GIC blocking device in neutral of Winding 2
•0 - no GIC blocking device present
•1 - GIC blocking device present
For an auto-transformers, if either GICBDI = 1 or GICBDJ = 1, that auto-transformer is
treated as if it has GIC blocking device present.
--- Page 179 ---
GIC Data File Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
170GICBDJ = 0 by default
GICBDK GIC blocking device in neutral of Winding 3
•0 - no GIC blocking device present
•1 - GIC blocking device present
GICBDK = 0 by default. GICBDK = 0 for two-winding transformers.
VECGRP Alphanumeric identifier specifying vector group based on transformer winding con-
nections and phase angles.
If vector group is specified in power flow data that data will be used and it is not needed
to be specified here. As far as GIC calculations are concerned, winding grounding con-
nection information is used; its clock angles are not used. Refer to POM Sections “Two
Winding Transformer Vector Groups” and “Three Winding Transformer Vector Groups”
for allowed vector groups.
•When bus number orders in GIC data file record are different than the bus number
orders in power flow RAW data file, the bus number orders in power flow RAW data
file is used to assign winding configuration as per vector group specified.
•For auto-transformers, bus with lower base bus voltage is treated as common wind-
ing bus.
•For three-winding auto-transformers, windings on bus I and bus J form auto-trans-
former.
VECGRP = "" by default
CORE Number of cores in transformer core design. This information is used to calculate trans-
former reactive power loss from GIC flowing its winding.
•-1 - three phase shell form
•0 - unknown core design
•1 - single phase core
•3 - three phase 3-legged core form
•5 - three phase 5-legged core form
CORE = 0 by default
KFACTOR Factor to calculate transformer reactive power loss from GIC flowing its winding
(MVAR/AMP)
KFACTOR = 0.0 by default
GRDWRI Winding 1 grounding DC resistance in ohms
GRDWRI = 0.0 by default (no grounding resistance; solidly grounded)
GRDWRJ Winding 2 grounding DC resistance in ohms
GRDWRJ = 0.0 by default (no grounding resistance; solidly grounded)
--- Page 180 ---
GIC Data File Contents
 Fixed Bus Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
171GRDWRK Winding 3 grounding DC resistance in ohms
DC resistance of exciting unit grounding of PAR (see Note 2).
GRDWRK = 0.0 by default (no grounding resistance; solidly grounded)
TMODEL Number of cores in transformer core design. This information is used to calculate trans-
former reactive power loss from GIC flowing its winding.
•0 - two/three/auto transformer model as defined by its vector group
•1 - transformer as T model in DC network (See Note 2)
TMODEL = 0 by default
4.6. Fixed Bus Shunt Data
Only in-service fixed bus shunts provided on this data record are modeled in the GIC DC network.
Each fixed bus shunt data record has the following format:
I, ID, R, RG
I Bus number of the bus to which the fixed shunt is connected. It must be present in
the working case.
No default allowed
ID One- or two-character fixed bus shunt identifier.
ID = '1' by default
R DC resistance in ohms/phase. It must be > 0.0. Fixed bus shunt records with R = 0.0
will be ignored.
No default allowed
RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
4.7. Branch Data
Only in-service branches are modeled in the GIC DC network.
Each user branch data record has the following format:
I, J, CKT, RBRN, INDVP, INDVQ, RLNSHI, RLNSHJ
I Branch from bus number
No default allowed
J Branch to bus number
No default allowed
CKT One- or two-character transformer circuit identifier; a transformer with circuit identi-
fier CKT between buses I and J must be in the working case.
--- Page 181 ---
GIC Data File Contents
 User Earth Model Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
172CKT = '1' by default
RBRN Branch DC resistance in ohms/phase. When RBRN is not specified or RBRN=0.0, power
flow data branch resistance in pu is converted to ohms/phase and used. There is no
temperature correction applied.
RBRN = 0.0 by default
INDVP Real part of total branch GMD induced electric field in volts. See note below on how
to specify this value.
INDVQ Imaginary part of total branch GMD induced electric field in volts. See note below on
how to specify this value.
Note 1 : Total branch GMD induced electric field, INDUCEDV = INDVP + j INDVQ volts
Branch INDUCEDV is determined as below:
•When INDUCEDV is not specified, GIC activity calculates this according to its options
specified
•When INDUCEDV is specified, it is used as GMD induced on that branch
•When INDUCEDV is specified as INDVP = 0.0 and INDVQ = 0.0, then that branch is
treated as part of the GIC DC network but does not have GMD induced voltage, like
“underground pipe-type cables (cables enclosed in the steel pipe).”
For uniform filed modeling INDUCEDV will be real value, but for non-uniform field
modeling this will be complex value.
This voltage will have positive polarity at branch To Bus (J bus).
Note 2 :
•When the Branch INDVP + j INDVQ is specified, it is used as is. There are no other
scaling factors applied to this voltage.
•When INDVP + j INDVQ is specified for all branches, specified GMD event option is
ignored during GIC calculations.
•When INDVP + j INDVQ is specified for few branches, the induced Efield for remain-
ing branches in study subsystem is calculated using the GMD event option specified.
RLNSHI DC resistance in ohms/phase of the line shunt at the bus I end of the branch. It must be
> 0. When RLNSHI = 0 or not specified, there will be no ground path for this line shunt.
No default allowed
RLNSHJ DC resistance in ohms/phase of the line shunt at the bus J end of the branch. It must be
> 0. When RLNSHJ = 0 or not specified, there will be no ground path for this line shunt.
No default allowed
4.8. User Earth Model Data
Activity GIC models US and Canada Earth Models (Ref…). However, if any other Earth Model is required, use
this data to define such an earth model. A total of up to 50 user earth models are allowed. Also each earth
--- Page 182 ---
GIC Data File Contents
 Switched Shunt Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
173model may have up to 25 layers. Use as many records needed to specify the data. The thickness of the last
layer is infinity. This is specified as any value less than 0.0 (= -999.0 for example). The thickness value less
than 0.0 is also used as end of earth model data.
Each user earth model data record has the following format:
NAME, BETAFTR, DESC, RESISTIVITY1, THICKNESS1, RESISTIVITY2, THICKNESS2, ...,
RESISTIVITY24, THICKNESS24, RESISTIVITY25, THICKNESS25
NAME The non-blank alphanumeric identifier assigned to this earth model. Each earth model
must have unique name. NAME may be up to 32 characters. This name should be
different than the Standard US and Canada Earth Models defined (Refer …).
No default allowed
BETAFTR Earth Model scaling factor used when calculating branch induced electric field for
Benchmark GMD event.
BETAFTR = 1 by default
DESC Description of the earth model. NAME may be up to 72 characters. This is used for
information purpose only
DESC = "" by default
RESISTIVITY 1 Layer 1 resistivity in ohm-m
No default allowed
THICKNESS 1 Layer 1 thickness in km
No default allowed
RESISTIVITY N Nth layer resistivity in ohm-m. Up to 25 layers are allowed
No default allowed
THICKNESS N Nth layer 1 thickness in km. Up to 25 layers are allowed. The thickness of the last layer
is infinity. This is specified as any value less than 0 (= -999.0 for example).
No default allowed
4.9. Switched Shunt Data
Only in-service switched shunts provided on this data record are modeled in the GIC DC network.
Each switched shunt data record has the following format:
GICFILEVRSN 4
I, R, RG
GICFILEVRSN 5
I, ID, R, RG
I Bus number; bus I must be present in the working case with positive sequence
switched shunt data.
--- Page 183 ---
GIC Data File Contents
 Two-Terminal DC Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
174R DC resistance in ohms/phase. It must be > 0.0. Switched shunt records with R = 0.0
will be ignored.
No default allowed
RG Grounding DC resistance in ohms. No grounding resistance; solidly grounded.
RG = 0.0 by default
4.10. Two-Terminal DC Data
The rectifier and inverter converter stations are connected to ac network through converter transformers.
If these converter transformers are not explicitly modeled in the power flow, then use this data record to
specify the GIC DC network data for them.
Provide DC resistance data of grounded windings of converter transformers.
The status (blocked or in-service) of two-terminal DC lines from power flow data is not considered. The DC
data provided on this data record is modeled in the GIC DC network. Up to 10 GIC DC network elements are
allowed per DC line.
Each two-terminal DC data record has the following format:
NAME, I, ID, R, RG
NAME The non-blank alphanumeric identifier assigned to this DC line. It must be present in
the working case. NAME may be up to 12 characters.
No default allowed
I Bus number of the rectifier (IPR) or inverter (IPI) converter AC bus. It must be present
in the working case.
No default allowed
ID One- or two-character non-blank alphanumeric identifier. There could be more than
one ground path at rectifier or inverter ac bus. This ID is used to be able to specify
more than one ground path. This is specific to GIC data and it does not exist in the
working case.
No default allowed
R DC resistance in ohms/phase of grounded winding of converter transformers. It must
be > 0.0. R = 0.0 or not specified means there will be no ground path.
No default allowed
RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
Two-terminal DC data input is terminated with a record specifying a bus number of zero.
--- Page 184 ---
GIC Data File Contents
 VSC DC Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
175
4.11. VSC DC Data
The rectifier and inverter converter stations are connected to ac network through converter transformers.
If these converter transformers are not explicitly modeled in the power flow, then use this data record to
specify the GIC DC network data for them.
Provide DC resistance data of grounded windings of converter transformers.
The status (blocked or in-service) of VSC DC lines from power flow data is not considered. The DC data
provided on this data record is modeled in the GIC DC network. Up to 10 GIC DC network elements are allowed
per DC line.
Each VSC DC data record has the following format:
NAME, I, ID, R, RG
NAME The non-blank alphanumeric identifier assigned to this VSC DC line. It must be present
in the working case. NAME may be up to 12 characters.
No default allowed
I Bus number of the rectifier (IPR) or inverter (IPI) converter AC bus. It must be present
in the working case.
No default allowed
ID One- or two-character non-blank alphanumeric identifier. There could be more than
one ground path at rectifier or inverter ac bus. This ID is used to be able to specify
more than one ground path. This is specific to the GIC data and it does not exist in
the working case.
No default allowed
R DC resistance in ohms/phase of grounded winding of converter transformers. It must
be > 0.0. R = 0.0 or not specified means there will be no ground path.
--- Page 185 ---
GIC Data File Contents
 Multi-Terminal DC Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
176No default allowed
RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
VSC DC data input is terminated with a record specifying a bus number of zero.
4.12. Multi-Terminal DC Data
The multi-terminal converters are connected to ac network through converter transformers. If these convert-
er transformers are not explicitly modeled in the working case, then use this data record to specify the GIC
DC network data for them.
Provide DC resistance data of grounded windings of converter transformers.
The status (blocked or in-service) of multi-terminal DC lines in the working case is not considered. The DC
data provided on this data record is modeled in the GIC DC network. Up to 10 GIC DC network elements are
allowed per DC line.
Each multi-terminal DC data record has the following format:
NAME, I, ID, R, RG
NAME The non-blank alphanumeric identifier assigned to this Multi-Terminal DC line. It must
be present in the working case. NAME may be up to 12 characters.
No default allowed
I Converter ac bus number (IB). It must be present in the working case.
No default allowed
ID One- or two-character non-blank alphanumeric identifier. There could be more than
one ground path at ac bus. This ID is used to be able to specify more than one ground
path. This is specific to the GIC data and it does not exist in the working case.
No default allowed
R DC resistance in ohms/phase of grounded winding of converter transformers. It must
be > 0.0. R = 0.0 or not specified means there will be no ground path.
No default allowed
RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
Multi-Terminal DC data input is terminated with a record specifying a bus number of zero.
4.13. FACTS Device Data
The FACTS device converters are connected to ac network through converter transformers. If these converter
transformers are not explicitly modeled in the working case, then use this data record to specify the GIC DC
network data for them.
--- Page 186 ---
GIC Data File Contents
 Load Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
177Provide DC resistance data of grounded windings of converter transformers.
The status (in-service or out-of-service) of FACTS device from power flow data is not considered. The DC
data provided on this data record is modeled in the GIC DC network. Up to 10 GIC DC network elements are
allowed per DC line.
Each multi-terminal DC data record has the following format:
NAME, I, ID, R, RG
NAME The non-blank alphanumeric identifier assigned to this FACTS device. It must be
present in the working case. NAME may be up to 12 characters.
No default allowed
I FACTS device sending end bus number (IBUS). It must be present in the working case.
No default allowed
ID One- or two-character non-blank alphanumeric identifier. There could be more than
one ground path at ac bus. This ID is used to be able to specify more than one ground
path. This is specific to the GIC data and it does not exist in the working case.
No default allowed
R DC resistance in ohms/phase of grounded winding of converter transformers. It must
be > 0.0. R = 0.0 or not specified means there will be no ground path.
No default allowed
RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
FACTS device data input is terminated with a record specifying a bus number of zero.
4.14. Load Data
Only in-service loads provided on this data record are modeled in the GIC DC network.
Each load data record has the following format:
I, ID, R, RG
I Bus number of the bus to which load is connected. It must be present in the working
case.
No default allowed
ID One- or two-character non-blank alphanumeric load identifier.
ID = '1' by default
R DC resistance in ohms/phase of grounded loads. It must be > 0.0. R = 0.0 or not spec-
ified means there will be no ground path.
No default allowed
--- Page 187 ---
GIC Data File Contents
 Load Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
178RG Grounding DC resistance in ohms
RG = 0.0 by default (no grounding resistance; solidly grounded)
Load data input is terminated with a record specifying a bus number of zero.
--- Page 188 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
179Chapter 5
Harmonics Data File Contents
5.1. Overview
To perform network frequency scan or run harmonic analayis following data is needed.
1. power flow data (.raw file)
2. sequence flow data (.seq file)
3. network elements frequency dependency data
4. harmonic sources data
These additional data items (3) and (4) are provided in Harmonics data text file (.har) or xml file (.xhar). The
accuracy of Harmonic analysis will depend on the quality this additional data provided.
This section describes the format of .har file. All data in .har file is read in free format with data items sepa-
rated by a comma or one or more blanks. Each category of data except the File Identification is terminated
by a record specifying a value of zero as first value on that data record. Termination of all data is indicated
by a value of Q.
File Identification Data
Impedance Characteristics Data
Voltage Sources Data
Current Sources Data
Load Data
Generator (Synchronous Machine) Data
Non-Transformer Branch Data
Transformer Data
Two-Terminal DC Data
VSC DC Data
--- Page 189 ---
Harmonics Data File Contents
 File Identification Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
180FACTS Device Data
Induction Machine (Asynchronous Machine) Data
Q Record
5.2. File Identification Data
This record contains only one data item which is specified as:
HARFILEVRSN=vernum
HARFILEVRSN HARFILEVRSN is the keyword to specify the harmonics data file version number.
vernum is an integer value specifying harmonics data file version number. No default
allowed.
Allowed, HARFILEVRSN = 1
5.3. Impedance Characteristics Data
The frequency dependency of various network elements is defined in this table. The resistance (R), induc-
tance (L) and capacitance (C) at harmonic number 'N' is specified as ratio of its value at that harmonic to its
fundamental (system) frequency value (RN/R0, LN/L0 and CN/C0).
There is no limit on number of harmonics modeled in each impedance characteristics table. The table data
can span on more than one text line. End of data for the table is specified by specifying 0 value for harmonic
number.
Each Impedance Characteristics data table has the following format:
NAME, FREQ1, R1/R0, L1/LO, C1/C0, FREQ2, R2/R0, L2/LO, C2/C0, .., FREQN, RN/
R0, LN/LO, CN/C0, 0
NAME The non-blank alphanumeric identifier assigned to table. Each Impedance Character-
istics table must have unique NAME. The NAME may be up to 40 characteristics and
may contain any combination of blanks, uppercase letters, numbers and special char-
acters, but the first character must not be a minus sign. The NAME must be enclosed
in single or double quotes if it contains any blanks or special characters.
No default allowed.
FREG Frequency
No default allowed.
HN = 0 means end of table data
--- Page 190 ---
Harmonics Data File Contents
 Voltage Sources Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
181RN/R0 Ratio of resistance at harmonic number 'N' to its resistance at fundamental frequency.
No default allowed.
LN/L0 Ratio of inductance at harmonic number 'N' to its inductance at fundamental frequen-
cy.
No default allowed.
CN/C0 Ratio of capacitance at harmonic number 'N' to its capacitance at fundamental fre-
quency.
No default allowed.
Impedance Characteristics Data input is terminated with a record specifying table NAME as 0 (zero).
5.4. Voltage Sources Data
The harmonic voltage sources are defined in this table.
There is no limit on number of harmonics modeled in each voltage sources table. The table data can span
on more than one text line. End of data for the table is specified by specifying 0 value for harmonic number.
Each Voltage Sources data table has the following format:
NAME, ANGLE, H1, V1/V0, ANG1, H2, V2/V0, ANG2, .., HN, VN/V0, ANGN, 0
NAME The non-blank alphanumeric identifier assigned to table. Each Voltage Sources table
must have unique NAME. The NAME may be up to 40 characteristics and may contain
any combination of blanks, uppercase letters, numbers and special characters, but the
first character must not be a minus sign. The NAME must be enclosed in single or
double quotes if it contains any blanks or special characters.
No default allowed.
ANGLE Default angle determination
•0 - No
•1 - Yes
ANGLE = 0 by default
SINCAL value = Flag_DefAngle
HN Harmonic Number, N
No default allowed.
HN = 0 means end of table data
VN/V0 Ratio of voltage at harmonic number 'N' to voltage at fundamental frequency.
No default allowed.
ANGN Angle in degrees at harmonic number 'N'.
--- Page 191 ---
Harmonics Data File Contents
 Current Sources Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
182No default allowed.
Voltage Sources Data input is terminated with a record specifying table NAME as 0 (zero).
5.5. Current Sources Data
The harmonic current sources are defined in this table.
There is no limit on number of harmonics modeled in each current sources table. The table data can span on
more than one text line. End of data for the table is specified by specifying 0 value for harmonic number.
Each Current Sources data table has the following format:
NAME, CURTYP, ANGLE, H1, I1/I0, ANG1, H2, I2/I0, ANG2, .., HN, IN/I0, ANGN, 0
NAME The non-blank alphanumeric identifier assigned to table. Each Current Sources table
must have unique NAME. The NAME may be up to 40 characteristics and may contain
any combination of blanks, uppercase letters, numbers and special characters, but the
first character must not be a minus sign. The NAME must be enclosed in single or
double quotes if it contains any blanks or special characters.
No default allowed.
CURTYP Current source state
•1 - Current in A
•2 - Current in %
CURTYP = 2 by default
SINCAL value = Flag_I
ANGLE Default angle determination
•0 - No
•1 - Yes
ANGLE = 1 by default
SINCAL value = Flag_DefAngle
HN Harmonic Number, N
No default allowed.
HN = 0 means end of table data
IN/I0 Ratio of current at harmonic number 'N' to current at fundamental frequency.
No default allowed.
ANGN Angle in degrees at harmonic number 'N'.
No default allowed.
--- Page 192 ---
Harmonics Data File Contents
 Load Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
183Current Sources Data input is terminated with a record specifying table NAME as 0 (zero).
5.6. Load Data
The frequency dependency behavior and harmonics contribution of only in-service loads provided on this
data record are modeled.
Each load data record has the following format:
I, ID, HSTATE, HTYPE, HQUALITY, HIREG, HPK, ZHARM_TABLE, VHARM_TABLE, IHARM_TABLE
I Bus number of the bus to which the load is connected. It must be present in the work-
ing case.
No default allowed
ID One- or two-character load identifier.
ID = '1' by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
HTYPE Harmonic type
•0 - No frequency dependency
•1 - Quality - R constant (serial)
•2 - Quality - X/R constant (serial)
•3 - Impedance characteristics
•4 - Quality - X/R constant (parallel)
•5 - CIGRE
•6 - Quality - R constant (parallel)
•9 - Infinite
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
HQUALITY = 1.0 by default.
HIREG Control current
HCTRLCUR = 0.0 by default
--- Page 193 ---
Harmonics Data File Contents
 Generator Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
184SINCAL value = Ireg
HPK Reference Compensation Power
HPK = 0.0 by default
SINCAL value = pk
ZHARM_TABLE Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the load is modeled using the impedance characteristics specified in this
table. When table is not found or specified as blank, the frequency dependency of the
load is ignored.
ZHARM_TABLE = '' by default.
VHARM_TABLE Previously defined Harmonic Voltage Source table name. When modeled the load will
inject the voltage harmonics to the network at bus 'I'. The voltage source value at har-
monic 'N' is calculated using the voltage source characteristics specified in this table.
When table is not found or specified as blank, the voltage harmonics by the load are
ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the load will
inject the current harmonics in the network at bus 'I'. The Current source value at har-
monic 'N' is calculated using the current source characteristics specified in this table.
When table is not found or specified as blank, the current harmonics by the load are
ignored.
IHARM_TABLE = '' by default.
Load Data input is terminated with a record specifying bus number 0 (zero).
5.7. Generator Data
The frequency dependency behavior and harmonics contribution of only in-service generators provided on
this data record are modeled.
Each generator data record has the following format:
I, ID, HSTATE, HTYPE, HQUALITY, ZHARM_TABLE, VHARM_TABLE, IHARM_TABLE
I Bus number of the bus to which the generator is connected. It must be present in the
working case.
No default allowed
ID One- or two-character generator identifier.
ID = '1' by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
--- Page 194 ---
Harmonics Data File Contents
 Non-Transformer Branch Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
185HSTATE = 1 by default
HTYPE Harmonic type
•0 - No frequency dependency
•1 - Quality - R constant (serial)
•2 - Quality - X/R constant (serial)
•3 - Impedance characteristics
•4 - CIGRE model - A
•5 - CIGRE model - B
•9 - Infinite
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
HQUALITY = 1.0 by default.
ZHARM_TABLE Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the generator is modeled using the impedance characteristics specified in
this table. When table is not found or specified as blank, the frequency dependency
of the generator is ignored.
ZHARM_TABLE = '' by default.
VHARM_TABLE Previously defined Harmonic Voltage Source table name. When modeled the genera-
tor will inject the voltage harmonics to the network at bus 'I'. The voltage source value
at harmonic 'N' is calculated using the voltage source characteristics specified in this
table. When table is not found or specified as blank, the voltage harmonics by the
generator are ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the genera-
tor will inject the current harmonics in the network at bus 'I'. The Current source val-
ue at harmonic 'N' is calculated using the current source characteristics specified in
this table. When table is not found or specified as blank, the current harmonics by the
generator are ignored.
IHARM_TABLE = '' by default.
Generator Data input is terminated with a record specifying bus number 0 (zero).
5.8. Non-Transformer Branch Data
The frequency dependency behavior of only in-service non-transformer branches provided on this data
record are modeled.
Each non-transformer branch data record has the following format:
--- Page 195 ---
Harmonics Data File Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
186I, J, CKT, HSTATE, HTYPE, HQUALITY, ZHARM_TABLE
I Branch from bus number. It must be present in the working case.
No default allowed
J Branch to bus number. It must be present in the working case.
No default allowed
CKT One- or two-character non-transformer branch identifier. A branch with cicuit identi-
fier CKT between buses I and J must be in the working case.
CKT = '1' by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
HTYPE Harmonic type
•0 - No frequency dependency
•1 - Quality - R constant
•2 - Quality - X/R constant
•3 - Impedance characteristics
•4 - CIGRE
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
HQUALITY = 1.0 by default.
ZHARM_TABLE Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the non-transformer branch is modeled using the impedance characteris-
tics specified in this table. When table is not found or specified as blank, the frequency
dependency of the non-transformer branch is ignored.
ZHARM_TABLE = '' by default.
Non-Transformer Branch Data input is terminated with a record specifying bus number 0 (zero).
5.9. Transformer Data
The frequency dependency behavior and harmonics contribution of only in-service transformers provided
on this data record are modeled.
Each transformer data record has the following format:
--- Page 196 ---
Harmonics Data File Contents
 Transformer Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
187I, J, K, CKT, HSTATE, HTYPE, HQUALITY, ZHARM_TABLE, IHARM_TABLE
I Transformer Winding 1 bus number. It must be present in the working case.
No default allowed
J Transformer Winding 2 bus number. It must be present in the working case.
No default allowed
K Transformer Winding 3 bus number. K is specified as 0 (zero) to indicate that no third
winding is present. When K specified is non-zero, it must be present in the working
case.
K = 0 by default.
CKT One- or two-character transformer identifier. A transformer with cicuit identifier CKT
between buses I and J (and K if K is non-zero) must be in the working case.
CKT = '1' by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
HTYPE Harmonic type
•0 - No frequency dependency
•1 - Quality - R constant (serial)
•2 - Quality - X/R constant (serial)
•3 - Impedance characteristics
•4 - CIGRE model - A
•5 - CIGRE model - B
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
HQUALITY = 1.0 by default.
ZHARM_TABLE Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the transformer is modeled using the impedance characteristics specified
in this table. When table is not found or specified as blank, the frequency dependency
of the transformer is ignored.
ZHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the trans-
former will inject the current harmonics in the network at bus 'I'. The Current source
--- Page 197 ---
Harmonics Data File Contents
 Two-Terminal DC Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
188value at harmonic 'N' is calculated using the current source characteristics specified
in this table. When table is not found or specified as blank, the current harmonics by
the transformer are ignored.
IHARM_TABLE = '' by default.
Transformer Data input is terminated with a record specifying bus number 0 (zero).
5.10. Two-Terminal DC Data
The voltage and current harmonics contribution of only in-service two-terminal dc lines provided on this data
record are modeled.
Each two-terminal dc line data record has the following format:
NAME, ACBUS, HSTATE, VHARM_TABLE, IHARM_TABLE
NAME The non-blank alphanumeric two-terminal dc line name. It must be present in the
working case.
No default allowed
ACBUS Two-terminal dc line rectifier or inverter converter AC bus number. A two-terminal dc
line 'NAME' with 'ACBUS' as one of the rectifier or inverter buses must be in the working
case.
No default allowed
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
VHARM_TABLE Previously defined Harmonic Voltage Source table name. When modeled the two-ter-
minal dc line will inject the voltage harmonics to the network at bus 'ACBUS'. The volt-
age source value at harmonic 'N' is calculated using the voltage source characteristics
specified in this table. When table is not found or specified as blank, the voltage har-
monics by the two-terminal dc line are ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the two-ter-
minal dc line will inject the current harmonics in the network at bus 'ACBUS'. The Cur-
rent source value at harmonic 'N' is calculated using the current source characteristics
specified in this table. When table is not found or specified as blank, the current har-
monics by the two-terminal dc line are ignored.
IHARM_TABLE = '' by default.
Two-Terminal DC Data input is terminated with a record specifying NAME as 0 (zero).
--- Page 198 ---
Harmonics Data File Contents
 VSC DC Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
1895.11. VSC DC Data
The voltage and current harmonics contribution of only in-service VSC dc lines provided on this data record
are modeled.
Each VSC dc line data record has the following format:
NAME, ACBUS, HSTATE, VHARM_TABLE, IHARM_TABLE
NAME The non-blank alphanumeric VSC dc line name. It must be present in the working case.
No default allowed
ACBUS VSC dc line converter AC bus number. A VSC dc line 'NAME' with 'ACBUS' as one of its
converters AC buses must be in the working case.
No default allowed
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
VHARM_TABLE Previously defined Harmonic Voltage Source table name. When modeled the VSC dc
line will inject the voltage harmonics to the network at bus 'ACBUS'. The voltage source
value at harmonic 'N' is calculated using the voltage source characteristics specified
in this table. When table is not found or specified as blank, the voltage harmonics by
the VSC dc line are ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the VSC dc
line will inject the current harmonics in the network at bus 'ACBUS'. The Current source
value at harmonic 'N' is calculated using the current source characteristics specified
in this table. When table is not found or specified as blank, the current harmonics by
the VSC dc line are ignored.
IHARM_TABLE = '' by default.
VSC DC Data input is terminated with a record specifying NAME as 0 (zero).
5.12. FACTS Device Data
The voltage and current harmonics contribution of only in-service FACTS devices provided on this data record
are modeled.
Each FACTS device data record has the following format:
NAME, SENDBUS, TERMBUS, HSTATE, HTYPE, HQUALITY, ZHARM_TABLE, VHARM_TABLE,
IHARM_TABLE
NAME The non-blank alphanumeric FACTS device name. It must be present in the working
case.
--- Page 199 ---
Harmonics Data File Contents
 FACTS Device Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
190No default allowed
SENDBUS FACTS device Sending End AC bus number. A FACTS device 'NAME' with 'SENDBUS' as
its sending AC bus must be in the working case. A SENDBUS value must be provided
as every FACTS device has as least one bus, making it a shunt device.
No default allowed
TERMBUS FACTS device Terminal End AC bus number. A FACTS device 'NAME' with 'TERMBUS' as
its terminal AC bus must be in the working case. A TERMBUS value must be provided
if the FACTS device is operating as a serial device.
TERMBUS = 0 by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
HSTATE = 1 by default
HTYPE Harmonic type
If TERMBUS = 0:
•0 - No frequency dependency
•1 - Quality - R constant (serial)
•2 - Quality - X/R constant (serial)
•3 - Impedance characteristics
•4 - Quality - X/R constant (parallel)
•5 - CIGRE
•6 - Quality - R constant (parallel)
•9 - Infinite
If TERMBUS != 0:
•0 - No frequency dependency
•1 - Quality - R constant (serial)
•2 - Quality - X/R constant (serial)
•3 - Impedance characteristics
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
--- Page 200 ---
Harmonics Data File Contents
 Induction Machine (Asynchronous ma-
chine) Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
191HQUALITY = 1.0 by default.
ZHARM_TABLE This item is required if the FACTS device is serial device, SENDBUS AND TERMBUS != 0.
Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the generator is modeled using the impedance characteristics specified in
this table. When table is not found or specified as blank, the frequency dependency
of the generator is ignored.
ZHARM_TABLE = '' by default.
VHARM_TABLE This item is required if the FACTS device is shunt device, TERMBUS != 0.
Previously defined Harmonic Voltage Source table name. When modeled the FACTS
device will inject the voltage harmonics to the network at bus 'ACBUS'. The voltage
source value at harmonic 'N' is calculated using the voltage source characteristics spec-
ified in this table. When table is not found or specified as blank, the voltage harmonics
by the FACTS device are ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE This item is required if the FACTS device is shunt device, TERMBUS != 0.
Previously defined Harmonic Current Source table name. When modeled the FACTS
device will inject the current harmonics in the network at bus 'ACBUS'. The Current
source value at harmonic 'N' is calculated using the current source characteristics spec-
ified in this table. When table is not found or specified as blank, the current harmonics
by the FACTS device are ignored.
IHARM_TABLE = '' by default.
FACTS Device Data input is terminated with a record specifying NAME as 0 (zero).
5.13. Induction Machine (Asynchronous machine) Data
The frequency dependency behavior and harmonics contribution of only in-service induction machines pro-
vided on this data record are modeled.
Each induction machine data record has the following format:
I, ID, HSTATE, HTYPE, HQUALITY, ZHARM_TABLE, VHARM_TABLE, IHARM_TABLE
I Bus number of the bus to which the induction machine is connected. It must be
present in the working case.
No default allowed
ID One- or two-character induction machine identifier.
ID = '1' by default
HSTATE Current control state
•0 - Not active in harmonics
•1 - Active in harmonics
--- Page 201 ---
Harmonics Data File Contents
 Induction Machine (Asynchronous ma-
chine) Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
192HSTATE = 1 by default
HTYPE Harmonic type
•0 - No frequency dependency
•1 - Quality - R constant
•2 - Quality - X/R constant
•3 - Impedance characteristics
•9 - Infinite
HTYPE = 1 by default
SINCAL value = Flag_Har
HQUALITY Harmonic quality constant
HQUALITY = 1.0 by default.
ZHARM_TABLE Previously defined Harmonic Impedance Characteristics table name. The frequency
behavior of the induction machine is modeled using the impedance characteristics
specified in this table. When table is not found or specified as blank, the frequency
dependency of the induction machine is ignored.
ZHARM_TABLE = '' by default.
VHARM_TABLE Previously defined Harmonic Voltage Source table name. When modeled the induction
machine will inject the voltage harmonics to the network at bus 'I'. The voltage source
value at harmonic 'N' is calculated using the voltage source characteristics specified
in this table. When table is not found or specified as blank, the voltage harmonics by
the induction machine are ignored.
VHARM_TABLE = '' by default.
IHARM_TABLE Previously defined Harmonic Current Source table name. When modeled the induction
machine will inject the current harmonics in the network at bus 'I'. The Current source
value at harmonic 'N' is calculated using the current source characteristics specified in
this table. When table is not found or specified as blank, the current harmonics by the
induction machine are ignored.
IHARM_TABLE = '' by default.
Induction Machine Data input is terminated with a record specifying bus number 0 (zero).
--- Page 202 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
193Chapter 6
Machine Capability Curve Data File
6.1. Overview
The input stream to activity REGC consists of a Change Code record, followed by a group of capability curve
data records. One such record is specified for each machine whose capability curve is to be read into the
working case.
All data is read in free format with data items separated by a comma or one or more blanks. The capability
curve data category is terminated by a record specifying an I value of zero.
6.2. Change Code Data
The first record in the Capability Curve Data File contains two data items as follows:
IC, REV
IC New capability curve data flag:
IC = 0 indicates the initial input of capability curve data for the network contained in
the working case. Any capability curves in the case are deleted from the case before
adding the capability curves specified in the input file.
IC = 1 indicates change case input of capability curve data for the network contained
in the working case. If a record is specified for a machine that already has a capability
curve, default values for omitted data items are the existing values of the capability
curve.
IC = 0 by default
REV PSSE revision number
REV = current revision by default
6.3. Capability Curve Data
Each machine to be represented with its capability curve in PSSE must be specified in a capability curve data
record. Each data record in this file has the following format:
I, ID, P1, QT1, QB1, P2, QT2, QB2, ... P20, QT20, QB20
where:
I Bus number. Bus I must be present in the working case with a plant sequence number
assigned to it (refer to Plant and Machine Sequence Numbers ).
--- Page 203 ---
Machine Capability Curve Data File
 Capability Curve Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
194No default is allowed
ID One-or two character machine identifier used to distinguish among multiple machines
at a plant (i.e., at a generator bus)
ID = 1 by default
Pi Generator active power output along the MW axis of the machine's capability curve,
entered in MW
No default is allowed
QTi Maximum (i.e., overexcited) reactive power limit at P i MW, entered in Mvar
QTi = 0.0 by default
QBi Minimum (i.e., underexcited) reactive power limit at P i MW, entered in Mvar
QBi = 0.0 by default
At least two sets of points must be specified for each capability curve; up to 20 sets of points on the capability
curve may be entered. When the machine is a generator, the P i values must be in ascending order with P 1
greater than or equal to zero. When the machine is a motor, the P i values must be in descending order with
P1 less than or equal to zero.
Capability curves may be specified for non-conventional as well as conventional machines. For a non-conven-
tional machines with a machine control mode (WMOD) greater than 1, the capability curve will not be used
to update the machine's reactive power limits; the limits will be determined from the specified power factor
(WPF) for mode 2 and 3 machines, and from the machine's fixed reactive power (QG) for mode 4 machines.
Data input is terminated with a record specifying a bus number of zero.
In the PSS®E EXAMPLE directory, there is an example capability curve established for the machines in the
savnw.sav  power flow case. The data file is savnw.gcp, and the contents of the file are listed in Figure 6.1,
“Capability Curve Example for savnw.sav Case” . A generic plot of the reactive limits for the machine at bus
206 is shown.
--- Page 204 ---
Machine Capability Curve Data File
 Capability Curve Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
195
Figure 6.1. Capability Curve Example for savnw.sav Case
--- Page 205 ---
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
196Chapter 7
IEC Data File
7.1. Overview
The impedances and admittances of electrical equipment in the working case are modified according to the
correction factors defined in IEC 60909-0 (2016 Section 6 or 2001 Section 3).
Generators, equivalent generators, asynchronous motors, wind farms and photovoltaic (PV) farms are repre-
sented as sources in the working case. To calculate their impedance correction factors or correct contribution
to fault additional data is required. This additional data is provided in IEC data file (.iec).
Additional data is needed in the following cases.
•If a generator model includes a GSU transformer. It is recommended to represent the GSU transformer
explicitly as a separate power component so as to be able to correctly modify the generator and transformer
impedances as per the IEC 60909 standard.
•If a generator in the working case is an equivalent generator representation.
•If a generator is a used in the modeling of a synchronous motor.
•If a generator is set with QMIN=QMAX, then it is treated as an asynchronous motor. If this is the case, then
additional data is not necessary.
•If a transformer winding MVA specified in system MVA base and not nameplate winding MVA.
•If an induction machine base power (MBASE) is specified as mechanical power output (BCODE=1), then
power factor and efficiency are needed to calculate base MVA. If an induction machine scheduled active
power (PSET) is set as real electrical power drawn by the machine, efficiency is needed to calculate me-
chanical power output (MW) of the machine.
•If a generator in the working case is a Wind Farm or PV Farm.
There are six groups of records, with each group containing a particular type of data required. Each record
is terminated by specifying a bus number of 0.
7.2. File Identification Data
This record contains only one data item which is specified as:
IECFILEVRSN=vernum
IECFILEVRSN IECFILEVRSN is the keyword to specify the IEC data file version number.
vernum is an integer value specifying IEC data file version number. No default al-
lowed.
--- Page 206 ---
IEC Data File
 GSU, Equivalent Generator and Motor
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
197Allowed, IECFILEVRSN = 2
Note: The first release of the IEC data file did not have this record and is treated as
file version 1
7.3. GSU, Equivalent Generator and Motor Data
(Induction Motors are specified as part of Generator data category.)
Each data record has the following format:
I, ID, MCTYPE, UrG, PG, PFactor, PolePair, GSUType, Ix, Jx, Kx, Ckt, PT
where:
I Machine bus number
ID Machine ID
MCTYPE Machine type
MCTYPE =1, for Generator
MCTYPE =2, for Equivalent generator
MCTYPE =3, for Induction machines specified as part of generator data category
MCTYPE =4, for Wind power station asynchronous generator
MCTYPE =5, for Wind power station double fed asynchronous generator
MCTYPE =6, for Wind or Photovoltaic power station generator with full size convert-
er
UrG Rated terminal voltage, line-to-line in kV r.m.s. (this need not be the rated bus volt-
age)
PG Range of generator voltage regulation in %, e.g., if PG is ±5%, enter PG=5.
PG = (UG -UrG)/UrG, where UG is the scheduled generator terminal voltage
= 0 default
PFactor Generator rated power factor (used only if MCTYPE=1).
This is used in impedance correction factor calculations.
= 1.0 default
PolePair Number of pole pairs if machine is induction machine (used only if the machine is
modeled as induction machine)
Example: If the induction machine has a six pole construction then Polepair=3
GSUType Generator step-up-transformer type
GSUType =0, no GSU, GSU transformer modeled explicitly.
GSUType =1, GSU with OLTC
--- Page 207 ---
IEC Data File
 Transformer Nameplate Winding MVA
Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
198GSUType =2, GSU without OLTC
Ix GSU transformer I bus number
= 0 by default to specify no GSU transformer
Jx GSU transformer J bus number
= 0 by default to specify no GSU transformer
Kx GSU transformer K bus number
= 0 by default to specify no GSU transformer
Ckt GSU transformer circuit identifier
= blank ('') by default to specify no GSU transformer
PT Tap range of GSU off-load tap-changer transformer in % of transformer winding rat-
ed voltage, e.g., if PT is ±5%, enter PT=5
= 0.0 by default to ignore off-load tap range
7.4. Transformer Nameplate Winding MVA Data
Each data record has the following format:
I, J, K, Ckt, Sbase 1-2, Sbase 2-3, Sbase 3-1
where:
I Winding 1 bus number
J Winding 2 bus number
K Winding 3 bus number ( =0 for two-winding transformer)
Ckt Transformer circuit identifier
Sbase 1-2 Winding 1 to winding 2 Nameplate MVA
Sbase 2-3 Winding 2 to winding 3 Nameplate MVA (not required for two-winding transformer)
Sbase 3-1 Winding 3 to winding 1 Nameplate MVA (not required for two-winding transformer)
7.5. Induction Machine Data
(Induction Motors are specified as part of Induction machine data category)
Each data record has the following format:
I, ID, PolePair, PFactor, Efficiency
where:
--- Page 208 ---
IEC Data File
 Wind Power Station Asynchronous Gen-
erator Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
199I Machine bus number
ID Machine ID
PolePair Number of pole pairs
Example: If the induction machine has a six pole construction then Polepair=3
PFactor Induction Machine rated power factor
= 1.0 default
Efficiency Induction Machine percent efficiency.
=100 by default
Example: Efficiency=96.5 for 96.5% efficiency.
7.6. Wind Power Station Asynchronous Generator Data
(These equipment are specified as part of Generator data category and modeled as MCTYPE=4 in IEC data
file.)
Each data record has the following format:
I, ID, ILR2IR_1, R2X_1, ILR2IR_2, R2X_2, ILR2IR_0, R2X_0
where:
I Machine bus number
ID Machine ID
ILR2IR_1 Ratio of Positive Sequence locked rotor current to rated current, no default allowed.
R2X_1 Ratio of Positive Sequence resistance to reactance =0.1 by default
ILR2IR_2 Ratio of Negative Sequence locked rotor current to rated current ILR2IR_2=ILR2IR_1
by default
R2X_2 Ratio of Negative Sequence resistance to reactance R2X_2=R2X_1 by default
ILR2IR_0 Ratio of Zero Sequence locked rotor current to rated current ILR2IR_0=ILR2IR_1 by
default
R2X_0 Ratio of Zero Sequence resistance to reactance R2X_0=R2X_1 by default
7.7. Wind Power Station Doubly Fed Asynchronous
Generator Data
(These equipment are specified as part of Generator data category, and modeled as MCTYPE=5 in IEC data
file.)
Each data record has the following format:
I, ID, IMAX_1, R2X_1, IMAX_2, R2X_2, IMAX_0, R2X_0, KWD, IKWDmax, IKWDmin
where:
I Machine bus number
--- Page 209 ---
IEC Data File
 Wind and Photovoltaic Power Station
Generator with Full Size Converter Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
200ID Machine ID
IMAX2IR_1 Ratio of Positive Sequence highest instantaneous short circuit value in case of a
three-phase short circuit to rated current, no default allowed
R2X_1 Ratio of Positive Sequence resistance to reactance. R2X_1=0.1 by default
IMAX2IR_2 Ratio of Negative Sequence highest instantaneous short circuit value in case of an
unbalance short circuit to rated current, IMAX2IR_2=IMAX2IR_1 by default
R2X_2 Ratio of Negative Sequence resistance to reactance. R2X_2=R2X_1 by default
IMAX2IR_0 Ratio of Zero Sequence highest instantaneous short circuit value in case of an unbal-
ance short circuit to rated current, IMAX2IR_0=IMAX2IR_1 by default
R2X_0 Ratio of Zero Sequence resistance to reactance. R2X_0=R2X_1 by default
KWD Factor for the calculation of the peak short-circuit current, KWD=1.7 by default
IkWDmax Ratio of maximum steady state short circuit current to rated current,
IkWDmax=IMAX2IR_1 by default. This is used in calculation of symmetrical breaking
current.
IkWDmin Ratio of minimum steady state short circuit current to rated current,
IkWDmin=IMAX2IR_1 by default. This is used in calculation of symmetrical breaking
current.
7.8. Wind and Photovoltaic Power Station Generator
with Full Size Converter Data
(These equipment are specified as part of Generator data category, and modeled as MCTYPE=6, 7 in IEC
data file.)
Each data record has the following format:
I, ID, IMAX_1, R2X_1, IMAX_2, R2X_2, IMAX_0, R2X_0, IkPFmax, IkPFmin
where:
I Machine bus number
ID Machine ID
IMAX2IR_1 Ratio of Positive Sequence short circuit current value to rated current, no default al-
lowed
R2X_1 Ratio of Positive Sequence resistance to reactance. R2X_1=0.1 by default
IMAX2IR_2 Ratio of Negative Sequence short circuit current value to rated current,
IMAX2IR_2=IMAX2IR_1 by default
R2X_2 Ratio of Negative Sequence resistance to reactance. R2X_2=R2X_1 by default
IMAX2IR_0 Ratio of Zero Sequence short circuit value in case of an unbalance short circuit to
rated current, IMAX2IR_0=IMAX2IR_1 by default
R2X_0 Ratio of Zero Sequence resistance to reactance. R2X_0=R2X_1 by default
IkPFmax Ratio of maximum steady state short circuit current to rated current,
IkPFmax=IMAX2IR_1 by default. This is used in calculation of symmetrical breaking
current.
IkPFmin Ratio of minimum steady state short circuit current to rated current,
IkPFmin=IMAX2IR_1 by default. This is used in calculation of symmetrical breaking
current.
--- Page 210 ---
IEC Data File
 Wind and Photovoltaic Power Station
Generator with Full Size Converter Data
PSS®E 34.8.0
All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
201IEC File Data records for generators, transformers and motors are created only for those units that need IEC
data and they may be entered in any order and using a free format with blanks or commas separating each
data item in each record group.
Following is an example of IEC data for IEC 60909-4, Section 6 network, refer PAG for details.
1  Q1 2
5  Q2 2
6  G3 1 10.5 0.0 0.8
41 G1 1 21.0 0.0 0.85 0 1 4 41 0 T1 12
31 G2 1 10.5 7.5 0.9  0 2 3 31 0 T2
0 / END OF GSU, EQV, GEN, AND INDUCTION MACHINE DATA
0 / END OF TRANSFORMER DATA
7  M1 1 0.88 97.5
7  M2 2 0.89 96.8
7  M3 2 0.89 96.8
0 / END OF INDUCTION MACHINE DATA
Q
or
IECFILEVRSN=2
1  Q1 2
5  Q2 2
6  G3 6 10.5 0.0 0.8
41 G1 4 21.0 0.0 0.85 0 1 4 41 0 T1 12
31 G2 5 10.5 7.5 0.9  0 2 3 31 0 T2
0 / END OF GSU, EQV, GEN, AND MOTOR DATA
0 / END OF TRANSFORMER DATA
7  M1 1 0.88 97.5
7  M2 2 0.89 96.8
7  M3 2 0.89 96.8
0 / END OF INDUCTION MACHINE DATA
41 G1 5.0 0.2
0 / END OF WIND POWER STATION ASYNCHRONOUS GENERATOR DATA
31 G2 6.0 0.3
0 / END OF WIND POWER STATION DOUBLY FED ASYNCHRONOUS GENERATOR DATA
6 G3 7.0 0.4
0 / END OF WIND AND PHOTOVOLTAIC POWER STATION GENERATOR WITH FULL SIZE CONVERTER DATA
Q