"""
Performance Tracking System for HDB to RAW Pipeline Converters

This module provides comprehensive performance tracking for all converter classes,
logging execution times and tracking improvements over time.
"""

import time
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from contextlib import contextmanager
import logging


class PerformanceTracker:
    """
    Tracks performance metrics for converter classes and methods.
    
    Features:
    - Individual converter timing
    - Total pipeline execution time
    - Performance improvement tracking
    - Historical performance data
    - Optimization recommendations
    """
    
    def __init__(self, performance_file: Union[str, Path] = "performance_tracking.txt"):
        """
        Initialize performance tracker.
        
        Args:
            performance_file: Path to performance tracking file
        """
        self.performance_file = Path(performance_file)
        self.current_session = {
            'start_time': None,
            'end_time': None,
            'total_duration': None,
            'converter_times': {},
            'converter_counts': {},
            'optimizations_applied': [],
            'session_id': datetime.now().strftime("%Y%m%d_%H%M%S")
        }
        self.logger = logging.getLogger(__name__)
        
        # Ensure performance file exists
        self._initialize_performance_file()
    
    def _initialize_performance_file(self) -> None:
        """Initialize performance tracking file if it doesn't exist."""
        if not self.performance_file.exists():
            self.performance_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.performance_file, 'w', encoding='utf-8') as f:
                f.write("# HDB to RAW Pipeline Performance Tracking\n")
                f.write("# Format: [TIMESTAMP] [SESSION_ID] [EVENT_TYPE] [DETAILS]\n")
                f.write("# Event Types: SESSION_START, SESSION_END, CONVERTER_TIME, OPTIMIZATION\n")
                f.write(f"# Tracking started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def start_session(self) -> None:
        """Start a new performance tracking session."""
        self.current_session['start_time'] = time.time()
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(self.performance_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] [{self.current_session['session_id']}] SESSION_START Pipeline execution started\n")
        
        self.logger.info(f"📊 Performance tracking session started: {self.current_session['session_id']}")
    
    def end_session(self) -> None:
        """End the current performance tracking session."""
        if self.current_session['start_time'] is None:
            self.logger.warning("Cannot end session - no session started")
            return
        
        self.current_session['end_time'] = time.time()
        self.current_session['total_duration'] = self.current_session['end_time'] - self.current_session['start_time']
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        total_time = self.current_session['total_duration']
        
        with open(self.performance_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] [{self.current_session['session_id']}] SESSION_END Total time: {total_time:.3f}s\n")
            
            # Write converter summary
            f.write(f"[{timestamp}] [{self.current_session['session_id']}] CONVERTER_SUMMARY:\n")
            for converter_name, duration in self.current_session['converter_times'].items():
                count = self.current_session['converter_counts'].get(converter_name, 0)
                f.write(f"  - {converter_name}: {duration:.3f}s ({count} records)\n")
            
            # Write optimizations applied
            if self.current_session['optimizations_applied']:
                f.write(f"[{timestamp}] [{self.current_session['session_id']}] OPTIMIZATIONS_APPLIED:\n")
                for optimization in self.current_session['optimizations_applied']:
                    f.write(f"  - {optimization}\n")
            
            f.write("\n")
        
        self.logger.info(f"📊 Performance tracking session ended: {total_time:.3f}s")
        self._generate_performance_report()
    
    @contextmanager
    def track_converter(self, converter_name: str, record_count: int = 0):
        """
        Context manager to track converter execution time.
        
        Args:
            converter_name: Name of the converter class
            record_count: Number of records being processed
        
        Usage:
            with tracker.track_converter('TransformerConverter', 1367):
                result = converter.convert()
        """
        start_time = time.time()
        self.logger.info(f"⏱️  Starting {converter_name} conversion...")
        
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            # Store in current session
            self.current_session['converter_times'][converter_name] = duration
            self.current_session['converter_counts'][converter_name] = record_count
            
            # Log to file
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.performance_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] [{self.current_session['session_id']}] CONVERTER_TIME {converter_name}: {duration:.3f}s ({record_count} records)\n")
            
            # Calculate rate if we have records
            if record_count > 0:
                rate = record_count / duration if duration > 0 else 0
                self.logger.info(f"⏱️  {converter_name} completed: {duration:.3f}s ({record_count} records, {rate:.1f} records/sec)")
            else:
                self.logger.info(f"⏱️  {converter_name} completed: {duration:.3f}s")
    
    def log_optimization(self, converter_name: str, optimization_description: str, 
                        before_time: Optional[float] = None, after_time: Optional[float] = None) -> None:
        """
        Log an optimization applied to a converter.
        
        Args:
            converter_name: Name of the converter that was optimized
            optimization_description: Description of the optimization
            before_time: Execution time before optimization (if available)
            after_time: Execution time after optimization (if available)
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        optimization_entry = f"{converter_name}: {optimization_description}"
        if before_time is not None and after_time is not None:
            improvement = ((before_time - after_time) / before_time) * 100 if before_time > 0 else 0
            optimization_entry += f" (improved by {improvement:.1f}%: {before_time:.3f}s → {after_time:.3f}s)"
        
        self.current_session['optimizations_applied'].append(optimization_entry)
        
        with open(self.performance_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] [{self.current_session['session_id']}] OPTIMIZATION {optimization_entry}\n")
        
        self.logger.info(f"🚀 Optimization logged: {optimization_entry}")
    
    def get_historical_performance(self, converter_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get historical performance data for analysis.
        
        Args:
            converter_name: Optional filter for specific converter
            
        Returns:
            List of performance records
        """
        if not self.performance_file.exists():
            return []
        
        performance_data = []
        
        with open(self.performance_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('#') or not line.strip():
                    continue
                
                try:
                    # Parse line format: [TIMESTAMP] [SESSION_ID] [EVENT_TYPE] [DETAILS]
                    if not line.strip().startswith('['):
                        continue
                    
                    # Split by '] [' to get the bracketed parts
                    bracket_parts = line.strip().split('] [')
                    if len(bracket_parts) < 3:
                        continue
                    
                    # Extract timestamp, session_id, and the rest
                    timestamp_str = bracket_parts[0][1:]  # Remove leading [
                    session_id = bracket_parts[1]
                    
                    # The rest contains event type and details
                    remaining = '] ['.join(bracket_parts[2:])
                    
                    # Split event type from details
                    event_parts = remaining.split('] ', 1)
                    if len(event_parts) < 2:
                        continue
                    
                    event_type = event_parts[0]
                    details = event_parts[1]
                    
                    if event_type == 'CONVERTER_TIME':
                        # Parse converter time entry: "TransformerConverter: 0.101s (1367 records)"
                        converter_details = details.split(': ', 1)
                        if len(converter_details) == 2:
                            conv_name = converter_details[0]
                            time_info = converter_details[1]
                            
                            # Skip if filtering for specific converter
                            if converter_name and conv_name != converter_name:
                                continue
                            
                            # Extract time and record count
                            time_parts = time_info.split('s (')
                            if len(time_parts) == 2:
                                exec_time = float(time_parts[0])
                                record_count_str = time_parts[1].split(' records')[0]
                                record_count = int(record_count_str)
                                
                                performance_data.append({
                                    'timestamp': timestamp_str,
                                    'session_id': session_id,
                                    'converter_name': conv_name,
                                    'execution_time': exec_time,
                                    'record_count': record_count,
                                    'records_per_second': record_count / exec_time if exec_time > 0 else 0
                                })
                
                except (ValueError, IndexError) as e:
                    self.logger.debug(f"Error parsing performance line: {line.strip()} - {e}")
                    continue
        
        return performance_data
    
    def _generate_performance_report(self) -> None:
        """Generate a performance report showing trends and recommendations."""
        historical_data = self.get_historical_performance()
        
        if not historical_data:
            return
        
        # Group by converter
        converter_stats = {}
        for record in historical_data:
            conv_name = record['converter_name']
            if conv_name not in converter_stats:
                converter_stats[conv_name] = {
                    'executions': [],
                    'total_records': 0,
                    'avg_time': 0,
                    'avg_rate': 0
                }
            
            converter_stats[conv_name]['executions'].append(record)
            converter_stats[conv_name]['total_records'] += record['record_count']
        
        # Calculate averages and identify slow converters
        slow_converters = []
        
        for conv_name, stats in converter_stats.items():
            executions = stats['executions']
            if executions:
                stats['avg_time'] = sum(e['execution_time'] for e in executions) / len(executions)
                stats['avg_rate'] = sum(e['records_per_second'] for e in executions) / len(executions)
                
                # Consider converter slow if avg time > 1 second or rate < 100 records/sec
                if stats['avg_time'] > 1.0 or (stats['avg_rate'] < 100 and stats['avg_rate'] > 0):
                    slow_converters.append((conv_name, stats))
        
        # Write performance report
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(self.performance_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] PERFORMANCE_REPORT Generated for session {self.current_session['session_id']}\n")
            f.write(f"  Total converters analyzed: {len(converter_stats)}\n")
            f.write(f"  Slow converters identified: {len(slow_converters)}\n")
            
            if slow_converters:
                f.write("  Slow converters needing optimization:\n")
                for conv_name, stats in slow_converters:
                    f.write(f"    - {conv_name}: {stats['avg_time']:.3f}s avg, {stats['avg_rate']:.1f} records/sec\n")
            
            f.write("\n")
        
        if slow_converters:
            self.logger.info(f"📊 Performance report: {len(slow_converters)} converters identified for optimization")
            for conv_name, stats in slow_converters:
                self.logger.info(f"  🐌 {conv_name}: {stats['avg_time']:.3f}s avg, {stats['avg_rate']:.1f} records/sec")
    
    def get_optimization_recommendations(self) -> List[str]:
        """
        Get optimization recommendations based on performance data.
        
        Returns:
            List of optimization recommendations
        """
        historical_data = self.get_historical_performance()
        recommendations = []
        
        # Group by converter and analyze patterns
        converter_performance = {}
        for record in historical_data:
            conv_name = record['converter_name']
            if conv_name not in converter_performance:
                converter_performance[conv_name] = []
            converter_performance[conv_name].append(record)
        
        for conv_name, records in converter_performance.items():
            if not records:
                continue
            
            avg_time = sum(r['execution_time'] for r in records) / len(records)
            avg_rate = sum(r['records_per_second'] for r in records) / len(records)
            max_records = max(r['record_count'] for r in records)
            
            # Recommend optimizations based on patterns
            if avg_time > 5.0:
                recommendations.append(f"{conv_name}: Very slow execution ({avg_time:.1f}s avg) - needs major optimization")
            elif avg_time > 1.0:
                recommendations.append(f"{conv_name}: Slow execution ({avg_time:.1f}s avg) - consider optimization")
            
            if avg_rate < 50 and max_records > 100:
                recommendations.append(f"{conv_name}: Low processing rate ({avg_rate:.1f} records/sec) - likely has nested loops")
            
            if max_records > 1000 and avg_time > 2.0:
                recommendations.append(f"{conv_name}: Large dataset with slow processing - implement lookup tables and caching")
        
        return recommendations


# Global performance tracker instance
_performance_tracker = None


def get_performance_tracker() -> PerformanceTracker:
    """Get the global performance tracker instance."""
    global _performance_tracker
    if _performance_tracker is None:
        _performance_tracker = PerformanceTracker()
    return _performance_tracker


def track_converter_performance(converter_name: str, record_count: int = 0):
    """
    Decorator/context manager for tracking converter performance.
    
    Args:
        converter_name: Name of the converter class
        record_count: Number of records being processed
    
    Returns:
        Context manager for tracking execution time
    """
    return get_performance_tracker().track_converter(converter_name, record_count)


def log_optimization(converter_name: str, optimization_description: str, 
                    before_time: Optional[float] = None, after_time: Optional[float] = None) -> None:
    """
    Log an optimization applied to a converter.
    
    Args:
        converter_name: Name of the converter that was optimized
        optimization_description: Description of the optimization
        before_time: Execution time before optimization (if available)
        after_time: Execution time after optimization (if available)
    """
    get_performance_tracker().log_optimization(converter_name, optimization_description, before_time, after_time)


def start_performance_session() -> None:
    """Start a new performance tracking session."""
    get_performance_tracker().start_session()


def end_performance_session() -> None:
    """End the current performance tracking session."""
    get_performance_tracker().end_session() 