#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wrapper script to run comparison and save results to file.
Handles Unicode output properly.
"""

import sys
import os
import subprocess
from datetime import datetime

def main():
    # Create output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output_demo/device_comparison_results_{timestamp}.txt"
    
    print(f"Running comparison tool...")
    print(f"Results will be saved to: {output_file}")
    
    # Run the comparison script and capture output
    try:
        # Run with UTF-8 encoding
        result = subprocess.run(
            [sys.executable, "run_comparison.py"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # Save output to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"Device-Level Comparison Results\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            
            if result.stdout:
                f.write("STANDARD OUTPUT:\n")
                f.write("-" * 40 + "\n")
                f.write(result.stdout)
                f.write("\n\n")
            
            if result.stderr:
                f.write("ERROR OUTPUT:\n")
                f.write("-" * 40 + "\n")
                f.write(result.stderr)
                f.write("\n\n")
            
            f.write("=" * 80 + "\n")
            f.write(f"Exit code: {result.returncode}\n")
        
        print(f"✅ Results saved to: {output_file}")
        
        # Also print a summary to console
        if result.returncode == 0:
            print("\n✅ Comparison completed successfully!")
        else:
            print(f"\n⚠️ Comparison completed with warnings/errors (exit code: {result.returncode})")
            
        # Show file size
        file_size = os.path.getsize(output_file)
        print(f"📁 Output file size: {file_size:,} bytes")
        
    except Exception as e:
        print(f"❌ Error running comparison: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 