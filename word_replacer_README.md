# Word Replacer Tool

A Python tool for replacing individual words in Python files with detailed context logging.

## Features

- **Precise Word Matching**: Uses word boundaries to replace only exact words (not substrings)
- **Context Tracking**: Logs the class name and method name where each replacement occurs
- **AST-Based Analysis**: Uses Python's AST parser for accurate context detection
- **Named Log Files**: Creates log files named after the word being replaced
- **Automatic Backups**: Creates timestamped backup files before making changes
- **Dry Run Mode**: Preview changes without modifying files
- **Comprehensive Statistics**: Shows total files processed, replacements made, etc.

## Usage

### Basic Usage

```bash
python word_replacer.py "target_word" "replacement_word"
```

### Advanced Usage

```bash
python word_replacer.py "old_method" "new_method" \
    --path "RawEditor/database" \
    --file-patterns "*.py" \
    --dry-run
```

### Command Line Options

- `target_word`: The word to replace (required)
- `replacement_word`: The word to replace it with (required)
- `--path`, `-p`: Root path to search (default: current directory)
- `--file-patterns`, `-f`: File patterns to search (default: *.py)
- `--dry-run`, `-d`: Show what would be changed without making changes
- `--no-backup`, `-b`: Do not create backup files

## Log Format

The tool creates a log file named `{target_word}_replacement_log_{timestamp}.log` with detailed information about each replacement:

```
REPLACEMENT: /path/to/file.py:123:45 in BusConverter::convert
  OLD: old_field_name = data.get('field')
  NEW: new_field_name = data.get('field')
  CONTEXT: class='BusConverter', method='convert'
```

### Log Entry Format

Each replacement is logged with:
- **File path and line number**: Exact location of the replacement
- **Column position**: Character position within the line
- **Context**: Class name and method name where the replacement occurred
- **Before/After lines**: The original and modified lines
- **Structured context**: Separate class and method fields for parsing

## Examples

### 1. Dry Run (Preview Changes)

```bash
python word_replacer.py "old_field" "new_field" --dry-run
```

### 2. Replace in Specific Directory

```bash
python word_replacer.py "deprecated_method" "new_method" --path "X-Pipeline"
```

### 3. Replace in Multiple File Types

```bash
python word_replacer.py "old_name" "new_name" --file-patterns "*.py" "*.md"
```

### 4. Replace Without Backups

```bash
python word_replacer.py "temp_var" "result_var" --no-backup
```

## Output Files

### Log Files
- **Name**: `{target_word}_replacement_log_{YYYYMMDD_HHMMSS}.log`
- **Content**: Detailed replacement information with context
- **Location**: Current directory

### Backup Files
- **Name**: `{original_file}.{ext}.backup_{YYYYMMDD_HHMMSS}`
- **Content**: Original file content before changes
- **Location**: Same directory as original file

## Context Detection

The tool uses two methods to detect context:

1. **AST Analysis** (Primary): Uses Python's AST parser for accurate class/method detection
2. **Line-by-Line Analysis** (Fallback): Regex-based detection if AST parsing fails

### Context Examples

```python
# Module level
target_word = "value"  # Context: module_level

class MyClass:
    def my_method(self):
        target_word = "value"  # Context: MyClass::my_method
    
    def another_method(self):
        if condition:
            target_word = "value"  # Context: MyClass::another_method
```

## Safety Features

- **Word Boundaries**: Only replaces complete words, not substrings
- **Backup Creation**: Automatic timestamped backups before changes
- **Dry Run Mode**: Preview changes without modifying files
- **Error Handling**: Graceful handling of file access errors
- **Validation**: Prevents replacing word with itself

## Common Use Cases

### Refactoring Field Names
```bash
python word_replacer.py "field_name" "canonical_field_name" --path "RawEditor"
```

### Renaming Methods
```bash
python word_replacer.py "old_method" "new_method" --path "anode/controller"
```

### Updating Class Names
```bash
python word_replacer.py "OldClassName" "NewClassName" --file-patterns "*.py"
```

### Variable Renaming
```bash
python word_replacer.py "temp_data" "processed_data" --path "X-Pipeline"
```

## Tips

1. **Always use dry run first** to preview changes
2. **Be specific with paths** to avoid unwanted replacements
3. **Check log files** for detailed replacement information
4. **Use backups** to restore if needed
5. **Test with small scope** before large refactoring operations

## Log Analysis

The structured log format allows for easy parsing and analysis:

```bash
# Count replacements by class
grep "CONTEXT:" target_word_replacement_log.log | cut -d"'" -f2 | sort | uniq -c

# Find replacements in specific method
grep "::method_name" target_word_replacement_log.log

# Show all files that were modified
grep "REPLACEMENT:" target_word_replacement_log.log | cut -d: -f1 | sort | uniq
``` 