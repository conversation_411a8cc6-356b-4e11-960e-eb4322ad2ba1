# Building Seed Case Primer

## 1. Overview

This document explains how to build a PSSE-compatible seed case from EMS export files. It covers the role of the HdbContext, how each section of the EMS export is mapped to PSSE objects, and how to use the resulting case in power system studies.

---

## 2. Building the HDB Context

### 2.1 What is the HdbContext?

- **Class:** `HdbContext`(in`data/hdb/context.py`)
- **Purpose:** Aggregates all the parsed `.out` files into a single object, providing a unified, in-memory view of the entire EMS model.

### 2.2 How to Use

```python
from modelling.packages import ExportPackage
from data.hdb.context import HdbContext
package = ExportPackage("path/to/export_dir")
hdb = HdbContext(package)
```text

- The `HdbContext`parses each`.out` file and exposes records as dictionaries.
- All model data (buses, lines, transformers, loads, etc.) is accessible via the context.

---

## 3. Mapping HDB Context to PSSE Case

### 3.1 The Build Process

- The `PsseCase.build()`method (in`modelling/psse/case_utilities.py`) takes an`ExportPackage` and optional merged context.
- It creates a new PSSE case, populates it with all objects from the HdbContext, and saves the `.sav`and`.raw` files.

```python
from modelling.psse.case_utilities import PsseCase
PsseCase.build(package, merged_context=hdb)
```text

### 3.2 What Gets Mapped

- **Buses:** From `bus.out` → PSSE Bus objects
- **Lines:** From `ln.out` → PSSE Branch objects
- **Transformers:** From `xf.out` → PSSE Transformer objects
- **Loads:** From `ld.out` → PSSE Load objects
- **Generators:** From `un.out` → PSSE Generator objects
- **Areas, Zones, Owners:** From `area.out`,`st.out`,`co.out`
- **Other Entities:** As defined in the EMS export and supported by the context

---

## 4. Using the Seed Case in Studies

### 4.1 Loading the Case

- The resulting `.sav`and`.raw` files can be loaded into PSSE for simulation and analysis.
- Use the `PsseCase.load()` method or PSSE GUI.

### 4.2 Example Workflow

```python
from modelling.packages import ExportPackage
from data.hdb.context import HdbContext
from modelling.psse.case_utilities import PsseCase

package = ExportPackage("path/to/export_dir")
hdb = HdbContext(package)
PsseCase.build(package, merged_context=hdb)
PsseCase.load(package.data['sav'])
```text

- After loading, you can run power flow, contingency, and other studies as needed.

---

## 5. Advanced Topics

### 5.1 Customizing the Build

- You can merge custom topology packages into the HdbContext before building the case.
- See the Topology Package Primer for details.

### 5.2 Automation

- The build process can be scripted for batch studies or CI pipelines.
- See `controller/filesystem/export_archive_manager.py` for automation examples.

---

## 6. Best Practices

- **Validate Inputs:** Check `.out` files for completeness and consistency before building.
- **Version Control:** Keep export directories and build scripts under version control.
- **Document Changes:** Track any manual edits or customizations.
- **Test Cases:** Always validate the resulting case with basic power flow and contingency runs.
- **Documentation:** Every public interface and class is documented for auto-generation.

---

## 7. References

- See `data/hdb/context.py` for HdbContext implementation.
- See `modelling/psse/case_utilities.py` for case build logic.
- See `controller/filesystem/export_archive_manager.py` for automation scripts.
- See the EMS Export Primer and Topology Package Primer for related workflows.
