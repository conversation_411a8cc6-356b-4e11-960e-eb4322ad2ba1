# Topology Package Primer

## 1. Overview

This document explains the concept, structure, and usage of custom topology packages in the EMS-to-PSSE workflow. It covers how to create, edit, and apply topology packages for scenario and future-state studies.

---

## 2. What is a Topology Package?

- **Purpose:** Allows users to define changes (additions, removals, edits) to the base EMS model for scenario/future-state studies.
- **Class:** `TopologyChangePackage`(in`modelling/packages.py`)
- **Structure:** Each topology package is a directory with CSV files for each entity type, split into `*_add.csv`,`*_remove.csv`,`*_edit.csv`.

---

## 3. Creating a Custom Topology Package

### 3.1 Steps to Create

1. **Create a new directory** for your topology package.
2. **For each entity type you want to change**, create a CSV file named `<entity>_add.csv`,`<entity>_remove.csv`, or`<entity>_edit.csv`.
   - Example: To add a new line, create `ln_add.csv` with the appropriate headers and data.
3. **Document your changes** in a `_description.txt` file.

### 3.2 Example: Creating a TopologyChangePackage

```python
from modelling.packages import TopologyChangePackage
topology = TopologyChangePackage("path/to/topology_dir")
```text

### 3.3 Which .out Files to Adjust

- Only those corresponding to the entities you want to change (e.g., `ln_add.csv`for new lines,`un_remove.csv` for removing generators).
- The rest can be left empty or omitted.

---

## 4. Applying a Topology Package in Studies

### 4.1 How is it used?

- **Merged into the HdbContext** before building the case, so the changes are reflected in the resulting PSSE case.
- **Can be applied programmatically** using methods in the reliability analysis or case build scripts.

### 4.2 Example Workflow

```python
from modelling.packages import ExportPackage, TopologyChangePackage
from data.hdb.context import HdbContext
from modelling.psse.case_utilities import PsseCase

# Load base export
package = ExportPackage("path/to/export_dir")
hdb = HdbContext(package)

# Merge custom topology
topology = TopologyChangePackage("path/to/topology_dir")
hdb.merge(topology)

# Build the PSSE case
PsseCase.build(package, merged_context=hdb)
```text

---

## 5. Advanced Usage: Future Topology and Scenario Studies

- Topology packages can be chained or sequenced for time-varying studies.
- The system supports merging multiple packages and tracking effective dates for scenario analysis.
- See `controller/psse/base.py`and`modelling/psse/topology.py` for advanced merging and sequencing logic.

---

## 6. Best Practices

- **Edit only what you need:** Only create or modify CSVs for entities you want to change.
- **Document changes:** Always include a `_description.txt` explaining the intent and scope of the package.
- **Test before use:** Validate the merged context and resulting case before running studies.
- **Version control:** Keep topology packages under version control for traceability.

---

## 7. References

- See `modelling/packages.py` for class documentation and usage examples.
- See `modelling/psse/topology.py` for topology manipulation functions.
- See `controller/psse/base.py` for integration with reliability and scenario studies.
