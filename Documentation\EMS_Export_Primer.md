# EMS Export Primer

## 1. Overview

This document explains how EMS model data is exported, received, and structured for use in power system studies. It covers the SFTP download process, the structure and meaning of the exported `.out` files, and provides a detailed addendum on the headers and fields of each file type.

---

## 2. EMS SFTP Export Workflow

### 2.1 What is the EMS Export?

The EMS (Energy Management System) regularly exports a snapshot of the power system model as a set of text files (with `.out` extension), which are then used for studies and case building in PSSE.

### 2.2 How Files Are Received

- **Automated SFTP Download:**
  - The script `ems_sftp_download_and_export_package_build.py` orchestrates the download of EMS export ZIP files from the NETMOM SFTP server.
  - The `SftpArchiveManager` class manages the connection, download, and extraction of these files.
  - Files are named like `transfer_files_YYYY_MM_DD_HH_MM.zip`and contain all`.out` files for a snapshot.
- **Extraction:**
  - The ZIP is extracted to a local directory, making the `.out` files available for further processing.

### 2.3 What Are the .out Files?

- Each `.out` file represents a table/entity from the EMS model (e.g., buses, lines, transformers, loads).
- Files are named by type: `area.out`,`bus.out`,`cb.out`,`ld.out`,`ln.out`,`xf.out`, etc.
- Each file is a CSV-like text file with a header line and data lines.

---

## 3. Structure and Headers of .out Files

### 3.1 File Format

- **Header line:** Starts with `#record`, listing the field names.
- **Data lines:** Each line is a record, fields separated by commas.
- **Example:**

```csv
#record area, mrid_area, Number, Name, Interchange
area, '1', 1, 'FIRST', 0.0
```text

### 3.2 How Are Files Parsed?

- Each file is parsed by a `RecordContext`subclass (see`data/hdb/context.py`).
- The header is mapped to human-readable field names, and each record is stored as a dictionary.

---

## 4. Addendum: .out File Types and Full Headers

Below is a summary of common `.out`files, their purpose, and their exact headers as found in real EMS exports. For full details, see the code in`data/hdb/context.py` and sample files in your export directories.

### 4.1 area.out

**Purpose:** Defines system areas (e.g., balancing authorities)

**Header:**

```csv
#record area, mrid_area, %SUBSCRIPT, id_area, wnet_area
```text

  | Field         | Description                                      |
  |-------------- |-------------------------------------------------|
  | area          | Record type identifier (always 'area')           |
  | mrid_area     | EMS unique identifier for the area               |
  | %SUBSCRIPT    | Area number (unique integer for the area)        |
  | id_area       | Area name or code                                |
  | wnet_area     | Net MW for the area                              |

### 4.2 bus.out

**Purpose:** Bus records (nodes in the network)

**Header:**

```csv
#record bs, %SUBSCRIPT, mrid_bs, v_bs, adeg_bs
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | bs            | Record type identifier (always 'bs')             |
  | %SUBSCRIPT    | Bus number (unique integer for the bus)          |
  | mrid_bs       | EMS unique identifier for the bus                |
  | v_bs          | Bus voltage magnitude (per unit)                 |
  | adeg_bs       | Bus voltage angle (degrees)                      |

### 4.3 cb.out

**Purpose:** Circuit breaker records

**Header:**

```csv
#record cb, %SUBSCRIPT, mrid_cb, id_cb, id_cbtyp, id_st, nd_cb, znd_cb, open_cb, nmlopen_cb, p__cblim_cb
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | cb            | Record type identifier (always 'cb')             |
  | %SUBSCRIPT    | Breaker number (unique integer for the breaker)  |
  | mrid_cb       | EMS unique identifier for the breaker            |
  | id_cb         | Breaker name or code                             |
  | id_cbtyp      | Breaker type code                                |
  | id_st         | Substation number or identifier                  |
  | nd_cb         | Node number the breaker is connected to (references nd.out)          |
  | znd_cb        | Secondary node number (references nd.out)                            |
  | open_cb       | Breaker open status (1=open, 0=closed)           |
  | nmlopen_cb    | Breaker normal open status (1=open, 0=closed)    |
  | p__cblim_cb   | Breaker thermal limit (MW)                       |

### 4.4 ld.out

**Purpose:** Load records

**Header:**

```csv
#record ld, %SUBSCRIPT, mrid_ld, id_ld, nd_ld, id_st, id_co, remove_ld, open_ld, r_ld, w_ld, zipmdl_ld, rconsti_ld, rconstp_ld, rconstz_ld, wconsti_ld, wconstp_ld, wconstz_ld, ldarea_ld
```text

  | Field         | Description                                                        |
  |-------------- |--------------------------------------------------------------------|
  | ld            | Record type identifier (always 'ld')                               |
  | %SUBSCRIPT    | Load number (unique integer for the load)                          |
  | mrid_ld       | EMS unique identifier for the load                                 |
  | id_ld         | Load name or code                                                  |
  | nd_ld         | Node number the load is connected to (references nd.out)           |
  | id_st         | Substation number or identifier (references st.out: id_st)         |
  | id_co         | Company number or identifier (references co.out: id_co)            |
  | remove_ld     | Removal flag (1=remove, 0=keep)                                    |
  | open_ld       | Load open status (1=open, 0=closed)                                |
  | r_ld          | Load resistance (ohms)                                             |
  | w_ld          | Load MW                                                           |
  | zipmdl_ld     | ZIP model type code                                                |
  | rconsti_ld    | Constant current real power component                              |
  | rconstp_ld    | Constant power real power component                                |
  | rconstz_ld    | Constant impedance real power component                            |
  | wconsti_ld    | Constant current reactive power component                          |
  | wconstp_ld    | Constant power reactive power component                            |
  | wconstz_ld    | Constant impedance reactive power component                        |
  | ldarea_ld     | Load area number or identifier (references ldarea.out: id_ldarea)  |

### 4.5 ln.out

**Purpose:** Line segment records (branches)

**Header:**

```csv
#record ln, %SUBSCRIPT, mrid_ln, id_ln, id_line, id_dv, nd_ln, st_ln, znd_ln, zst_ln, r_ln, x_ln, bch_ln, remove_ln, zprime_ln, zmeter_ln, p__lnlim_ln, energ_ln
```text

  | Field         | Description                                                    |
  |-------------- |----------------------------------------------------------------|
  | ln            | Record type identifier (always 'ln')                           |
  | %SUBSCRIPT    | Line segment number (unique integer)                           |
  | mrid_ln       | EMS unique identifier for the line segment                     |
  | id_ln         | Line segment name or code                                      |
  | id_line       | Parent line identifier (references line.out: id_line)          |
  | id_dv         | Division identifier (references dv.out)                        |
  | nd_ln         | Node number at one end of the segment (references nd.out)      |
  | st_ln         | Substation number at one end of the segment (references st.out)|
  | znd_ln        | Node number at the other end (references nd.out)               |
  | zst_ln        | Substation number at the other end (references st.out: id_st)  |
  | r_ln          | Resistance (ohms)                                              |
  | x_ln          | Reactance (ohms)                                               |
  | bch_ln        | Charging susceptance (S)                                       |
  | remove_ln     | Removal flag (1=remove, 0=keep)                                |
  | zprime_ln     | Zero-sequence impedance (ohms)                                 |
  | zmeter_ln     | Metered impedance (ohms)                                       |
  | p__lnlim_ln   | Line segment thermal limit (MW)                                |
  | energ_ln      | Energized status (1=energized, 0=not)                          |

### 4.6 xf.out

**Purpose:** Transformer records

**Header:**

```csv
#record xf, %SUBSCRIPT, mrid_xf, id_xf, id_xfmr, id_st, nd_xf, znd_xf, regnd_xf, tap_xf, tapty_xf, kvnom, ztap_xf, ztapty_xf, zkvnom, remove_xf, r_xf, x_xf, bmag_xf, gmag_xf, step_xf
```text

  | Field         | Description                                                        |
  |-------------- |--------------------------------------------------------------------|
  | xf            | Record type identifier (always 'xf')                               |
  | %SUBSCRIPT    | Transformer number (unique integer)                                |
  | mrid_xf       | EMS unique identifier for the transformer                          |
  | id_xf         | Transformer name or code                                           |
  | id_xfmr       | Transformer model/type identifier                                  |
  | id_st         | Substation number or identifier (references st.out: id_st)         |
  | nd_xf         | Node number at one end of the transformer (references nd.out)      |
  | znd_xf        | Node number at the other end (references nd.out)                   |
  | regnd_xf      | Regulating node number (references nd.out)                         |
  | tap_xf        | Tap position                                                       |
  | tapty_xf      | Tap changer type identifier (references tapty.out: id_tapty)       |
  | kvnom         | Nominal voltage (kV)                                               |
  | ztap_xf       | Secondary tap position                                             |
  | ztapty_xf     | Secondary tap changer type identifier (references tapty.out: id_tapty) |
  | zkvnom        | Secondary nominal voltage (kV)                                     |
  | remove_xf     | Removal flag (1=remove, 0=keep)                                    |
  | r_xf          | Resistance (ohms)                                                  |
  | x_xf          | Reactance (ohms)                                                   |
  | bmag_xf       | Magnetizing susceptance (S)                                        |
  | gmag_xf       | Magnetizing conductance (S)                                        |
  | step_xf       | Step number or identifier                                          |

### 4.7 st.out

**Purpose:** Substation records

**Header:**

```csv
#record st, %SUBSCRIPT, mrid_st, id_st
```text

  | Field         | Description                                                |
  |-------------- |------------------------------------------------------------|
  | st            | Record type identifier (always 'st')                       |
  | %SUBSCRIPT    | Substation number (unique integer)                         |
  | mrid_st       | EMS unique identifier for the substation                   |
  | id_st         | Substation name or code                                    |

### 4.8 un.out

**Purpose:** Generator unit records

**Header:**

```csv
#record un, %SUBSCRIPT, mrid_un, id_un, nd_un, regnd_un, vtarget_un, open_un, w_un, wmx_un, wmn_un, r_un, rmx_un, rmn_un, id_co, id_st, regst_un, acrcy_un
```text

  | Field         | Description                                                        |
  |-------------- |--------------------------------------------------------------------|
  | un            | Record type identifier (always 'un')                               |
  | %SUBSCRIPT    | Generator unit number (unique integer)                             |
  | mrid_un       | EMS unique identifier for the generator unit                       |
  | id_un         | Generator unit name or code                                        |
  | nd_un         | Node number the generator is connected to (references nd.out)      |
  | regnd_un      | Regulating node number (references nd.out)                         |
  | vtarget_un    | Voltage target (per unit)                                          |
  | open_un       | Generator open status (1=open, 0=closed)                           |
  | w_un          | Generator MW output                                                |
  | wmx_un        | Maximum MW output                                                  |
  | wmn_un        | Minimum MW output                                                  |
  | r_un          | Generator MVAR output                                              |
  | rmx_un        | Maximum MVAR output                                                |
  | rmn_un        | Minimum MVAR output                                                |
  | id_co         | Company number or identifier (references co.out: id_co)            |
  | id_st         | Substation number or identifier (references st.out: id_st)         |
  | regst_un      | Regulating substation number (references st.out: id_st)            |
  | acrcy_un      | Accuracy or control mode                                           |

### 4.9 svs.out

**Purpose:** Static VAR system records

**Header:**

```csv
#record svs,%subscript,id,id_st,nd,regnd,regsk,regst,remove,bmn,bmx,qpcmn,qpcmx,qssmn,qssmx,vtarget,mrnom,mrresn,mrresp,rmn,rmx,rm,r,deviat,disdev,distarg,vqslope,energ
```text

  | Field        | Description                                                      |
  |------------- |------------------------------------------------------------------|
  | svs          | Record type identifier (always 'svs')                            |
  | %subscript   | Static VAR system number (unique integer)                        |
  | id           | Static VAR system identifier                                     |
  | id_st        | Substation number or identifier (references st.out: id_st)       |
  | nd           | Node number the SVS is connected to (references nd.out)          |
  | regnd        | Regulating node number (references nd.out)                       |
  | regsk        | Regulating shunt number                                          |
  | regst        | Regulating substation number (references st.out: id_st)          |
  | remove       | Removal flag (1=remove, 0=keep)                                  |
  | bmn          | Minimum susceptance (S)                                          |
  | bmx          | Maximum susceptance (S)                                          |
  | qpcmn        | Minimum reactive power (MVAR)                                    |
  | qpcmx        | Maximum reactive power (MVAR)                                    |
  | qssmn        | Minimum steady-state reactive power (MVAR)                       |
  | qssmx        | Maximum steady-state reactive power (MVAR)                       |
  | vtarget      | Voltage target (per unit)                                        |
  | mrnom        | Nominal rating                                                   |
  | mrresn       | Reserve rating                                                   |
  | mrresp       | Response rating                                                  |
  | rmn          | Minimum resistance (ohms)                                        |
  | rmx          | Maximum resistance (ohms)                                        |
  | rm           | Resistance (ohms)                                                |
  | r            | Reactance (ohms)                                                 |
  | deviat       | Deviation                                                        |
  | disdev       | Discrete deviation                                               |
  | distarg      | Disturbance argument                                             |
  | vqslope      | Voltage-Q slope                                                  |
  | energ        | Energized status (1=energized, 0=not)                            |

### 4.10 tapty.out

**Purpose:** Tap changer type records

**Header:**

```csv
#record tapty, %SUBSCRIPT, oid_tapty, id_tapty, mn_tapty, mx_tapty, nom_tapty, step_tapty
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | tapty         | Record type identifier (always 'tapty')          |
  | %SUBSCRIPT    | Tap changer type number (unique integer)         |
  | oid_tapty     | Object identifier for tap changer type           |
  | id_tapty      | Tap changer type name or code                    |
  | mn_tapty      | Minimum tap position                             |
  | mx_tapty      | Maximum tap position                             |
  | nom_tapty     | Nominal tap position                             |
  | step_tapty    | Tap step size                                    |

### 4.11 zbr.out

**Purpose:** Zero impedance branch records

**Header:**

```csv
#record zbr, %SUBSCRIPT, mrid, id, remove, zmeter, zprime, nd, st, znd, zst, id_line
```text

  | Field         | Description                                                   |
  |-------------- |-------------------------------------------------------------- |
  | zbr           | Record type identifier (always 'zbr')                         |
  | %SUBSCRIPT    | Zero impedance branch number (unique integer)                 |
  | mrid          | EMS unique identifier for the branch                          |
  | id            | Branch name or code                                           |
  | remove        | Removal flag (1=remove, 0=keep)                               |
  | zmeter        | Metered impedance (ohms)                                      |
  | zprime        | Zero-sequence impedance (ohms)                                |
  | nd            | Node number at one end (references nd.out)                    |
  | st            | Substation number at one end (references st.out: id_st)       |
  | znd           | Node number at the other end (references nd.out)              |
  | zst           | Substation number at the other end (references st.out: id_st) |
  | id_line       | Parent line identifier (references line.out: id_line)         |

### 4.12 altlim.out

**Purpose:** Alternate Limit records (temperature-dependent ratings for lines and transformers)

**Header:**

```csv
#record altlim, %SUBSCRIPT, oid_altlim, id_substn, id_devtyp, id_device, id_analog, id_limit, id, hilim
```text

  | Field         | Description                                                  |
  |-------------- |--------------------------------------------------------------|
  | altlim        | Record type identifier (always 'altlim')                     |
  | %SUBSCRIPT    | Alternate limit record number (unique integer)               |
  | oid_altlim    | Object identifier for the alternate limit                    |
  | id_substn     | Substation identifier (references st.out: id_st)             |
  | id_devtyp     | Device type (e.g., LINE, TX, CB)                             |
  | id_device     | Device name or code (references device in related .out file) |
  | id_analog     | Units (e.g., AMPS, MVA)                                      |
  | id_limit      | Limit set (e.g., NORM, EMRG, LDS)                            |
  | id            | Temperature set or 'DEFAULT'                                 |
  | hilim         | Limit value (e.g., MVA, Amps)                                |

### 4.13 cblim.out

**Purpose:** Circuit Breaker Limit records (thermal limits for breakers)

**Header:**

```csv
#record cblim, %SUBSCRIPT, oid_cblim, id_st, id_cb, id_cblim, limit1_cblim, limit2_cblim, limit3_cblim
```text

  | Field         | Description                                                 |
  |-------------- |-------------------------------------------------------------|
  | cblim         | Record type identifier (always 'cblim')                     |
  | %SUBSCRIPT    | Circuit breaker limit record number                         |
  | oid_cblim     | Object identifier for the breaker limit                     |
  | id_st         | Substation identifier (references st.out: id_st)            |
  | id_cb         | Breaker identifier (references cb.out: id_cb)               |
  | id_cblim      | Breaker limit set identifier                                |
  | limit1_cblim  | Limit 1 (e.g., normal rating)                               |
  | limit2_cblim  | Limit 2 (e.g., emergency rating)                            |
  | limit3_cblim  | Limit 3 (e.g., load-shedding rating)                        |

### 4.14 co.out

**Purpose:** Company records (ownership and hierarchy)

**Header:**

```csv
#record co, %SUBSCRIPT, mrid_co, id_co
```text

  | Field        | Description                                                  |
  |------------- |--------------------------------------------------------------|
  | co           | Record type identifier (always 'co')                         |
  | %SUBSCRIPT   | Company number (unique integer)                              |
  | mrid_co      | EMS unique identifier for the company                        |
  | id_co        | Company name or code                                         |

### 4.15 cp.out

**Purpose:** Shunt records (fixed shunt devices, capacitors/reactors)

**Header:**

```csv
#record cp, %SUBSCRIPT, mrid_cp, id_cp, id_st, nd_cp, mrnom_cp, open_cp, cb_cp
```text

  | Field         | Description                                                     |
  |-------------- |---------------------------------------------------------------- |
  | cp            | Record type identifier (always 'cp')                            |
  | %SUBSCRIPT    | Shunt device number (unique integer)                            |
  | mrid_cp       | EMS unique identifier for the shunt device                      |
  | id_cp         | Shunt device name or code                                       |
  | id_st         | Substation identifier                                           |
  | nd_cp         | Node number the shunt is connected to (references nd.out)       |
  | mrnom_cp      | Nominal rating                                                  |
  | open_cp       | Shunt open status (1=open, 0=closed)                            |
  | cb_cp         | Circuit breaker identifier                                      |

### 4.16 ldarea.out

**Purpose:** Load Area records (hierarchical load distribution)

**Header:**

```csv
#record ldarea, %SUBSCRIPT, mrid_ldarea, id_ldarea, manual_ldarea, wload_ldarea, parfracw_ldarea, hldarea_ldarea, wsum, rsum
```text

  | Field             | Description                                      |
  |------------------ |------------------------------------------------- |
  | ldarea            | Record type identifier (always 'ldarea')         |
  | %SUBSCRIPT        | Load area number (unique integer)                |
  | mrid_ldarea       | EMS unique identifier for the load area          |
  | id_ldarea         | Load area name or code                           |
  | manual_ldarea     | Manual assignment flag                           |
  | wload_ldarea      | Total MW load in the area                        |
  | parfracw_ldarea   | Parent fraction of MW load                       |
  | hldarea_ldarea    | Parent load area identifier                      |
  | wsum              | Sum of MW loads                                  |
  | rsum              | Sum of MVAR loads                                |

### 4.17 line.out

**Purpose:** Line records (parent containers for line segments)

**Header:**

```csv
#record line, %SUBSCRIPT, mrid_line, id_line
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | line          | Record type identifier (always 'line')           |
  | %SUBSCRIPT    | Line number (unique integer)                     |
  | mrid_line     | EMS unique identifier for the line               |
  | id_line       | Line name or code                                |

### 4.18 lnlim.out

**Purpose:** Line Limit records (thermal limits for line segments)

**Header:**

```csv
#record lnlim, %SUBSCRIPT, oid_line, id_line, id_ln, id_lnlim, limit1_lnlim, limit2_lnlim, limit3_lnlim
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | lnlim         | Record type identifier (always 'lnlim')          |
  | %SUBSCRIPT    | Line limit record number (unique integer)        |
  | oid_line      | Object identifier for the line                   |
  | id_line       | Line identifier                                  |
  | id_ln         | Line segment identifier                          |
  | id_lnlim      | Line limit set identifier                        |
  | limit1_lnlim  | Limit 1 (e.g., normal rating)                    |
  | limit2_lnlim  | Limit 2 (e.g., emergency rating)                 |
  | limit3_lnlim  | Limit 3 (e.g., load-shedding rating)             |

### 4.19 nd.out

**Purpose:** Node records (vertices in the network graph)

**Header:**

```csv
#record nd, %SUBSCRIPT, mrid_nd, id_nd, id_kv, vl_kv, id_co, id_dv, id_st, un_nd, open_nd, i$bs_nd
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | nd            | Record type identifier (always 'nd')             |
  | %SUBSCRIPT    | Node number (unique integer)                     |
  | mrid_nd       | EMS unique identifier for the node               |
  | id_nd         | Node name or code                                |
  | id_kv         | Voltage level identifier                         |
  | vl_kv         | Voltage level (kV)                               |
  | id_co         | Company identifier (references co.out: id_co)    |
  | id_dv         | Division identifier                              |
  | id_st         | Substation identifier (references st.out: id_st) |
  | un_nd         | Unit number (if applicable)                      |
  | open_nd       | Node open status (1=open, 0=closed)              |
  | i$bs_nd       | Bus number (if applicable)                       |

### 4.20 ndlim.out

**Purpose:** Node Limit records (voltage limits for nodes)

**Header:**

```csv
#record ndlim, %SUBSCRIPT, oid_ndlim, p__nd_ndlim, vhnrm_ndlim, vhemer_ndlim, vlnrm_ndlim, vlemer_ndlim
```text

  | Field             | Description                                      |
  |------------------ |------------------------------------------------- |
  | ndlim             | Record type identifier (always 'ndlim')          |
  | %SUBSCRIPT        | Node limit record number (unique integer)        |
  | oid_ndlim         | Object identifier for the node limit             |
  | p__nd_ndlim       | Node identifier                                  |
  | vhnrm_ndlim       | High normal voltage limit (per unit)             |
  | vhemer_ndlim      | High emergency voltage limit (per unit)          |
  | vlnrm_ndlim       | Low normal voltage limit (per unit)              |
  | vlemer_ndlim      | Low emergency voltage limit (per unit)           |

### 4.21 xflim.out

**Purpose:** Transformer Limit records (thermal limits for transformers)

**Header:**

```csv
#record xflim, %SUBSCRIPT, id_st, id_xfmr, id_xf, id_xflim, limit1_xflim, limit2_xflim, limit3_xflim
```text

  | Field         | Description                                      |
  |-------------- |-------------------------------------------------|
  | xflim         | Record type identifier (always 'xflim')          |
  | %SUBSCRIPT    | Transformer limit record number (unique integer) |
  | id_st         | Substation identifier                            |
  | id_xfmr       | Transformer model/type identifier                |
  | id_xf         | Transformer identifier                           |
  | id_xflim      | Transformer limit set identifier                 |
  | limit1_xflim  | Limit 1 (e.g., normal rating)                    |
  | limit2_xflim  | Limit 2 (e.g., emergency rating)                 |
  | limit3_xflim  | Limit 3 (e.g., load-shedding rating)             |

### 4.22 solution_ascii_time.out

**Purpose:** Solution time records (state estimator timing)

**Header:**

```csv
#record solution_ascii_time, ...
```text

  | Field                  | Description                               |
  |----------------------- |-------------------------------------------|
  | solution_ascii_time    | Human readable timestamp of the solution  |

### 4.23 solution_quality.out

**Purpose:** Solution quality records (state estimator quality)

**Header:**

```csv
#record solution_quality, ...
```text

  | Field         | Description                                   |
  |-------------- |-----------------------------------------------|
  | ...           | ... (fields depend on export version)         |

### 4.24 value.out

**Purpose:** Value records (various parameter values)

**Header:**

```csv
#record value, %SUBSCRIPT, oid_value, id_skedty, id_sked, id_value, v1_value, v2_value, i__tympt_value
```text

  | Field             | Description                                      |
  |------------------ |------------------------------------------------- |
  | value             | Record type identifier (always 'value')          |
  | %SUBSCRIPT        | Value record number (unique integer)             |
  | oid_value         | Object identifier for the value                  |
  | id_skedty         | Schedule type identifier                         |
  | id_sked           | Schedule identifier                              |
  | id_value          | Value identifier                                 |
  | v1_value          | Value 1                                          |
  | v2_value          | Value 2                                          |
  | i__tympt_value    | Timepoint index or identifier                    |

### 4.25 analog.out

**Purpose:** SCADA mappings and setpoints (especially for STATCOMs)

**Header:**

```csv
#record analog,id_substn,id_devtyp,id_device,id_meas,id,dis,loreas,hireas,site,nis,flip,nodisabl
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | analog        | Record type identifier (always 'analog')         |
  | id_substn     | Substation identifier                            |
  | id_devtyp     | Device type identifier                           |
  | id_device     | Device identifier                                |
  | id_meas       | Measurement identifier                           |
  | id            | Measurement value identifier                     |
  | dis           | Discrete status                                  |
  | loreas        | Low reason code                                  |
  | hireas        | High reason code                                 |
  | site          | Site identifier                                  |
  | nis           | Not in service flag                              |
  | flip          | Flip status                                      |
  | nodisabl      | No disable flag                                  |

### 4.26 congrp.out

**Purpose:** Parent Contingency Group records (contingency naming)

**Header:**

```csv
#record congrp, %SUBSCRIPT, id_congrp, dscrpt_congrp
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | congrp        | Record type identifier (always 'congrp')         |
  | %SUBSCRIPT    | Contingency group number (unique integer)        |
  | id_congrp     | Contingency group identifier                     |
  | dscrpt_congrp | Contingency group description                    |

### 4.27 ctg.out

**Purpose:** Contingency records (define contingency names)

**Header:**

```csv
#record ctg, %SUBSCRIPT, id_ctg, s__ctgl_ctg, dscrpt_ctg, ctgplan_ctg, lostgen_ctg, lostload_ctg
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | ctg           | Record type identifier (always 'ctg')            |
  | %SUBSCRIPT    | Contingency number (unique integer)              |
  | id_ctg        | Contingency identifier                           |
  | s__ctgl_ctg   | Contingency list pointer                         |
  | dscrpt_ctg    | Contingency description                          |
  | ctgplan_ctg   | Contingency plan identifier                      |
  | lostgen_ctg   | Lost generation (MW)                             |
  | lostload_ctg  | Lost load (MW)                                   |

### 4.28 ctgl.out

**Purpose:** Contingency List records (elements in a contingency)

**Header:**

```csv
#record ctgl, %SUBSCRIPT, i__ctg_ctgl, f__ctgl, ctgstate_ctgl, name1_ctgl, name2_ctgl, name3_ctgl, type_ctgl
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | ctgl          | Record type identifier (always 'ctgl')           |
  | %SUBSCRIPT    | Contingency list number (unique integer)         |
  | i__ctg_ctgl   | Contingency index                                |
  | f__ctgl       | Forward pointer                                  |
  | ctgstate_ctgl | Contingency state                                |
  | name1_ctgl    | Element name 1                                   |
  | name2_ctgl    | Element name 2                                   |
  | name3_ctgl    | Element name 3                                   |
  | type_ctgl     | Element type                                     |

### 4.29 ctgrp.out

**Purpose:** Contingency Group records (define contingency groups)

**Header:**

```csv
#record ctgrp, %SUBSCRIPT, id_ctgrp, i__ctg_ctgrp, l__congrp_ctgrp
```text

  | Field           | Description                                      |
  |---------------- |------------------------------------------------- |
  | ctgrp           | Record type identifier (always 'ctgrp')          |
  | %SUBSCRIPT      | Contingency group number (unique integer)        |
  | id_ctgrp        | Contingency group identifier                     |
  | i__ctg_ctgrp    | Contingency index                                |
  | l__congrp_ctgrp | Linked group identifier                        |

### 4.30 ctvl.out

**Purpose:** Contingency Violation records (RTCA results)

**Header:**

```csv
#record ctvl, %SUBSCRIPT, curbas_ctvl, f1__ctvl_ctvl, f2__ctvl_ctvl, i__ctg_ctvl, limit1_ctvl, limit2_ctvl, limit3_ctvl, monelm_ctvl, perclim1_ctvl, perclim2_ctvl, perclim3_ctvl, prectg_ctvl, type_ctv
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | ctvl          | Record type identifier (always 'ctvl')           |
  | %SUBSCRIPT    | Contingency violation number (unique integer)    |
  | curbas_ctvl   | Current base value                               |
  | f1__ctvl_ctvl | Forward pointer 1                                |
  | f2__ctvl_ctvl | Forward pointer 2                                |
  | i__ctg_ctvl   | Contingency index                                |
  | limit1_ctvl   | Limit 1                                          |
  | limit2_ctvl   | Limit 2                                          |
  | limit3_ctvl   | Limit 3                                          |
  | monelm_ctvl   | Monitored element identifier                     |
  | perclim1_ctvl | Percent of limit 1                               |
  | perclim2_ctvl | Percent of limit 2                               |
  | perclim3_ctvl | Percent of limit 3                               |
  | prectg_ctvl   | Pre-contingency value                            |
  | type_ctv      | Violation type                                   |

### 4.31 dv.out

**Purpose:** Division records (model hierarchy)

**Header:**

```csv
#record dv, %SUBSCRIPT, mrid_dv, id_dv, i$area_dv
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | dv            | Record type identifier (always 'dv')             |
  | %SUBSCRIPT    | Division number (unique integer)                 |
  | mrid_dv       | EMS unique identifier for the division           |
  | id_dv         | Division name or code                            |
  | i$area_dv     | Area number or identifier                        |

### 4.32 type.out

**Purpose:** Contingency Type records (type of contingencies)

**Header:**

```csv
#record type, %SUBSCRIPT, id_type
```text

  | Field         | Description                                      |
  |-------------- |------------------------------------------------- |
  | type          | Record type identifier (always 'type')           |
  | %SUBSCRIPT    | Contingency type number (unique integer)         |
  | id_type       | Contingency type identifier                      |

---

## Undocumented .out files

- load.out (may be a synonym for ld.out)
- node.out (may be a synonym for nd.out)
- parent_contingency_group.out (may be a synonym for congrp.out)
- shunt.out (may be a synonym for cp.out)
- solutionasciitime.out (may be a synonym for solution_ascii_time.out)
- solutionquality.out (may be a synonym for solution_quality.out)

## 5. References

- See `
