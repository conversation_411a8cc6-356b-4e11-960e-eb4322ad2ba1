#!/usr/bin/env python3

from hdb_to_raw_pipeline import RawxBackend, PureFieldMapper

print("Testing field mapping on actual RAWX terminal data...")

# Load RAWX data
backend = RawxBackend()
backend.load('savnw_nb.rawx')
canonical = backend.to_canonical()

# Get terminal data
subterm_section = canonical.get('subterm', {})
terminal_fields = subterm_section.get('fields', [])
terminal_records = subterm_section.get('data', [])

print(f"Terminal fields: {terminal_fields}")
print(f"Terminal records: {len(terminal_records)}")

if terminal_records:
    print(f"First terminal record: {terminal_records[0]}")
    
    # Test field mapping
    field_mapper = PureFieldMapper()
    try:
        mapped_record = field_mapper.map_record('terminal', terminal_records[0], terminal_fields)
        print(f"✅ Mapping successful!")
        print(f"Mapped record: {mapped_record}")
        
        # Test specific field access
        print("\nField access test:")
        print(f"  substation_number: {mapped_record.get('substation_number')}")
        print(f"  node_number: {mapped_record.get('node_number')}")
        print(f"  terminal_type: {mapped_record.get('terminal_type')}")
        print(f"  equipment_id: {mapped_record.get('equipment_id')}")
        print(f"  bus_number: {mapped_record.get('bus_number')}")
        
    except Exception as e:
        print(f"❌ Mapping failed: {e}")
        import traceback
        traceback.print_exc()
else:
    print("❌ No terminal records found") 