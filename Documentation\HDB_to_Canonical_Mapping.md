# HDB Context to Canonical/PSSE Equipment Mapping

This document provides a comprehensive mapping between HDB Context (EMS export) equipment keys and the canonical/PSSE (psspy) backend data structure, including all data manipulations, logic, and edge cases. Canonical field names are matched to backend usage. This is derived from `case_utilities_nb.py`,`context.py`, and related modules.

---

## Table of Contents

- [Bus](#bus)
- [Node](#node)
- [Load](#load)
- [Fixed Shunt](#fixed-shunt)
- [Switched Shunt](#switched-shunt)
- [Machine (Generator)](#machine-generator)
- [Branch (Line Segment)](#branch-line-segment)
- [Zero Impedance Branch](#zero-impedance-branch)
- [Breaker/Switching Device](#breakerswitching-device)
- [Two-Winding Transformer](#two-winding-transformer)
- [Three-Winding Transformer](#three-winding-transformer)
- [Static Var System (SVC/STATCOM/FACTS)](#static-var-system-svcstatcomfacts)
- [DC Line / Converter](#dc-line--converter)
- [Ownership & Fractional Ownership](#ownership--fractional-ownership)
- [Limits & Ratings](#limits--ratings)
- [Contingency & Violation](#contingency--violation)
- [Analog/SCADA/Setpoint](#analogscadasetpoint)
- [Solution Quality & Time](#solution-quality--time)
- [Load Area & Fraction](#load-area--fraction)
- [Custom/Auxiliary/Extension Fields](#customauxiliaryextension-fields)
- [Error Handling & Fallback Logic](#error-handling--fallback-logic)
- [Concrete Mapping Examples](#concrete-mapping-examples)
- [Switching Device (Unified Model)](#switching-device-unified-model)

---

## Bus

| HDB Key                | Canonical Field           | Manipulation/Logic                                                                 |
|------------------------|--------------------------|------------------------------------------------------------------------------------|
| `Number`|`number`                 | Direct copy                                                                        |
| `Station`|`station`                | Direct copy                                                                        |
| `Id`|`id`                     | Direct copy                                                                        |
| `Base KV`|`base_kv`                | Direct copy                                                                        |
| `Area Number`|`area_number`            | Direct copy                                                                        |
| `Zone Number`|`zone_number`            | Direct copy                                                                        |
| `Owner Number`|`owner_number`           | Direct copy                                                                        |
| `Voltage Magnitude (pu)`|`per_unit_voltage_magnitude`| Direct copy                                                                   |
| `Voltage Angle (deg)`|`voltage_angle_in_degrees`| Direct copy                                                                   |
| *Name*                 | `name`| If`len(station + '_' + id) > 12`, trim station name to fit 12 chars total         |
| *Type*                 | `type_code`| Set to 4 if`per_unit_voltage_magnitude < 0.89`. May also be set to 2 (generator) or 3 (swing) by Machine logic. |

---

## Node

| HDB Key      | Canonical Field | Manipulation/Logic |
|--------------|-----------------|--------------------|
| `Station`|`station_number`| Map to station number |
| `Number`|`node_number`   | Direct copy        |
| `Id`|`name`          | Direct copy        |
| `Bus Number`|`bus`           | Direct copy        |
| *Status*     | `status`        | Set to 1 (active)  |

---

## Load

| HDB Key         | Canonical Field         | Manipulation/Logic                                                                 |
|-----------------|------------------------|------------------------------------------------------------------------------------|
| `Node`|`bus_number`           | Map via node record                                                                |
| `Number`|`load_id`|`str(number % 100)`                                                                |
| `Is ZIP Model`  |                        | If 'T', use ZIP logic below                                                        |
| `Total MW`|`constant_mw_load`| If ZIP:`Total MW * Constant P MW Multiplier`; else: direct copy                   |
| `Total MVAR`|`constant_mvar_load`| If ZIP:`Total MVAR * Constant P MVAR Multiplier`; else: direct copy               |
| `Id`|`is_scalable`          | Set to 0 if 'SS' or 'AUX' in Id                                                    |
|                 | `status`| Set to 0 if`Total MW == 0`                                                        |

---

## Fixed Shunt

| HDB Key         | Canonical Field           | Manipulation/Logic                |
|-----------------|--------------------------|-----------------------------------|
| `Node`|`bus_number`             | Map via node record               |
| `Number`|`identifier`|`str(number % 100)`               |
| `Nominal MVAR`|`nominal_mvar_shunt_value`| Direct copy                      |
| `Is Open`|`status`                 | 0 if 'T', else 1                  |

---

## Switched Shunt

- Created from Fixed Shunt via `FixedShuntConverter` logic.
- Each Fixed Shunt becomes a block in a Switched Shunt at the same bus.
- Block assignment logic (see code for block_1...block_8).
- Voltage limits copied from bus; upper limit uses `emergency_high_voltage_limit`.

---

## Machine (Generator)

| HDB Key                | Canonical Field                   | Manipulation/Logic                                                      |
|------------------------|------------------------------------|-------------------------------------------------------------------------|
| `Node`|`bus_number`                       | Map via node record                                                     |
| `Number`|`identifier`|`str(number % 100)`                                                     |
| `Is Open`|`status`| 0 if 'T' **or** both`MW Output`and`MVAR Output` are zero; else 1     |
| `Regulated Node Id`|`remote_regulated_bus_number`      | If present and != Node Id, map via node                                  |
| `Target Voltage`|`regulated_voltage_setpoint`       | If remote regulated, else use bus per_unit_voltage_magnitude             |
| `MW Output`|`mw_loading`                       | Direct copy                                                             |
| `MVAR Output`|`mvar_loading`                     | Direct copy                                                             |
| `MW Maximum`|`maximum_real_power_output`        | Direct copy                                                             |
| `MW Minimum`|`minimum_real_power_output`        | Direct copy                                                             |
| `MVAR Maximum`|`maximum_reactive_power_output`    | Direct copy                                                             |
| `MVAR Minimum`|`minimum_reactive_power_output`    | Direct copy                                                             |
| `Company`|`first_owner`                      | Map via company record                                                  |
|                        | `first_owner_fractional_ownership` | Set to 1                                                                |
|                        | `total_mva_base`                   | Set to 100                                                              |
| *Swing Bus*            | `bus.type_code`                    | Set to 3 if matches swing bus settings                                  |
|                        |                                    | Set to 2 (PV) otherwise                                                 |
| `Node`|`node_number`                      | Set to node["Number"] (if used in canonical backend)                    |

---

## Branch (Line Segment)

| HDB Key         | Canonical Field         | Manipulation/Logic                                 |
|-----------------|------------------------|----------------------------------------------------|
| `From Node`|`from_bus_number`      | Map via node record                                |
| `To Node`|`to_bus_number`        | Map via node record                                |
| `Id`|`circuit_id`           | Direct copy                                        |
| `Is Removed`|`status`               | 0 if 'T', else 1                                   |
| `Resistance`|`resistance`           | Divide by 100 (percent to per unit)                |
| `Reactance`|`reactance`            | Divide by 100 (percent to per unit)                |
| `Charging`|`charging`             | Divide by 100 (percent to per unit)                |

---

## Zero Impedance Branch

| HDB Key         | Canonical Field         | Manipulation/Logic                                 |
|-----------------|------------------------|----------------------------------------------------|
| `From Node`|`from_bus_number`      | Map via node record                                |
| `To Node`|`to_bus_number`        | Map via node record                                |
| `Id`|`circuit_id`           | Direct copy                                        |
| `Is Removed`|`status`               | 0 if 'T', else 1                                   |

---

## Breaker/Switching Device

| HDB Key         | Canonical Field         | Manipulation/Logic                                 |
|-----------------|------------------------|----------------------------------------------------|
| `From Node`|`from_bus_number`      | Map via node record                                |
| `To Node`|`to_bus_number`        | Map via node record                                |
| `Type`|`circuit_id`| '@' prefix for CB, '*' for switch, plus`number % 10`|
| `Is Open`|`status`               | 0 if 'T', else 1                                   |
| `Normally Open`|`normally_open`        | 'T' if normally open, 'F' if normally closed; may not always be provided |

**Note:** The `Normally Open` field is now included as a canonical field because it is critical for operational and analytical purposes (breaker categorization, outage handling, and switching logic). If not provided, downstream logic should handle its absence gracefully, but its presence is highly recommended for accurate modeling.

---

## Two-Winding Transformer

| HDB Key                | Canonical Field                   | Manipulation/Logic                                                      |
|------------------------|------------------------------------|-------------------------------------------------------------------------|
| `From Node`|`from_bus_number`                  | Map via node record                                                     |
| `To Node`|`to_bus_number`                    | Map via node record                                                     |
| `Number`|`circuit_id`|`str(number % 100)`                                                     |
| `Is Removed`|`status`                           | 0 if 'T', else 1                                                        |
| `From Node Tap`|`winding_one_*`                    | See tap logic below                                                     |
| `To Node Tap`|`winding_two_*`                    | See tap logic below                                                     |
| `Regulated Node Id`|`controlled_bus_number`            | If present, map via node; else 0                                        |
|                        | `adjustment_code`                  | 1 if regulated, else 0                                                  |
| `Id`|`name`                             | Direct copy                                                             |
| `Resistance`|`resistance`                       | Divide by 100                                                           |
| `Reactance`|`reactance`                        | Divide by 100                                                           |
| `Magnetizing Conductance`|`magnetizing_conductance`         | Divide by 100                                                           |
| `Magnetizing Susceptance`|`magnetizing_susceptance`         | Divide by 100                                                           |
|                        | `impedance_data_io_code`           | Set to 1                                                                |
|                        | `magnetizing_admittance_io_code`   | Set to 1                                                                |

**Tap Logic:**

- If tap present, use tap record to compute:
  - `number_of_tap_positions`,`winding_one_ratio_or_angle_lower_limit`,`winding_one_ratio_or_angle_upper_limit`,`winding_one_off_nominal_turns_ratio` (see code for formulas)
- If no tap, set `winding_one_off_nominal_turns_ratio = 1.0`

---

## Three-Winding Transformer

*Not directly mapped in the current codebase. If present, would require mapping three nodes and associated windings, with logic similar to two-winding but extended for three terminals. See future extension notes.*

---

## Static Var System (SVC/STATCOM/FACTS)

- Created from `svs`and`analog` records.
- Mapped as Machine records with special type or flag.
- Setpoints and SCADA analogs mapped via `analog` context.

---

## DC Line / Converter

*Not directly mapped in the current codebase. If present, would require mapping converter terminals, control points, and associated logic. See future extension notes.*

---

## Ownership & Fractional Ownership

| HDB Key         | Canonical Field         | Manipulation/Logic                                 |
|-----------------|------------------------|----------------------------------------------------|
| `Company`|`first_owner`          | Map via company record                             |
|                 | `first_owner_fractional_ownership` | Set to 1                                 |

---

## Limits & Ratings

- Limits (normal/emergency) for branches, transformers, nodes, etc. are mapped from `altlim`,`lnlim`,`xflim`,`ndlim`,`cblim` records.
- Alternate limits applied via `AlternateLimitCaseGenerator` and related logic.
- Per-unit and percent conversions as needed.

---

## Contingency & Violation

- Contingency records (`ctg`,`ctgl`,`ctgrp`,`ctvl`) mapped to canonical contingency and violation structures.
- Linked via forward pointers and group IDs.

---

## Analog/SCADA/Setpoint

- Analog records (`analog`) mapped to setpoints, SCADA values, and SVC/STATCOM controls.
- Used for dynamic and real-time data.

---

## Solution Quality & Time

- Solution quality and time records mapped from `solution_quality`and`solution_ascii_time`.
- Used for diagnostics and validation.

---

## Load Area & Fraction

- Load area records (`ldarea`) used to distribute system load hierarchically.
- Fractional assignment logic as per code.

---

## Custom/Auxiliary/Extension Fields

- Any extra fields in HDB context not mapped above are available for extension.
- User-defined fields can be added to canonical records as needed.

---

## Error Handling & Fallback Logic

- If a required field is missing or blank, default/fallback values are used (e.g., status=0, ratio=1.0).
- All mapping logic includes robust error handling and logging.

---

## Concrete Mapping Examples

### Example: Load Mapping

```python
# HDB record
{
    "Node": "STN1_001",
    "Number": 101,
    "Is ZIP Model": "F",
    "Total MW": 50.0,
    "Total MVAR": 10.0,
    "Id": "L1"
}

# Canonical mapping
Load(
    bus_number=context.node.records["STN1_001"]["Number"],
    load_id="1",
    constant_mw_load=50.0,
    constant_mvar_load=10.0,
    is_scalable=1,
    status=1
)
```text

### Example: Two-Winding Transformer Tap Logic

```python
if transformer_record["From Node Tap"]:
    from_node_tap_record = context.tap_type.records[transformer_record["From Node Tap"]]
    virtual_minimum_tap_position = from_node_tap_record['Minimum Position'] - from_node_tap_record['Nominal Position']
    virtual_maximum_tap_position = from_node_tap_record['Maximum Position'] - from_node_tap_record['Nominal Position']
    virtual_actual_tap_position = transformer_record['From Node Tap Position'] - from_node_tap_record['Nominal Position']
    transformer.number_of_tap_positions = from_node_tap_record["Maximum Position"] - from_node_tap_record["Minimum Position"] + 1
    transformer.winding_one_ratio_or_angle_lower_limit = 1.0 + (virtual_minimum_tap_position * (from_node_tap_record['Step Size']))
    transformer.winding_one_ratio_or_angle_upper_limit = 1.0 + (virtual_maximum_tap_position * (from_node_tap_record['Step Size']))
    transformer.winding_one_off_nominal_turns_ratio = 1.0 + (virtual_actual_tap_position * (from_node_tap_record['Step Size']))
```text

---

*For future extensions (three-winding transformers, DC lines, etc.), follow the same mapping and documentation pattern as above, ensuring all canonical names match backend usage.*

<a id="switching-device-unified-model"></a>

## Switching Device (Unified Model)

All switching devices (substation and system) are stored as a single canonical type: `switching_device`.

| HDB Key         | Canonical Field         | Manipulation/Logic                                 |
|-----------------|------------------------|----------------------------------------------------|
| `From Node`|`from_node_number`     | Optional; map via node record if available         |
| `To Node`|`to_node_number`       | Optional; map via node record if available         |
| `From Bus`|`from_bus_number`      | Required; bus number at one end                    |
| `To Bus`|`to_bus_number`        | Required; bus number at other end                  |
| `Station`|`station_number`       | Optional; substation number if available           |
| `Id`|`circuit_id`           | Direct copy                                        |
| `Is Open`|`status`               | 0 if 'T', else 1                                   |
| `Normally Open`|`normally_open`        | 'T' if normally open, 'F' if normally closed; may not always be provided |
| `Name`|`name`                 | Direct copy                                        |
| `Type`|`type`                 | Device type code/label if available                |
| `Rating`|`rating`               | Device rating if available                         |
| `Reactance`|`reactance`            | Device reactance if available                      |

**Note:**

- The distinction between substation and system switches is *derived*:

```python
if from_bus_number == to_bus_number:
    device_type = 'substation_switch'
else:
    device_type = 'system_switch'
```text

- Node and station fields are optional and used if available.
- All switching devices are handled identically in the canonical database; format-specific logic (e.g., for PSSE) splits them as needed on export.
