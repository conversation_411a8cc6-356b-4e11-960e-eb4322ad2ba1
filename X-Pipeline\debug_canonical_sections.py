#!/usr/bin/env python3

from hdb_to_raw_pipeline import RawxBackend

print("Checking canonical sections...")

# Load RAWX data
backend = RawxBackend()
backend.load('savnw_nb.rawx')

print("Raw RAWX sections:")
for section_name, section_data in backend.data.items():
    if isinstance(section_data, dict) and 'data' in section_data:
        count = len(section_data.get('data', []))
        print(f"  {section_name}: {count} records")

canonical = backend.to_canonical()

print(f"\nCanonical sections ({len(canonical)} total):")
for section_name, section_data in canonical.items():
    if isinstance(section_data, dict) and 'data' in section_data:
        count = len(section_data.get('data', []))
        fields = section_data.get('fields', [])
        print(f"  {section_name}: {count} records, fields: {fields[:3]}...")  # Show first 3 fields
    else:
        print(f"  {section_name}: {type(section_data)}")

# Check specifically for terminal-related sections
terminal_sections = ['terminal', 'subterm', 'substation_terminal']
print(f"\nTerminal-related sections:")
for section in terminal_sections:
    if section in canonical:
        section_data = canonical[section]
        if isinstance(section_data, dict):
            count = len(section_data.get('data', []))
            print(f"  ✅ {section}: {count} records")
        else:
            print(f"  ✅ {section}: {type(section_data)}")
    else:
        print(f"  ❌ {section}: NOT FOUND") 