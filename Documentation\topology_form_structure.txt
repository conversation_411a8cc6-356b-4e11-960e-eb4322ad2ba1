Excel Topology Package Tool - Sheet Structure
============================================

Sheet 1: Device Entry (Landing Page)
------------------------------------
- Cell A1: Instructions ("Fill out the form below to add, edit, or remove a device. Select the device type and action, then enter the required information.")
- Cell A2: Label: Device Type
- Cell B2: Dropdown (Data Validation) fed from Sheet3, column A (Equipment Types)
- Cell A3: Label: Action
- Cell B3: Dropdown (Data Validation): Add, Edit, Remove
- Rows 5–15: Dynamic input fields for device parameters (labels and input cells). Labels are populated from Sheet3 based on selected device type.
- Row 17+: Placeholder shapes for Add, Edit, Remove, Export, Run Study (visual only for now)

Sheet 2: Device List (Storage)
------------------------------
- Row 1: Column headers (Device Type, Action, plus all possible parameter fields from Sheet3)
- Rows 2+: Table of all added devices (user copies from Sheet1 or enters directly)
- Cell A1: Instructions ("This sheet stores all device entries. Edit cells directly to update device information. Use Excel's table features to sort or filter.")

Sheet 3: Lookup Table (Equipment Types & Fields)
------------------------------------------------
- Column A: Equipment Type (e.g., Bus, Branch, Transformer, etc.)
- Columns B–Z: Field names for each equipment type (e.g., Name, ID, Parameter1, Parameter2, etc.)
- Each row: Defines the fields required for that equipment type
- This sheet feeds the dropdown in Sheet1 and provides the field labels for dynamic input area

Usage Notes
-----------
- Sheet1's device type dropdown is populated from Sheet3 column A.
- When a device type is selected, the parameter labels in Sheet1 update to show only the relevant fields (manual for now, can be automated with formulas or macros later).
- Sheet2 serves as the running list of all devices, with columns for all possible fields.
- Sheet3 is the master lookup for equipment types and their associated fields.

This structure allows for easy extension and later automation with macros or Python scripts. 