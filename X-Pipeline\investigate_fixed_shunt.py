#!/usr/bin/env python3
"""
Investigation of fixed shunt data issue in HDB export.
"""

def investigate_fixed_shunt():
    """Investigate why fixed shunt data is missing from HDB export."""
    
    print('=== FIXED SHUNT INVESTIGATION ===')
    print()
    
    try:
        from hdb_to_raw_pipeline import HdbBackend
        
        # Load HDB data
        backend = HdbBackend()
        backend.load('hdbcontext_original.hdb')
        print('1. HDB Source Data:')
        shunt_data = backend.data.get('shunt', {})
        print(f'   HDB shunt records: {len(shunt_data)}')
        if shunt_data:
            first_key = list(shunt_data.keys())[0]
            first_record = shunt_data[first_key]
            if isinstance(first_record, dict):
                print(f'   Sample keys: {list(first_record.keys())}')
                print(f'   Sample G: {first_record.get("G", "N/A")}')
                print(f'   Sample B: {first_record.get("B", "N/A")}')
        print()

        # Check canonical conversion
        canonical_data = backend.to_canonical()
        print('2. Canonical Data:')
        fixed_shunt_data = canonical_data.get('fixed_shunt', {})
        if fixed_shunt_data:
            data_records = fixed_shunt_data.get('data', [])
            data_fields = fixed_shunt_data.get('fields', [])
            print(f'   Canonical fixed_shunt records: {len(data_records)}')
            print(f'   Fields: {data_fields}')
            if data_records:
                first_record = data_records[0]
                print(f'   First record: {first_record}')
        else:
            print('   No fixed_shunt section in canonical data!')
        
        print()
        
        # Check field mapping
        from hdb_to_raw_pipeline import FIELD_MAP
        print('3. Field Mapping Status:')
        print(f'   fixed_shunt mapping exists: {"fixed_shunt" in FIELD_MAP}')
        print(f'   shunt mapping exists: {"shunt" in FIELD_MAP}')
        if 'fixed_shunt' in FIELD_MAP:
            fields = list(FIELD_MAP['fixed_shunt'].keys())
            print(f'   fixed_shunt fields: {fields}')
        if 'shunt' in FIELD_MAP:
            fields = list(FIELD_MAP['shunt'].keys())
            print(f'   shunt fields: {fields}')
        
        # Test field mapping directly
        if shunt_data:
            print()
            print('4. Field Mapping Test:')
            test_record = list(shunt_data.values())[0]
            print(f'   Test record: {test_record}')
            
            # Try both possible field mappings
            for mapping_name in ['fixed_shunt', 'shunt']:
                if mapping_name in FIELD_MAP:
                    try:
                        mapped_record = backend.field_mapper.map_record(mapping_name, test_record)
                        print(f'   {mapping_name} mapping result: {mapped_record}')
                    except Exception as e:
                        print(f'   {mapping_name} mapping failed: {e}')
        
        print()
        print('=== INVESTIGATION COMPLETE ===')
        
    except Exception as e:
        print(f'Investigation failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_fixed_shunt() 