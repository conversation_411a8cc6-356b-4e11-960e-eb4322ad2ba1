#!/usr/bin/env python3
"""
Test script to verify that zero impedance branches have proper ownership fractions.

This script tests both:
1. Zero impedance branches from HDB data (via ZeroImpedanceBranchConverter)
2. Zero impedance branches created from switches (via ModelTransformations)
"""

import sys
import os
from pathlib import Path

# Add the current directory to the path so we can import from hdb_to_raw_pipeline
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import ZeroImpedanceBranchConverter, ModelTransformations

def test_zero_impedance_branch_converter():
    """Test that ZeroImpedanceBranchConverter adds ownership fractions."""
    print("Testing ZeroImpedanceBranchConverter ownership fractions...")
    
    # Create sample HDB data
    hdb_context = {
        'zero_impedance_branches': {
            'zib1': {
                'From Node': 'NODE1',
                'To Node': 'NODE2', 
                'From Station': 'STATION1',
                'To Station': 'STATION1',
                'Id': 'ZIB1',
                'Owner': 2  # Test with owner 2
            }
        },
        'node': {
            'NODE1': {
                'Number': 1,
                'Bus Number': 101
            },
            'NODE2': {
                'Number': 2,
                'Bus Number': 102
            }
        },
        'station': {
            'STATION1': {
                'Name': 'STATION1',
                'Number': 1
            }
        }
    }
    
    # Create converter
    converter = ZeroImpedanceBranchConverter(hdb_context)
    result = converter.convert()
    
    print(f"Fields: {result['fields']}")
    print(f"Data: {result['data']}")
    
    # Check that ownership fields are present
    expected_ownership_fields = ['owner_1', 'fraction_1', 'owner_2', 'fraction_2', 'owner_3', 'fraction_3', 'owner_4', 'fraction_4']
    for field in expected_ownership_fields:
        if field not in result['fields']:
            print(f"❌ Missing ownership field: {field}")
            return False
    
    # Check that ownership data is correct
    if result['data']:
        record = result['data'][0]
        fields = result['fields']
        
        # Find ownership field indices
        o1_idx = fields.index('owner_1')
        f1_idx = fields.index('fraction_1')
        
        owner_1 = record[o1_idx]
        fraction_1 = record[f1_idx]
        
        print(f"Owner 1: {owner_1}, Fraction 1: {fraction_1}")
        
        if owner_1 != 2 or fraction_1 != 1.0:
            print(f"❌ Incorrect ownership data: owner_1={owner_1}, fraction_1={fraction_1}")
            return False
    
    print("✅ ZeroImpedanceBranchConverter ownership fractions test passed")
    return True

def test_model_transformations_ownership():
    """Test that ModelTransformations adds ownership fractions to ZBR branches."""
    print("\nTesting ModelTransformations ZBR ownership fractions...")
    
    # Create sample canonical data with switching devices
    canonical_data = {
        'switching_device': {
            'fields': ['isub', 'from_node', 'to_node', 'ckt', 'status', 'name'],
            'data': [
                [1, 1, 2, '1', 1, 'SWITCH1']
            ]
        },
        'substation': {
            'fields': ['isub', 'name'],
            'data': [
                [1, 'SUBSTATION1']
            ]
        },
        'node': {
            'fields': ['isub', 'inode', 'ibus'],
            'data': [
                [1, 1, 101],
                [1, 2, 102]
            ]
        }
    }
    
    # Test the hybrid conversion
    result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
    
    if not result.success:
        print(f"❌ Hybrid conversion failed: {result.warnings}")
        return False
    
    # Check that ac_line section has ownership fields
    ac_line = result.canonical_data.get('ac_line', {})
    if not ac_line:
        print("❌ No ac_line section found")
        return False
    
    fields = ac_line.get('fields', [])
    data = ac_line.get('data', [])
    
    print(f"AC Line fields: {fields}")
    print(f"AC Line data count: {len(data)}")
    
    # Check for ownership fields (using canonical field names)
    ownership_fields = ['owner_1', 'fraction_1', 'owner_2', 'fraction_2', 'owner_3', 'fraction_3', 'owner_4', 'fraction_4']
    missing_fields = [f for f in ownership_fields if f not in fields]
    
    if missing_fields:
        print(f"❌ Missing ownership fields: {missing_fields}")
        return False
    
    # Check that ZBR branches have proper ownership data
    zbr_count = 0
    for record in data:
        if len(record) >= len(fields):
            # Check if this is a zero impedance branch (circuit_id = "ZZ")
            ckt_idx = fields.index('circuit_id') if 'circuit_id' in fields else -1
            if ckt_idx >= 0 and record[ckt_idx] == "ZZ":
                zbr_count += 1
                
                # Check ownership fractions
                o1_idx = fields.index('owner_1')
                f1_idx = fields.index('fraction_1')
                
                o1 = record[o1_idx]
                f1 = record[f1_idx]
                
                print(f"ZBR {zbr_count}: owner_1={o1}, fraction_1={f1}")
                
                if o1 != 1 or f1 != 1.0:
                    print(f"❌ Incorrect ZBR ownership: owner_1={o1}, fraction_1={f1}")
                    return False
    
    if zbr_count == 0:
        print("❌ No zero impedance branches found")
        return False
    
    print(f"✅ ModelTransformations ZBR ownership test passed ({zbr_count} ZBR branches)")
    return True

def main():
    """Run all tests."""
    print("Testing Zero Impedance Branch Ownership Fractions")
    print("=" * 50)
    
    success = True
    
    # Test ZeroImpedanceBranchConverter
    if not test_zero_impedance_branch_converter():
        success = False
    
    # Test ModelTransformations
    if not test_model_transformations_ownership():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main() 