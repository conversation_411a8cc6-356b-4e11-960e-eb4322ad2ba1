#!/usr/bin/env python3
"""Debug script to investigate substation data in the canonical format."""

from hdb_to_raw_pipeline import Backend

def debug_substation_data():
    """Debug substation data in the canonical format."""
    print("🔍 Debugging substation data...")
    
    backend = Backend.load('sample_nb.rawx')
    # Access the actual backend instance and call to_canonical()
    canonical_data = backend._backend.to_canonical()
    
    print(f"📊 Canonical data sections:")
    for section, data in canonical_data.items():
        if isinstance(data, dict) and 'data' in data:
            print(f"  {section}: {len(data.get('data', []))} records")
        elif isinstance(data, list):
            print(f"  {section}: {len(data)} records")
        else:
            print(f"  {section}: {type(data)}")
    
    print(f"\n🏢 Substation data details:")
    substation_data = canonical_data.get('substation', {})
    print(f"  Records: {len(substation_data.get('data', []))}")
    
    if substation_data.get('data'):
        print(f"  Fields: {substation_data.get('fields', [])}")
        print(f"  First record: {substation_data['data'][0]}")
        if len(substation_data['data']) > 1:
            print(f"  Second record: {substation_data['data'][1]}")
    else:
        print("  ❌ No substation data found!")
        
        # Check if 'sub' section exists instead
        sub_data = canonical_data.get('sub', {})
        if sub_data:
            print(f"  💡 Found 'sub' section instead:")
            print(f"     Records: {len(sub_data.get('data', []))}")
            if sub_data.get('data'):
                print(f"     First record: {sub_data['data'][0]}")
    
    # Check other substation-related sections
    print(f"\n🔧 Other substation-related sections:")
    for section_name in ['subnode', 'subswd', 'subterm', 'node', 'switching_device', 'terminal']:
        section_data = canonical_data.get(section_name, {})
        if section_data:
            records = len(section_data.get('data', []))
            print(f"  {section_name}: {records} records")
            if records > 0 and section_data.get('data'):
                print(f"     First record: {section_data['data'][0]}")

if __name__ == "__main__":
    debug_substation_data() 