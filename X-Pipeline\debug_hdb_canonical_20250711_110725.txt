DEBUG: HDB Backend Canonical Output
============================================================
Debug output will be saved to: debug_hdb_canonical_20250711_110725.txt
Creating HDB backend with file: hdbcontext_original.hdb...
HDB backend created successfully
Getting canonical data...
Canonical conversion successful

TOTAL SECTIONS: 15
   ac_line: 2314 records, 25 fields
      Fields: ['ibus', 'jbus', 'kbus', 'ckt', 'r', 'x', 'b', 'ratea', 'rateb', 'ratec', 'stat', 'o1', 'f1', 'o2', 'f2', 'o3', 'f3', 'o4', 'f4', 'name', 'len', 'from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   area: 8 records, 5 fields
      Fields: ['i', 'isw', 'pdes', 'ptol', 'arname']
   bus: 3197 records, 15 fields
      Fields: ['ibus', 'name', 'baskv', 'ide', 'gl', 'bl', 'area', 'zone', 'owner', 'vm', 'va', 'nvhi', 'nvlo', 'evhi', 'evlo']
   fixed_shunt: 363 records, 7 fields
      Fields: ['ibus', 'shunt_id', 'stat', 'gl', 'bl', 'substation_id', 'node_id']
   generator: 478 records, 24 fields
      Fields: ['ibus', 'genid', 'pg', 'qg', 'qt', 'qb', 'vs', 'pt', 'pb', 'stat', 'rmpct', 'baslod', 'o1', 'f1', 'o2', 'f2', 'o3', 'f3', 'o4', 'f4', 'wmod', 'wpf', 'substation_id', 'node_id']
   load: 2473 records, 20 fields
      Fields: ['ibus', 'loadid', 'stat', 'area', 'zone', 'pl', 'ql', 'ip', 'iq', 'yp', 'yq', 'owner', 'scale', 'intrpt', 'dgenp', 'dgenq', 'dgenm', 'loadtype', 'substation_id', 'node_id']
   node: 17519 records, 7 fields
      Fields: ['isub', 'inode', 'name', 'ibus', 'stat', 'vm', 'va']
   node_limits: 1788 records, 5 fields
      Fields: ['node_id', 'nvhi', 'nvlo', 'evhi', 'evlo']
   owner: 8 records, 2 fields
      Fields: ['i', 'owname']
   substation: 1665 records, 5 fields
      Fields: ['isub', 'name', 'lati', 'long', 'srg']
   subterm: 10294 records, 7 fields
      Fields: ['isub', 'inode', 'type', 'eqid', 'ibus', 'jbus', 'kbus']
   switching_device: 15540 records, 12 fields
      Fields: ['isub', 'inode', 'jnode', 'swdid', 'name', 'type', 'stat', 'nstat', 'xpu', 'rate1', 'rate2', 'rate3']
   transformer: 1367 records, 127 fields
      Fields: ['ibus', 'jbus', 'kbus', 'ckt', 'cw', 'cz', 'cm', 'mag1', 'mag2', 'nmet', 'name', 'stat', 'o1', 'f1', 'o2', 'f2', 'o3', 'f3', 'o4', 'f4', 'vecgrp', 'zcod', 'r1_2', 'x1_2', 'sbase1_2', 'r2_3', 'x2_3', 'sbase2_3', 'r3_1', 'x3_1', 'sbase3_1', 'vmstar', 'anstar', 'windv1', 'nomv1', 'ang1', 'wdg1rate1', 'wdg1rate2', 'wdg1rate3', 'wdg1rate4', 'wdg1rate5', 'wdg1rate6', 'wdg1rate7', 'wdg1rate8', 'wdg1rate9', 'wdg1rate10', 'wdg1rate11', 'wdg1rate12', 'wdg1rate13', 'wdg1rate14', 'wdg1rate15', 'cod1', 'cont1', 'node1', 'rma1', 'rmi1', 'vma1', 'vmi1', 'ntp1', 'tab1', 'cr1', 'cx1', 'cnxa1', 'windv2', 'nomv2', 'ang2', 'wdg2rate1', 'wdg2rate2', 'wdg2rate3', 'wdg2rate4', 'wdg2rate5', 'wdg2rate6', 'wdg2rate7', 'wdg2rate8', 'wdg2rate9', 'wdg2rate10', 'wdg2rate11', 'wdg2rate12', 'wdg2rate13', 'wdg2rate14', 'wdg2rate15', 'cod2', 'cont2', 'node2', 'rma2', 'rmi2', 'vma2', 'vmi2', 'ntp2', 'tab2', 'cr2', 'cx2', 'cnxa2', 'windv3', 'nomv3', 'ang3', 'wdg3rate1', 'wdg3rate2', 'wdg3rate3', 'wdg3rate4', 'wdg3rate5', 'wdg3rate6', 'wdg3rate7', 'wdg3rate8', 'wdg3rate9', 'wdg3rate10', 'wdg3rate11', 'wdg3rate12', 'wdg3rate13', 'wdg3rate14', 'wdg3rate15', 'cod3', 'cont3', 'node3', 'rma3', 'rmi3', 'vma3', 'vmi3', 'ntp3', 'tab3', 'cr3', 'cx3', 'cnxa3', 'from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   zero_impedance_branch: 86 records, 9 fields
      Fields: ['ibus', 'jbus', 'ckt', 'stat', 'name', 'from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   zone: 1665 records, 2 fields
      Fields: ['i', 'zoname']

CHECKING KEY SECTIONS:
   substation: FOUND - 1665 records
      First record: [1, 'ROGERSRD', 0.0, 0.0, 0.0]
   node: FOUND - 17519 records
      First record: [1, 1, 'ROGERSRD_9', 3, 1, 1.0, 0.0]
   switching_device: FOUND - 15540 records
      First record: [1, 11, 4, '@1', 'ROGERSRD_CB_1', 1, 1, 1, 0.0001, 3775.8706, 3810.5117, 4641.896]
   terminal: NOT FOUND
   subterm: FOUND - 10294 records
      First record: [3, 61, 'L', 'TX3', 8, None, None]

SEARCHING FOR NODE/SWITCHING DATA IN OTHER SECTIONS:
   load: Contains node/switching fields - 2473 records
      Relevant fields: ['substation_id', 'node_id']
   generator: Contains node/switching fields - 478 records
      Relevant fields: ['substation_id', 'node_id']
   ac_line: Contains node/switching fields - 2314 records
      Relevant fields: ['from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   transformer: Contains node/switching fields - 1367 records
      Relevant fields: ['node1', 'node2', 'node3', 'from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   fixed_shunt: Contains node/switching fields - 363 records
      Relevant fields: ['substation_id', 'node_id']
   zero_impedance_branch: Contains node/switching fields - 86 records
      Relevant fields: ['from_substation_id', 'from_node_id', 'to_substation_id', 'to_node_id']
   node: Contains node/switching fields - 17519 records
      Relevant fields: ['inode']
   switching_device: Contains node/switching fields - 15540 records
      Relevant fields: ['inode', 'jnode']
   subterm: Contains node/switching fields - 10294 records
      Relevant fields: ['inode']
   node_limits: Contains node/switching fields - 1788 records
      Relevant fields: ['node_id']

============================================================
DEBUG: Complete
Complete debug output saved to: debug_hdb_canonical_20250711_110725.txt
