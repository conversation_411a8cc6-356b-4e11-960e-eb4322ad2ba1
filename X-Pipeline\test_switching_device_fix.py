#!/usr/bin/env python3
"""
Test script to verify switching device processing fix.
"""

import logging
from pathlib import Path
import sys

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import HdbBackend, ModelTransformations

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_switching_device_processing():
    """Test that switching devices are being processed correctly."""
    
    print("🧪 Testing Switching Device Processing Fix")
    print("="*50)
    
    # Create HDB backend
    hdb_backend = HdbBackend(use_sample_data=True)
    
    # Get canonical data from HDB backend
    print("📂 Loading HDB canonical data...")
    canonical_data = hdb_backend.get_canonical_data()
    
    # Check if we have switching device data
    has_switching_device_data = ('switching_device' in canonical_data and 
                               canonical_data['switching_device'] and 
                               canonical_data['switching_device'].get('data'))
    
    print(f"✅ Has switching device data: {has_switching_device_data}")
    
    if has_switching_device_data:
        switching_devices = canonical_data['switching_device']['data']
        print(f"📊 Found {len(switching_devices)} switching devices")
        print("📋 First few switching devices:")
        for i, device in enumerate(switching_devices[:3]):
            print(f"   {i+1}: {device}")
    else:
        print("❌ No switching device data found")
        # Check what sections we do have
        print("📋 Available sections:")
        for section_name in sorted(canonical_data.keys()):
            if canonical_data[section_name] and canonical_data[section_name].get('data'):
                count = len(canonical_data[section_name]['data'])
                print(f"   {section_name}: {count} records")
    
    # Test model type detection
    print("\n🔍 Testing model type detection...")
    model_type = ModelTransformations.detect_model_type(canonical_data)
    print(f"📊 Detected model type: {model_type}")
    
    # Test hybrid conversion
    print("\n🔄 Testing hybrid conversion...")
    result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
    
    if result.success:
        print("✅ Hybrid conversion successful")
        print(f"📊 Warnings: {len(result.warnings)}")
        for warning in result.warnings[:5]:  # Show first 5 warnings
            print(f"   ⚠️  {warning}")
        
        # Check if ZBR branches were created
        hybrid_data = result.canonical_data
        if 'ac_line' in hybrid_data and hybrid_data['ac_line'].get('data'):
            branches = hybrid_data['ac_line']['data']
            print(f"📊 Total branches in hybrid model: {len(branches)}")
            
            # Count ZBR branches (zero resistance branches)
            zbr_count = 0
            for branch in branches:
                if len(branch) >= 4 and branch[3] == 0.0:  # r field is 0
                    zbr_count += 1
            
            print(f"⚡ ZBR branches created: {zbr_count}")
            
            if zbr_count > 0:
                print("🎉 SUCCESS: Switching devices are being converted to ZBR branches!")
            else:
                print("❌ ISSUE: No ZBR branches found - switching devices may not be processed")
        else:
            print("❌ No AC line data found in hybrid model")
    else:
        print("❌ Hybrid conversion failed")
        print(f"📊 Errors: {result.errors}")
    
    print("\n" + "="*50)
    print("🏁 Test Complete")

if __name__ == "__main__":
    test_switching_device_processing() 