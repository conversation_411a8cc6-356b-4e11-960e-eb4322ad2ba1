#!/usr/bin/env python3
"""
Simple test to verify PureFieldMapper is working correctly.
"""

from hdb_to_raw_pipeline import PureFieldMapper

def test_field_mapper():
    """Test that PureFieldMapper can get canonical fields."""
    print("Testing PureFieldMapper...")
    
    field_mapper = PureFieldMapper()
    ac_line_fields = field_mapper.get_canonical_fields('ac_line')
    
    print(f"ac_line canonical fields: {ac_line_fields}")
    
    # Check that ownership fields are included
    ownership_fields = ['owner_1', 'fraction_1', 'owner_2', 'fraction_2', 'owner_3', 'fraction_3', 'owner_4', 'fraction_4']
    missing_fields = [f for f in ownership_fields if f not in ac_line_fields]
    
    if missing_fields:
        print(f"❌ Missing ownership fields: {missing_fields}")
        return False
    else:
        print("✅ All ownership fields found in ac_line canonical fields")
        return True

if __name__ == "__main__":
    success = test_field_mapper()
    if success:
        print("✅ PureFieldMapper test passed")
    else:
        print("❌ PureFieldMapper test failed") 