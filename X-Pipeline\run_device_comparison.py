#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Direct device-level comparison with file output.
No Unicode characters to avoid encoding issues.
"""

import os
import sys
from datetime import datetime

# Import the comparison function from the main script
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from run_comparison import parse_raw_file, comprehensive_device_comparison

def main():
    """Run device comparisons and save to file."""
    
    # Create output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output_demo/device_comparison_{timestamp}.txt"
    
    print(f"Running device-level comparisons...")
    print(f"Output will be saved to: {output_file}")
    
    # Redirect stdout to file
    original_stdout = sys.stdout
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            sys.stdout = f  # Redirect output to file
            
            print("=" * 80)
            print("DEVICE-LEVEL FIELD COMPARISON REPORT")
            print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            print()
            
            # Define files to compare
            comparisons = [
                # HDB exports vs HDB references (hybrid model is appropriate for HDB)
                {
                    'name': 'HDB v35_hybrid_current vs output_demo_reference',
                    'file1': 'output_demo/HDB_export_v35_hybrid_current.raw',
                    'file2': 'output_demo_reference/HDB_export_v35_hybrid_current.raw'
                },
                {
                    'name': 'HDB v35_hybrid_normal vs output_demo_reference',
                    'file1': 'output_demo/HDB_export_v35_hybrid_normal.raw',
                    'file2': 'output_demo_reference/HDB_export_v35_hybrid_normal.raw'
                },
                {
                    'name': 'HDB v33_hybrid_current vs output_demo_reference',
                    'file1': 'output_demo/HDB_export_v33_hybrid_current.raw',
                    'file2': 'output_demo_reference/HDB_export_v33_hybrid_current.raw'
                },
                {
                    'name': 'HDB v35_hybrid_current vs psse_33.raw',
                    'file1': 'output_demo/HDB_export_v35_hybrid_current.raw',
                    'file2': 'psse_33.raw'  # In X-Pipeline root folder
                },
                
                # RAWX exports vs RAWX references (node-breaker model is appropriate for RAWX)
                {
                    'name': 'RAWX v35_node_breaker vs output_demo_reference',
                    'file1': 'output_demo/RAWX_export_v35_node_breaker.raw',
                    'file2': 'output_demo_reference/RAWX_export_v35_node_breaker.raw'
                },
                {
                    'name': 'RAWX v35_bus_branch vs output_demo_reference',
                    'file1': 'output_demo/RAWX_export_v35_bus_branch.raw',
                    'file2': 'output_demo_reference/RAWX_export_v35_bus_branch.raw'
                },
                {
                    'name': 'RAWX v33_hybrid_current vs output_demo_reference',
                    'file1': 'output_demo/RAWX_export_v33_hybrid_current.raw',
                    'file2': 'output_demo_reference/RAWX_export_v33_hybrid_current.raw'
                },
                {
                    'name': 'RAWX v35_node_breaker vs savnw_nb.raw',
                    'file1': 'output_demo/RAWX_export_v35_node_breaker.raw',
                    'file2': 'savnw_nb.raw'  # In X-Pipeline root folder
                }
            ]
            
            # Run comparisons
            for comp in comparisons:
                if os.path.exists(comp['file1']) and os.path.exists(comp['file2']):
                    print("\n" + "=" * 60)
                    comprehensive_device_comparison(comp['file1'], comp['file2'], comp['name'])
                else:
                    print(f"\nSkipping {comp['name']}: One or both files not found")
                    if not os.path.exists(comp['file1']):
                        print(f"  Missing: {comp['file1']}")
                    if not os.path.exists(comp['file2']):
                        print(f"  Missing: {comp['file2']}")
            
            print("\n" + "=" * 80)
            print("END OF REPORT")
            
    finally:
        sys.stdout = original_stdout  # Restore stdout
    
    # Report results
    file_size = os.path.getsize(output_file)
    with open(output_file, 'r', encoding='utf-8') as f:
        line_count = sum(1 for _ in f)
    
    print(f"\nComparison complete!")
    print(f"Results saved to: {output_file}")
    print(f"File size: {file_size:,} bytes")
    print(f"Total lines: {line_count:,}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 