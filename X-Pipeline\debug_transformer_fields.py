#!/usr/bin/env python3
"""Debug script to check transformer field mappings."""

import sys
import os
sys.path.append('.')

from hdb_to_raw_pipeline import load_hdb_data, get_field_mapper

def debug_transformer_fields():
    """Debug transformer field mapping."""
    print("🔍 Debugging transformer field mappings...")
    
    # Load HDB data
    hdb_context = load_hdb_data("sample_nb.rawx")
    field_mapper = get_field_mapper()
    
    # Get transformer data
    transformers = hdb_context.get('xfm2w', {})
    
    print(f"📊 Found {len(transformers)} 2-winding transformers")
    
    # Check first few transformers
    for i, (key, xfm) in enumerate(list(transformers.items())[:3]):
        print(f"\n🔧 Transformer {i+1}: {key}")
        print(f"   Raw HDB record keys: {list(xfm.keys())}")
        
        # Check specific fields
        relevant_fields = ['winding_config', 'impedance_config', 'magnetizing_config', 'metered_winding',
                          'cw', 'cz', 'cm', 'nmet', 'nmetr', 'metered_end', 'metered_winding']
        
        for field in relevant_fields:
            if field in xfm:
                print(f"   {field}: {xfm[field]}")
        
        # Try field mapping
        print(f"   Field mapping result:")
        try:
            canonical_record = field_mapper.map_record('transformer', xfm)
            for field in ['winding_config', 'impedance_config', 'magnetizing_config', 'metered_winding']:
                if field in canonical_record:
                    print(f"     {field}: {canonical_record[field]}")
        except Exception as e:
            print(f"     Error: {e}")

if __name__ == "__main__":
    debug_transformer_fields() 