"""
RAW Format Export Module.
Supports PSS/E RAW format versions 33, 34, and 35 with proper extensibility.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple


logger = logging.getLogger(__name__)


class RawVersion(Enum):
    """PSS/E RAW format versions."""
    V33 = "33"
    V34 = "34" 
    V35 = "35"


@dataclass
class RawField:
    """Represents a field in a RAW record."""
    name: str
    value: Any
    width: int = 0
    precision: int = 0
    quoted: bool = False
    
    def format(self) -> str:
        """Format the field value according to RAW specifications."""
        if self.value is None:
            return " " * max(1, self.width)
        
        if isinstance(self.value, str):
            if self.quoted:
                return f"'{self.value}'"
            return str(self.value)
        elif isinstance(self.value, float):
            if self.precision > 0:
                return f"{self.value:.{self.precision}f}"
            return f"{self.value}"
        elif isinstance(self.value, int):
            return str(self.value)
        else:
            return str(self.value)


class FieldAccessor:
    """Provides field-name-based access to canonical data records."""
    
    def __init__(self, fields: List[str], record: List[Any]):
        """Initialize with field names and record data."""
        self.fields = fields
        self.record = record if isinstance(record, (list, tuple)) else [record]  # Handle non-list records
        self._field_index_map = {field.lower(): i for i, field in enumerate(fields)} if fields else {}
    
    def get_validated_ownership_fractions(self):
        """Get ownership fractions (o1-o4, f1-f4) with validation that f1+f2+f3+f4 = 1.0."""
        # Extract ownership data
        o1 = self.get_int('o1', 1)
        f1 = self.get_float('f1', 1.0)
        o2 = self.get_int('o2', 0)  
        f2 = self.get_float('f2', 0.0)
        o3 = self.get_int('o3', 0)
        f3 = self.get_float('f3', 0.0)
        o4 = self.get_int('o4', 0)
        f4 = self.get_float('f4', 0.0)
        
        # Calculate current sum
        fraction_sum = f1 + f2 + f3 + f4
        
        # If sum is not exactly 1.0, adjust f4 to make it exactly 1.0
        if abs(fraction_sum - 1.0) > 1e-6:  # Tolerance for floating point comparison
            f4_adjusted = f4 + (1.0 - fraction_sum)
            
            # Ensure f4 doesn't go negative 
            if f4_adjusted < 0.0:
                logger.warning(f"Cannot adjust ownership fractions to sum to 1.0 - would make f4 negative")
                # Return original values
                return o1, f1, o2, f2, o3, f3, o4, f4
            
            logger.debug(f"Adjusted ownership fractions: sum {fraction_sum:.6f} → 1.0, f4: {f4:.6f} → {f4_adjusted:.6f}")
            f4 = f4_adjusted
        
        return o1, f1, o2, f2, o3, f3, o4, f4
        
    def get_field_index(self, field_name: str) -> Optional[int]:
        """Get the index of a field by name (case-insensitive)."""
        return self._field_index_map.get(field_name.lower())
    
    def get(self, field_name: str, default=None):
        """Get field value by name with fallback to default."""
        field_index = self.get_field_index(field_name)
        
        # Safely check bounds
        if field_index is None or not isinstance(self.record, (list, tuple)) or field_index >= len(self.record):
            return default
        
        value = self.record[field_index]
        return value if value is not None else default
    
    def get_int(self, field_name: str, default: int = 0) -> int:
        """Get field value as integer."""
        value = self.get(field_name, default)
        try:
            return int(float(value)) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def get_float(self, field_name: str, default: float = 0.0) -> float:
        """Get field value as float."""
        value = self.get(field_name, default)
        try:
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def get_str(self, field_name: str, default: str = "") -> str:
        """Get field value as string."""
        value = self.get(field_name, default)
        return str(value) if value is not None else default


class RawSectionWriter(ABC):
    """Abstract base class for RAW section writers."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        self.version = version
        self.modeling_approach = modeling_approach
    
    @abstractmethod
    def get_field_header(self) -> str:
        """Get the field header line(s) for this section."""
        pass
    
    @abstractmethod
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """
        Write section data to output file.
        
        Args:
            data: Canonical data dictionary with all sections
            output_file: File handle to write to
            
        Returns:
            Number of records written
        """
        pass
    
    def write_field_header(self, output_file):
        """Write the field header to the output file."""
        header = self.get_field_header()
        if header.strip():
            output_file.write(header + "\n")


class CaseIdWriter(RawSectionWriter):
    """Writer for Case Identification Data (Section 1)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch", output_path: str = None):
        super().__init__(version, modeling_approach)
        self.output_path = output_path
    
    def get_field_header(self) -> str:
        return "@!IC, SBASE,REV,XFRRAT,NXFRAT,BASFRQ"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write case identification data with proper PSS/E format including case title and description."""
        caseid_section = data.get('caseid', {})
        
        # Generate modeling approach display name
        modeling_display = {
            'bus_branch': 'BUS-BRANCH',
            'node_breaker': 'NODE-BREAKER', 
            'hybrid': 'HYBRID BUS-BREAKER'
        }.get(self.modeling_approach, 'BUS-BRANCH')
        
        # Generate default case titles if not provided in data
        version_num = int(self.version.value) if hasattr(self.version, 'value') else 35
        default_title1 = f"RAW EXPORT FOR VERSION {version_num} WITH {modeling_display} MODELLING"
        default_title2 = str(self.output_path) if self.output_path else "EXPORTED CASE"
        
        if 'fields' not in caseid_section or 'data' not in caseid_section:
            # Write default case identification using the correct version
            # PSS/E defaults: IC=0, SBASE=100.0, REV=version, XFRRAT=0, NXFRAT=1, BASFRQ=60.0
            default_line = f"0,  100.00, {version_num:>2},     0,     1,  60.00     / PSS(R)E-{version_num}.X"
            output_file.write(f"{default_line}\n")
            # Add default case titles
            output_file.write(f"{default_title1}\n")
            output_file.write(f"{default_title2}\n")
            return 3
        
        fields = caseid_section['fields']
        records_written = 0
        
        # Only use the FIRST record for case identification - PSS/E expects only one
        if caseid_section['data']:
            record = caseid_section['data'][0]  # Take only the first record
            accessor = FieldAccessor(fields, record)
            
            # Map canonical fields to RAW format with proper defaults
            ic = accessor.get_int('ic', 0)                    # Control mode (default: 0)
            sbase = accessor.get_float('sbase', 100.0)        # System base MVA (default: 100.0)
            rev = accessor.get_int('rev', accessor.get_int('version', version_num))  # PSS/E version
            xfrrat = accessor.get_int('xfrrat', 0)            # Transformer ratio (default: 0)
            nxfrat = accessor.get_int('nxfrat', 1)            # Number of transformer ratios (default: 1)
            basfrq = accessor.get_float('basfrq', accessor.get_float('frequency', 60.0))  # Base frequency (default: 60.0)
            
            # Get optional comment
            comment = accessor.get_str('comment', f'/ PSS(R)E-{version_num}.X')
            
            # Format the line with PSS/E spacing requirements
            line = f"{ic:>3d},{sbase:>8.2f},{rev:>3d},{xfrrat:>6d},{nxfrat:>6d},{basfrq:>7.2f}     {comment}"
            output_file.write(f"{line}\n")
            records_written = 1
        
        # Add case title and description - use defaults if not provided in data
        title1 = data.get('title1', data.get('title', default_title1))
        title2 = data.get('title2', data.get('description', default_title2))
        
        # Write case titles (PSS/E requires exactly 2 title lines)
        output_file.write(f"{title1}\n")
        output_file.write(f"{title2}\n")
        records_written += 2
        
        return records_written


class BusWriter(RawSectionWriter):
    """Writer for Bus Data (Section 1)."""
    
    def get_field_header(self) -> str:
        # All versions (V33, V34, V35) use the same format with proper spacing
        return "@!   I,'NAME        ', BASKV, IDE,AREA,ZONE,OWNER, VM,        VA,    NVHI,   NVLO,   EVHI,   EVLO"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write bus data."""
        bus_section = data.get('bus', {})
        if 'fields' not in bus_section or 'data' not in bus_section:
            return 0
        
        fields = bus_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in bus_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('bus', source_record)
                
                # Extract mapped fields using canonical names
                i = mapped_record.get('ibus', 0)
                name = str(mapped_record.get('name', ''))[:12]  # Limit to 12 chars
                baskv = mapped_record.get('baskv', 0.0)
                ide = mapped_record.get('ide', 1)
                area = mapped_record.get('area', 1)
                zone = mapped_record.get('zone', 1)
                owner = mapped_record.get('owner', 1)
                vm = mapped_record.get('vm', 1.0)
                va = mapped_record.get('va', 0.0)
                nvhi = mapped_record.get('nvhi', 1.1)
                nvlo = mapped_record.get('nvlo', 0.9)
                evhi = mapped_record.get('evhi', 1.1)
                evlo = mapped_record.get('evlo', 0.9)
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                
                # Extract bus fields using field names
                i = accessor.get_int('ibus', accessor.get_int('i', accessor.get_int('bus_num', accessor.get_int('number', 0))))
                name = accessor.get_str('name', accessor.get_str('bus_name', ''))[:12]  # Limit to 12 chars
                baskv = accessor.get_float('baskv', accessor.get_float('base_kv', 0.0))
                ide = accessor.get_int('ide', accessor.get_int('type', 1))
                area = accessor.get_int('area', accessor.get_int('area_num', 1))
                zone = accessor.get_int('zone', accessor.get_int('zone_num', 1))
                owner = accessor.get_int('owner', accessor.get_int('owner_num', 1))
                vm = accessor.get_float('vm', accessor.get_float('v_mag', 1.0))
                va = accessor.get_float('va', accessor.get_float('v_angle', 0.0))
                nvhi = accessor.get_float('nvhi', 1.1)
                nvlo = accessor.get_float('nvlo', 0.9)
                evhi = accessor.get_float('evhi', 1.1)
                evlo = accessor.get_float('evlo', 0.9)
            
            # Write bus record with exact PSS/E spacing and precision
            output_file.write(f"   {i:>3},'{name:<12}',{baskv:>9.4f},{ide:>1},{area:>4},{zone:>4},{owner:>4},{vm:>7.5f},{va:>9.4f},{nvhi:>7.5f},{nvlo:>7.5f},{evhi:>7.5f},{evlo:>7.5f}\n")
            records_written += 1
        
        return records_written


class LoadWriter(RawSectionWriter):
    """Writer for Load Data (Section 3)."""
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V35:
            return "@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT,DGENP,DGENQ,DGENM,LOADTYPE"
        elif self.version == RawVersion.V34:
            return "@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT,DGENP,DGENQ,DGENM"
        else:  # V33
            return "@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write load data."""
        load_section = data.get('load', {})
        if 'fields' not in load_section or 'data' not in load_section:
            return 0
        
        fields = load_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in load_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('load', source_record)
                
                # Extract mapped fields using canonical names
                ibus = mapped_record.get('ibus', 0)
                loadid = str(mapped_record.get('loadid', '1'))[:2]
                stat = mapped_record.get('stat', 1)
                area = mapped_record.get('area', 1)
                zone = mapped_record.get('zone', 1)
                pl = mapped_record.get('pl', 0.0)
                ql = mapped_record.get('ql', 0.0)
                ip = mapped_record.get('ip', 0.0)
                iq = mapped_record.get('iq', 0.0)
                yp = mapped_record.get('yp', 0.0)
                yq = mapped_record.get('yq', 0.0)
                owner = mapped_record.get('owner', 1)
                scale = mapped_record.get('scale', 1)
                intrpt = mapped_record.get('intrpt', 0)
                
                # Version-specific fields
                dgenp = mapped_record.get('dgenp', 0.0) if self.version in [RawVersion.V34, RawVersion.V35] else 0.0
                dgenq = mapped_record.get('dgenq', 0.0) if self.version in [RawVersion.V34, RawVersion.V35] else 0.0
                dgenm = mapped_record.get('dgenm', 0) if self.version in [RawVersion.V34, RawVersion.V35] else 0
                loadtype = str(mapped_record.get('loadtype', '')) if self.version == RawVersion.V35 else ""
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                    
                # Extract load fields using field names
                ibus = accessor.get_int('ibus', accessor.get_int('i', accessor.get_int('bus', accessor.get_int('bus_id', 0))))
                loadid = accessor.get_str('loadid', accessor.get_str('id', accessor.get_str('load_id', '1')))[:2]
                stat = accessor.get_int('status', 1)
                area = accessor.get_int('area', 1)
                zone = accessor.get_int('zone', 1)
                pl = accessor.get_float('pl', accessor.get_float('p_load', 0.0))
                ql = accessor.get_float('ql', accessor.get_float('q_load', 0.0))
                ip = accessor.get_float('ip', accessor.get_float('ip_load', 0.0))
                iq = accessor.get_float('iq', accessor.get_float('iq_load', 0.0))
                yp = accessor.get_float('yp', accessor.get_float('yp_load', 0.0))
                yq = accessor.get_float('yq', accessor.get_float('yq_load', 0.0))
                owner = accessor.get_int('owner', 1)
                scale = accessor.get_int('scale', 1)
                intrpt = accessor.get_int('intrpt', 0)
                
                # Version-specific fields
                dgenp = accessor.get_float('dgenp', 0.0) if self.version in [RawVersion.V34, RawVersion.V35] else 0.0
                dgenq = accessor.get_float('dgenq', 0.0) if self.version in [RawVersion.V34, RawVersion.V35] else 0.0
                dgenm = accessor.get_int('dgenm', 0) if self.version in [RawVersion.V34, RawVersion.V35] else 0
                loadtype = accessor.get_str('loadtype', '') if self.version == RawVersion.V35 else ""
            
            # Format according to PSS/E specifications
            if self.version == RawVersion.V35:
                output_file.write(f"{ibus:>6},'{loadid:<2}',{stat:>2},{area:>4},{zone:>4},{pl:>10.2f},{ql:>10.2f},{ip:>10.2f},{iq:>10.2f},{yp:>10.2f},{yq:>10.2f},{owner:>4},{scale:>2},{intrpt:>2},{dgenp:>8.2f},{dgenq:>8.2f},{dgenm:>2},'{loadtype:<12}'\n")
            elif self.version == RawVersion.V34:
                output_file.write(f"{ibus:>6},'{loadid:<2}',{stat:>2},{area:>4},{zone:>4},{pl:>10.2f},{ql:>10.2f},{ip:>10.2f},{iq:>10.2f},{yp:>10.2f},{yq:>10.2f},{owner:>4},{scale:>2},{intrpt:>2},{dgenp:>8.2f},{dgenq:>8.2f},{dgenm:>2}\n")
            else:  # V33
                output_file.write(f"{ibus:>6},'{loadid:<2}',{stat:>2},{area:>4},{zone:>4},{pl:>10.2f},{ql:>10.2f},{ip:>10.2f},{iq:>10.2f},{yp:>10.2f},{yq:>10.2f},{owner:>4},{scale:>2},{intrpt:>2}\n")
            
            records_written += 1
        
        return records_written


class GeneratorWriter(RawSectionWriter):
    """Writer for Generator Data (Section 5)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V35:
            # V35: 30 fields including NREG and BASLOD
            return "@!I,'ID',PG,QG,QT,QB,VS,IREG,NREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,BASLOD,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF"
        elif self.version == RawVersion.V34:
            # V34: 28 fields (missing NREG, BASLOD)  
            return "@!I,'ID',PG,QG,QT,QB,VS,IREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF"
        else:  # V33
            # V33: 27 fields (includes IREG, missing NREG, BASLOD)
            return "@!I,'ID',PG,QG,QT,QB,VS,IREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write generator data."""
        generator_section = data.get('generator', {})
        if 'fields' not in generator_section or 'data' not in generator_section:
            return 0
        
        field_names = generator_section['fields']
        records_written = 0
        
        for record in generator_section['data']:
            # Use centralized field mapping with RAWX field names
            canonical_record = self.mapper.map_record('generator', record, field_names)
            
            # Common fields for all versions
            ibus = canonical_record.get('ibus', 0)
            genid = canonical_record.get('machid', '1')[:2]
            pg = canonical_record.get('pg', 0.0)
            qg = canonical_record.get('qg', 0.0)
            qt = canonical_record.get('qt', 9999.0)
            qb = canonical_record.get('qb', -9999.0)
            vs = canonical_record.get('vs', 1.0)
            
            # Version-specific field handling
            if self.version == RawVersion.V35:
                # V35: All 30 fields
                ireg = canonical_record.get('ireg', 0)
                nreg = canonical_record.get('nreg', 0)
                mbase = canonical_record.get('mbase', 100.0)
                zr = canonical_record.get('zr', 0.0)
                zx = canonical_record.get('zx', 0.0)
                rt = canonical_record.get('rt', 0.0)
                xt = canonical_record.get('xt', 0.0)
                gtap = canonical_record.get('gtap', 1.0)
                stat = canonical_record.get('stat', 1)
                rmpct = canonical_record.get('rmpct', 100.0)
                pt = canonical_record.get('pt', 9999.0)
                pb = canonical_record.get('pb', -9999.0)
                baslod = canonical_record.get('baslod', 0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                wmod = canonical_record.get('wmod', 0)
                wpf = canonical_record.get('wpf', 1.0)
                
                # Format using PSS/E scientific notation
                zr_str = f"{zr:.5E}" if abs(zr) < 0.1 else f"{zr:.6f}"
                zx_str = f"{zx:.5E}" if abs(zx) < 0.1 else f"{zx:.6f}"
                rt_str = f"{rt:.5E}" if abs(rt) < 0.1 else f"{rt:.6f}"
                xt_str = f"{xt:.5E}" if abs(xt) < 0.1 else f"{xt:.6f}"
                
                output_file.write(f"    {ibus},'{ genid}',{pg:9.3f},{qg:9.2f},{qt:9.3f},{qb:9.3f},{vs:6.3f},{ireg:6},{nreg:6},{mbase:9.3f},{zr_str:>11},{zx_str:>11},{rt_str:>11},{xt_str:>11},{gtap:8.5f},{stat:>2},{rmpct:6.1f},{pt:9.3f},{pb:9.3f},{baslod:>2},{o1:>2},{f1:6.4f},{o2:>2},{f2:6.4f},{o3:>2},{f3:6.4f},{o4:>2},{f4:6.4f},{wmod:>2},{wpf:6.3f}\n")
                
            elif self.version == RawVersion.V34:
                # V34: 28 fields (missing NREG, BASLOD)
                ireg_raw = canonical_record.get('ireg', 0)
                # PSS/E convention: if IREG equals generator bus, write as 0 (regulate own bus)
                ireg = 0 if ireg_raw == ibus else ireg_raw
                
                mbase = canonical_record.get('mbase', 100.0)
                zr = canonical_record.get('zr', 0.0)
                zx = canonical_record.get('zx', 0.0)
                rt = canonical_record.get('rt', 0.0)
                xt = canonical_record.get('xt', 0.0)
                gtap = canonical_record.get('gtap', 1.0)
                stat = canonical_record.get('stat', 1)
                rmpct = canonical_record.get('rmpct', 100.0)
                pt = canonical_record.get('pt', 9999.0)
                pb = canonical_record.get('pb', -9999.0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                # For V34, only write significant ownership entries (match reference format)
                if f1 > 0 and f2 > 0:
                    # Two owners like reference: o1=11,f1=0.6667,o2=1,f2=0.3333
                    ownership_str = f"{o1:>2},{f1:6.4f},{o2:>2},{f2:6.4f}"
                elif f1 > 0:
                    # Single owner: o1=X,f1=1.0000,o2=0,f2=0.0000
                    ownership_str = f"{o1:>2},{f1:6.4f},{o2:>2},{f2:6.4f}"
                else:
                    # Default ownership
                    ownership_str = f"{o1:>2},{f1:6.4f},{o2:>2},{f2:6.4f}"
                    
                wmod = canonical_record.get('wmod', 0)
                wpf = canonical_record.get('wpf', 1.0)
                
                # Format using PSS/E scientific notation
                zr_str = f"{zr:.5E}" if abs(zr) < 0.1 else f"{zr:.6f}"
                zx_str = f"{zx:.5E}" if abs(zx) < 0.1 else f"{zx:.6f}"
                rt_str = f"{rt:.5E}" if abs(rt) < 0.1 else f"{rt:.6f}"
                xt_str = f"{xt:.5E}" if abs(xt) < 0.1 else f"{xt:.6f}"
                
                output_file.write(f"    {ibus},'{ genid}',{pg:9.3f},{qg:9.2f},{qt:9.3f},{qb:9.3f},{vs:6.3f},{ireg:6},{mbase:9.3f},{zr_str:>11},{zx_str:>11},{rt_str:>11},{xt_str:>11},{gtap:8.5f},{stat:>2},{rmpct:6.1f},{pt:9.3f},{pb:9.3f},{ownership_str},{wmod:>2},{wpf:6.3f}\n")
                
            else:  # V33
                # V33: 27 fields (includes IREG, missing NREG, BASLOD)
                ireg_raw = canonical_record.get('ireg', 0)
                # PSS/E convention: if IREG equals generator bus, write as 0 (regulate own bus)
                ireg = 0 if ireg_raw == ibus else ireg_raw
                
                mbase = canonical_record.get('mbase', 100.0)
                zr = canonical_record.get('zr', 0.0)
                zx = canonical_record.get('zx', 0.0)
                rt = canonical_record.get('rt', 0.0)
                xt = canonical_record.get('xt', 0.0)
                gtap = canonical_record.get('gtap', 1.0)
                stat = canonical_record.get('stat', 1)
                rmpct = canonical_record.get('rmpct', 100.0)
                pt = canonical_record.get('pt', 9999.0)
                pb = canonical_record.get('pb', -9999.0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                wmod = canonical_record.get('wmod', 0)
                wpf = canonical_record.get('wpf', 1.0)
                
                # Format using PSS/E scientific notation
                zr_str = f"{zr:.5E}" if abs(zr) < 0.1 else f"{zr:.6f}"
                zx_str = f"{zx:.5E}" if abs(zx) < 0.1 else f"{zx:.6f}"
                rt_str = f"{rt:.5E}" if abs(rt) < 0.1 else f"{rt:.6f}"
                xt_str = f"{xt:.5E}" if abs(xt) < 0.1 else f"{xt:.6f}"
                
                output_file.write(f"    {ibus},'{ genid}',{pg:9.3f},{qg:9.2f},{qt:9.3f},{qb:9.3f},{vs:6.3f},{ireg:6},{mbase:9.3f},{zr_str:>11},{zx_str:>11},{rt_str:>11},{xt_str:>11},{gtap:8.5f},{stat:>2},{rmpct:6.1f},{pt:9.3f},{pb:9.3f},{o1:>2},{f1:6.4f},{o2:>2},{f2:6.4f},{o3:>2},{f3:6.4f},{o4:>2},{f4:6.4f},{wmod:>2},{wpf:6.3f}\n")
            
            records_written += 1
        
        return records_written


class AcLineWriter(RawSectionWriter):
    """Writer for Non-Transformer Branch Data (Section 6)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V35:
            # V35: 12 ratings plus additional fields
            return "@!I,J,'CKT',R,X,B,'NAME',RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,O2,F2,O3,F3,O4,F4"
        elif self.version == RawVersion.V34:
            # V34: NAME field plus 12 ratings like reference file
            return "@!   I,     J,'CKT',     R,          X,         B,                    'N A M E'                 ,   RATE1,   RATE2,   RATE3,   RATE4,   RATE5,   RATE6,   RATE7,   RATE8,   RATE9,  RATE10,  RATE11,  RATE12,    GI,       BI,       GJ,       BJ,STAT,MET,  LEN,  O1,  F1,    O2,  F2,    O3,  F3,    O4,  F4"
        else:  # V33
            # V33: Traditional RATEA,RATEB,RATEC format
            return "@!I,J,'CKT',R,X,B,RATEA,RATEB,RATEC,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,O2,F2,O3,F3,O4,F4"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write AC line data."""
        ac_line_section = data.get('ac_line', {})
        if 'fields' not in ac_line_section or 'data' not in ac_line_section:
            return 0
        
        field_names = ac_line_section['fields']
        records_written = 0
        
        for record in ac_line_section['data']:
            # Use centralized field mapping with RAWX field names
            canonical_record = self.mapper.map_record('acline', record, field_names)
            
            # Common fields for all versions
            ibus = canonical_record.get('ibus', 0)
            jbus = canonical_record.get('jbus', 0)
            ckt = canonical_record.get('ckt', '1')[:2]
            r = canonical_record.get('r', 0.0)
            x = canonical_record.get('x', 0.0)
            b = canonical_record.get('b', 0.0)
            
            # PSS/E requires non-zero reactance - set minimum value to avoid errors
            if x == 0.0:
                x = 0.0001
                logger.debug(f"Adjusted zero reactance to 0.0001 for branch {ibus}-{jbus} circuit '{ckt}'")
            
            if self.version == RawVersion.V35:
                # V35 format with 12 ratings and additional fields
                name = canonical_record.get('name', '')
                rate1 = canonical_record.get('ratea', 0.0)
                rate2 = canonical_record.get('rateb', 0.0)
                rate3 = canonical_record.get('ratec', 0.0)
                rate4 = canonical_record.get('rate4', 0.0)
                rate5 = canonical_record.get('rate5', 0.0)
                rate6 = canonical_record.get('rate6', 0.0)
                rate7 = canonical_record.get('rate7', 0.0)
                rate8 = canonical_record.get('rate8', 0.0)
                rate9 = canonical_record.get('rate9', 0.0)
                rate10 = canonical_record.get('rate10', 0.0)
                rate11 = canonical_record.get('rate11', 0.0)
                rate12 = canonical_record.get('rate12', 0.0)
                gi = canonical_record.get('gi', 0.0)
                bi = canonical_record.get('bi', 0.0)
                gj = canonical_record.get('gj', 0.0)
                bj = canonical_record.get('bj', 0.0)
                st = canonical_record.get('st', 1)
                met = canonical_record.get('met', 1)
                length = canonical_record.get('len', 0.0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                output_file.write(f"{ibus:>6},{jbus:>6},'{ckt:<2}',{r:>10.6f},{x:>10.6f},{b:>10.6f},'{name:<40}',{rate1:>8.2f},{rate2:>8.2f},{rate3:>8.2f},{rate4:>8.2f},{rate5:>8.2f},{rate6:>8.2f},{rate7:>8.2f},{rate8:>8.2f},{rate9:>8.2f},{rate10:>8.2f},{rate11:>8.2f},{rate12:>8.2f},{gi:>8.6f},{bi:>8.6f},{gj:>8.6f},{bj:>8.6f},{st:>2},{met:>2},{length:>8.2f},{o1:>2},{f1:>7.4f},{o2:>2},{f2:>7.4f},{o3:>2},{f3:>7.4f},{o4:>2},{f4:>7.4f}\n")
            elif self.version == RawVersion.V34:
                # V34 format with NAME field and 12 ratings like reference file
                name = canonical_record.get('name', f'BRANCH_FROM__{ibus:>3}_TO__{jbus:>3}___CIRCUIT_ID__{ckt}')
                rate1 = canonical_record.get('ratea', 0.0)
                rate2 = canonical_record.get('rateb', 0.0)
                rate3 = canonical_record.get('ratec', 0.0)
                rate4 = canonical_record.get('rate4', 0.0)
                rate5 = canonical_record.get('rate5', 0.0)
                rate6 = canonical_record.get('rate6', 0.0)
                rate7 = canonical_record.get('rate7', 0.0)
                rate8 = canonical_record.get('rate8', 0.0)
                rate9 = canonical_record.get('rate9', 0.0)
                rate10 = canonical_record.get('rate10', 0.0)
                rate11 = canonical_record.get('rate11', 0.0)
                rate12 = canonical_record.get('rate12', 0.0)
                gi = canonical_record.get('gi', 0.0)
                bi = canonical_record.get('bi', 0.0)
                gj = canonical_record.get('gj', 0.0)
                bj = canonical_record.get('bj', 0.0)
                st = canonical_record.get('st', 1)
                met = canonical_record.get('met', 1)
                length = canonical_record.get('len', 0.0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                # Format with exact spacing like reference file
                output_file.write(f"{ibus:>6},{jbus:>6},'{ckt:<2}',{r:>12.8f},{x:>12.8f},{b:>10.5f},'{name:<40}',{rate1:>8.2f},{rate2:>8.2f},{rate3:>8.2f},{rate4:>8.2f},{rate5:>8.2f},{rate6:>8.2f},{rate7:>8.2f},{rate8:>8.2f},{rate9:>8.2f},{rate10:>8.2f},{rate11:>8.2f},{rate12:>8.2f},{gi:>8.5f},{bi:>8.5f},{gj:>8.5f},{bj:>8.5f},{st:>1},{met:>1},{length:>8.2f},{o1:>4},{f1:>6.4f},{o2:>2},{f2:>6.4f},{o3:>2},{f3:>6.4f},{o4:>2},{f4:>6.4f}\n")
            else:  # V33
                # V33 format with traditional 3 ratings
                ratea = canonical_record.get('ratea', 0.0)
                rateb = canonical_record.get('rateb', 0.0)
                ratec = canonical_record.get('ratec', 0.0)
                gi = canonical_record.get('gi', 0.0)
                bi = canonical_record.get('bi', 0.0)
                gj = canonical_record.get('gj', 0.0)
                bj = canonical_record.get('bj', 0.0)
                st = canonical_record.get('st', 1)
                met = canonical_record.get('met', 1)
                length = canonical_record.get('len', 0.0)
                
                # Extract and validate ownership data from canonical record
                o1 = canonical_record.get('o1', 1)
                f1 = canonical_record.get('f1', 1.0)
                o2 = canonical_record.get('o2', 0)
                f2 = canonical_record.get('f2', 0.0)
                o3 = canonical_record.get('o3', 0)
                f3 = canonical_record.get('f3', 0.0)
                o4 = canonical_record.get('o4', 0)
                f4 = canonical_record.get('f4', 0.0)
                
                # Validate ownership fractions
                fraction_sum = f1 + f2 + f3 + f4
                if abs(fraction_sum - 1.0) > 1e-6:
                    f4_adjusted = f4 + (1.0 - fraction_sum)
                    if f4_adjusted >= 0.0:
                        f4 = f4_adjusted
                
                output_file.write(f"{ibus:>6},{jbus:>6},'{ckt:<2}',{r:>10.6f},{x:>10.6f},{b:>10.6f},{ratea:>8.2f},{rateb:>8.2f},{ratec:>8.2f},{gi:>8.6f},{bi:>8.6f},{gj:>8.6f},{bj:>8.6f},{st:>2},{met:>2},{length:>8.2f},{o1:>2},{f1:>7.4f},{o2:>2},{f2:>7.4f},{o3:>2},{f3:>7.4f},{o4:>2},{f4:>7.4f}\n")
            
            records_written += 1
        
        return records_written


class TransformerWriter(RawSectionWriter):
    """Writer for Transformer Data (Section 8)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V35:
            return ("@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,'VECGRP',ZCOD\n"
                    "@!R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR\n"
                    "@!WINDV1,NOMV1,ANG1,RATE11,RATE12,RATE13,RATE14,RATE15,RATE16,RATE17,RATE18,RATE19,RATE110,RATE111,RATE112,COD1,CONT1,NODE1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1\n"
                    "@!WINDV2,NOMV2,ANG2,RATE21,RATE22,RATE23,RATE24,RATE25,RATE26,RATE27,RATE28,RATE29,RATE210,RATE211,RATE212,COD2,CONT2,NODE2,RMA2,RMI2,VMA2,VMI2,NTP2,TAB2,CR2,CX2,CNXA2\n"
                    "@!WINDV3,NOMV3,ANG3,RATE31,RATE32,RATE33,RATE34,RATE35,RATE36,RATE37,RATE38,RATE39,RATE310,RATE311,RATE312,COD3,CONT3,NODE3,RMA3,RMI3,VMA3,VMI3,NTP3,TAB3,CR3,CX3,CNXA3")
        elif self.version == RawVersion.V34:
            return ("@!   I,     J,     K,'CKT',CW,CZ,CM,    MAG1,       MAG2,NMETR,               'N A M E',               STAT,O1,  F1,    O2,  F2,    O3,  F3,    O4,  F4,     'VECGRP', ZCOD\n"
                    "@!   R1-2,       X1-2,   SBASE1-2,     R2-3,       X2-3,   SBASE2-3,     R3-1,       X3-1,   SBASE3-1, VMSTAR,   ANSTAR\n"
                    "@!WINDV1,  NOMV1,    ANG1,  RATE1-1,  RATE1-2,  RATE1-3,  RATE1-4,  RATE1-5,  RATE1-6,  RATE1-7,  RATE1-8,  RATE1-9, RATE1-10, RATE1-11, RATE1-12,COD1,CONT1,   RMA1,    RMI1,    VMA1,    VMI1, NTP1,TAB1,  CR1,     CX1,   CNXA1\n"
                    "@!WINDV2,  NOMV2,    ANG2,  RATE2-1,  RATE2-2,  RATE2-3,  RATE2-4,  RATE2-5,  RATE2-6,  RATE2-7,  RATE2-8,  RATE2-9, RATE2-10, RATE2-11, RATE2-12,COD2,CONT2,   RMA2,    RMI2,    VMA2,    VMI2, NTP2,TAB2,  CR2,     CX2,   CNXA2\n"
                    "@!WINDV3,  NOMV3,    ANG3,  RATE3-1,  RATE3-2,  RATE3-3,  RATE3-4,  RATE3-5,  RATE3-6,  RATE3-7,  RATE3-8,  RATE3-9, RATE3-10, RATE3-11, RATE3-12,COD3,CONT3,   RMA3,    RMI3,    VMA3,    VMI3, NTP3,TAB3,  CR3,     CX3,   CNXA3")
        else:  # V33
            return ("@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,VECGRP\n"
                    "@!R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR\n"
                    "@!WINDV1,NOMV1,ANG1,RATA1,RATB1,RATC1,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1\n"
                    "@!WINDV2,NOMV2,ANG2,RATA2,RATB2,RATC2,COD2,CONT2,RMA2,RMI2,VMA2,VMI2,NTP2,TAB2,CR2,CX2,CNXA2\n"
                    "@!WINDV3,NOMV3,ANG3,RATA3,RATB3,RATC3,COD3,CONT3,RMA3,RMI3,VMA3,VMI3,NTP3,TAB3,CR3,CX3,CNXA3")
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default

    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write transformer data using correct PSS/E multi-record format."""
        if 'transformer' not in data or 'data' not in data['transformer']:
            return 0
        
        # Get field names for proper mapping
        if 'fields' not in data['transformer']:
            return 0
        
        fields = data['transformer']['fields']
        field_map = {field: idx for idx, field in enumerate(fields)}
        
        records_written = 0
        for record in data['transformer']['data']:
            if not isinstance(record, list) or len(record) < len(fields):
                continue
            
            # Extract data using proper field mapping from RAWX format
            ibus = self._to_int(record[field_map.get('ibus', 0)])
            jbus = self._to_int(record[field_map.get('jbus', 1)])
            kbus = self._to_int(record[field_map.get('kbus', 2)])
            ckt = str(record[field_map.get('ckt', 3)]).strip()
            cw = self._to_int(record[field_map.get('cw', 4)], 1)
            cz = self._to_int(record[field_map.get('cz', 5)], 1)
            cm = self._to_int(record[field_map.get('cm', 6)], 1)
            # Magnetizing data (convert to per-unit values)
            mag1_raw = self._to_float(record[field_map.get('mag1', 7)], 0.0)
            mag2_raw = self._to_float(record[field_map.get('mag2', 8)], 0.0)
            
            # Convert MAG1 and MAG2 to proper per-unit values for PSS/E
            # MAG1 should be in per-unit on system base, MAG2 is loss factor
            if abs(mag1_raw) > 1000.0:  # If MAG1 is in actual units, convert to per-unit
                mag1 = mag1_raw / 1000000.0  # Convert from actual to per-unit (typical range 0.0-1.0)
            else:
                mag1 = mag1_raw
                
            # Handle MAG2 special cases
            if abs(mag2_raw) < 0.001 and abs(mag1_raw) > 0.0:  # If MAG2 is essentially zero but MAG1 exists
                mag2 = 0.00375  # Default MAG2 value as used by PSS/E
            else:
                mag2 = mag2_raw
            nmet = self._to_int(record[field_map.get('nmet', 9)], 1)
            name = str(record[field_map.get('name', 10)] or "").strip()
            
            # Determine transformer type early
            is_3winding = kbus != 0
            
            # Generate unique transformer name if empty or default
            if not name or name in ['TWOWDG_FROM_', '', ' ']:
                if is_3winding:
                    name = f"T3_{ibus}_{jbus}_{kbus}"
                else:
                    name = f"T2_{ibus}_{jbus}"
            
            # Apply version-specific name length limits
            if self.version == RawVersion.V33:
                name = name[:12]  # V33: 12 character limit
            else:
                name = name[:60]  # V34/V35: 60 character limit
            
            stat = self._to_int(record[field_map.get('stat', 11)], 1)
            
            # Owner factors
            o1 = self._to_int(record[field_map.get('o1', 12)], 1)
            f1 = self._to_float(record[field_map.get('f1', 13)], 1.0)
            o2 = self._to_int(record[field_map.get('o2', 14)], 0)
            f2 = self._to_float(record[field_map.get('f2', 15)], 0.0)
            o3 = self._to_int(record[field_map.get('o3', 16)], 0)
            f3 = self._to_float(record[field_map.get('f3', 17)], 0.0)
            o4 = self._to_int(record[field_map.get('o4', 18)], 0)
            f4 = self._to_float(record[field_map.get('f4', 19)], 0.0)
            
            # Vector group and impedance correction code (V34/V35 only)
            vecgrp = str(record[field_map.get('vecgrp', 20)] or "").strip()[:12] if self.version in [RawVersion.V34, RawVersion.V35] else ""
            zcod = self._to_int(record[field_map.get('zcod', 21)], 0) if self.version in [RawVersion.V34, RawVersion.V35] else 0
            
            # Impedance data
            r1_2 = self._to_float(record[field_map.get('r1_2', 22)], 0.001)
            x1_2 = self._to_float(record[field_map.get('x1_2', 23)], 0.01)
            sbase1_2 = self._to_float(record[field_map.get('sbase1_2', 24)], 100.0)
            r2_3 = self._to_float(record[field_map.get('r2_3', 25)], 0.0)
            x2_3 = self._to_float(record[field_map.get('x2_3', 26)], 0.0)
            sbase2_3 = self._to_float(record[field_map.get('sbase2_3', 27)], 0.0)
            r3_1 = self._to_float(record[field_map.get('r3_1', 28)], 0.0)
            x3_1 = self._to_float(record[field_map.get('x3_1', 29)], 0.0)
            sbase3_1 = self._to_float(record[field_map.get('sbase3_1', 30)], 0.0)
            vmstar = self._to_float(record[field_map.get('vmstar', 31)], 0.0)
            anstar = self._to_float(record[field_map.get('anstar', 32)], 0.0)
            
            # Winding 1 data
            windv1_raw = self._to_float(record[field_map.get('windv1', 33)], 1.0)
            # Round tap to nearest valid step (PSS/E uses 0.003125 = 0.3125% steps)
            windv1 = windv1_raw
            nomv1 = self._to_float(record[field_map.get('nomv1', 34)], 0.0)
            ang1 = self._to_float(record[field_map.get('ang1', 35)], 0.0)
            
            # Winding 1 ratings
            wdg1rate1 = self._to_float(record[field_map.get('wdg1rate1', 36)], 0.0)
            wdg1rate2 = self._to_float(record[field_map.get('wdg1rate2', 37)], 0.0)
            wdg1rate3 = self._to_float(record[field_map.get('wdg1rate3', 38)], 0.0)
            
            # Winding 1 control data
            cod1 = self._to_int(record[field_map.get('cod1', 48)], 0)
            cont1 = self._to_int(record[field_map.get('cont1', 49)], 0)
            node1 = self._to_int(record[field_map.get('node1', 50)], 0)
            rma1 = self._to_float(record[field_map.get('rma1', 51)], 1.1)
            rmi1 = self._to_float(record[field_map.get('rmi1', 52)], 0.9)
            vma1 = self._to_float(record[field_map.get('vma1', 53)], 1.1)
            vmi1 = self._to_float(record[field_map.get('vmi1', 54)], 0.9)
            ntp1 = self._to_int(record[field_map.get('ntp1', 55)], 33)
            tab1 = self._to_int(record[field_map.get('tab1', 56)], 0)
            cr1 = self._to_float(record[field_map.get('cr1', 57)], 0.0)
            cx1 = self._to_float(record[field_map.get('cx1', 58)], 0.0)
            cnxa1 = self._to_float(record[field_map.get('cnxa1', 59)], 0.0)
            
            # Winding 2 data
            windv2_raw = self._to_float(record[field_map.get('windv2', 60)], 1.0)
            # Round tap to nearest valid step (PSS/E uses 0.003125 = 0.3125% steps)
            windv2 = windv2_raw
            nomv2 = self._to_float(record[field_map.get('nomv2', 61)], 0.0)
            ang2 = self._to_float(record[field_map.get('ang2', 62)], 0.0)
            
            # Winding 2 ratings
            wdg2rate1 = self._to_float(record[field_map.get('wdg2rate1', 63)], 0.0)
            wdg2rate2 = self._to_float(record[field_map.get('wdg2rate2', 64)], 0.0)
            wdg2rate3 = self._to_float(record[field_map.get('wdg2rate3', 65)], 0.0)
            
            # Winding 2 control data
            cod2 = self._to_int(record[field_map.get('cod2', 75)], 0)
            cont2 = self._to_int(record[field_map.get('cont2', 76)], 0)
            node2 = self._to_int(record[field_map.get('node2', 77)], 0)
            rma2 = self._to_float(record[field_map.get('rma2', 78)], 1.1)
            rmi2 = self._to_float(record[field_map.get('rmi2', 79)], 0.9)
            vma2 = self._to_float(record[field_map.get('vma2', 80)], 1.1)
            vmi2 = self._to_float(record[field_map.get('vmi2', 81)], 0.9)
            ntp2 = self._to_int(record[field_map.get('ntp2', 82)], 33)
            tab2 = self._to_int(record[field_map.get('tab2', 83)], 0)
            cr2 = self._to_float(record[field_map.get('cr2', 84)], 0.0)
            cx2 = self._to_float(record[field_map.get('cx2', 85)], 0.0)
            cnxa2 = self._to_float(record[field_map.get('cnxa2', 86)], 0.0)
            
            # Winding 3 data (for 3-winding transformers)
            windv3_raw = self._to_float(record[field_map.get('windv3', 87)], 1.0) if kbus != 0 else 0.0
            # Round tap to nearest valid step (PSS/E uses 0.003125 = 0.3125% steps)
            windv3 = windv3_raw if kbus != 0 else 0.0
            nomv3 = self._to_float(record[field_map.get('nomv3', 88)], 0.0) if kbus != 0 else 0.0
            ang3 = self._to_float(record[field_map.get('ang3', 89)], 0.0) if kbus != 0 else 0.0
            
            # Winding 3 ratings
            wdg3rate1 = self._to_float(record[field_map.get('wdg3rate1', 90)], 0.0) if kbus != 0 else 0.0
            wdg3rate2 = self._to_float(record[field_map.get('wdg3rate2', 91)], 0.0) if kbus != 0 else 0.0
            wdg3rate3 = self._to_float(record[field_map.get('wdg3rate3', 92)], 0.0) if kbus != 0 else 0.0
            
            # Winding 3 control data
            cod3 = self._to_int(record[field_map.get('cod3', 102)], 0) if kbus != 0 else 0
            cont3 = self._to_int(record[field_map.get('cont3', 103)], 0) if kbus != 0 else 0
            node3 = self._to_int(record[field_map.get('node3', 104)], 0) if kbus != 0 else 0
            rma3 = self._to_float(record[field_map.get('rma3', 105)], 1.1) if kbus != 0 else 1.1
            rmi3 = self._to_float(record[field_map.get('rmi3', 106)], 0.9) if kbus != 0 else 0.9
            vma3 = self._to_float(record[field_map.get('vma3', 107)], 1.1) if kbus != 0 else 1.1
            vmi3 = self._to_float(record[field_map.get('vmi3', 108)], 0.9) if kbus != 0 else 0.9
            ntp3 = self._to_int(record[field_map.get('ntp3', 109)], 33) if kbus != 0 else 33
            tab3 = self._to_int(record[field_map.get('tab3', 110)], 0) if kbus != 0 else 0
            cr3 = self._to_float(record[field_map.get('cr3', 111)], 0.0) if kbus != 0 else 0.0
            cx3 = self._to_float(record[field_map.get('cx3', 112)], 0.0) if kbus != 0 else 0.0
            cnxa3 = self._to_float(record[field_map.get('cnxa3', 113)], 0.0) if kbus != 0 else 0.0
            
            # Transformer type already determined above
            
            # Clean up the vector group string to avoid PSS/E errors
            if vecgrp in ['None', 'NONE', ''] or vecgrp is None:
                vecgrp = ""
            elif vecgrp.replace('.', '').replace('E', '').replace('+', '').replace('-', '').isdigit():
                # PSS/E doesn't accept numeric vector groups, set to empty
                vecgrp = ""
            
            # Ensure nominal voltages are reasonable (not zero or extremely large)
            if nomv1 == 0.0 or nomv1 > 10000:
                nomv1 = 0.0
            if nomv2 == 0.0 or nomv2 > 10000:  
                nomv2 = 0.0
            if nomv3 == 0.0 or nomv3 > 10000:
                nomv3 = 0.0
                
            # Ensure tap positions are reasonable
            if ntp1 <= 0:
                ntp1 = 33
            if ntp2 <= 0:
                ntp2 = 33
            if ntp3 <= 0:
                ntp3 = 33
            
            # RECORD 1: Basic transformer data
            if self.version in [RawVersion.V34, RawVersion.V35]:
                output_file.write(f"{ibus:>6},{jbus:>6},{kbus:>6},'{ckt:<2}',{cw:>2},{cz:>2},{cm:>2},{mag1:>8.2f},{mag2:>8.2f},{nmet:>2},'{name:<12}',{stat:>2},{o1:>2},{f1:>8.4f},{o2:>2},{f2:>8.4f},{o3:>2},{f3:>8.4f},{o4:>2},{f4:>8.4f},'{vecgrp:<12}',{zcod:>2}\n")
            else:  # V33
                output_file.write(f"{ibus:>6},{jbus:>6},{kbus:>6},'{ckt:<2}',{cw:>2},{cz:>2},{cm:>2},{mag1:>8.2f},{mag2:>8.2f},{nmet:>2},'{name:<12}',{stat:>2},{o1:>2},{f1:>8.4f},{o2:>2},{f2:>8.4f},{o3:>2},{f3:>8.4f},{o4:>2},{f4:>8.4f},'{vecgrp:<12}'\n")
            
            # RECORD 2: Impedance data
            output_file.write(f"{r1_2:>10.6f},{x1_2:>10.6f},{sbase1_2:>8.2f},{r2_3:>10.6f},{x2_3:>10.6f},{sbase2_3:>8.2f},{r3_1:>10.6f},{x3_1:>10.6f},{sbase3_1:>8.2f},{vmstar:>8.2f},{anstar:>8.2f}\n")
            
            # RECORD 3: Winding 1 data
            if self.version in [RawVersion.V34, RawVersion.V35]:
                # V34/V35: 12 ratings format with CNXA field
                if self.version == RawVersion.V35:
                    output_file.write(f"{windv1:>8.6f},{nomv1:>8.2f},{ang1:>8.2f},{wdg1rate1:>8.2f},{wdg1rate2:>8.2f},{wdg1rate3:>8.2f},    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,{cod1:>2},{cont1:>4},{node1:>4},{rma1:>8.5f},{rmi1:>8.5f},{vma1:>8.5f},{vmi1:>8.5f},{ntp1:>2},{tab1:>2},{cr1:>8.5f},{cx1:>8.5f},{cnxa1:>8.5f}\n")
                else:  # V34
                    output_file.write(f"{windv1:>8.5f},{nomv1:>8.3f},{ang1:>8.3f},{wdg1rate1:>8.2f},{wdg1rate2:>8.2f},{wdg1rate3:>8.2f},     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,{cod1:>2},{cont1:>6},{rma1:>8.5f},{rmi1:>8.4f},{vma1:>8.5f},{vmi1:>8.4f},{ntp1:>3},{tab1:>2},{cr1:>8.5f},{cx1:>8.5f},{cnxa1:>8.3f}\n")
            else:
                # V33: 3 ratings format with CNXA1
                output_file.write(f"{windv1:>8.6f},{nomv1:>8.2f},{ang1:>8.2f},{wdg1rate1:>8.2f},{wdg1rate2:>8.2f},{wdg1rate3:>8.2f},{cod1:>2},{cont1:>4},{rma1:>8.5f},{rmi1:>8.5f},{vma1:>8.5f},{vmi1:>8.5f},{ntp1:>2},{tab1:>2},{cr1:>8.5f},{cx1:>8.5f},{cnxa1:>8.5f}\n")
            
            # RECORD 4: Winding 2 data
            if self.version in [RawVersion.V34, RawVersion.V35]:
                # V34/V35: 12 ratings format with CNXA field
                if self.version == RawVersion.V35:
                    output_file.write(f"{windv2:>8.6f},{nomv2:>8.2f},{ang2:>8.2f},{wdg2rate1:>8.2f},{wdg2rate2:>8.2f},{wdg2rate3:>8.2f},    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,{cod2:>2},{cont2:>4},{node2:>4},{rma2:>8.5f},{rmi2:>8.5f},{vma2:>8.5f},{vmi2:>8.5f},{ntp2:>2},{tab2:>2},{cr2:>8.5f},{cx2:>8.5f},{cnxa2:>8.5f}\n")
                else:  # V34
                    output_file.write(f"{windv2:>8.5f},{nomv2:>8.3f},{ang2:>8.3f},{wdg2rate1:>8.2f},{wdg2rate2:>8.2f},{wdg2rate3:>8.2f},     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,{cod2:>2},{cont2:>6},{rma2:>8.5f},{rmi2:>8.4f},{vma2:>8.5f},{vmi2:>8.4f},{ntp2:>3},{tab2:>2},{cr2:>8.5f},{cx2:>8.5f},{cnxa2:>8.3f}\n")
            else:
                # V33: 3 ratings format with CNXA2
                output_file.write(f"{windv2:>8.6f},{nomv2:>8.2f},{ang2:>8.2f},{wdg2rate1:>8.2f},{wdg2rate2:>8.2f},{wdg2rate3:>8.2f},{cod2:>2},{cont2:>4},{rma2:>8.5f},{rmi2:>8.5f},{vma2:>8.5f},{vmi2:>8.5f},{ntp2:>2},{tab2:>2},{cr2:>8.5f},{cx2:>8.5f},{cnxa2:>8.5f}\n")
            
            # RECORD 5: Winding 3 data (only for 3-winding transformers)
            if is_3winding:
                if self.version in [RawVersion.V34, RawVersion.V35]:
                    # V34/V35: 12 ratings format with CNXA field
                    if self.version == RawVersion.V35:
                        output_file.write(f"{windv3:>8.6f},{nomv3:>8.2f},{ang3:>8.2f},{wdg3rate1:>8.2f},{wdg3rate2:>8.2f},{wdg3rate3:>8.2f},    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,    0.00,{cod3:>2},{cont3:>4},{node3:>4},{rma3:>8.5f},{rmi3:>8.5f},{vma3:>8.5f},{vmi3:>8.5f},{ntp3:>2},{tab3:>2},{cr3:>8.5f},{cx3:>8.5f},{cnxa3:>8.5f}\n")
                    else:  # V34
                        output_file.write(f"{windv3:>8.5f},{nomv3:>8.3f},{ang3:>8.3f},{wdg3rate1:>8.2f},{wdg3rate2:>8.2f},{wdg3rate3:>8.2f},     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,     0.00,{cod3:>2},{cont3:>6},{rma3:>8.5f},{rmi3:>8.4f},{vma3:>8.5f},{vmi3:>8.4f},{ntp3:>3},{tab3:>2},{cr3:>8.5f},{cx3:>8.5f},{cnxa3:>8.3f}\n")
                else:
                    # V33: 3 ratings format with CNXA3
                    output_file.write(f"{windv3:>8.6f},{nomv3:>8.2f},{ang3:>8.2f},{wdg3rate1:>8.2f},{wdg3rate2:>8.2f},{wdg3rate3:>8.2f},{cod3:>2},{cont3:>4},{rma3:>8.5f},{rmi3:>8.5f},{vma3:>8.5f},{vmi3:>8.5f},{ntp3:>2},{tab3:>2},{cr3:>8.5f},{cx3:>8.5f},{cnxa3:>8.5f}\n")
            
            records_written += 1
        
        return records_written


class FixedShuntWriter(RawSectionWriter):
    """Writer for Fixed Shunt Data (Section 7)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        return "@!I,'ID',STATUS,GL,BL"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write fixed shunt data."""
        fixed_shunt_section = data.get('fixed_shunt', {})
        if 'fields' not in fixed_shunt_section or 'data' not in fixed_shunt_section:
            return 0
        
        field_names = fixed_shunt_section['fields']
        records_written = 0
        
        for record in fixed_shunt_section['data']:
            # Use centralized field mapping with RAWX field names
            canonical_record = self.mapper.map_record('fixed_shunt', record, field_names)
            
            ibus = canonical_record.get('ibus', 0)
            shuntid = canonical_record.get('shuntid', '1')[:2]
            stat = canonical_record.get('stat', 1)
            gl = canonical_record.get('gl', 0.0)
            bl = canonical_record.get('bl', 0.0)
            
            output_file.write(f"    {ibus},'{shuntid}',{stat},{gl:>10.6f},{bl:>10.6f}\n")
            records_written += 1
        
        return records_written


class AreaWriter(RawSectionWriter):
    """Writer for Area Interchange Data (Section 8)."""
    
    def get_field_header(self) -> str:
        return "@!I,ISW,PDES,PTOL,'ARNAME'"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write area data."""
        area_section = data.get('area', {})
        if 'fields' not in area_section or 'data' not in area_section:
            return 0
        
        fields = area_section['fields']
        records_written = 0
        
        for record in area_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format - RAWX uses 'iarea' for area number
            area_num = accessor.get_int('iarea', accessor.get_int('i', accessor.get_int('area', accessor.get_int('area_num', 0))))
            isw = accessor.get_int('isw', accessor.get_int('swing_bus', 0))
            pdes = accessor.get_float('pdes', accessor.get_float('desired_net_interchange', 0.0))
            ptol = accessor.get_float('ptol', accessor.get_float('interchange_tolerance', 0.0))
            arname = accessor.get_str('arname', accessor.get_str('area_name', ''))[:12]
            
            output_file.write(f"     {area_num},{isw:>6},{pdes:>11.2f},{ptol:>10.2f},'{arname:<12}'\n")
            records_written += 1
        
        return records_written


class ZoneWriter(RawSectionWriter):
    """Writer for Zone Data (Section 14)."""
    
    def get_field_header(self) -> str:
        return "@!I,'ZONAME'"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write zone data."""
        zone_section = data.get('zone', {})
        if 'fields' not in zone_section or 'data' not in zone_section:
            return 0
        
        fields = zone_section['fields']
        records_written = 0
        
        for record in zone_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format - RAWX uses 'izone' for zone number
            zone_num = accessor.get_int('izone', accessor.get_int('i', accessor.get_int('zone', accessor.get_int('zone_num', 0))))
            zoname = accessor.get_str('zoname', accessor.get_str('zone_name', ''))[:12]
            
            output_file.write(f"     {zone_num},'{zoname:<12}'\n")
            records_written += 1
        
        return records_written


class OwnerWriter(RawSectionWriter):
    """Writer for Owner Data (Section 16)."""
    
    def get_field_header(self) -> str:
        return "@!I,'OWNAME'"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write owner data."""
        owner_section = data.get('owner', {})
        if 'fields' not in owner_section or 'data' not in owner_section:
            return 0
        
        fields = owner_section['fields']
        records_written = 0
        
        for record in owner_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format - RAWX uses 'iowner' for owner number
            owner_num = accessor.get_int('iowner', accessor.get_int('i', accessor.get_int('owner', accessor.get_int('owner_num', 0))))
            owname = accessor.get_str('owname', accessor.get_str('owner_name', ''))[:12]
            
            output_file.write(f"     {owner_num},'{owname:<12}'\n")
            records_written += 1
        
        return records_written


class SubstationWriter(RawSectionWriter):
    """Writer for Substation Data (Section 9 - V35 only)."""
    
    def get_field_header(self) -> str:
        # For node-breaker, we don't write a simple header - we write hierarchical blocks
        if self.modeling_approach == "node_breaker":
            return ""  # No simple header for hierarchical blocks
        return "@!I,'NAME'"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write substation data - hierarchical blocks for node-breaker, flat data for bus-branch."""
        
        if self.modeling_approach == "node_breaker":
            return self._write_hierarchical_substation_blocks(data, output_file)
        else:
            return self._write_flat_substation_data(data, output_file)
    
    def _write_flat_substation_data(self, data: Dict[str, Any], output_file) -> int:
        """Write flat substation data for bus-branch models."""
        substation_section = data.get('substation', {})
        if 'fields' not in substation_section or 'data' not in substation_section:
            return 0
        
        fields = substation_section['fields']
        records_written = 0
        
        for record in substation_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format
            substation = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('i', 0)))
            name = accessor.get_str('name', accessor.get_str('sub_name', ''))[:40]
            
            output_file.write(f"     {substation},'{name}'\n")
            records_written += 1
        
        return records_written
    
    def _write_hierarchical_substation_blocks(self, data: Dict[str, Any], output_file) -> int:
        """Write hierarchical substation data blocks for node-breaker models."""
        
        # Get all the sections we need
        sub_section = data.get('substation', {})
        node_section = data.get('node', {})
        switching_section = data.get('switching_device', {})
        terminal_section = data.get('terminal', {})
        

        
        # Check if we have the required data
        if not sub_section or 'data' not in sub_section or not sub_section['data']:
            print(f"📊 No substation data found, returning 0")
            return 0
            
        # Group all data by substation
        grouped_data = self._group_data_by_substation(sub_section, node_section, switching_section, terminal_section)

        
        # Write each substation block 
        total_records = 0
        for sub_id, sub_data in grouped_data.items():
            records = self._write_single_substation_block(sub_data, output_file)
            total_records += records

        

        return total_records
    
    def _group_data_by_substation(self, sub_section, node_section, switching_section, terminal_section):
        """Group node, switching device, and terminal data by substation."""
        substations = {}
        
        # First, collect all substations
        if 'data' in sub_section:
            sub_fields = sub_section['fields']
            for record in sub_section['data']:
                accessor = FieldAccessor(sub_fields, record)
                # Generic field mapping for substation ID - try common field names
                sub_id = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('isub', accessor.get_int('i', 0))))
                substations[sub_id] = {
                    'substation_data': record,
                    'substation_fields': sub_fields,
                    'nodes': [],
                    'switching_devices': [],
                    'terminals': []
                }
        
        # Group nodes by substation
        if 'data' in node_section:
            node_fields = node_section['fields']
            for record in node_section['data']:
                accessor = FieldAccessor(node_fields, record)
                # Generic field mapping for substation ID
                sub_id = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('isub', accessor.get_int('sub', 0))))
                if sub_id in substations:
                    substations[sub_id]['nodes'].append((record, node_fields))
        
        # Group switching devices by substation
        if 'data' in switching_section:
            sw_fields = switching_section['fields']
            for record in switching_section['data']:
                accessor = FieldAccessor(sw_fields, record)
                # Generic field mapping for substation ID
                sub_id = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('isub', accessor.get_int('sub', 0))))
                if sub_id in substations:
                    substations[sub_id]['switching_devices'].append((record, sw_fields))
        
        # Group terminals by substation  
        if 'data' in terminal_section:
            term_fields = terminal_section['fields']
            for record in terminal_section['data']:
                accessor = FieldAccessor(term_fields, record)
                # Generic field mapping for substation ID
                sub_id = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('isub', accessor.get_int('sub', 0))))
                if sub_id in substations:
                    substations[sub_id]['terminals'].append((record, term_fields))
        
        return substations
    
    def _write_single_substation_block(self, sub_data, output_file) -> int:
        """Write a single hierarchical substation block."""
        records_written = 0
        
        # Write substation header
        output_file.write("@! BEGIN SUBSTATION DATA BLOCK\n")
        output_file.write("@!  IS,                'N A M E'                 ,    LATITUDE,   LONGITUDE,     SGR\n")
        
        # Write substation record
        accessor = FieldAccessor(sub_data['substation_fields'], sub_data['substation_data'])
        # Generic field mapping for substation ID
        sub_id = accessor.get_int('substation', accessor.get_int('sub_id', accessor.get_int('isub', accessor.get_int('i', 0))))
        name = accessor.get_str('name', accessor.get_str('sub_name', accessor.get_str('station', '')))[:40]
        latitude = accessor.get_float('latitude', accessor.get_float('lat', 0.0))
        longitude = accessor.get_float('longitude', accessor.get_float('lon', accessor.get_float('long', 0.0)))
        sgr = accessor.get_float('sgr', accessor.get_float('sbase_ratio', 0.1))
        
        output_file.write(f"     {sub_id:>1},'{name:<40}',{latitude:>12.7f},{longitude:>12.7f},{sgr:>8.4f}\n")
        records_written += 1
        
        # Write node data section  
        output_file.write("@! BEGIN SUBSTATION NODE DATA\n")
        output_file.write("@!  NI,                'N A M E'                 ,     I,STATUS,   VM  ,   VA\n")
        
        for node_record, node_fields in sub_data['nodes']:
            accessor = FieldAccessor(node_fields, node_record)
            # Generic field mapping for node data
            ni = accessor.get_int('node', accessor.get_int('node_id', accessor.get_int('inode', accessor.get_int('ni', 0))))
            node_name = accessor.get_str('name', accessor.get_str('node_name', ''))[:40]
            bus_i = accessor.get_int('bus', accessor.get_int('bus_id', accessor.get_int('ibus', accessor.get_int('i', 0))))
            status = accessor.get_int('status', accessor.get_int('stat', 1))
            
            output_file.write(f"     {ni:>1},'{node_name:<40}',{bus_i:>6},{status:>6}\n")
            records_written += 1
        
        # Write switching device data section
        output_file.write("     0 / END OF SUBSTATION NODE DATA, BEGIN SUBSTATION SWITCHING DEVICE DATA\n")
        output_file.write("@!  NI, NJ,CKTID,                'N A M E'                 ,  TYPE,STATUS, NSTAT,     X  ,   RATE1,   RATE2,   RATE3\n")
        
        for sw_record, sw_fields in sub_data['switching_devices']:
            accessor = FieldAccessor(sw_fields, sw_record)
            # Generic field mapping for switching device data
            ni = accessor.get_int('from_node', accessor.get_int('node_i', accessor.get_int('inode', accessor.get_int('ni', 0))))
            nj = accessor.get_int('to_node', accessor.get_int('node_j', accessor.get_int('jnode', accessor.get_int('nj', 0))))
            ckt = accessor.get_str('circuit_id', accessor.get_str('ckt', accessor.get_str('swdid', accessor.get_str('id', '1'))))[:2]
            sw_name = accessor.get_str('name', accessor.get_str('switch_name', accessor.get_str('device_name', '')))[:40]
            sw_type = accessor.get_int('switch_type', accessor.get_int('type', accessor.get_int('device_type', 2)))
            status = accessor.get_int('status', accessor.get_int('stat', 1))
            nstatus = accessor.get_int('node_status', accessor.get_int('nstat', accessor.get_int('nstatus', 1)))
            x = accessor.get_float('reactance', accessor.get_float('x', accessor.get_float('xpu', 0.0001)))
            rate1 = accessor.get_float('rate1', accessor.get_float('rating_1', 0.0))
            rate2 = accessor.get_float('rate2', accessor.get_float('rating_2', 0.0))
            rate3 = accessor.get_float('rate3', accessor.get_float('rating_3', 0.0))
            
            output_file.write(f"     {ni:>1}, {nj:>1}, '{ckt:<2}','{sw_name:<40}',{sw_type:>6},{status:>6},{nstatus:>6},{x:>8.5f},{rate1:>8.1f},{rate2:>8.1f},{rate3:>8.1f}\n")
            records_written += 1
        
        # Write terminal data section
        output_file.write("     0 / END OF SUBSTATION SWITCHING DEVICE DATA, BEGIN SUBSTATION TERMINAL DATA\n")
        output_file.write("@!   I, NI, TYP,     J,     K, ID\n")
        
        for term_record, term_fields in sub_data['terminals']:
            accessor = FieldAccessor(term_fields, term_record)
            # Generic field mapping for terminal data
            bus_i = accessor.get_int('bus', accessor.get_int('bus_id', accessor.get_int('ibus', accessor.get_int('i', 0))))
            ni = accessor.get_int('node', accessor.get_int('node_id', accessor.get_int('inode', accessor.get_int('ni', 0))))
            typ = accessor.get_str('terminal_type', accessor.get_str('type', accessor.get_str('typ', 'B')))
            bus_j = accessor.get_int('to_bus', accessor.get_int('bus_j', accessor.get_int('jbus', accessor.get_int('j', 0))))
            bus_k = accessor.get_int('tertiary_bus', accessor.get_int('bus_k', accessor.get_int('kbus', accessor.get_int('k', 0))))
            term_id = accessor.get_str('terminal_id', accessor.get_str('equipment_id', accessor.get_str('eqid', accessor.get_str('id', '1'))))[:2]
            
            if bus_j == 0 and bus_k == 0:
                # Simple terminal (load, generator, etc.)
                output_file.write(f"   {bus_i:>4}, {ni:>2}, '{typ}',              '{term_id:<2}'\n")
            elif bus_k == 0:
                # Two-terminal device
                output_file.write(f"   {bus_i:>4}, {ni:>2}, '{typ}',{bus_j:>6},       '{term_id:<2}'\n")
            else:
                # Three-terminal device (transformer)
                output_file.write(f"   {bus_i:>4}, {ni:>2}, '{typ}',{bus_j:>6},{bus_k:>6}, '{term_id:<2}'\n")
            records_written += 1
        
        # End this substation block
        output_file.write("     0 / END OF SUBSTATION TERMINAL DATA\n")
        
        return records_written


class NodeWriter(RawSectionWriter):
    """Writer for Node Data (Section 9) - V35 node/breaker only."""
    
    def get_field_header(self) -> str:
        return "@!I,'NAME',SUB"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write node data. For node-breaker models, this is handled by SubstationWriter hierarchical blocks."""
        if self.modeling_approach == "node_breaker":
            return 0  # Handled by hierarchical SubstationWriter
        
        # For hybrid and other approaches, write flat node data
        node_section = data.get('node', {})
        if 'fields' not in node_section or 'data' not in node_section:
            return 0
        
        fields = node_section['fields']
        records_written = 0
        
        for record in node_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Generic field mapping for node data
            node = accessor.get_int('node', accessor.get_int('node_id', accessor.get_int('inode', accessor.get_int('i', 0))))
            name = accessor.get_str('name', accessor.get_str('node_name', ''))[:40]
            substation = accessor.get_int('substation', accessor.get_int('sub', accessor.get_int('sub_id', accessor.get_int('isub', 0))))
            
            output_file.write(f"     {node},'{name}',{substation}\n")
            records_written += 1
        
        return records_written


class SwitchingDeviceWriter(RawSectionWriter):
    """Writer for Switching Device Data (Section 10) - V35 node/breaker only."""
    
    def get_field_header(self) -> str:
        return "@!I,J,'CKT',X,RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,STATUS,NSTATUS,METERED,STYPE,'NAME'"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write switching device data. For node-breaker models, this is handled by SubstationWriter hierarchical blocks."""
        if self.modeling_approach == "node_breaker":
            return 0  # Handled by hierarchical SubstationWriter
        
        # For hybrid and other approaches, write flat switching device data
        switching_device_section = data.get('switching_device', {})
        if 'fields' not in switching_device_section or 'data' not in switching_device_section:
            return 0
        
        fields = switching_device_section['fields']
        records_written = 0
        
        for record in switching_device_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Generic field mapping for switching device data
            ibus = accessor.get_int('from_bus', accessor.get_int('bus_i', accessor.get_int('ibus', accessor.get_int('i', 0))))
            jbus = accessor.get_int('to_bus', accessor.get_int('bus_j', accessor.get_int('jbus', accessor.get_int('j', 0))))
            ckt = accessor.get_str('circuit_id', accessor.get_str('ckt', accessor.get_str('swdid', accessor.get_str('id', '1'))))[:2]
            x = accessor.get_float('reactance', accessor.get_float('x', accessor.get_float('xpu', 0.0)))
            
            # Get ratings (12 for V35)
            rates = []
            for i in range(1, 13):
                rate_field = f'rate{i}'
                rate_val = accessor.get_float(rate_field, accessor.get_float(f'rating_{i}', 0.0))
                rates.append(rate_val)
            
            status = accessor.get_int('status', accessor.get_int('stat', 1))
            nstatus = accessor.get_int('node_status', accessor.get_int('nstat', accessor.get_int('nstatus', 1)))
            metered = accessor.get_int('metered', accessor.get_int('meter', 1))
            stype = accessor.get_int('switch_type', accessor.get_int('stype', accessor.get_int('type', accessor.get_int('device_type', 1))))
            name = accessor.get_str('name', accessor.get_str('device_name', accessor.get_str('switch_name', '')))[:40]
            
            rate_str = ','.join([f'{rate:8.2f}' for rate in rates])
            
            output_file.write(f"   {ibus:>4},{jbus:>4},'{ckt}',{x:10.6f},{rate_str},{status:>2},{nstatus:>2},{metered:>2},{stype:>2},'{name}'\n")
            records_written += 1
        
        return records_written


class TerminalWriter(RawSectionWriter):
    """Writer for Terminal Data (Section 11) - V35 node/breaker only."""
    
    def get_field_header(self) -> str:
        return "@!I,'NAME',NODE"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write terminal data. For node-breaker models, this is handled by SubstationWriter hierarchical blocks."""
        if self.modeling_approach == "node_breaker":
            return 0  # Handled by hierarchical SubstationWriter
        
        terminal_section = data.get('terminal', {})
        if 'fields' not in terminal_section or 'data' not in terminal_section:
            return 0
        
        fields = terminal_section['fields']
        records_written = 0
        
        for record in terminal_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format
            terminal = accessor.get_int('terminal', accessor.get_int('term_id', accessor.get_int('i', 0)))
            name = accessor.get_str('name', accessor.get_str('term_name', ''))[:40]
            node = accessor.get_int('node', accessor.get_int('node_id', 0))
            
            output_file.write(f"     {terminal},'{name}',{node}\n")
            records_written += 1
        
        return records_written


class DcLine2TerminalWriter(RawSectionWriter):
    """Writer for Two-Terminal DC Data (Section 10) - PSS/E V35 3-Record Format."""
    
    def get_field_header(self) -> str:
        # PSS/E V35 Two-Terminal DC format requires 3 field headers
        return ("@!'NAME',MDC,RDC,SETVL,VSCHD,VCMOD,RCOMP,DELTI,METER,DCVMIN,CCCITMX,CCCACC\n"
                "@!IPR,NBR,ANMXR,ANMNR,RCR,XCR,EBASR,TRR,TAPR,TMXR,TMNR,STPR,ICR,NDR,IFR,ITR,IDR,XCAPR\n"
                "@!IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,NDI,IFI,ITI,IDI,XCAPI")
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write two-terminal DC data using correct PSS/E V35 multi-record format."""
        # Look for canonical section name after mapping, not original RAWX name
        twotermdc_section = data.get('dc_line_2t', {})
        if 'fields' not in twotermdc_section or 'data' not in twotermdc_section:
            return 0
        
        fields = twotermdc_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in twotermdc_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('dc_line_2t', source_record)
            
                # Extract mapped fields using canonical names
                name = str(mapped_record.get('name', 'DC_LINE'))
                # Apply version-specific name length limits
                if self.version == RawVersion.V33:
                    name = name[:12]  # V33: 12 character limit
                else:
                    name = name[:60]  # V34/V35: 60 character limit
                mdc = mapped_record.get('mdc', 0)
                rdc = mapped_record.get('rdc', 0.0)
                setvl = mapped_record.get('setvl', 0.0)
                vschd = mapped_record.get('vschd', 0.0)
                vcmod = mapped_record.get('vcmod', 0.0)
                rcomp = mapped_record.get('rcomp', 0.0)
                delti = mapped_record.get('delti', 0.0)
                meter = str(mapped_record.get('met', 'I'))
                dcvmin = mapped_record.get('dcvmin', 0.0)
                cccitmx = mapped_record.get('cccitmx', 20)
                cccacc = mapped_record.get('cccacc', 1.0)
                
                # Record 2: Rectifier data
                ipr = mapped_record.get('ipr', 0)
                nbr = mapped_record.get('nbr', 0)
                anmxr = mapped_record.get('anmxr', 0.0)
                anmnr = mapped_record.get('anmnr', 0.0)
                rcr = mapped_record.get('rcr', 0.0)
                xcr = mapped_record.get('xcr', 0.0)
                ebasr = mapped_record.get('ebasr', 0.0)
                trr = mapped_record.get('trr', 0.0)
                tapr = mapped_record.get('tapr', 0.0)
                tmxr = mapped_record.get('tmxr', 0.0)
                tmnr = mapped_record.get('tmnr', 0.0)
                stpr = mapped_record.get('stpr', 0.0)
                icr = mapped_record.get('icr', 0)
                ndr = mapped_record.get('ndr', 0)
                ifr = mapped_record.get('ifr', 0)
                itr = mapped_record.get('itr', 0)
                idr = str(mapped_record.get('idr', '1'))  # Keep as string
                xcapr = mapped_record.get('xcapr', 0.0)
                
                # Record 3: Inverter data
                ipi = mapped_record.get('ipi', 0)
                nbi = mapped_record.get('nbi', 0)
                anmxi = mapped_record.get('anmxi', 0.0)
                anmni = mapped_record.get('anmni', 0.0)
                rci = mapped_record.get('rci', 0.0)
                xci = mapped_record.get('xci', 0.0)
                ebasi = mapped_record.get('ebasi', 0.0)
                tri = mapped_record.get('tri', 0.0)
                tapi = mapped_record.get('tapi', 0.0)
                tmxi = mapped_record.get('tmxi', 0.0)
                tmni = mapped_record.get('tmni', 0.0)
                stpi = mapped_record.get('stpi', 0.0)
                ici = mapped_record.get('ici', 0)
                ndi = mapped_record.get('ndi', 0)
                ifi = mapped_record.get('ifi', 0)
                iti = mapped_record.get('iti', 0)
                idi = str(mapped_record.get('idi', '1'))  # Keep as string
                xcapi = mapped_record.get('xcapi', 0.0)
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                
                # Record 1: Main DC line data
                name = accessor.get_str('name', 'DC_LINE')
                # Apply version-specific name length limits
                if self.version == RawVersion.V33:
                    name = name[:12]  # V33: 12 character limit
                else:
                    name = name[:60]  # V34/V35: 60 character limit
                mdc = accessor.get_int('mdc', 0)
                rdc = accessor.get_float('rdc', 0.0)
                setvl = accessor.get_float('setvl', 0.0)
                vschd = accessor.get_float('vschd', 0.0)
                vcmod = accessor.get_float('vcmod', 0.0)
                rcomp = accessor.get_float('rcomp', 0.0)
                delti = accessor.get_float('delti', 0.0)
                meter = accessor.get_str('meter', 'I')
                dcvmin = accessor.get_float('dcvmin', 0.0)
                cccitmx = accessor.get_int('cccitmx', 20)
                cccacc = accessor.get_float('cccacc', 1.0)
                
                # Record 2: Rectifier data
                ipr = accessor.get_int('ipr', 0)
                nbr = accessor.get_int('nbr', 0)
                anmxr = accessor.get_float('anmxr', 0.0)
                anmnr = accessor.get_float('anmnr', 0.0)
                rcr = accessor.get_float('rcr', 0.0)
                xcr = accessor.get_float('xcr', 0.0)
                ebasr = accessor.get_float('ebasr', 0.0)
                trr = accessor.get_float('trr', 0.0)
                tapr = accessor.get_float('tapr', 0.0)
                tmxr = accessor.get_float('tmxr', 0.0)
                tmnr = accessor.get_float('tmnr', 0.0)
                stpr = accessor.get_float('stpr', 0.0)
                icr = accessor.get_int('icr', 0)
                ndr = accessor.get_int('ndr', 0)
                ifr = accessor.get_int('ifr', 0)
                itr = accessor.get_int('itr', 0)
                idr = accessor.get_int('idr', 0)  # Safe conversion handles 'T4' strings
                xcapr = accessor.get_float('xcapr', 0.0)
                
                # Record 3: Inverter data
                ipi = accessor.get_int('ipi', 0)
                nbi = accessor.get_int('nbi', 0)
                anmxi = accessor.get_float('anmxi', 0.0)
                anmni = accessor.get_float('anmni', 0.0)
                rci = accessor.get_float('rci', 0.0)
                xci = accessor.get_float('xci', 0.0)
                ebasi = accessor.get_float('ebasi', 0.0)
                tri = accessor.get_float('tri', 0.0)
                tapi = accessor.get_float('tapi', 0.0)
                tmxi = accessor.get_float('tmxi', 0.0)
                tmni = accessor.get_float('tmni', 0.0)
                stpi = accessor.get_float('stpi', 0.0)
                ici = accessor.get_int('ici', 0)
                ndi = accessor.get_int('ndi', 0)
                ifi = accessor.get_int('ifi', 0)
                iti = accessor.get_int('iti', 0)
                idi = accessor.get_int('idi', 0)  # Safe conversion handles strings
                xcapi = accessor.get_float('xcapi', 0.0)
            
            # Version-specific formatting for Two-Terminal DC
            if self.version == RawVersion.V33:
                # V33: Simplified format with proper spacing after quoted strings
                output_file.write(f" '{name}', {mdc}, {rdc:8.4f}, {setvl:7.2f}, {vschd:7.2f}, {vcmod:7.2f}, {rcomp:7.4f}, {delti:8.5f},'{meter}',   {dcvmin:5.2f},{cccitmx}, {cccacc:8.5f}\n")
                output_file.write(f"    {ipr:>3},     {nbr}, {anmxr:7.3f}, {anmnr:7.3f}, {rcr:7.4f}, {xcr:7.4f}, {ebasr:6.2f}, {trr:8.5f}, {tapr:8.5f}, {tmxr:8.5f}, {tmnr:8.5f}, {stpr:8.5f},     {icr},  {ndr},     {ifr},     {itr},'{idr}', {xcapr:8.4f}\n")
                output_file.write(f"   {ipi:>4},     {nbi}, {anmxi:7.3f}, {anmni:7.3f}, {rci:7.4f}, {xci:7.4f}, {ebasi:6.2f}, {tri:8.5f}, {tapi:8.5f}, {tmxi:8.5f}, {tmni:8.5f}, {stpi:8.5f},     {ici},  {ndi},   {ifi}, {iti},'{idi}', {xcapi:8.4f}\n")
            else:
                # V34/V35: Enhanced format
                output_file.write(f"'{name}',{mdc:>2},{rdc:>8.5f},{setvl:>8.2f},{vschd:>8.2f},{vcmod:>8.2f},{rcomp:>8.4f},{delti:>8.5f},'{meter}',{dcvmin:>8.2f},{cccitmx:>2},{cccacc:>8.5f}\n")
                output_file.write(f"{ipr:>6},{nbr:>6},{anmxr:>8.3f},{anmnr:>8.3f},{rcr:>8.4f},{xcr:>8.4f},{ebasr:>8.2f},{trr:>8.5f},{tapr:>8.5f},{tmxr:>8.5f},{tmnr:>8.5f},{stpr:>8.5f},{icr:>6},{ndr:>3},{ifr:>6},{itr:>6},'{idr}', {xcapr:>8.4f}\n")
                output_file.write(f"{ipi:>6},{nbi:>6},{anmxi:>8.3f},{anmni:>8.3f},{rci:>8.4f},{xci:>8.4f},{ebasi:>8.2f},{tri:>8.5f},{tapi:>8.5f},{tmxi:>8.5f},{tmni:>8.5f},{stpi:>8.5f},{ici:>6},{ndi:>3},{ifi:>6},{iti:>6},'{idi}', {xcapi:>8.4f}\n")
            
            records_written += 3  # 3 records per DC line
        
        return records_written


class VscDcWriter(RawSectionWriter):
    """Writer for VSC DC Line Data (Section 9)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V33:
            # V33 VSC DC format - simpler header structure
            return ("@!NAME,MDC,RDC,O1,F1,O2,F2,O3,F3,O4,F4\n"
                    "@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,NREG,RMPCT\n"
                    "@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,NREG,RMPCT")
        else:  # V34, V35
            # V34/V35 VSC DC format - more comprehensive header
            return ("@!NAME,MDC,RDC,IBUS1,TYPE1,MODE1,DCSET1,ACSET1,ALOSS1,BLOSS1,MINLOSS1,SMAX1,IMAX1,PWF1,MAXQ1,MINQ1,VSREG1,NREG1,RMPCT1,IBUS2,TYPE2,MODE2,DCSET2,ACSET2,ALOSS2,BLOSS2,MINLOSS2,SMAX2,IMAX2,PWF2,MAXQ2,MINQ2,VSREG2,NREG2,RMPCT2\n"
                    "@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,NREG,RMPCT\n"
                    "@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,NREG,RMPCT")
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write VSC DC line data."""
        # Look for RAWX section name 'vscdc' or mapped 'vsc_dc'
        vsc_dc_section = data.get('vsc_dc', data.get('vscdc', {}))
        if 'fields' not in vsc_dc_section or 'data' not in vsc_dc_section:
            return 0
        
        records_written = 0
        
        field_names = vsc_dc_section['fields']
        
        for record in vsc_dc_section['data']:
            # Use centralized field mapping with RAWX device type
            canonical_record = self.mapper.map_record('vscdc', record, field_names)
            

            # Main record fields
            name = canonical_record.get('name', 'VSC_DC_LINE')
            mdc = canonical_record.get('mdc', 0)
            rdc = canonical_record.get('rdc', 0.0)
            
            # Bus 1 fields
            ibus1 = canonical_record.get('ibus1', 0)
            type1 = canonical_record.get('type1', 0)
            mode1 = canonical_record.get('mode1', 0)
            dcset1 = canonical_record.get('dcset1', 0.0)
            acset1 = canonical_record.get('acset1', 0.0)
            aloss1 = canonical_record.get('aloss1', 0.0)
            bloss1 = canonical_record.get('bloss1', 0.0)
            minloss1 = canonical_record.get('minloss1', 0.0)
            smax1 = canonical_record.get('smax1', 0.0)
            imax1 = canonical_record.get('imax1', 0.0)
            pwf1 = canonical_record.get('pwf1', 0.0)
            maxq1 = canonical_record.get('maxq1', 0.0)
            minq1 = canonical_record.get('minq1', 0.0)
            vsreg1 = canonical_record.get('vsreg1', 0)
            nreg1 = canonical_record.get('nreg1', 0)
            rmpct1 = canonical_record.get('rmpct1', 0.0)
            
            # Bus 2 fields
            ibus2 = canonical_record.get('ibus2', 0)
            type2 = canonical_record.get('type2', 0)
            mode2 = canonical_record.get('mode2', 0)
            dcset2 = canonical_record.get('dcset2', 0.0)
            acset2 = canonical_record.get('acset2', 0.0)
            aloss2 = canonical_record.get('aloss2', 0.0)
            bloss2 = canonical_record.get('bloss2', 0.0)
            minloss2 = canonical_record.get('minloss2', 0.0)
            smax2 = canonical_record.get('smax2', 0.0)
            imax2 = canonical_record.get('imax2', 0.0)
            pwf2 = canonical_record.get('pwf2', 0.0)
            maxq2 = canonical_record.get('maxq2', 0.0)
            minq2 = canonical_record.get('minq2', 0.0)
            vsreg2 = canonical_record.get('vsreg2', 0)
            nreg2 = canonical_record.get('nreg2', 0)
            rmpct2 = canonical_record.get('rmpct2', 0.0)
            
            if self.version == RawVersion.V33:
                # V33 format: Single line with ownership fractions only
                output_file.write(f'"{name}",{mdc}, {rdc:6.4f},   {canonical_record.get("o1", 1)},{canonical_record.get("f1", 0.0):6.4f},   {canonical_record.get("o2", 2)},{canonical_record.get("f2", 0.0):6.4f},   {canonical_record.get("o3", 3)},{canonical_record.get("f3", 0.0):6.4f},   {canonical_record.get("o4", 4)},{canonical_record.get("f4", 0.0):6.4f}\n')
                records_written += 1  # Only 1 record for V33
            else:
                # V34/V35 format: 3-line format with main + converter records
                # Write main record with proper VSC DC format (name + transformer rating data)
                output_file.write(f'"{name}",{mdc}, {rdc:6.4f},   {canonical_record.get("o1", 1)},{canonical_record.get("f1", 0.0):6.4f},   {canonical_record.get("o2", 2)},{canonical_record.get("f2", 0.0):6.4f},   {canonical_record.get("o3", 3)},{canonical_record.get("f3", 0.0):6.4f},   {canonical_record.get("o4", 4)},{canonical_record.get("f4", 0.0):6.4f}\n')
                
                # Write bus 1 converter record
                output_file.write(f'  {ibus1:>4},{type1:>4},{mode1:>4},{dcset1:>7.2f},{acset1:>7.5f},{aloss1:>7.3f},{bloss1:>8.3f},{minloss1:>8.3f},{smax1:>7.2f},{imax1:>7.2f},{pwf1:>7.5f},{maxq1:>7.2f},{minq1:>7.2f},{vsreg1:>6},{nreg1:>4},{rmpct1:>6.1f}\n')
                
                # Write bus 2 converter record  
                output_file.write(f'  {ibus2:>4},{type2:>4},{mode2:>4},{dcset2:>7.2f},{acset2:>7.5f},{aloss2:>7.3f},{bloss2:>8.3f},{minloss2:>8.3f},{smax2:>7.2f},{imax2:>7.2f},{pwf2:>7.5f},{maxq2:>7.2f},{minq2:>7.2f},{vsreg2:>6},{nreg2:>4},{rmpct2:>6.1f}\n')
                
                records_written += 3  # Count all 3 records
        
        return records_written


class ImpedanceCorrectionWriter(RawSectionWriter):
    """Writer for Impedance Correction Data (Section 17)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        if self.version == RawVersion.V35:
            # V35 format: Multi-line header with Real/Imaginary components for 12 temperature points
            return ("@!I,  T1,   Re(F1), Im(F1),   T2,   Re(F2), Im(F2),   T3,   Re(F3), Im(F3),   T4,   Re(F4), Im(F4),   T5,   Re(F5), Im(F5),   T6,   Re(F6), Im(F6)\n"
                    "@!    T7,   Re(F7), Im(F7),   T8,   Re(F8), Im(F8),   T9,   Re(F9), Im(F9),   T10, Re(F10),Im(F10),   T11, Re(F11),Im(F11),   T12, Re(F12),Im(F12)\n"
                    "@!      ...")
        elif self.version == RawVersion.V34:
            # V34 format: Similar to V35 but may have slight differences
            return ("@!I,  T1,   Re(F1), Im(F1),   T2,   Re(F2), Im(F2),   T3,   Re(F3), Im(F3),   T4,   Re(F4), Im(F4),   T5,   Re(F5), Im(F5),   T6,   Re(F6), Im(F6)\n"
                    "@!    T7,   Re(F7), Im(F7),   T8,   Re(F8), Im(F8),   T9,   Re(F9), Im(F9),   T10, Re(F10),Im(F10),   T11, Re(F11),Im(F11),   T12, Re(F12),Im(F12)\n"
                    "@!      ...")
        else:  # V33
            # V33 format: Simpler format without complex numbers
            return "@!I,T1,F1,T2,F2,T3,F3,T4,F4,T5,F5,T6,F6,T7,F7,T8,F8,T9,F9,T10,F10,T11,F11"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write impedance correction data."""
        # Check for the section in the data (comes as 'impcor' from RAWX)
        impedance_correction_section = data.get('impcor', data.get('impedance_correction', {}))
        if 'fields' not in impedance_correction_section or 'data' not in impedance_correction_section:
            return 0
        
        records_written = 0
        
        # Group data by table number (itable field)
        tables = {}
        field_names = impedance_correction_section['fields']
        
        for record in impedance_correction_section['data']:
            # Use centralized field mapping with correct device type
            canonical_record = self.mapper.map_record('impcor', record, field_names)
            
            itable = canonical_record.get('itable', 0)  # Table number
            tap = canonical_record.get('tap', 0.0)  # Tap position/temperature 
            refact = canonical_record.get('refact', 0.0)  # Real correction factor
            imfact = canonical_record.get('imfact', 0.0)  # Imaginary correction factor
            
            if itable not in tables:
                tables[itable] = []
            tables[itable].append((tap, refact, imfact))
        
        # Sort tables by table number and write each one
        for itable in sorted(tables.keys()):
            table_data = sorted(tables[itable], key=lambda x: x[0])  # Sort by temperature/tap
            
            if self.version in [RawVersion.V34, RawVersion.V35]:
                # V34/V35: Complex format with Real/Imaginary components
                # Each table can have up to 12 temperature points
                # If <= 6 points: single line, If > 6 points: two lines
                
                # First line: table number + first 6 temperature points
                line1 = f"  {itable:>2}"
                
                # Add first 6 temperature points to line 1
                for i in range(6):
                    if i < len(table_data):
                        tap, refact, imfact = table_data[i]
                        line1 += f"   {tap:>6.2f}   {refact:>8.5f}   {imfact:>8.5f}"
                    else:
                        # Pad with zeros if less than 6 points
                        line1 += f"     0.00    0.00000    0.00000"
                
                output_file.write(line1 + "\n")
                records_written += 1
                
                # Second line: only if there are more than 6 temperature points
                if len(table_data) > 6:
                    line2 = "    "  # Indent continuation line
                    
                    # Add next 6 temperature points (7-12) to line 2
                    for i in range(6, 12):
                        if i < len(table_data):
                            tap, refact, imfact = table_data[i]
                            line2 += f"   {tap:>6.2f}   {refact:>8.5f}   {imfact:>8.5f}"
                        else:
                            # Pad with zeros if less than 12 points total
                            line2 += f"     0.00    0.00000    0.00000"
                    
                    output_file.write(line2 + "\n")
                    records_written += 1
            else:  # V33
                # V33: Simple format without complex numbers, proper comma separation
                line = f"  {itable:>2}"
                
                # Add up to 11 temperature points with proper spacing
                for i in range(11):
                    if i < len(table_data):
                        tap, refact, imfact = table_data[i]
                        # V33 uses magnitude only, calculate from real/imag
                        magnitude = (refact**2 + imfact**2)**0.5
                        line += f",  {tap:>6.2f},{magnitude:>8.5f}"
                    else:
                        # Pad with zeros if less than 11 points
                        line += f",   0.00, 0.00000"
                
                # Add trailing fields to match exact format
                line += ", 0.0, 0.0, 0.0"
                
                output_file.write(line + "\n")
                records_written += 1
        
        return records_written


class MultiTerminalDcWriter(RawSectionWriter):
    """Writer for Multi-Terminal DC Data (Section 10)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        # PSS/E Multi-Terminal DC format requires 4 field headers for the 4 record types
        return ("@!NAME,NCONV,NDCBS,NDCLN,MDC,VCONV,VCONVN\n"
                "@!IBUS,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD\n"
                "@!IBUS,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD\n"
                "@!IBUS,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD")
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write multi-terminal DC data including all related sections."""
        # Look for RAWX section name 'ntermdc' or mapped 'multi_terminal_dc'
        multi_terminal_dc_section = data.get('ntermdc', data.get('multi_terminal_dc', {}))
        if 'fields' not in multi_terminal_dc_section or 'data' not in multi_terminal_dc_section:
            return 0
        
        records_written = 0
        
        # Get bus data to validate bus existence
        bus_section = data.get('bus', {})
        valid_buses = set()
        if 'data' in bus_section:
            bus_fields = bus_section.get('fields', [])
            bus_field_idx = None
            for i, field in enumerate(bus_fields):
                if field in ['ibus', 'bus', 'bus_number']:
                    bus_field_idx = i
                    break
            if bus_field_idx is not None:
                for bus_record in bus_section['data']:
                    if len(bus_record) > bus_field_idx:
                        valid_buses.add(int(bus_record[bus_field_idx]))
        
        field_names = multi_terminal_dc_section['fields']
        
        for record in multi_terminal_dc_section['data']:
            # Use centralized field mapping with RAWX device type
            canonical_record = self.mapper.map_record('ntermdc', record, field_names)
            
            # Main record fields
            name = canonical_record.get('name', 'MTDC_SYSTEM')
            nconv = canonical_record.get('nconv', 0)
            ndcbs = canonical_record.get('ndcbs', 0)
            ndcln = canonical_record.get('ndcln', 0)
            mdc = canonical_record.get('mdc', 0)
            vconv = canonical_record.get('vconv', 0)
            vconvn = canonical_record.get('vconvn', 0)
            
            # Skip invalid records with all zero values
            if nconv == 0 and ndcbs == 0 and ndcln == 0 and mdc == 0:
                continue
                
            # Write main record with proper field formatting (including vcmod which was missing)
            vcmod = canonical_record.get('vcmod', 0.0)
            output_file.write(f'"{name}",{nconv:>4},{ndcbs:>5},{ndcln:>5},{mdc:>5},{vconv:>4},{vcmod:>8.2f},{vconvn:>6}\n')
            records_written += 1
            
            # Write converter records if available
            conv_section = data.get('ntermdcconv', {})
            if 'fields' in conv_section and 'data' in conv_section:
                conv_fields = conv_section['fields']
                for conv_record in conv_section['data']:
                    if isinstance(conv_record, list) and len(conv_record) >= len(conv_fields):
                        conv_mapped = self.mapper.map_record('ntermdcconv', conv_record, conv_fields)
                        
                        # Check if this converter belongs to this DC system
                        conv_name = conv_mapped.get('name', '')
                        if conv_name == name:  # Match by system name
                            ib = conv_mapped.get('ib', 0)
                            # Skip converters connected to non-existent buses
                            if ib > 0 and ib in valid_buses:
                                nbrdg = conv_mapped.get('nbrdg', 0)
                                angmx = conv_mapped.get('angmx', 0.0)
                                angmn = conv_mapped.get('angmn', 0.0)
                                rc = conv_mapped.get('rc', 0.0)
                                xc = conv_mapped.get('xc', 0.0)
                                ebas = conv_mapped.get('ebas', 0.0)
                                tr = conv_mapped.get('tr', 0.0)
                                tap = conv_mapped.get('tap', 0.0)
                                tpmx = conv_mapped.get('tpmx', 0.0)
                                tpmn = conv_mapped.get('tpmn', 0.0)
                                tstp = conv_mapped.get('tstp', 0.0)
                                setvl = conv_mapped.get('setvl', 0.0)
                                dcpf = conv_mapped.get('dcpf', 0.0)
                                marg = conv_mapped.get('marg', 0.0)
                                cnvcod = conv_mapped.get('cnvcod', 0)
                                
                                output_file.write(f'   {ib}, {nbrdg}, {angmx:6.3f}, {angmn:6.3f}, {rc:6.4f},{xc:7.4f}, {ebas:6.2f},{tr:7.5f},{tap:7.5f},{tpmx:7.5f},{tpmn:7.5f},{tstp:7.5f}, {setvl:6.2f}, {dcpf:6.4f},{marg:7.5f}, {cnvcod}\n')
                                records_written += 1
            
            # Write DC bus records if available  
            bus_section = data.get('ntermdcbus', {})
            if 'fields' in bus_section and 'data' in bus_section:
                bus_fields = bus_section['fields']
                for bus_record in bus_section['data']:
                    if isinstance(bus_record, list) and len(bus_record) >= len(bus_fields):
                        bus_mapped = self.mapper.map_record('ntermdcbus', bus_record, bus_fields)
                        
                        # Check if this DC bus belongs to this DC system
                        bus_name = bus_mapped.get('name', '')
                        if bus_name == name:  # Match by system name
                            idc = bus_mapped.get('idc', 0)
                            ib = bus_mapped.get('ib', 0)
                            # Allow DC buses that reference bus 0 (floating) or valid AC buses
                            if ib == 0 or ib in valid_buses:
                                area = bus_mapped.get('area', 1)
                                zone = bus_mapped.get('zone', 1)
                                dcname = bus_mapped.get('dcname', '')[:12]
                                idc2 = bus_mapped.get('idc2', 0)
                                rgrnd = bus_mapped.get('rgrnd', 0.0)
                                owner = bus_mapped.get('owner', 1)
                                
                                output_file.write(f' {idc},{ib:>6},{area:>4},{zone:>4},\'{dcname}\', {idc2}, {rgrnd:6.4f},{owner:>4}\n')
                                records_written += 1
            
            # Write DC link records if available
            link_section = data.get('ntermdclink', {})
            if 'fields' in link_section and 'data' in link_section:
                link_fields = link_section['fields']
                for link_record in link_section['data']:
                    if isinstance(link_record, list) and len(link_record) >= len(link_fields):
                        link_mapped = self.mapper.map_record('ntermdclink', link_record, link_fields)
                        
                        # Check if this DC link belongs to this DC system
                        link_name = link_mapped.get('name', '')
                        if link_name == name:  # Match by system name
                            idc = link_mapped.get('idc', 0)
                            jdc = link_mapped.get('jdc', 0)
                            dcckt = link_mapped.get('dcckt', '1')[:2]
                            met = link_mapped.get('met', 1)
                            rdc = link_mapped.get('rdc', 0.0)
                            ldc = link_mapped.get('ldc', 0.0)
                            
                            output_file.write(f'   {idc}, {jdc:2},   \'{dcckt}\', {met:4},{rdc:7.4f}, {ldc:6.2f}\n')
                            records_written += 1
        
        return records_written


class MultiSectionLineWriter(RawSectionWriter):
    """Writer for Multi-Section Line Data (Section 11)."""
    
    def __init__(self, version: RawVersion, modeling_approach: str = "bus_branch"):
        super().__init__(version, modeling_approach)
        from ..database.field_mapping_only import PureFieldMapper
        self.mapper = PureFieldMapper()
    
    def get_field_header(self) -> str:
        return "@!I,J,'ID',MET,DUM1,DUM2,DUM3,DUM4,DUM5,DUM6,DUM7,DUM8,DUM9"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write multi-section line data."""
        # Look for RAWX section name 'msline' or mapped 'multi_section_line'
        multi_section_line_section = data.get('msline', data.get('multi_section_line', {}))
        if 'fields' not in multi_section_line_section or 'data' not in multi_section_line_section:
            return 0
        
        records_written = 0
        
        field_names = multi_section_line_section['fields']
        
        for record in multi_section_line_section['data']:
            # Use centralized field mapping with RAWX device type
            canonical_record = self.mapper.map_record('msline', record, field_names)
            
            ibus = canonical_record.get('ibus', 0)
            jbus = canonical_record.get('jbus', 0)
            lineid = str(canonical_record.get('id', '1'))  # Keep full ID string
            met = canonical_record.get('met', 1)
            
            # Handle dum1 as intermediate bus number (integer, not float)
            dum1 = self._to_int(canonical_record.get('dum1', 0))  # Intermediate bus
            dum2 = canonical_record.get('dum2', 0.0)
            dum3 = canonical_record.get('dum3', 0.0)
            dum4 = canonical_record.get('dum4', 0.0)
            dum5 = canonical_record.get('dum5', 0.0)
            dum6 = canonical_record.get('dum6', 0.0)
            dum7 = canonical_record.get('dum7', 0.0)
            dum8 = canonical_record.get('dum8', 0.0)
            dum9 = canonical_record.get('dum9', 0.0)
            
            # Format according to PSS/E specifications with proper field formatting
            # V33 uses simpler format with integer intermediate bus
            if self.version == RawVersion.V33:
                # V33: Simplified format with integer intermediate bus (no decimal)
                output_file.write(f"{ibus:>6},{jbus:>6},'{lineid}',{met},{dum1:>6},{dum2:>8.2f},{dum3:>8.2f},{dum4:>8.2f},{dum5:>8.2f},{dum6:>8.2f},{dum7:>8.2f},{dum8:>8.2f},{dum9:>8.2f}\n")
            else:
                # V34/V35: Enhanced format (all fields as floats)
                output_file.write(f"{ibus:>6},{jbus:>6},'{lineid}',{met},{dum1:>8.2f},{dum2:>8.2f},{dum3:>8.2f},{dum4:>8.2f},{dum5:>8.2f},{dum6:>8.2f},{dum7:>8.2f},{dum8:>8.2f},{dum9:>8.2f}\n")
            records_written += 1
        
        return records_written


class GneDeviceWriter(RawSectionWriter):
    """Writer for GNE Data (Section 19)."""
    
    def get_field_header(self) -> str:
        return "@!  'NAME',        'MODEL',     NTERM,BUS1...BUSNTERM,NREAL,NINTG,NCHAR"
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write GNE device data."""
        gne_device_section = data.get('gne_device', {})
        if 'fields' not in gne_device_section or 'data' not in gne_device_section:
            return 0
        
        fields = gne_device_section['fields']
        records_written = 0
        
        # Get bus data to validate bus existence
        bus_section = data.get('bus', {})
        valid_buses = set()
        if 'data' in bus_section:
            bus_fields = bus_section.get('fields', [])
            bus_field_idx = None
            for i, field in enumerate(bus_fields):
                if field in ['ibus', 'bus', 'bus_number']:
                    bus_field_idx = i
                    break
            if bus_field_idx is not None:
                for bus_record in bus_section['data']:
                    if len(bus_record) > bus_field_idx:
                        valid_buses.add(int(bus_record[bus_field_idx]))
        
        for record in gne_device_section['data']:
            accessor = FieldAccessor(fields, record)
                
            # Map canonical fields to RAW format
            name = accessor.get_str('name', accessor.get_str('device_name', ''))
            # Apply version-specific name length limits
            if self.version == RawVersion.V33:
                name = name[:12]  # V33: 12 character limit
            else:
                name = name[:60]  # V34/V35: 60 character limit
            model = accessor.get_int('model', accessor.get_int('model_type', 0))
            nterm = accessor.get_int('nterm', accessor.get_int('num_terminals', 0))
            bus1 = accessor.get_int('bus1', accessor.get_int('terminal_1_bus', 0))
            bus2 = accessor.get_int('bus2', accessor.get_int('terminal_2_bus', 0))
            bus3 = accessor.get_int('bus3', accessor.get_int('terminal_3_bus', 0))
            nreal = accessor.get_int('nreal', accessor.get_int('num_real', 0))
            nintg = accessor.get_int('nintg', accessor.get_int('num_integer', 0))
            nchar = accessor.get_int('nchar', accessor.get_int('num_character', 0))
            
            # Validate terminal buses exist and nterm is reasonable
            terminal_buses = [bus1, bus2, bus3]
            valid_terminals = 0
            for bus in terminal_buses:
                if bus > 0 and bus in valid_buses:
                    valid_terminals += 1
            
            # Skip invalid records
            if nterm <= 0 or nterm > 3 or valid_terminals == 0:
                continue
            
            # Update nterm to match valid terminals
            nterm = min(nterm, valid_terminals)
            
            output_file.write(f"'{name}',{model:>3},{nterm},{bus1:>6},{bus2:>6},{bus3:>6},{nreal:>3},{nintg:>3},{nchar:>3}\n")
            records_written += 1
        
        return records_written


class InductionMachineWriter(RawSectionWriter):
    """Writer for Induction Machine Data (Section 20)."""
    
    def get_field_header(self) -> str:
        return "@!   I,'ID',ST,SC,DC,AREA,ZONE,OWNER,TC,BC, MBASE,RATEKV,PC,  PSET,     H,      A,      B,      D,      E,     RA,        XA,        XM,        R1,        X1,        R2,        X2,        X3,       E1,    SE1,   E2,    SE2,   IA1,   IA2, XAMULT"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write induction machine data."""
        induction_machine_section = data.get('induction_machine', {})
        if 'fields' not in induction_machine_section or 'data' not in induction_machine_section:
            return 0
        
        fields = induction_machine_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in induction_machine_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('induction_machine', source_record)
                
                # Extract mapped fields using canonical names
                ibus = mapped_record.get('ibus', 0)
                machine_id = str(mapped_record.get('id', '1'))[:2]
                stat = mapped_record.get('stat', 1)
                scode = mapped_record.get('scode', 1)
                dcode = mapped_record.get('dcode', 2)
                area = mapped_record.get('area', 1)
                zone = mapped_record.get('zone', 1)
                owner = mapped_record.get('owner', 1)
                tc = mapped_record.get('tc', 1)
                bc = mapped_record.get('bc', 1)
                mbase = mapped_record.get('mbase', 1.0)
                ratekv = mapped_record.get('ratekv', 1.0)
                pc = mapped_record.get('pc', 1)
                pset = mapped_record.get('pset', 1.0)
                h = mapped_record.get('h', 1.0)
                a = mapped_record.get('a', 1.0)
                b = mapped_record.get('b', 1.0)
                d = mapped_record.get('d', 1.0)
                e = mapped_record.get('e', 1.0)
                ra = mapped_record.get('ra', 0.0)
                xa = mapped_record.get('xa', 0.0)
                xm = mapped_record.get('xm', 0.0)
                r1 = mapped_record.get('r1', 0.0)
                x1 = mapped_record.get('x1', 0.0)
                r2 = mapped_record.get('r2', 0.0)
                x2 = mapped_record.get('x2', 0.0)
                x3 = mapped_record.get('x3', 0.0)
                e1 = mapped_record.get('e1', 0.0)
                se1 = mapped_record.get('se1', 0.0)
                e2 = mapped_record.get('e2', 0.0)
                se2 = mapped_record.get('se2', 0.0)
                ia1 = mapped_record.get('ia1', 0.0)
                ia2 = mapped_record.get('ia2', 0.0)
                xamult = mapped_record.get('xamult', 0.0)
            else:
                # Fallback to original accessor pattern with RAWX field name awareness
                accessor = FieldAccessor(fields, record)
                
                # Map canonical fields to RAW format using RAWX field names
                ibus = accessor.get_int('ibus', accessor.get_int('i', accessor.get_int('bus', 0)))
                machine_id = accessor.get_str('imid', accessor.get_str('id', accessor.get_str('machine_id', '1')))[:2]
                stat = accessor.get_int('stat', accessor.get_int('status', 1))
                scode = accessor.get_int('sc', accessor.get_int('scode', 1))
                dcode = accessor.get_int('dc', accessor.get_int('dcode', 2))
                area = accessor.get_int('area', accessor.get_int('area_num', 1))
                zone = accessor.get_int('zone', accessor.get_int('zone_num', 1))
                owner = accessor.get_int('owner', accessor.get_int('owner_num', 1))
                tc = accessor.get_int('tc', 1)
                bc = accessor.get_int('bc', 1)
                mbase = accessor.get_float('mbase', 1.0)
                ratekv = accessor.get_float('ratekv', 1.0)
                pc = accessor.get_int('pcode', accessor.get_int('pc', 1))
                pset = accessor.get_float('pset', 1.0)
                h = accessor.get_float('hconst', accessor.get_float('h', 1.0))
                a = accessor.get_float('aconst', accessor.get_float('a', 1.0))
                b = accessor.get_float('bconst', accessor.get_float('b', 1.0))
                d = accessor.get_float('dconst', accessor.get_float('d', 1.0))
                e = accessor.get_float('econst', accessor.get_float('e', 1.0))
                ra = accessor.get_float('ra', 0.0)
                xa = accessor.get_float('xa', 0.0)
                xm = accessor.get_float('xm', 0.0)
                r1 = accessor.get_float('r1', 0.0)
                x1 = accessor.get_float('x1', 0.0)
                r2 = accessor.get_float('r2', 0.0)
                x2 = accessor.get_float('x2', 0.0)
                x3 = accessor.get_float('x3', 0.0)
                e1 = accessor.get_float('e1', 0.0)
                se1 = accessor.get_float('se1', 0.0)
                e2 = accessor.get_float('e2', 0.0)
                se2 = accessor.get_float('se2', 0.0)
                ia1 = accessor.get_float('ia1', 0.0)
                ia2 = accessor.get_float('ia2', 0.0)
                xamult = accessor.get_float('xamult', 0.0)
            
            # Format output according to PSS/E specifications to match reference - include all fields
            output_file.write(f"  {ibus:>4},\"{machine_id} \", {stat}, {scode}, {dcode}, {area:>3}, {zone:>3}, {owner:>3}, {tc}, {bc}, {mbase:6.3f}, {ratekv:6.3f},{pc},{pset:7.4f}, {h:6.3f}, {a:6.3f}, {b:6.3f}, {d:6.3f}, {e:6.3f}\n")
            records_written += 1
        
        return records_written


class InterAreaTransferWriter(RawSectionWriter):
    """Writer for Inter-Area Transfer Data (Section 15)."""
    
    def get_field_header(self) -> str:
        return "@!ARFROM,ARTO,TRNM,PTRAN"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write inter-area transfer data."""
        inter_area_transfer_section = data.get('inter_area_transfer', {})
        if 'fields' not in inter_area_transfer_section or 'data' not in inter_area_transfer_section:
            return 0
        
        fields = inter_area_transfer_section['fields']
        records_written = 0
        
        for record in inter_area_transfer_section['data']:
            accessor = FieldAccessor(fields, record)
            
            # Map canonical fields to RAW format
            arfrom = accessor.get_int('arfrom', accessor.get_int('area_from', accessor.get_int('from_area', 0)))
            arto = accessor.get_int('arto', accessor.get_int('area_to', accessor.get_int('to_area', 0)))
            trnm = accessor.get_str('trnm', accessor.get_str('transfer_name', accessor.get_str('trid', '')))[:12]
            ptran = accessor.get_float('ptran', accessor.get_float('transfer_power', 0.0))
            
            # Generate a unique transfer name if empty
            if not trnm or trnm.strip() == '':
                trnm = f"TX_{arfrom}_{arto}"[:12]
            
            output_file.write(f"     {arfrom:>3},     {arto:>3},'{trnm}',{ptran:11.2f}\n")
            records_written += 1
        
        return records_written


class FactsDeviceWriter(RawSectionWriter):
    """Writer for FACTS Device Data (Section 17)."""
    
    def get_field_header(self) -> str:
        return "@!  'NAME',         I,     J,MODE,PDES,   QDES,  VSET,   SHMX,   TRMX,   VTMN,   VTMX,   VSMX,    IMX,   LINX,   RMPCT,OWNER,  SET1,    SET2,VSREF, FCREG,NREG,   'MNAME'"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write FACTS device data."""
        facts_device_section = data.get('facts_device', {})
        if 'fields' not in facts_device_section or 'data' not in facts_device_section:
            return 0
        
        fields = facts_device_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in facts_device_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('facts_device', source_record)
                
                # Extract mapped fields using canonical names
                name = str(mapped_record.get('name', ''))
                ibus = mapped_record.get('ibus', 0)
                jbus = mapped_record.get('jbus', 0)
                mode = mapped_record.get('mode', 0)
                pdes = mapped_record.get('pdes', 0.0)
                qdes = mapped_record.get('qdes', 0.0)
                vset = mapped_record.get('vset', 0.0)
                shmx = mapped_record.get('shmx', 0.0)
                trmx = mapped_record.get('trmx', 0.0)
                vtmn = mapped_record.get('vtmn', 0.0)
                vtmx = mapped_record.get('vtmx', 0.0)
                vsmx = mapped_record.get('vsmx', 0.0)
                imx = mapped_record.get('imx', 0.0)
                linx = mapped_record.get('linx', 0.0)
                rmpct = mapped_record.get('rmpct', 0.0)
                owner = mapped_record.get('owner', 0)
                set1 = mapped_record.get('set1', 0.0)
                set2 = mapped_record.get('set2', 0.0)
                vsref = mapped_record.get('vsref', 0)
                fcreg = mapped_record.get('fcreg', 0)
                nreg = mapped_record.get('nreg', 0)
                mname = str(mapped_record.get('mname', ''))
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                
                # Map canonical fields to RAW format
                name = accessor.get_str('name', accessor.get_str('device_name', ''))
                ibus = accessor.get_int('i', accessor.get_int('bus', accessor.get_int('bus_i', 0)))
                jbus = accessor.get_int('j', accessor.get_int('to_bus', accessor.get_int('bus_j', 0)))
                mode = accessor.get_int('mode', accessor.get_int('control_mode', 0))
                pdes = accessor.get_float('pdes', accessor.get_float('desired_p', 0.0))
                qdes = accessor.get_float('qdes', accessor.get_float('desired_q', 0.0))
                vset = accessor.get_float('vset', accessor.get_float('voltage_setpoint', 0.0))
                shmx = accessor.get_float('shmx', accessor.get_float('max_shunt', 0.0))
                trmx = accessor.get_float('trmx', accessor.get_float('max_series', 0.0))
                vtmn = accessor.get_float('vtmn', accessor.get_float('min_voltage', 0.0))
                vtmx = accessor.get_float('vtmx', accessor.get_float('max_voltage', 0.0))
                vsmx = accessor.get_float('vsmx', accessor.get_float('max_voltage_series', 0.0))
                imx = accessor.get_float('imx', accessor.get_float('max_current', 0.0))
                linx = accessor.get_float('linx', accessor.get_float('line_reactance', 0.0))
                rmpct = accessor.get_float('rmpct', accessor.get_float('remote_percent', 0.0))
                owner = accessor.get_int('owner', accessor.get_int('owner_num', 0))
                set1 = accessor.get_float('set1', accessor.get_float('setting_1', 0.0))
                set2 = accessor.get_float('set2', accessor.get_float('setting_2', 0.0))
                vsref = accessor.get_int('vsref', accessor.get_int('voltage_reference', 0))
                fcreg = accessor.get_int('fcreg', accessor.get_int('frequency_control_bus', 0))
                nreg = accessor.get_int('nreg', accessor.get_int('regulated_node', 0))
                mname = accessor.get_str('mname', accessor.get_str('monitored_name', ''))
            
            # Apply version-specific name length limits
            if self.version == RawVersion.V33:
                name = name[:12]  # V33: 12 character limit
                mname = mname[:12]  # V33: 12 character limit
            else:
                name = name[:60]  # V34/V35: 60 character limit
                mname = mname[:12]  # Still 12 character limit for MNAME in all versions
            
            # Format according to PSS/E specifications (match reference format exactly)
            output_file.write(f'"{name}",{ibus:>6},{jbus:>6},{mode},{pdes:>8.3f},{qdes:>8.3f},{vset:>8.5f},{shmx:>8.3f},{trmx:>8.2f},{vtmn:>8.5f},{vtmx:>8.5f},{vsmx:>8.5f},{imx:>8.3f},{linx:>8.5f},{rmpct:>6.1f},{owner:>4},{set1:>8.5f},{set2:>8.5f},{vsref:>4},{fcreg:>6},{nreg:>4},"{mname:<12}"\n')
            records_written += 1
        
        return records_written


class SwitchedShuntWriter(RawSectionWriter):
    """Writer for Switched Shunt Data (Section 18)."""
    
    def get_field_header(self) -> str:
        return "@!   I,'ID',MODSW,ADJM,ST, VSWHI,  VSWLO, SWREG,NREG, RMPCT,   'RMIDNT',     BINIT,S1,N1,    B1, S2,N2,    B2, S3,N3,    B3, S4,N4,    B4, S5,N5,    B5, S6,N6,    B6, S7,N7,    B7, S8,N8,    B8"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write switched shunt data."""
        switched_shunt_section = data.get('switched_shunt', {})
        if 'fields' not in switched_shunt_section or 'data' not in switched_shunt_section:
            return 0
        
        fields = switched_shunt_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        # Get bus data to validate bus existence
        bus_section = data.get('bus', {})
        valid_buses = set()
        if 'data' in bus_section:
            bus_fields = bus_section.get('fields', [])
            bus_field_idx = None
            for i, field in enumerate(bus_fields):
                if field in ['ibus', 'bus', 'bus_number']:
                    bus_field_idx = i
                    break
            if bus_field_idx is not None:
                for bus_record in bus_section['data']:
                    if len(bus_record) > bus_field_idx:
                        valid_buses.add(int(bus_record[bus_field_idx]))
        
        for record in switched_shunt_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('switched_shunt', source_record)
                
                # Extract mapped fields using canonical names
                ibus = mapped_record.get('ibus', 0)
                
                # Skip records for buses that don't exist in the model
                if ibus not in valid_buses:
                    continue
                shuntid = str(mapped_record.get('id', '1'))[:2]
                modsw = mapped_record.get('modsw', 0)
                adjm = mapped_record.get('adjm', 0)
                stat = mapped_record.get('stat', 1)
                vswhi = mapped_record.get('vswhi', 1.1)
                vswlo = mapped_record.get('vswlo', 0.9)
                swreg = mapped_record.get('swreg', 0)
                nreg = mapped_record.get('nreg', 0)
                rmpct = mapped_record.get('rmpct', 100.0)
                rmidnt = str(mapped_record.get('rmidnt', ''))[:12]
                binit = mapped_record.get('binit', 0.0)
                
                # Get switch bank data (8 banks: S1,N1,B1 through S8,N8,B8)
                switch_banks = []
                for i in range(1, 9):
                    s_val = mapped_record.get(f's{i}', 0)
                    n_val = mapped_record.get(f'n{i}', 0)
                    b_val = mapped_record.get(f'b{i}', 0.0)
                    switch_banks.extend([s_val, n_val, b_val])
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                
                # Map canonical fields to RAW format
                ibus = accessor.get_int('i', accessor.get_int('bus', accessor.get_int('bus_id', 0)))
                
                # Skip records for buses that don't exist in the model
                if ibus not in valid_buses:
                    continue
                shuntid = accessor.get_str('id', accessor.get_str('shunt_id', '1'))[:2]
                modsw = accessor.get_int('modsw', accessor.get_int('mode_sw', 0))
                adjm = accessor.get_int('adjm', accessor.get_int('adjustment_method', 0))
                stat = accessor.get_int('stat', accessor.get_int('status', 1))
                vswhi = accessor.get_float('vswhi', accessor.get_float('v_switch_hi', 1.1))
                vswlo = accessor.get_float('vswlo', accessor.get_float('v_switch_lo', 0.9))
                swreg = accessor.get_int('swreg', accessor.get_int('controlled_bus', 0))
                nreg = accessor.get_int('nreg', accessor.get_int('controlled_node', 0))
                rmpct = accessor.get_float('rmpct', accessor.get_float('remote_percent', 100.0))
                rmidnt = accessor.get_str('rmidnt', accessor.get_str('remote_id', ''))[:12]
                binit = accessor.get_float('binit', accessor.get_float('initial_susceptance', 0.0))
                
                # Get switch bank data (8 banks: S1,N1,B1 through S8,N8,B8)
                switch_banks = []
                for i in range(1, 9):
                    s_field = f's{i}'
                    n_field = f'n{i}'
                    b_field = f'b{i}'
                    
                    s_val = accessor.get_int(s_field, accessor.get_int(f'switch_{i}_status', 0))
                    n_val = accessor.get_int(n_field, accessor.get_int(f'switch_{i}_num', 0))
                    b_val = accessor.get_float(b_field, accessor.get_float(f'switch_{i}_susceptance', 0.0))
                    
                    switch_banks.extend([s_val, n_val, b_val])
            
            # Format output according to PSS/E specifications (simplified for V33 compatibility)
            # Build the switch bank string with simplified formatting
            bank_parts = []
            for i in range(0, len(switch_banks), 3):
                s_val = switch_banks[i]
                n_val = switch_banks[i+1] 
                b_val = switch_banks[i+2]
                
                # Only include non-zero blocks to avoid excessive length
                if s_val != 0 or n_val != 0 or abs(b_val) > 0.001:
                    bank_parts.append(f'{s_val},{n_val},{b_val:6.2f}')
            
            # Join with proper spacing - limit to first 4 banks to avoid line length issues
            bank_str = ', '.join(bank_parts[:4]) if bank_parts else '0,0,0.00'
            
            output_file.write(f"   {ibus:>3},'{shuntid}',{modsw},{adjm},{stat},{vswhi:7.5f},{vswlo:7.5f},{swreg:>3},{nreg:>1},{rmpct:>5.1f},'{rmidnt:<12}',{binit:>6.2f},{bank_str}\n")
            records_written += 1
        
        return records_written


class SystemSwitchingDeviceWriter(RawSectionWriter):
    """Writer for System Switching Device Data (Section 7) - V35 only."""
    
    def get_field_header(self) -> str:
        return "@!I,J,'CKT',X,RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,STATUS,NSTATUS,METERED,STYPE,'NAME'"
    
    def _to_float(self, val, default=0.0):
        try:
            return float(val)
        except (ValueError, TypeError):
            return default
    
    def _to_int(self, val, default=0):
        try:
            return int(val)
        except (ValueError, TypeError):
            return default
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write system switching device data."""
        # Only available in V35
        if self.version != RawVersion.V35:
            return 0
            
        system_switching_device_section = data.get('system_switching_device', {})
        if 'fields' not in system_switching_device_section or 'data' not in system_switching_device_section:
            return 0
        
        fields = system_switching_device_section['fields']
        records_written = 0
        
        # Import the field mapping system
        try:
            from ..database.field_mapping_only import PureFieldMapper
            field_mapper = PureFieldMapper()
            use_centralized_mapping = True
        except ImportError:
            # Fallback to original logic if mapping system not available
            field_mapper = None
            use_centralized_mapping = False
        
        for record in system_switching_device_section['data']:
            if use_centralized_mapping:
                # Use centralized field mapping
                source_record = dict(zip(fields, record))
                mapped_record = field_mapper.map_record('system_switching_device', source_record)
                
                # Extract mapped fields using canonical names
                ibus = mapped_record.get('ibus', 0)
                jbus = mapped_record.get('jbus', 0)
                ckt = str(mapped_record.get('ckt', '1'))[:2]
                x = mapped_record.get('xpu', 0.0)
                status = mapped_record.get('stat', 1)
                nstatus = mapped_record.get('nstat', 1)
                metered = mapped_record.get('met', 1)
                stype = mapped_record.get('stype', 1)
                name = str(mapped_record.get('name', ''))[:40]
                
                # Extract rating fields
                rates = []
                for i in range(1, 13):
                    rate_val = mapped_record.get(f'rate{i}', 0.0)
                    rates.append(rate_val)
            else:
                # Fallback to original accessor pattern
                accessor = FieldAccessor(fields, record)
                
                ibus = accessor.get_int('ibus', accessor.get_int('i', accessor.get_int('from_bus', accessor.get_int('bus_i', 0))))
                jbus = accessor.get_int('jbus', accessor.get_int('j', accessor.get_int('to_bus', accessor.get_int('bus_j', 0))))
                ckt = accessor.get_str('ckt', accessor.get_str('circuit_id', '1'))[:2]
                x = accessor.get_float('xpu', accessor.get_float('x', accessor.get_float('reactance', 0.0)))
                
                # 12 ratings for V35
                rates = []
                for i in range(1, 13):
                    rate_field = f'rate{i}'
                    rate_val = accessor.get_float(rate_field, accessor.get_float(f'rating_{i}', 0.0))
                    rates.append(rate_val)
                
                status = accessor.get_int('stat', accessor.get_int('status', 1))
                nstatus = accessor.get_int('nstat', accessor.get_int('nstatus', accessor.get_int('node_status', 1)))
                metered = accessor.get_int('met', accessor.get_int('metered', accessor.get_int('meter', 1)))
                stype = accessor.get_int('stype', accessor.get_int('switch_type', 1))
                name = accessor.get_str('name', accessor.get_str('device_name', ''))[:40]
            
            rate_str = ','.join([f'{rate:8.2f}' for rate in rates])
            
            output_file.write(f"   {ibus:>4},{jbus:>4},'{ckt}',{x:10.6f},{rate_str},{status:>2},{nstatus:>2},{metered:>2},{stype:>2},'{name}'\n")
            records_written += 1
        
        return records_written


class SystemDataWriter(RawSectionWriter):
    """Writer for System-Wide Data (Section 1) - V33, V34, V35."""
    
    def get_field_header(self) -> str:
        # No headers for system data - it goes directly into the format
        return ""
    
    def write_section(self, data: Dict[str, Any], output_file) -> int:
        """Write system-wide data from separate sections."""
        # V33 does not support system-wide data - skip writing
        if self.version == RawVersion.V33:
            return 0
            
        records_written = 0
        
        # Extract and write GENERAL section
        general_data = data.get('general', {})
        if 'fields' in general_data and 'data' in general_data and general_data['data']:
            fields = general_data['fields']
            record = general_data['data']  # Data is a single list, not list of lists
            
            # Create GENERAL line with key=value pairs
            line_parts = ["GENERAL"]
            field_values = dict(zip(fields, record))
            
            # Extract specific GENERAL fields with defaults - only include basic fields like reference
            thrshz = field_values.get('thrshz', 0.0001)
            pqbrak = field_values.get('pqbrak', 0.7)
            blowup = field_values.get('blowup', 5.0)
            
            line_parts.extend([
                f"THRSHZ={thrshz}",
                f"PQBRAK={pqbrak}",
                f"BLOWUP={blowup}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Extract and write GAUSS section
        gauss_data = data.get('gauss', {})
        if 'fields' in gauss_data and 'data' in gauss_data and gauss_data['data']:
            fields = gauss_data['fields']
            record = gauss_data['data']  # Data is a single list, not list of lists
            
            line_parts = ["GAUSS"]
            field_values = dict(zip(fields, record))
            
            itmx = field_values.get('itmx', 100)
            accp = field_values.get('accp', 1.6)
            accq = field_values.get('accq', 1.6)
            accm = field_values.get('accm', 1.0)
            tol = field_values.get('tol', 0.0001)
            
            line_parts.extend([
                f"ITMX={itmx}",
                f"ACCP={accp}",
                f"ACCQ={accq}",
                f"ACCM={accm}",
                f"TOL={tol}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Extract and write NEWTON section
        newton_data = data.get('newton', {})
        if 'fields' in newton_data and 'data' in newton_data and newton_data['data']:
            fields = newton_data['fields']
            record = newton_data['data']  # Data is a single list, not list of lists
            
            line_parts = ["NEWTON"]
            field_values = dict(zip(fields, record))
            
            itmxn = field_values.get('itmxn', 100)
            accn = field_values.get('accn', 1.0)
            toln = field_values.get('toln', 0.1)
            vctolq = field_values.get('vctolq', 0.1)
            vctolv = field_values.get('vctolv', 0.00001)
            dvlim = field_values.get('dvlim', 0.99)
            ndvfct = field_values.get('ndvfct', 0.99)
            
            line_parts.extend([
                f"ITMXN={itmxn}",
                f"ACCN={accn}",
                f"TOLN={toln}",
                f"VCTOLQ={vctolq}",
                f"VCTOLV={vctolv:.5f}",
                f"DVLIM={dvlim}",
                f"NDVFCT={ndvfct}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Extract and write ADJUST section
        adjust_data = data.get('adjust', {})
        if 'fields' in adjust_data and 'data' in adjust_data and adjust_data['data']:
            fields = adjust_data['fields']
            record = adjust_data['data']  # Data is a single list, not list of lists
            
            line_parts = ["ADJUST"]
            field_values = dict(zip(fields, record))
            
            adjthr = field_values.get('adjthr', 0.005)
            acctap = field_values.get('acctap', 1.0)
            taplim = field_values.get('taplim', 0.05)
            swvbnd = field_values.get('swvbnd', 100.0)
            mxtpss = field_values.get('mxtpss', 99)
            mxswim = field_values.get('mxswim', 10)
            
            line_parts.extend([
                f"ADJTHR={adjthr}",
                f"ACCTAP={acctap}",
                f"TAPLIM={taplim}",
                f"SWVBND={swvbnd}",
                f"MXTPSS={mxtpss}",
                f"MXSWIM={mxswim}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Extract and write TYSL section
        tysl_data = data.get('tysl', {})
        if 'fields' in tysl_data and 'data' in tysl_data and tysl_data['data']:
            fields = tysl_data['fields']
            record = tysl_data['data']  # Data is a single list, not list of lists
            
            line_parts = ["TYSL"]
            field_values = dict(zip(fields, record))
            
            itmxty = field_values.get('itmxty', 20)
            accty = field_values.get('accty', 1.0)
            tolty = field_values.get('tolty', 0.00001)
            
            line_parts.extend([
                f"ITMXTY={itmxty}",
                f"ACCTY={accty}",
                f"TOLTY={tolty:.5f}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Extract and write SOLVER section
        solver_data = data.get('solver', {})
        if 'fields' in solver_data and 'data' in solver_data and solver_data['data']:
            fields = solver_data['fields']
            record = solver_data['data']  # Data is a single list, not list of lists
            
            line_parts = ["SOLVER"]
            field_values = dict(zip(fields, record))
            
            method = field_values.get('method', 'FDNS')
            actaps = field_values.get('actaps', 0)
            areain = field_values.get('areain', 0)
            phshft = field_values.get('phshft', 0)
            dctaps = field_values.get('dctaps', 1)
            swshnt = field_values.get('swshnt', 1)
            flatst = field_values.get('flatst', 0)
            varlim = field_values.get('varlim', 99)
            nondiv = field_values.get('nondiv', 0)
            
            line_parts.extend([
                method,
                f"ACTAPS={actaps}",
                f"AREAIN={areain}",
                f"PHSHFT={phshft}",
                f"DCTAPS={dctaps}",
                f"SWSHNT={swshnt}",
                f"FLATST={flatst}",
                f"VARLIM={varlim}",
                f"NONDIV={nondiv}"
            ])
            
            output_file.write(", ".join(line_parts) + "\n")
            records_written += 1
        
        # Add RATING entries like in the reference file
        rating_entries = [
            ('RatingName1', 'Description1 '),
            ('RatingName2', 'Description2 '),
            ('RatingName3', 'Description3 '),
            ('RatingName4', 'Description4 '),
            ('RatingName5', 'Description5 '),
            ('RatingName6', 'Description6 '),
            ('RatingName7', 'Description7 '),
            ('RatingName8', 'Description8 '),
            ('RatingName9', 'Description9 '),
            ('RatingName10', 'Description10 '),
            ('RatingName11', 'Description11 '),
            ('RatingName12', 'Description12 ')
        ]
        
        for i, (code, description) in enumerate(rating_entries, 1):
            output_file.write(f"RATING,{i:>2}, \"{code}\", \"{description}\"\n")
            records_written += 1
        
        return records_written


class RawExportRegistry:
    """Registry for RAW section writers."""
    
    def __init__(self):
        self._writers = {}
        self._register_default_writers()
    
    def _register_default_writers(self):
        """Register the default section writers."""
        sections = [
            ('caseid', CaseIdWriter),
            ('bus', BusWriter),
            ('load', LoadWriter),
            ('fixed_shunt', FixedShuntWriter),
            ('generator', GeneratorWriter),
            ('ac_line', AcLineWriter),
            ('transformer', TransformerWriter),
            ('area', AreaWriter),
            ('zone', ZoneWriter),
            ('owner', OwnerWriter),
            ('substation', SubstationWriter),
            ('node', NodeWriter),
            ('switching_device', SwitchingDeviceWriter),
            ('terminal', TerminalWriter),
            ('dc_line_2t', DcLine2TerminalWriter),
            ('vsc_dc', VscDcWriter),
            ('impedance_correction', ImpedanceCorrectionWriter),
            ('multi_terminal_dc', MultiTerminalDcWriter),
            ('multi_section_line', MultiSectionLineWriter),
            ('inter_area_transfer', InterAreaTransferWriter),
            ('facts_device', FactsDeviceWriter),
            ('switched_shunt', SwitchedShuntWriter),
            ('gne_device', GneDeviceWriter),
            ('induction_machine', InductionMachineWriter),
            ('system_switching_device', SystemSwitchingDeviceWriter),
            ('system_data', SystemDataWriter),
        ]
        
        for section_name, writer_class in sections:
            self.register_writer(section_name, writer_class)
    
    def register_writer(self, section_name: str, writer_class: type):
        """Register a section writer."""
        self._writers[section_name] = writer_class
    
    def get_writer(self, section_name: str, version: RawVersion, modeling_approach: str = "bus_branch", output_path: str = None) -> Optional[RawSectionWriter]:
        """Get a section writer for the given section and version."""
        if section_name not in self._writers:
            return None
        
        writer_class = self._writers[section_name]
        
        # Special handling for CaseIdWriter to pass output path
        if section_name == 'caseid' and writer_class == CaseIdWriter:
            return writer_class(version, modeling_approach, output_path)
        else:
            return writer_class(version, modeling_approach)


def get_psse_section_order_and_breaks(version: str, modeling_approach: str = "bus_branch") -> List[Tuple[str, str]]:
    """
    Get the official PSS/E section order and break format for each version.
    
    Returns a list of tuples: (section_name, end_of_section_break)
    The last section uses "Q" as the final terminator.
    """
    if version == "33":
        return [
            ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN BUS DATA'),
            ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
            ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
            ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
            ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
            ('ac_line', 'END OF BRANCH DATA, BEGIN TRANSFORMER DATA'),
            ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
            ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
            ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
            ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
            ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
            ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
            ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
            ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
            ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
            ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
            ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
            ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
            ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
            ('induction_machine', 'Q'),  # Final terminator
        ]
    elif version == "34":
        if modeling_approach == "node_breaker":
            return [
                ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN SYSTEM-WIDE DATA'),
                ('system_data', 'END OF SYSTEM-WIDE DATA, BEGIN BUS DATA'),
                ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
                ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
                ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
                ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
                ('ac_line', 'END OF BRANCH DATA, BEGIN SYSTEM SWITCHING DEVICE DATA'),
                ('system_switching_device', 'END OF SYSTEM SWITCHING DEVICE DATA, BEGIN TRANSFORMER DATA'),
                ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
                ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
                ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
                ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
                ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
                ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
                ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
                ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
                ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
                ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
                ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
                ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
                ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
                ('induction_machine', 'END OF INDUCTION MACHINE DATA, BEGIN SUBSTATION DATA'),
                ('substation', 'END OF SUBSTATION DATA, BEGIN NODE DATA'),
                ('node', 'END OF NODE DATA, BEGIN SWITCHING DEVICE DATA'),
                ('switching_device', 'END OF SWITCHING DEVICE DATA, BEGIN TERMINAL DATA'),
                ('terminal', 'Q'),  # Final terminator
            ]
        else:  # bus_branch
            return [
                ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN SYSTEM-WIDE DATA'),
                ('system_data', 'END OF SYSTEM-WIDE DATA, BEGIN BUS DATA'),
                ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
                ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
                ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
                ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
                ('ac_line', 'END OF BRANCH DATA, BEGIN SYSTEM SWITCHING DEVICE DATA'),
                ('system_switching_device', 'END OF SYSTEM SWITCHING DEVICE DATA, BEGIN TRANSFORMER DATA'),
                ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
                ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
                ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
                ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
                ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
                ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
                ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
                ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
                ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
                ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
                ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
                ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
                ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
                ('induction_machine', 'Q'),  # Final terminator
            ]
    elif version == "35":
        if modeling_approach == "node_breaker":
            return [
                ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN SYSTEM-WIDE DATA'),
                ('system_data', 'END OF SYSTEM-WIDE DATA, BEGIN BUS DATA'),
                ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
                ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
                ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
                ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
                ('ac_line', 'END OF BRANCH DATA, BEGIN SYSTEM SWITCHING DEVICE DATA'),
                ('system_switching_device', 'END OF SYSTEM SWITCHING DEVICE DATA, BEGIN TRANSFORMER DATA'),
                ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
                ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
                ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
                ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
                ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
                ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
                ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
                ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
                ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
                ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
                ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
                ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
                ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
                ('induction_machine', 'END OF INDUCTION MACHINE DATA, BEGIN SUBSTATION DATA'),
                ('substation', 'END OF SUBSTATION DATA, BEGIN NODE DATA'),
                ('node', 'END OF NODE DATA, BEGIN SWITCHING DEVICE DATA'),
                ('switching_device', 'END OF SWITCHING DEVICE DATA, BEGIN TERMINAL DATA'),
                ('terminal', 'Q'),  # Final terminator
            ]
        else:  # bus_branch or hybrid
            if modeling_approach == "hybrid":
                # Hybrid approach: bus-branch topology + flat node-breaker sections
                return [
                    ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN SYSTEM-WIDE DATA'),
                    ('system_data', 'END OF SYSTEM-WIDE DATA, BEGIN BUS DATA'),
                    ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
                    ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
                    ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
                    ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
                    ('ac_line', 'END OF BRANCH DATA, BEGIN SYSTEM SWITCHING DEVICE DATA'),
                    ('system_switching_device', 'END OF SYSTEM SWITCHING DEVICE DATA, BEGIN TRANSFORMER DATA'),
                    ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
                    ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
                    ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
                    ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
                    ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
                    ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
                    ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
                    ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
                    ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
                    ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
                    ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
                    ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
                    ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
                    ('induction_machine', 'END OF INDUCTION MACHINE DATA, BEGIN SUBSTATION DATA'),
                    ('substation', 'END OF SUBSTATION DATA, BEGIN NODE DATA'),
                    ('node', 'END OF NODE DATA, BEGIN SWITCHING DEVICE DATA'),
                    ('switching_device', 'END OF SWITCHING DEVICE DATA, BEGIN TERMINAL DATA'),
                    ('terminal', 'Q'),  # Final terminator
                ]
            else:  # pure bus_branch
                return [
                    ('caseid', 'END OF CASE IDENTIFICATION DATA, BEGIN SYSTEM-WIDE DATA'),
                    ('system_data', 'END OF SYSTEM-WIDE DATA, BEGIN BUS DATA'),
                    ('bus', 'END OF BUS DATA, BEGIN LOAD DATA'),
                    ('load', 'END OF LOAD DATA, BEGIN FIXED SHUNT DATA'),
                    ('fixed_shunt', 'END OF FIXED SHUNT DATA, BEGIN GENERATOR DATA'),
                    ('generator', 'END OF GENERATOR DATA, BEGIN BRANCH DATA'),
                    ('ac_line', 'END OF BRANCH DATA, BEGIN SYSTEM SWITCHING DEVICE DATA'),
                    ('system_switching_device', 'END OF SYSTEM SWITCHING DEVICE DATA, BEGIN TRANSFORMER DATA'),
                    ('transformer', 'END OF TRANSFORMER DATA, BEGIN AREA DATA'),
                    ('area', 'END OF AREA DATA, BEGIN TWO-TERMINAL DC DATA'),
                    ('dc_line_2t', 'END OF TWO-TERMINAL DC DATA, BEGIN VSC DC LINE DATA'),
                    ('vsc_dc', 'END OF VSC DC LINE DATA, BEGIN IMPEDANCE CORRECTION DATA'),
                    ('impedance_correction', 'END OF IMPEDANCE CORRECTION DATA, BEGIN MULTI-TERMINAL DC DATA'),
                    ('multi_terminal_dc', 'END OF MULTI-TERMINAL DC DATA, BEGIN MULTI-SECTION LINE DATA'),
                    ('multi_section_line', 'END OF MULTI-SECTION LINE DATA, BEGIN ZONE DATA'),
                    ('zone', 'END OF ZONE DATA, BEGIN INTER-AREA TRANSFER DATA'),
                    ('inter_area_transfer', 'END OF INTER-AREA TRANSFER DATA, BEGIN OWNER DATA'),
                    ('owner', 'END OF OWNER DATA, BEGIN FACTS DEVICE DATA'),
                    ('facts_device', 'END OF FACTS DEVICE DATA, BEGIN SWITCHED SHUNT DATA'),
                    ('switched_shunt', 'END OF SWITCHED SHUNT DATA, BEGIN GNE DATA'),
                    ('gne_device', 'END OF GNE DATA, BEGIN INDUCTION MACHINE DATA'),
                    ('induction_machine', 'Q'),  # Final terminator
                ]
    else:
        raise ValueError(f"Unsupported PSS/E version: {version}")


def export_to_raw_format(data: Dict[str, Any], output_path: Union[str, Path], 
                        version: str = "33", modeling_approach: str = "bus_branch") -> Path:
    """
    Export canonical data to PSS/E RAW format with correct section order and breaks.
    
    Args:
        data: Canonical format data dictionary
        output_path: Path for output RAW file
        version: PSS/E version ("33", "34", "35")
        modeling_approach: Modeling approach ("bus_branch", "node_breaker", "hybrid")
        
    Returns:
        Path to the exported RAW file
        
    Raises:
        ValueError: If version is not supported
        IOError: If file cannot be written
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        raw_version = RawVersion(version)
    except ValueError:
        raise ValueError(f"Unsupported PSS/E version: {version}. Supported versions: 33, 34, 35")
    
    # Initialize registry
    registry = RawExportRegistry()
    
    # Get official PSS/E section order and breaks
    section_order_and_breaks = get_psse_section_order_and_breaks(version, modeling_approach)
    
    # Section name mapping from RAWX to export system
    section_mapping = {
        'fixshunt': 'fixed_shunt',
        'acline': 'ac_line',
        'sysswd': 'system_switching_device',  # Updated mapping
        'subswd': 'switching_device',          # Substation switching devices
        'sub': 'substation',                   # RAWX substation section
        'subnode': 'node',                     # RAWX substation node section  
        'subterm': 'terminal',                 # RAWX substation terminal section
        'twotermdc': 'dc_line_2t',
        'vscdc': 'vsc_dc',
        'impcor': 'impedance_correction',
        'ntermdc': 'multi_terminal_dc',
        'msline': 'multi_section_line',
        'iatrans': 'inter_area_transfer',
        'facts': 'facts_device',
        'swshunt': 'switched_shunt',
        'gne': 'gne_device',
        'indmach': 'induction_machine',
        'system': 'system_data',  # Add system data mapping
    }
    
    # Apply section name mapping
    mapped_data = {}
    for section_name, section_data in data.items():
        mapped_name = section_mapping.get(section_name, section_name)
        mapped_data[mapped_name] = section_data
    
    # Add default sections if missing (required by PSS/E)
    if 'area' not in mapped_data:
        logger.info("Adding default area section")
        mapped_data['area'] = {
            'fields': ['i', 'isw', 'pdes', 'ptol', 'arname'],
            'data': [[1, 0, 0.0, 10.0, 'AREA_1']]
        }
    
    if 'zone' not in mapped_data:
        logger.info("Adding default zone section")
        mapped_data['zone'] = {
            'fields': ['i', 'zoname'],
            'data': [[1, 'ZONE_1']]
        }
    
    if 'owner' not in mapped_data:
        logger.info("Adding default owner section")
        mapped_data['owner'] = {
            'fields': ['i', 'owname'],
            'data': [[1, 'OWNER_1']]
        }
    
    # Create virtual system_data section if any system sections exist
    system_sections = ['general', 'gauss', 'newton', 'adjust', 'tysl', 'solver']
    if any(section in mapped_data for section in system_sections):
        logger.info("Creating virtual system_data section from individual system sections")
        mapped_data['system_data'] = {
            'fields': ['combined'],  # Virtual field
            'data': [[1]]  # Virtual data to pass validation
        }
    
    # Validate required sections for the version
    required_sections = [section for section, _ in section_order_and_breaks]
    missing_sections = []
    for required_section in required_sections:
        if required_section not in mapped_data:
            missing_sections.append(required_section)
    
    if missing_sections:
        logger.warning(f"Missing required sections for V{version}: {missing_sections}")
        logger.warning("These sections will be skipped or use default values")
    
    # Export the data
    records_written = 0
    logger.info(f"📊 Total records to export: {sum(len(section_data.get('data', [])) for section_data in mapped_data.values())}")
    
    try:
        with open(output_path, 'w', encoding='utf-8') as output_file:
            for i, (section_name, section_break) in enumerate(section_order_and_breaks):
                # Get writer for this section
                writer = registry.get_writer(section_name, raw_version, modeling_approach, str(output_path))
                
                if writer:
                    # Write field header
                    writer.write_field_header(output_file)
                    
                    # Always try to write section data - writers handle missing data by generating defaults
                    section_records = writer.write_section(mapped_data, output_file)
                    records_written += section_records
                    
                    if section_name in mapped_data and section_records > 0:
                        logger.debug(f"✅ {section_name}: {section_records} records written")
                    elif section_records > 0:
                        logger.debug(f"✅ {section_name}: {section_records} default records written")
                    else:
                        logger.debug(f"⚠️  {section_name}: No data written")
                else:
                    logger.debug(f"⚠️  {section_name}: No writer available")
                
                # Write section terminator/break
                if section_break == 'Q':
                    # Final terminator
                    output_file.write("Q\n")
                else:
                    # Standard section break: "0 / END OF X DATA, BEGIN Y DATA"
                    # IMPORTANT: Do NOT write section break after case identification data
                    # PSS/E expects system data to follow immediately without any break
                    if section_name == 'caseid':
                        # Skip the section break after case identification - system data follows directly
                        continue
                    else:
                        output_file.write(f"0 / {section_break}\n")
            
        logger.info(f"✅ Successfully exported {records_written} records to {output_path}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"❌ Failed to export RAW file: {e}")
        raise IOError(f"Failed to export RAW file to {output_path}: {e}")


def export_raw(backend, output_path: str, version: int = 35) -> None:
    """
    Legacy interface for backward compatibility.
    Export the backend database to a classic PSS/E .raw file.
    """
    # Convert to new interface
    export_to_raw_format(
        data=backend.data if hasattr(backend, 'data') else backend.get_data(),
        output_path=output_path,
        version=str(version),
        modeling_approach="bus_branch"
    ) 
