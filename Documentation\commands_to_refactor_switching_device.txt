# Refactoring and Modernizing Device Backend Architecture: Step-by-Step Guide

This guide documents every step required to migrate from a legacy device module (e.g., `*_nb.py`) to a modern, modular, backend-agnostic, and testable architecture (e.g., `*_agile.py`).

**You must ensure that all agile device classes (e.g., `SwitchingDevice`, `Bus`, `Branch`, etc.) follow the full-featured, extensible, and robust pattern established by `switching_device_agile.py`. Minimal wrappers like the original `bus_agile.py` are NOT sufficient.**

---

## 0. **Critical Requirements Before You Begin**

> **You must follow these requirements to ensure correctness, maintainability, and ecosystem compatibility.**

1. **Every PSSPY API call in the original `*_nb.py` file must be accounted for in `psspy_api.py`.**
    - Double-check the original `*_nb.py` file for any `psspy.*` commands.
    - If any PSSPY command is missing from `psspy_api.py`, you **must** implement it.
    - Do not skip or omit any PSSPY API call, even if it seems unused.

2. **All device and field maps must be checked and updated according to the official documentation.**
    - Review the canonical field mappings in `Documentation/docs/api*.md` and the official PSS/E documentation.
    - Update all device and field maps in the codebase to match the official sources.

3. **Every method in the original `*_nb.py` file must be present in the new `*_agile.py` file.**
    - Do not omit or rename methods unless you provide a clear, documented migration path.
    - This is essential to avoid breaking the rest of the ecosystem and to maintain backward compatibility.

4. **All public methods must include sample usage examples in their docstrings.**
    - Every method in `psspy_api.py`, `database_api.py`, and the agile data model must have a usage example in its docstring.
    - Follow the format:

        ```python
        def example_method(...):
            """
            ...
            Example:
                >>> result = example_method(...)
            """
        ```

---

## 1. Preparation

1. **Copy the legacy file:**

    ```shell
    cp controller/psse/your_device_nb.py controller/psse/your_device_agile.py
    # Or for another device:
    # cp controller/psse/your_device_nb.py controller/psse/your_device_agile.py
    ```

2. **Gather references:**
    - Review all relevant `api*.md` documentation in `Documentation/docs` for canonical field mappings and expected API signatures.
    - Review Python wrappers in `RawEditor/API` for reference implementations and field logic.

---

## 2. Refactor the Data Model for Backend Agnosticism and Robustness

**MANDATORY: All agile device classes must match the pattern of `switching_device_agile.py`.**

### Checklist for Every Agile Device Class
- [ ] **Canonical fields**: All device fields (e.g., `number`, `name`, `voltage`, etc.) must be present as properties with getters/setters.
- [ ] **Active Record methods**: Implement `save()`, `delete()`, `refresh()`, and any device-specific actions, all routed through `api_manager`.
- [ ] **Validation**: Add a `validate()` method to check required fields and types.
- [ ] **Serialization**: Add a `to_dict()` method for backend operations.
- [ ] **Key property**: Add a `key` property for unique identification.
- [ ] **Docstrings and usage examples**: All public methods must have clear docstrings and usage examples.
- [ ] **Factory function**: Provide a `create_<device>()` factory for convenience.
- [ ] **Error handling and logging**: Use robust error handling and log failures.
- [ ] **Extensible**: Design for easy addition of new fields and behaviors.
- [ ] **No backend logic**: No backend-specific logic, imports, or selection in the device class. All backend operations must go through `api_manager`.

### Example Prompt for Device Class Refactor
> Refactor `<device>_agile.py` to match the robust, extensible, and idiomatic pattern of `switching_device_agile.py`. Implement property getters/setters for all canonical fields, active record methods (`save`, `delete`, `refresh`), validation, serialization, key property, docstrings, usage examples, error handling, and a factory function. All backend operations must go through `api_manager`. Remove any minimal wrappers or direct backend logic.

---

## 3. Implement/Refactor Backend Adapters

### 3.1. PSSPY Backend (`psspy_api.py`)

- Implement a `PSSPYBackend` class with unified methods: `save_switching_device`, `get_switching_device`, `delete_switching_device`, `move_switching_device`, etc.
- All logic for interacting with PSSPY must be here, not in the device model.
- Add clear docstrings and usage examples for all public methods.

### 3.2. Database Backend Adapter (`database_api.py`)

- Implement a `DatabaseBackendAdapter` class with unified methods: `save_switching_device`, `get_switching_device`, `delete_switching_device`, `move_switching_device`, etc.
- All logic for interacting with the canonical database must be here, not in the device model.
- Add clear docstrings and usage examples for all public methods.

### 3.3. API Manager (`api_manager.py`)

- Create an `APIManager` class that selects and instantiates the correct backend based on `backend_config.py`.
- Expose unified CRUD methods (e.g., `save_switching_device`, `get_switching_device`, etc.).
- All device classes must use `api_manager` for backend operations.

### Example Prompt for Backend Adapter Refactor
> Update `psspy_api.py` and `database_api.py` to implement `save_<device>`, `get_<device>`, `delete_<device>`, and (optionally) `move_<device>`. Use canonical field names and key logic. Add docstrings and usage examples. Ensure all logic is DRY and extensible.

---

## 4. Update API Manager

- Ensure `api_manager.py` exposes all CRUD methods for the device (e.g., `save_bus`, `get_bus`, `delete_bus`, etc.).
- All methods must be documented and routed to the correct backend.

### Example Prompt for API Manager
> Update `api_manager.py` to expose `save_<device>`, `get_<device>`, `delete_<device>`, and (optionally) `move_<device>`. All methods must be documented and routed to the correct backend. Ensure symmetry and extensibility.

---

## 5. Backend Implementations – Canonical Device Support

- In `database_manager.py`, `database_backend.py`, and `json_backend.py`:
  - Ensure CRUD for device records.
  - Use canonical field mapping and key construction.
  - No logic duplication; use shared utilities where possible.

### Example Prompt for Backend Implementation
> Update `database_manager.py`, `database_backend.py`, and `json_backend.py` to ensure full CRUD support for `<device>`. Use canonical field mapping and key construction. Refactor for DRY and extensibility.

---

## 6. Testing & Documentation

- Add/expand tests for all new/updated methods.
- Update docstrings and usage examples for all public methods and classes.
- Ensure all tests write to structured log files.

### Example Prompt for Testing
> Add or update tests for `<device>` covering all CRUD operations, edge cases, and error handling. Ensure all tests write to structured log files. Update docstrings and usage examples for all public methods.

---

## 7. DRY, SOLID, Extensible Patterns

- No logic duplication: all field mapping, key construction, and CRUD logic should be in one place per concern.
- Use composition and dependency injection where appropriate.
- All device classes must be backend-agnostic and only use `api_manager`.
- All backend adapters must be swappable and mockable.

---

## 8. Commit Hygiene

- Each step should be atomic, test-passing, and clearly documented.

---

## 9. Final Review and Checklist

- [ ] All device logic is backend-agnostic, robust, and modular (matches `switching_device_agile.py` pattern).
- [ ] All backend adapters are general-purpose and extensible.
- [ ] All public APIs are documented and consistent across backends.
- [ ] All tests pass and logs are clean.
- [ ] Linting and formatting are clean.
- [ ] Architecture diagrams and documentation are up to date.

---

## 10. Adapting for Other Device Types

- Replace all instances of the device name and related field names with your target device type and canonical fields.
- Follow the same robust, extensible, and backend-agnostic pattern as `switching_device_agile.py`.
- Do NOT use minimal wrappers—always implement the full pattern.

---

**References:**
- `RawEditor/API/api*.md` for canonical field mappings and command lists.
- Python wrappers in `RawEditor/API` for reference implementations.
- This file for step-by-step migration and best practices. 