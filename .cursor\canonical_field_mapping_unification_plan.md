# Canonical Field Mapping Unification Plan
**Date**: 2025-01-02  
**Issue**: Multiple canonical field mappings scattered across backends, converters, and exporters  
**Impact**: Major architectural unification, single source of truth for all field mappings

## Problem Analysis

### Current Scattered Architecture:
```
FIELD_MAP (PureFieldMapper) - Lines ~600-3000
├── bus: {canonical_name: FieldSpec(aliases=[...])}
├── load: {canonical_name: FieldSpec(aliases=[...])}
└── generator: {canonical_name: FieldSpec(aliases=[...])}

HdbBackend.to_canonical() - Lines ~6750-6900
├── BusConverter(source_data) - Uses PureFieldMapper
├── LoadConverter(source_data) - Uses PureFieldMapper  
└── GeneratorConverter(source_data) - Uses PureFieldMapper

RawSectionWriter._get_mapped_record() - Lines ~10280-10330
├── Creates duplicate field mapping logic
├── Hardcoded canonical field sets
└── Violates single source of truth

GeneratorWriter.__init__() - Lines ~10560-10580
├── self.mapper = PureFieldMapper() - Duplicate instance
└── map_record() calls - Redundant mapping

AcLineWriter.__init__() - Lines ~10675-10695
├── self.mapper = PureFieldMapper() - Duplicate instance
└── map_record() calls - Redundant mapping
```

### Proposed Unified Architecture:
```
FIELD_MAP (PureFieldMapper) - SINGLE SOURCE OF TRUTH
├── bus: {canonical_name: FieldSpec(aliases=[...])}
├── load: {canonical_name: FieldSpec(aliases=[...])}
├── generator: {canonical_name: FieldSpec(aliases=[...])}
└── ALL_DEVICES: {canonical_name: FieldSpec(aliases=[...])}

ALL_BACKENDS.to_canonical()
├── Uses PureFieldMapper.map_record() for ALL field mapping
├── Returns {'fields': [...], 'data': [dict, dict, ...]}
└── No duplicate field mapping logic

ALL_WRITERS.write_section()
├── Direct dict access: record.get('canonical_field_name', default)
├── No field mapping - data already canonical
└── No PureFieldMapper instances in writers
```

## Benefits of Unified Architecture

1. **Single Source of Truth**: Only FIELD_MAP defines canonical field names and aliases
2. **Consistency**: All backends, converters, and exporters use identical field names
3. **Maintainability**: Add new field/alias in one place, works everywhere
4. **Performance**: Eliminate duplicate mapper instances and redundant mapping calls
5. **Debugging**: Clear field name resolution through single mapper
6. **Testing**: Single point to test field mapping logic

## Current Field Mapping Analysis

### A. FIELD_MAP Definition (Lines ~600-3000)
**Status**: ✅ Single source of truth for field specifications
**Contains**: 
- 15+ device types (bus, load, generator, acline, transformer, etc.)
- 200+ canonical field names with aliases
- Field validation and transformation functions

**Example Structure**:
```python
'bus': {
    'ibus': FieldSpec(
        canonical_name='ibus',
        aliases=['Number', 'bus', 'bus_id', 'bus_num', 'bus_number', 'i', 'ibus', 'number'],
        transform=safe_int
    ),
    'voltage_magnitude': FieldSpec(
        canonical_name='voltage_magnitude', 
        aliases=['Voltage Magnitude (pu)', 'v_mag', 'vm', 'vmag', 'voltage_magnitude'],
        transform=safe_float,
        validation=validate_voltage
    )
}
```

### B. Backend Data Access Patterns

#### 1. HdbBackend.to_canonical() (Lines ~6750-6900)
**Current Pattern**:
```python
converter_map = {
    'bus': BusConverter(source_data),
    'load': LoadConverter(source_data), 
    'generator': GeneratorConverter(source_data),
    # ... other converters
}

for equipment_type, converter in converter_map.items():
    result = converter.convert()  # Uses PureFieldMapper internally
    canonical_data[equipment_type] = result
```

**Status**: ✅ Correctly uses converters with PureFieldMapper
**Field Access**: Through converter.map_record() calls

#### 2. CanonicalBackend.to_canonical() (Lines ~7500-7600)
**Current Pattern**: 
```python
# Direct canonical data access
return self.data
```

**Status**: ✅ Already canonical, no mapping needed
**Field Access**: Direct dictionary access

#### 3. RawxBackend.to_canonical() (Lines ~8000-8100)
**Current Pattern**:
```python
# Uses PureFieldMapper for RAWX→canonical conversion
mapper = PureFieldMapper()
canonical_record = mapper.map_record(device_type, record, fields)
```

**Status**: ✅ Correctly uses PureFieldMapper
**Field Access**: Through mapper.map_record() calls

### C. Converter Field Mapping Patterns

#### 1. BusConverter.convert() (Lines ~4000-4300)
**Current Pattern**:
```python
# Creates buses_data dictionary with canonical field names
buses_data[bus_number] = {
    'ibus': bus_number,
    'name': bus_name,
    'base_kv': baskv,
    'voltage_magnitude': voltage_data['vm'],
    'normal_high_voltage_limit': voltage_limits.get('normal_high_voltage_limit', 1.1),
    # ... other canonical fields
}

# Converts to list format (ARCHITECTURAL FLAW)
record_list = [bus_data.get(field, None) for field in canonical_fields]
```

**Status**: ❌ Creates canonical field names manually instead of using PureFieldMapper
**Issue**: Hardcoded field names that could diverge from FIELD_MAP
**Field Access**: Direct dictionary creation with hardcoded canonical names

#### 2. LoadConverter.convert() (Lines ~5000-5500)
**Current Pattern**:
```python
# Similar pattern - creates canonical field names manually
canonical_record = {
    'ibus': load_bus,
    'real_power': load_data.get('MW', 0.0),
    'reactive_power': load_data.get('MVAR', 0.0),
    # ... other canonical fields
}
```

**Status**: ❌ Same issue - hardcoded canonical field names
**Issue**: Field names not sourced from FIELD_MAP

#### 3. GeneratorConverter.convert() (Lines ~5500-6000)
**Current Pattern**:
```python
# Uses PureFieldMapper correctly
canonical_record = self.field_mapper.map_record('generator', gen_data, field_names)
```

**Status**: ✅ Correctly uses PureFieldMapper
**Field Access**: Through field_mapper.map_record() calls

### D. Writer Field Access Patterns

#### 1. RawSectionWriter._get_mapped_record() (Lines ~10280-10330)
**Current Pattern**:
```python
def _get_mapped_record(self, section_type: str, record: list, fields: list) -> Dict[str, Any]:
    # Creates duplicate field mapping logic
    canonical_fields_by_type = {
        'bus': {'ibus', 'name', 'base_kv', 'bus_type', ...},
        'load': {'ibus', 'loadid', 'stat', 'area', ...},
        # ... hardcoded field sets
    }
    
    if canonical_fields and set(fields).intersection(canonical_fields):
        return dict(zip(fields, record))  # Skip mapping
    
    # Use PureFieldMapper as fallback
    mapper = PureFieldMapper()
    return mapper.map_record(section_type, record, fields)
```

**Status**: ❌ Creates duplicate field mapping logic
**Issue**: Hardcoded canonical field sets that duplicate FIELD_MAP
**Field Access**: Mixed - sometimes direct dict, sometimes through mapper

#### 2. BusWriter.write_section() (Lines ~10470-10520)
**Current Pattern**:
```python
for record in bus_section['data']:
    mapped_record = self._get_mapped_record('bus', record, fields)
    i = self._safe_get(mapped_record, 'ibus', 0)
    name = str(self._safe_get(mapped_record, 'name', ''))
    # ... more field extractions
```

**Status**: ❌ Uses _get_mapped_record instead of direct dict access
**Issue**: Unnecessary mapping when data is already canonical
**Field Access**: Through _get_mapped_record wrapper

#### 3. LoadWriter.write_section() (Lines ~10520-10580)
**Current Pattern**:
```python
for record in load_section['data']:
    mapped_record = self._get_mapped_record('load', record, fields)
    ibus = self._safe_get(mapped_record, 'ibus', 0)
    pl = self._safe_get(mapped_record, 'real_power', 0.0)
    # ... more field extractions
```

**Status**: ❌ Same issue - unnecessary mapping
**Field Access**: Through _get_mapped_record wrapper

#### 4. GeneratorWriter.write_section() (Lines ~10580-10700)
**Current Pattern**:
```python
def __init__(self, version: RawVersion):
    self.mapper = PureFieldMapper()  # Duplicate instance

for record in generator_section['data']:
    canonical_record = self.mapper.map_record('generator', record, field_names)
    ibus = canonical_record.get('ibus', 0)
    # ... more field extractions
```

**Status**: ❌ Creates duplicate PureFieldMapper instance
**Issue**: Redundant mapping when data is already canonical
**Field Access**: Through duplicate mapper instance

#### 5. AcLineWriter.write_section() (Lines ~10700-10800)
**Current Pattern**:
```python
def __init__(self, version: RawVersion):
    self.mapper = PureFieldMapper()  # Duplicate instance

for record in ac_line_section['data']:
    canonical_record = self.mapper.map_record('acline', record, field_names)
    # ... field extractions
```

**Status**: ❌ Same issue - duplicate mapper instance
**Field Access**: Through duplicate mapper instance

### E. Export Function Patterns

#### 1. export_to_raw_format() (Lines ~12822-12922)
**Current Pattern**:
```python
for section_name, section_break in section_order_and_breaks:
    writer = registry.get_writer(section_name, raw_version, str(output_path))
    if writer:
        section_records = writer.write_section(mapped_data, output_file)
```

**Status**: ✅ Correctly delegates to writers
**Field Access**: Through writer.write_section() calls

#### 2. UniversalBackend.export_raw() (Lines ~13259-13300)
**Current Pattern**:
```python
def export_raw(self, output_path: Union[str, Path], version: str = "35") -> None:
    canonical_data = self.to_canonical()
    export_to_raw_format(canonical_data, output_path, version)
```

**Status**: ✅ Correctly converts to canonical then exports
**Field Access**: Through canonical data structure

## Inconsistent Field Name Usage

### A. Canonical Field Name Variations Found:

#### 1. Bus Fields:
- **FIELD_MAP**: `'ibus'`, `'voltage_magnitude'`, `'normal_high_voltage_limit'`
- **BusConverter**: `'ibus'`, `'voltage_magnitude'`, `'normal_high_voltage_limit'` ✅
- **BusWriter**: `'ibus'`, `'voltage_magnitude'`, `'normal_high_voltage_limit'` ✅

#### 2. Load Fields:
- **FIELD_MAP**: `'ibus'`, `'real_power'`, `'reactive_power'`
- **LoadConverter**: `'ibus'`, `'real_power'`, `'reactive_power'` ✅
- **LoadWriter**: `'ibus'`, `'real_power'`, `'reactive_power'` ✅

#### 3. Generator Fields:
- **FIELD_MAP**: `'ibus'`, `'real_power_output'`, `'reactive_power_output'`
- **GeneratorConverter**: Uses PureFieldMapper ✅
- **GeneratorWriter**: Uses PureFieldMapper ✅

### B. Incorrect Field Access Patterns:

#### 1. Hardcoded Field Names in Converters:
```python
# BusConverter.convert() - Lines ~4100-4150
buses_data[bus_number] = {
    'ibus': bus_number,  # Should use FIELD_MAP canonical name
    'voltage_magnitude': voltage_data['vm'],  # Should use FIELD_MAP canonical name
    'normal_high_voltage_limit': voltage_limits.get('normal_high_voltage_limit', 1.1),
}

# LoadConverter.convert() - Lines ~5200-5250  
canonical_record = {
    'ibus': load_bus,  # Should use FIELD_MAP canonical name
    'real_power': load_data.get('MW', 0.0),  # Should use FIELD_MAP canonical name
}
```

#### 2. Duplicate Field Sets in Writers:
```python
# RawSectionWriter._get_mapped_record() - Lines ~10300-10320
canonical_fields_by_type = {
    'bus': {'ibus', 'name', 'base_kv', 'bus_type', ...},  # Duplicates FIELD_MAP
    'load': {'ibus', 'loadid', 'stat', 'area', ...},      # Duplicates FIELD_MAP
}
```

#### 3. Redundant Mapper Instances:
```python
# GeneratorWriter.__init__() - Lines ~10560-10570
self.mapper = PureFieldMapper()  # Duplicate instance

# AcLineWriter.__init__() - Lines ~10675-10685  
self.mapper = PureFieldMapper()  # Duplicate instance
```

## Implementation Plan

### Phase 1: Unify Converter Field Mapping (High Impact)

#### 1.1 Update BusConverter.convert() (Lines ~4000-4300)
**Current**:
```python
buses_data[bus_number] = {
    'ibus': bus_number,
    'name': bus_name,
    'base_kv': baskv,
    # ... hardcoded canonical names
}
```

**Proposed**:
```python
# Use PureFieldMapper to ensure canonical field names
field_mapper = PureFieldMapper()
bus_data = {
    'ibus': bus_number,
    'name': bus_name, 
    'base_kv': baskv,
    # ... other fields
}
# Validate against FIELD_MAP canonical names
canonical_bus_data = field_mapper.map_record('bus', bus_data, list(bus_data.keys()))
buses_data[bus_number] = canonical_bus_data
```

#### 1.2 Update LoadConverter.convert() (Lines ~5000-5500)
**Current**: Hardcoded canonical field names
**Proposed**: Use PureFieldMapper.map_record() for field name validation

#### 1.3 Update All Other Converters
**Pattern**: Replace hardcoded field names with PureFieldMapper validation
**Impact**: Ensures all converters use FIELD_MAP canonical names

### Phase 2: Eliminate Writer Field Mapping (Medium Impact)

#### 2.1 Remove RawSectionWriter._get_mapped_record() (Lines ~10280-10330)
**Action**: Delete entire method
**Justification**: Unnecessary when canonical data is already dictionaries

#### 2.2 Remove RawSectionWriter._safe_get() (Lines ~10310-10320)
**Action**: Delete entire method  
**Justification**: Standard dict.get() with None handling is sufficient

#### 2.3 Update BusWriter.write_section() (Lines ~10470-10520)
**Current**:
```python
mapped_record = self._get_mapped_record('bus', record, fields)
i = self._safe_get(mapped_record, 'ibus', 0)
```

**Proposed**:
```python
# record is already a dictionary with canonical field names
i = record.get('ibus', 0)
name = str(record.get('name', ''))
baskv = record.get('base_kv', 0.0)
# ... direct field access
```

#### 2.4 Update LoadWriter.write_section() (Lines ~10520-10580)
**Pattern**: Replace _get_mapped_record calls with direct dict access
**Impact**: Cleaner, more readable code

#### 2.5 Update GeneratorWriter.write_section() (Lines ~10580-10700)
**Current**:
```python
def __init__(self, version: RawVersion):
    self.mapper = PureFieldMapper()  # Remove this

canonical_record = self.mapper.map_record('generator', record, field_names)
```

**Proposed**:
```python
def __init__(self, version: RawVersion):
    super().__init__(version)  # No mapper needed

# record is already canonical
ibus = record.get('ibus', 0)
genid = record.get('genid', '6')[:2]
# ... direct field access
```

#### 2.6 Update AcLineWriter.write_section() (Lines ~10700-10800)
**Pattern**: Remove mapper instance, use direct dict access
**Impact**: Eliminate duplicate PureFieldMapper instances

#### 2.7 Update All Other Writers
**Pattern**: Same changes - remove mapping, use direct dict access
**Impact**: Consistent architecture across all writers

### Phase 3: Validate FIELD_MAP Completeness (Low Impact)

#### 3.1 Audit FIELD_MAP Coverage
**Action**: Ensure all canonical field names used in converters are defined in FIELD_MAP
**Method**: Search for hardcoded field names in converters and add missing ones to FIELD_MAP

#### 3.2 Add Missing Field Specifications
**Action**: Add any missing FieldSpec entries to FIELD_MAP
**Impact**: Complete single source of truth for all field mappings

#### 3.3 Update Field Aliases
**Action**: Ensure all field aliases work correctly across all backends
**Method**: Test field mapping with various alias combinations

### Phase 4: Performance Optimization (Low Impact)

#### 4.1 Eliminate Duplicate Mapper Instances
**Action**: Remove all PureFieldMapper instances from writers
**Impact**: Memory usage reduction and cleaner architecture

#### 4.2 Optimize Field Access
**Action**: Use direct dict access instead of mapping calls
**Impact**: Performance improvement for 57,000+ record accesses

## Validation Plan

### Unit Tests Required:

#### 1. FIELD_MAP Completeness Test
```python
def test_field_map_completeness():
    """Verify all canonical field names used in converters are in FIELD_MAP"""
    # Extract all hardcoded field names from converters
    # Verify each exists in FIELD_MAP
```

#### 2. Converter Field Mapping Test
```python
def test_converter_field_mapping():
    """Verify all converters use FIELD_MAP canonical names"""
    # Test each converter with sample data
    # Verify output uses FIELD_MAP canonical names
```

#### 3. Writer Field Access Test
```python
def test_writer_field_access():
    """Verify all writers can access canonical field names directly"""
    # Test each writer with canonical dictionary data
    # Verify no mapping errors occur
```

#### 4. Alias Resolution Test
```python
def test_alias_resolution():
    """Verify all field aliases resolve to correct canonical names"""
    # Test each alias in FIELD_MAP
    # Verify correct canonical name resolution
```

### Integration Tests Required:

#### 1. Full Pipeline Test
```python
def test_full_pipeline_field_consistency():
    """Test HDB→canonical→RAW export with field name validation"""
    # Load HDB data
    # Convert to canonical
    # Export to RAW
    # Verify all field names are consistent throughout
```

#### 2. Cross-Backend Consistency Test
```python
def test_cross_backend_field_consistency():
    """Test field names are consistent across all backends"""
    # Test HDB, RAWX, and Canonical backends
    # Verify identical canonical field names
```

#### 3. Performance Benchmark Test
```python
def test_performance_improvement():
    """Measure performance improvement from unified field mapping"""
    # Benchmark before/after conversion times
    # Verify 30-50% improvement expected
```

## Success Metrics

### Code Quality Improvements:
- **Single Source of Truth**: Only FIELD_MAP defines canonical field names
- **Eliminated Duplication**: Remove 200+ lines of duplicate field mapping code
- **Consistent Architecture**: All components use identical field access patterns
- **Maintainability**: Add new field/alias in one place, works everywhere

### Performance Improvements:
- **Memory Usage**: 25-40% reduction from eliminated mapper instances
- **Conversion Speed**: 30-50% faster from direct dict access
- **Code Complexity**: 300+ lines of redundant code eliminated

### Reliability Improvements:
- **Field Name Consistency**: 100% consistency across all components
- **Alias Support**: All aliases work correctly in all contexts
- **Error Reduction**: No more field name mismatches or mapping errors

## Risk Assessment

### High Risk Items: NONE
- This is a pure architectural improvement with no functionality changes
- All field names remain the same canonical names
- All business logic remains unchanged

### Medium Risk Items:
- **Converter updates**: Need careful testing to ensure field mapping still works
- **Writer updates**: Need validation that all field accesses are correct

### Low Risk Items:
- **Method removal**: Dead code elimination
- **Performance optimization**: Pure improvement with no functional changes

## Post-Implementation

### Documentation Updates:
1. **Field Mapping Guide**: Document single FIELD_MAP as source of truth
2. **Developer Guide**: Update examples to show direct dict access patterns
3. **Architecture Diagram**: Show simplified field mapping flow

### Future Benefits:
1. **Extensibility**: Easy to add new fields/aliases in one place
2. **Testing**: Simple to test field mapping with single mapper
3. **Debugging**: Clear field name resolution through single source
4. **Performance**: Foundation for future optimization opportunities

---

**CRITICAL SUCCESS FACTOR**: This unification eliminates the scattered field mapping architecture and establishes FIELD_MAP as the single source of truth for all canonical field names and aliases. The current duplicate field mapping logic across converters and writers violates the DRY principle and creates maintenance nightmares. 