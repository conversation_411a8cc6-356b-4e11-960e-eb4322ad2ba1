#!/usr/bin/env python3
"""
Test script to verify AC line naming convention fixes.
"""

def test_ac_line_naming_fix():
    """Test that AC line data is correctly converted using the new naming convention."""
    
    print("🔍 Testing AC Line Naming Convention Fix")
    print("=" * 50)
    
    try:
        from hdb_to_raw_pipeline import HdbBackend, FIELD_MAP
        
        # Test 1: Check that ac_line field mapping exists
        print("1. Testing field mapping...")
        if 'ac_line' in FIELD_MAP:
            print("   ✅ ac_line field mapping exists")
            ac_line_fields = list(FIELD_MAP['ac_line'].keys())
            print(f"   📋 Available fields: {ac_line_fields}")
        else:
            print("   ❌ ac_line field mapping missing")
            return False
        
        # Test 2: Test field mapping functionality
        print("\n2. Testing field mapping functionality...")
        backend = HdbBackend()
        test_record = {
            'From Node': 'TEST_NODE_1',
            'To Node': 'TEST_NODE_2', 
            'Resistance': 5.0,
            'Reactance': 10.0,
            'Charging': 2.0,
            'Circuit': 'A'
        }
        
        mapped_record = backend.field_mapper.map_record('ac_line', test_record)
        if mapped_record:
            print("   ✅ Field mapping works correctly")
            print(f"   📋 Mapped fields: {list(mapped_record.keys())}")
        else:
            print("   ❌ Field mapping failed")
            return False
            
        # Test 3: Test actual HDB conversion
        print("\n3. Testing HDB conversion...")
        backend.load('hdbcontext_original.hdb')
        canonical_data = backend.to_canonical()
        
        if 'ac_line' in canonical_data:
            ac_line_count = len(canonical_data['ac_line']['data'])
            print(f"   ✅ HDB conversion successful: {ac_line_count} AC line records")
            print(f"   📋 AC line fields: {canonical_data['ac_line']['fields']}")
        else:
            print("   ❌ No ac_line section in canonical data")
            return False
        
        # Test 4: Test canonical interface
        print("\n4. Testing canonical interface...")
        from canonical_data_interface import CanonicalDataInterface
        
        # Create interface correctly
        interface = CanonicalDataInterface()
        interface.data = canonical_data
        
        ac_lines = interface.get_ac_lines()
        print(f"   ✅ Canonical interface works: {len(ac_lines)} AC line records")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ AC line naming convention fix is working correctly!")
        print("✅ Field mapping: ac_line (with underscore)")
        print("✅ Canonical data: ac_line (with underscore)")
        print("✅ Interface: ac_line (with underscore)")
        print("✅ RAWX section: acline (without underscore) - preserved as requested")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ac_line_naming_fix() 