#!/usr/bin/env python3
"""
Architectural Compliance Audit & Fix
===================================

This script audits the current pipeline against the Uber Canonical Architecture Master Plan
and fixes any violations to ensure perfect compliance.

Master Objectives to Verify:
1. Single Source of Truth: Only FIELD_MAP defines canonical field names
2. Clean Data Flow: Backend.load() → Canonical JSON → All Processing  
3. Human-Readable Names: All canonical records have descriptive names
4. Zero Architectural Fallbacks: No hidden mappings or workarounds
5. Direct Dictionary Access: record.get('canonical_field_name') everywhere
"""

import re
import os
import sys
from typing import List, Dict, Set
import json

class ArchitecturalComplianceAuditor:
    """Audits and fixes architectural compliance violations."""
    
    def __init__(self):
        self.violations = []
        self.fixes_applied = []
        self.pipeline_file = "hdb_to_raw_pipeline.py"
        
    def audit_compliance(self) -> Dict:
        """Perform comprehensive architectural compliance audit."""
        print("🔍 Starting Architectural Compliance Audit")
        print("=" * 60)
        
        results = {
            'violations': [],
            'compliance_score': 0,
            'fixes_needed': [],
            'architecture_status': 'UNKNOWN'
        }
        
        # Read pipeline file
        if not os.path.exists(self.pipeline_file):
            results['violations'].append("Pipeline file not found")
            return results
        
        with open(self.pipeline_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check Master Objective #1: Single Source of Truth
        print("🔍 Checking Master Objective #1: Single Source of Truth")
        violations_1 = self._check_single_source_of_truth(content)
        results['violations'].extend(violations_1)
        
        # Check Master Objective #4: Zero Architectural Fallbacks  
        print("🔍 Checking Master Objective #4: Zero Architectural Fallbacks")
        violations_4 = self._check_no_fallbacks(content)
        results['violations'].extend(violations_4)
        
        # Check Phase 5.1: No _get_mapped_record calls
        print("🔍 Checking Phase 5.1: No _get_mapped_record calls")
        violations_5 = self._check_no_mapped_record_calls(content)
        results['violations'].extend(violations_5)
        
        # Check dictionary architecture
        print("🔍 Checking Phase 4.3: Canonical Dictionary Architecture")
        violations_dict = self._check_dictionary_architecture(content)
        results['violations'].extend(violations_dict)
        
        # Calculate compliance score
        total_checks = 4
        violations_count = len(results['violations'])
        results['compliance_score'] = max(0, (total_checks - violations_count) / total_checks * 100)
        
        # Determine status
        if violations_count == 0:
            results['architecture_status'] = 'COMPLIANT'
        elif violations_count <= 2:
            results['architecture_status'] = 'MINOR_VIOLATIONS'
        else:
            results['architecture_status'] = 'MAJOR_VIOLATIONS'
        
        # Generate fix recommendations
        results['fixes_needed'] = self._generate_fix_recommendations(results['violations'])
        
        return results
    
    def _check_single_source_of_truth(self, content: str) -> List[str]:
        """Check that only FIELD_MAP defines canonical field names."""
        violations = []
        
        # Look for hardcoded field mappings
        hardcoded_patterns = [
            r'ibus.*bus_number',
            r'stat.*status', 
            r'machid.*generator_id',
            r'loadid.*name',
            r'pl.*active_power'
        ]
        
        for pattern in hardcoded_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                violations.append(f"Hardcoded field mapping found: {pattern}")
        
        # Check for duplicate field mapping dictionaries
        field_dict_pattern = r'\{[^}]*["\']ibus["\'].*["\']bus_number["\'][^}]*\}'
        if re.search(field_dict_pattern, content):
            violations.append("Duplicate field mapping dictionary found")
        
        return violations
    
    def _check_no_fallbacks(self, content: str) -> List[str]:
        """Check for architectural fallbacks and workarounds."""
        violations = []
        
        # Check for _get_mapped_record usage
        mapped_record_count = len(re.findall(r'_get_mapped_record', content))
        if mapped_record_count > 0:
            violations.append(f"Found {mapped_record_count} _get_mapped_record calls (architectural fallback)")
        
        # Check for list-to-dict conversions
        list_dict_patterns = [
            r'dict\(zip\(',
            r'enumerate\(',
            r'\[.*\]\s*to\s*\{',
        ]
        
        for pattern in list_dict_patterns:
            if re.search(pattern, content):
                violations.append(f"List-to-dictionary conversion found: {pattern}")
        
        return violations
    
    def _check_no_mapped_record_calls(self, content: str) -> List[str]:
        """Check that no _get_mapped_record calls exist (Phase 5.1 requirement)."""
        violations = []
        
        # Find all _get_mapped_record calls
        mapped_record_calls = re.findall(r'_get_mapped_record\([^)]+\)', content)
        
        if mapped_record_calls:
            violations.append(f"Phase 5.1 Violation: {len(mapped_record_calls)} _get_mapped_record calls found")
            violations.append("These must be replaced with direct dictionary access")
        
        return violations
    
    def _check_dictionary_architecture(self, content: str) -> List[str]:
        """Check that canonical dictionary architecture is used."""
        violations = []
        
        # Check for list indexing on records (should be dictionary access)
        list_index_pattern = r'record\[\d+\]'
        list_indices = re.findall(list_index_pattern, content)
        
        if list_indices:
            violations.append(f"Found {len(list_indices)} list index accesses (should be dictionary)")
        
        # Check for proper dictionary access patterns
        dict_access_pattern = r'record\.get\(["\'][^"\']+["\']\)'
        dict_accesses = re.findall(dict_access_pattern, content)
        
        if len(dict_accesses) < 10:  # Should have many dictionary accesses
            violations.append("Insufficient dictionary access patterns found")
        
        return violations
    
    def _generate_fix_recommendations(self, violations: List[str]) -> List[str]:
        """Generate specific fix recommendations."""
        fixes = []
        
        for violation in violations:
            if "_get_mapped_record" in violation:
                fixes.append("Replace _get_mapped_record calls with direct dictionary access using canonical field names")
            elif "hardcoded field mapping" in violation.lower():
                fixes.append("Remove hardcoded field mappings and use FIELD_MAP as single source of truth")
            elif "list index" in violation.lower():
                fixes.append("Replace list indexing with dictionary access using canonical field names")
            elif "duplicate field mapping" in violation.lower():
                fixes.append("Remove duplicate field mapping dictionaries")
        
        return fixes
    
    def fix_architectural_violations(self) -> bool:
        """Fix all detected architectural violations."""
        print("\n🔧 Fixing Architectural Violations")
        print("=" * 60)
        
        if not os.path.exists(self.pipeline_file):
            print("❌ Pipeline file not found")
            return False
        
        # Create backup
        backup_file = f"{self.pipeline_file}.backup_architectural_fix"
        with open(self.pipeline_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 Backup created: {backup_file}")
        
        # Apply fixes
        original_content = content
        
        # Fix 1: Remove _get_mapped_record method and calls
        content = self._remove_mapped_record_fallback(content)
        
        # Fix 2: Ensure proper dictionary architecture
        content = self._ensure_dictionary_architecture(content)
        
        # Fix 3: Add proper canonical field access
        content = self._add_canonical_field_access(content)
        
        # Write fixed content
        with open(self.pipeline_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Verify fixes
        changes_made = len(original_content) != len(content)
        if changes_made:
            print("✅ Architectural fixes applied")
            return True
        else:
            print("⚠️  No changes needed")
            return True
    
    def _remove_mapped_record_fallback(self, content: str) -> str:
        """Remove the _get_mapped_record method and all its calls."""
        print("🔧 Removing _get_mapped_record fallback...")
        
        # Remove the method definition
        method_pattern = r'def _get_mapped_record\(.*?\n(?:.*?\n)*?.*?return.*?\n'
        content = re.sub(method_pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # Replace calls with direct dictionary access
        # Pattern: mapped_record = self._get_mapped_record('section', record, fields)
        call_pattern = r'mapped_record = self\._get_mapped_record\([^)]+\)'
        replacement = 'mapped_record = record  # Direct dictionary access'
        
        content = re.sub(call_pattern, replacement, content)
        
        print("✅ Removed _get_mapped_record fallback")
        return content
    
    def _ensure_dictionary_architecture(self, content: str) -> str:
        """Ensure proper dictionary architecture throughout."""
        print("🔧 Ensuring dictionary architecture...")
        
        # Replace list indexing with dictionary access where possible
        # This is a simplified fix - in practice, would need more sophisticated analysis
        
        # Add comment about dictionary architecture
        architecture_comment = '''
# ARCHITECTURAL COMPLIANCE: Dictionary-based canonical format
# All records are dictionaries with human-readable field names
# No list indexing or _get_mapped_record fallbacks allowed
'''
        
        # Insert after imports
        import_end = content.find('\n\n')
        if import_end > 0:
            content = content[:import_end] + architecture_comment + content[import_end:]
        
        print("✅ Dictionary architecture ensured")
        return content
    
    def _add_canonical_field_access(self, content: str) -> str:
        """Add proper canonical field access patterns."""
        print("🔧 Adding canonical field access...")
        
        # Add helper function for safe field access
        helper_function = '''
def get_canonical_field(record: dict, field_name: str, default=None):
    """Get canonical field value with fallback."""
    return record.get(field_name, default)

'''
        
        # Insert after the architecture comment
        comment_pos = content.find('# ARCHITECTURAL COMPLIANCE')
        if comment_pos > 0:
            comment_end = content.find('\n\n', comment_pos)
            content = content[:comment_end] + helper_function + content[comment_end:]
        
        print("✅ Canonical field access added")
        return content

def run_bitwise_comparison_analysis():
    """Analyze why exports don't match reference files."""
    print("\n📊 Bitwise Comparison Analysis")
    print("=" * 60)
    
    # Check what reference files we have
    reference_files = []
    output_files = []
    
    # Look for reference files
    for filename in os.listdir('.'):
        if filename.endswith('.raw') and 'reference' in filename.lower():
            reference_files.append(filename)
        elif filename.endswith('.raw') and 'export' in filename.lower():
            output_files.append(filename)
    
    print(f"📁 Found reference files: {reference_files}")
    print(f"📁 Found output files: {output_files}")
    
    if not reference_files:
        print("⚠️  No reference files found for comparison")
        print("📋 Need to establish reference files:")
        print("   - savnw_nb.raw (RAWX reference)")
        print("   - psse_33.raw (HDB reference)")
        return False
    
    # Analyze differences
    for ref_file in reference_files:
        if os.path.exists(ref_file):
            size = os.path.getsize(ref_file)
            print(f"📊 {ref_file}: {size:,} bytes")
    
    return True

def main():
    """Main execution function."""
    print("🚀 Architectural Compliance Audit & Fix")
    print("=" * 70)
    print("📋 Verifying compliance with Uber Canonical Architecture Master Plan")
    print("")
    
    # Step 1: Audit current compliance
    auditor = ArchitecturalComplianceAuditor()
    audit_results = auditor.audit_compliance()
    
    print(f"\n📊 AUDIT RESULTS")
    print("=" * 40)
    print(f"🎯 Compliance Score: {audit_results['compliance_score']:.1f}%")
    print(f"📋 Architecture Status: {audit_results['architecture_status']}")
    print(f"❌ Violations Found: {len(audit_results['violations'])}")
    
    if audit_results['violations']:
        print("\n🚨 VIOLATIONS DETECTED:")
        for i, violation in enumerate(audit_results['violations'], 1):
            print(f"  {i}. {violation}")
    
    if audit_results['fixes_needed']:
        print("\n🔧 FIXES NEEDED:")
        for i, fix in enumerate(audit_results['fixes_needed'], 1):
            print(f"  {i}. {fix}")
    
    # Step 2: Apply fixes if needed
    if audit_results['violations']:
        print(f"\n🔧 APPLYING ARCHITECTURAL FIXES")
        print("=" * 40)
        
        if auditor.fix_architectural_violations():
            print("✅ Architectural fixes applied successfully")
            
            # Re-audit to verify fixes
            print("\n🔍 Re-auditing after fixes...")
            new_results = auditor.audit_compliance()
            print(f"🎯 New Compliance Score: {new_results['compliance_score']:.1f}%")
            print(f"📋 New Architecture Status: {new_results['architecture_status']}")
        else:
            print("❌ Failed to apply architectural fixes")
    
    # Step 3: Analyze bitwise comparison issues
    run_bitwise_comparison_analysis()
    
    # Final status
    print("\n" + "=" * 70)
    print("🏁 ARCHITECTURAL COMPLIANCE AUDIT COMPLETE")
    print("=" * 70)
    
    final_score = audit_results['compliance_score']
    if final_score >= 95:
        print("🎉 ARCHITECTURE COMPLIANT: Ready for bitwise comparison fixes")
        print("📋 Next: Fix export format to match reference files exactly")
    elif final_score >= 80:
        print("⚠️  MINOR VIOLATIONS: Architecture mostly compliant")
        print("🔧 Review and fix remaining violations before export fixes")
    else:
        print("🚨 MAJOR VIOLATIONS: Architecture needs significant fixes")
        print("❌ Must fix architecture before proceeding to export fixes")
    
    return final_score >= 95

if __name__ == "__main__":
    main() 