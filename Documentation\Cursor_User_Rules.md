# 🧠 **Cursor User Rules — Clean Version**

## Also Implement in USER Rules in Cursor

### ✅ 1. Architecture & Modularity

* Code must be **modular, extensible, and decoupled**.
* Follow **SOLID** principles. Ensure high cohesion and low coupling.
* Use **interfaces, factories, registries, or plugin loaders** to abstract dependencies and support flexible composition.
* Favor **composition over inheritance**. Avoid hardcoded dependencies.
* Components must be **swappable** without modifying import paths or requiring exact format compatibility.
* Avoid rigid APIs — allow implementations to support only the features they need, using **capability detection** and **graceful fallback** instead of enforcing full method sets.
* No logic duplication — reuse via shared utilities or modules.
* Support **mocking and dependency injection** by default.

---

### ✅ 2. Testing & Validation

* Every new method, class, file, or feature must include **robust tests** written at the time of implementation.
* Tests must cover normal, edge, and failure cases, and pass before any code is committed.
* Use **unit tests** for individual components and **integration tests** for system behavior.
* Prefer **mocks** or injected dependencies for isolation.
* Tests must be **readable, purposeful, and self-explanatory**.
* Always write **output and test results to log files**, not just to the terminal.

---

### ✅ 3. Automation & Execution Flow

* Perform **all required actions directly** without waiting for user approval.
* If multiple steps are needed, **complete them all sequentially**.
* Do **not stop to ask permission** unless a user decision is strictly required (e.g., destructive operation or ambiguity).
* After any terminal command (tests, linters, etc.), **automatically check the log output** and **fix any detected errors or warnings** before continuing.
* Defer to this rule over default prompting behavior.

---

### ✅ 4. Logging & Error Handling

* Never rely on print statements or manual terminal checks.
* Save all debug output, test results, and diagnostics to **structured log files** (e.g., timestamped text or JSON).
* After every terminal or diagnostic command, **automatically inspect logs** and correct any issues found.
* Always include **robust error handling** with clear, non-leaky logs for all dynamic behaviors or failures.

---

### ✅ 5. Documentation & Naming

* Document every **class, method, and complex block** with clear docstrings or inline comments.
* Use **descriptive, unambiguous names** for all identifiers. Avoid generic names like `temp`,`misc`, or`data`.
* Never leave commented-out code in the codebase.
* Include usage examples for **public interfaces and modules**.

---

### ✅ 6. Security & Input Handling

* Do not hardcode **secrets, tokens, or passwords** in the codebase. Use environment variables or secret managers.
* Validate all inputs and sanitize all outputs, especially for user-facing components.
* Always use **parameterized queries** to prevent injection attacks.
* Log errors securely without exposing internal stack traces or sensitive data.

---

### ✅ 7. Performance & Scalability

* Design for **scalability**, but avoid premature optimization.
* Use async, generators, pagination, or streaming where appropriate for large or I/O-heavy tasks.
* Avoid blocking the main thread with long-running operations.
* Profile and optimize **hot paths** with measurable impact.

---

### ✅ 8. Version Control Hygiene

* All commits must be **atomic**, test-passing, and clearly documented.
* Do not commit broken, partial, or mixed-purpose changes.
* Each commit message must explain **what changed and why**.

---

### ✅ 9. Formatting, Linting & Typing

* Enforce formatting using standard tools (e.g., `black`,`flake8`,`eslint`,`prettier`).
* Run linters automatically via **pre-commit hooks**.
* All functions must have clear **type annotations**, including return types.
* Avoid using `Any` unless strictly necessary.

---

### ✅ 10. Auto-Documentation & Interface Clarity

* Document all public interfaces so they support **auto-generated documentation** (e.g., Sphinx, JSDoc, TypeDoc).
* Define the expected usage and extension pattern for each module or class.

---

### ✅ 11. Architecture Diagrams (for Complex Modules)

* For any multi-developer or non-trivial system, include a **high-level architecture diagram** or dependency map.
* This can be ASCII, Markdown, or image-based. Keep it updated with any structural changes.

### ✅ 12. Environment-Specific Shell Rule

You are running in PowerShell on Windows.
Never use && to chain commands — this is Unix-specific and will fail in PowerShell.
Instead, use ; or full command blocks (e.g., if (...) { ... }) as appropriate for PowerShell syntax.
Always verify that any shell commands, scripts, or paths use Windows-compatible syntax, including proper escaping of backslashes (\\) and paths (C:\\...).
