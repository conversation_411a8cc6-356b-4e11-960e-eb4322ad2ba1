#!/usr/bin/env python3
"""
Debug script to trace RAWX backend loading step by step.
"""

import os
import json
from hdb_to_raw_pipeline import RawxBackend, ModelTransformations

def debug_rawx_loading():
    print("🔍 Debugging RAWX backend loading...")
    
    file_path = 'savnw_nb.rawx'
    
    # Check file exists
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    file_size = os.path.getsize(file_path)
    print(f"✅ File exists: {file_path} ({file_size} bytes)")
    
    # Load JSON manually first
    print(f"\n📖 Loading JSON manually...")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            rawx_data = json.load(f)
        
        print(f"✅ JSON loaded successfully")
        print(f"📊 Top-level keys: {list(rawx_data.keys())}")
        
        if 'network' in rawx_data:
            network_data = rawx_data['network']
            print(f"📊 Network data type: {type(network_data)}")
            if isinstance(network_data, dict):
                print(f"📊 Network sections: {len(network_data)}")
                print(f"📝 Network section names: {list(network_data.keys())}")
                
                # Check for substation data
                for section_name in ['sub', 'subnode', 'subswd', 'substation', 'node', 'switching_device']:
                    if section_name in network_data:
                        section_data = network_data[section_name]
                        if isinstance(section_data, dict) and 'data' in section_data:
                            record_count = len(section_data['data'])
                            print(f"   ✅ {section_name}: {record_count} records")
                        else:
                            print(f"   ❓ {section_name}: {type(section_data)} (unexpected format)")
                    else:
                        print(f"   ❌ {section_name}: not found")
            else:
                print(f"❌ Network data is not a dictionary: {type(network_data)}")
        else:
            print(f"❌ No 'network' key in JSON")
            
    except Exception as e:
        print(f"❌ Error loading JSON: {e}")
        return
    
    # Test backend initialization WITHOUT file_path
    print(f"\n🏗️ Testing backend without file_path...")
    try:
        backend_empty = RawxBackend()
        print(f"✅ Empty backend created")
        print(f"📊 Empty backend data sections: {len(backend_empty.data)}")
        
        # Now manually call load
        print(f"\n📥 Manually calling load()...")
        backend_empty.load(file_path)
        print(f"✅ Manual load completed")
        print(f"📊 After manual load - data sections: {len(backend_empty.data)}")
        if backend_empty.data:
            print(f"📝 Section names: {list(backend_empty.data.keys())[:10]}...")
            
        # Test canonical conversion
        print(f"\n🔄 Testing canonical conversion...")
        canonical_data = backend_empty.to_canonical()
        print(f"📊 Canonical sections: {len(canonical_data)}")
        if canonical_data:
            print(f"📝 Canonical section names: {list(canonical_data.keys())[:10]}...")
            
            # Check for hybrid conversion requirements
            has_substation = 'substation' in canonical_data and canonical_data['substation'].get('data')
            has_node = 'node' in canonical_data and canonical_data['node'].get('data')
            print(f"🏗️ Hybrid conversion possible: substation={has_substation}, node={has_node}")
            
            if has_substation and has_node:
                # Test model detection
                model_type = ModelTransformations.detect_model_type(canonical_data)
                print(f"🔍 Detected model type: {model_type}")
                
                # Test hybrid conversion  
                print(f"🔄 Testing hybrid conversion...")
                try:
                    result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
                    print(f"✅ Hybrid conversion: success={result.success}")
                    if result.errors:
                        print(f"❌ Hybrid errors: {result.errors}")
                    if result.canonical_data:
                        print(f"📊 Hybrid result sections: {len(result.canonical_data)}")
                except Exception as e:
                    print(f"❌ Hybrid conversion failed: {e}")
            
        
    except Exception as e:
        print(f"❌ Manual load failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_rawx_loading() 