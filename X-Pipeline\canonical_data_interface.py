#!/usr/bin/env python3
"""
Canonical Data Interface - Controlled Access to Power System Data

This module provides the canonical data interface that enforces architectural
separation between backend data management and export operations.

Key Principles:
1. Immutable data access for export operations
2. Controlled interface preventing direct backend access
3. Canonical field names throughout
4. Type-safe data access methods
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Iterator
from dataclasses import dataclass
from copy import deepcopy
import logging


@dataclass(frozen=True)
class CanonicalRecord:
    """Immutable record with canonical field names."""
    _data: Dict[str, Any]
    
    def __post_init__(self):
        # Deep copy and freeze the data to ensure true immutability
        frozen_data = self._freeze_data(deepcopy(self._data))
        object.__setattr__(self, '_data', frozen_data)
    
    def _freeze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively freeze data structures to prevent modification."""
        if isinstance(data, dict):
            # Convert to a frozen dict-like structure
            frozen_dict = {}
            for key, value in data.items():
                frozen_dict[key] = self._freeze_data(value)
            return frozenset(frozen_dict.items())
        elif isinstance(data, list):
            return tuple(self._freeze_data(item) for item in data)
        elif isinstance(data, set):
            return frozenset(self._freeze_data(item) for item in data)
        else:
            return data
    
    def _unfreeze_data(self, frozen_data) -> Dict[str, Any]:
        """Convert frozen data back to regular dict for access."""
        if isinstance(frozen_data, frozenset):
            # This is a frozen dict
            return {key: self._unfreeze_data(value) for key, value in frozen_data}
        elif isinstance(frozen_data, tuple):
            return [self._unfreeze_data(item) for item in frozen_data]
        else:
            return frozen_data
    
    def get(self, field_name: str, default: Any = None) -> Any:
        """Get field value using canonical name."""
        data_dict = self._unfreeze_data(self._data)
        return data_dict.get(field_name, default)
    
    def __getitem__(self, field_name: str) -> Any:
        """Direct access to field values."""
        data_dict = self._unfreeze_data(self._data)
        return data_dict[field_name]
    
    def keys(self) -> Iterator[str]:
        """Get all available field names."""
        data_dict = self._unfreeze_data(self._data)
        return data_dict.keys()
    
    @property
    def data(self) -> Dict[str, Any]:
        """Read-only access to data (returns copy to prevent modification)."""
        return self._unfreeze_data(self._data)


@dataclass(frozen=True)
class CanonicalSection:
    """Immutable section containing records with canonical field names."""
    section_name: str
    records: List[CanonicalRecord]
    metadata: Dict[str, Any]
    
    def __iter__(self) -> Iterator[CanonicalRecord]:
        """Iterate over records in section."""
        return iter(self.records)
    
    def __len__(self) -> int:
        """Get number of records in section."""
        return len(self.records)
    
    def is_empty(self) -> bool:
        """Check if section has no records."""
        return len(self.records) == 0


class CanonicalDataInterface(ABC):
    """
    Abstract interface for accessing power system data in canonical format.
    
    This interface enforces architectural separation by:
    1. Providing only immutable data access
    2. Using canonical field names throughout
    3. Preventing direct backend data access
    4. Type-safe data retrieval methods
    """
    
    @abstractmethod
    def get_section(self, section_name: str) -> Optional[CanonicalSection]:
        """Get section data with canonical field names."""
        pass
    
    @abstractmethod
    def has_section(self, section_name: str) -> bool:
        """Check if section exists."""
        pass
    
    @abstractmethod
    def get_section_names(self) -> List[str]:
        """Get all available section names."""
        pass
    
    @abstractmethod
    def get_buses(self) -> CanonicalSection:
        """Get bus data with canonical field names."""
        pass
    
    @abstractmethod
    def get_generators(self) -> CanonicalSection:
        """Get generator data with canonical field names."""
        pass
    
    @abstractmethod
    def get_loads(self) -> CanonicalSection:
        """Get load data with canonical field names."""
        pass
    
    @abstractmethod
    def get_ac_lines(self) -> CanonicalSection:
        """Get AC line data with canonical field names."""
        pass
    
    @abstractmethod
    def get_transformers(self) -> CanonicalSection:
        """Get transformer data with canonical field names."""
        pass
    
    @abstractmethod
    def get_case_info(self) -> CanonicalRecord:
        """Get case identification data."""
        pass


class ReadOnlyCanonicalDataInterface(CanonicalDataInterface):
    """
    Read-only implementation of canonical data interface.
    
    This class provides immutable access to backend data through
    the canonical interface, ensuring architectural compliance.
    """
    
    def __init__(self, backend_data: Dict[str, Any], field_mapper=None):
        """
        Initialize with backend data and field mapper.
        
        Args:
            backend_data: Raw backend data (will be converted to canonical)
            field_mapper: Mapper to convert backend fields to canonical names
        """
        self._logger = logging.getLogger(self.__class__.__name__)
        self._field_mapper = field_mapper
        
        # Convert backend data to canonical format and make immutable
        self._canonical_data = self._convert_to_canonical(backend_data)
        
        # Freeze the data to prevent modification
        self._frozen = True
    
    def _convert_to_canonical(self, backend_data: Dict[str, Any]) -> Dict[str, CanonicalSection]:
        """Convert backend data to canonical format with immutable sections."""
        canonical_sections = {}
        
        for section_name, section_data in backend_data.items():
            if not isinstance(section_data, dict):
                continue
                
            fields = section_data.get('fields', [])
            raw_records = section_data.get('data', [])
            
            # Convert each record to canonical format
            canonical_records = []
            
            # Handle special case for caseid (flat structure)
            if section_name == 'caseid' and isinstance(raw_records, list) and raw_records and not isinstance(raw_records[0], list):
                # Caseid has flat structure: data is [ic, sbase, rev, ...]
                canonical_record_data = self._map_record_to_canonical(section_name, raw_records, fields)
                canonical_records.append(CanonicalRecord(_data=canonical_record_data))
            else:
                # Normal sections: data is [[record1], [record2], ...]
                for raw_record in raw_records:
                    if isinstance(raw_record, list):
                        canonical_record_data = self._map_record_to_canonical(section_name, raw_record, fields)
                        canonical_records.append(CanonicalRecord(_data=canonical_record_data))
            
            # Create immutable section
            metadata = {
                'original_fields': fields,
                'record_count': len(canonical_records)
            }
            
            canonical_sections[section_name] = CanonicalSection(
                section_name=section_name,
                records=canonical_records,
                metadata=metadata
            )
        
        return canonical_sections
    
    def _map_record_to_canonical(self, section_name: str, record: List[Any], fields: List[str]) -> Dict[str, Any]:
        """Map a single record to canonical field names."""
        # Use PureFieldMapper as single source of truth for field mapping
        if self._field_mapper:
            try:
                # PureFieldMapper handles all field transformations and canonical naming
                canonical_data = self._field_mapper.map_record(section_name, record, fields)
                return canonical_data
            except Exception as e:
                self._logger.warning(f"Field mapping failed for {section_name}: {e}")
        
        # Fallback: create basic mapping from raw field names to values
        canonical_data = {}
        for i, field_value in enumerate(record):
            if i < len(fields):
                canonical_data[fields[i]] = field_value
        
        return canonical_data
    
    def get_section(self, section_name: str) -> Optional[CanonicalSection]:
        """Get section data with canonical field names."""
        return self._canonical_data.get(section_name)
    
    def has_section(self, section_name: str) -> bool:
        """Check if section exists."""
        return section_name in self._canonical_data
    
    def get_section_names(self) -> List[str]:
        """Get all available section names."""
        return list(self._canonical_data.keys())
    
    def get_buses(self) -> CanonicalSection:
        """Get bus data with canonical field names."""
        section = self.get_section('bus')
        if section is None:
            return CanonicalSection('bus', [], {})
        return section
    
    def get_generators(self) -> CanonicalSection:
        """Get generator data with canonical field names."""
        section = self.get_section('generator')
        if section is None:
            return CanonicalSection('generator', [], {})
        return section
    
    def get_loads(self) -> CanonicalSection:
        """Get load data with canonical field names."""
        section = self.get_section('load')
        if section is None:
            return CanonicalSection('load', [], {})
        return section
    
    def get_ac_lines(self) -> CanonicalSection:
        """Get AC line data with canonical field names."""
        section = self.get_section('ac_line')
        if section is None:
            return CanonicalSection('ac_line', [], {})
        return section
    
    def get_transformers(self) -> CanonicalSection:
        """Get transformer data with canonical field names."""
        section = self.get_section('transformer')
        if section is None:
            return CanonicalSection('transformer', [], {})
        return section
    
    def get_case_info(self) -> CanonicalRecord:
        """Get case identification data."""
        section = self.get_section('caseid')
        if section is None or section.is_empty():
            # Return default case info
            default_data = {
                'ic': 0,
                'sbase': 100.0,
                'rev': 35,
                'xfrrat': 0,
                'nxfrat': 1,
                'basfrq': 60.0,
                'title1': 'DEFAULT CASE',
                'title2': 'EXPORTED CASE'
            }
            return CanonicalRecord(_data=default_data)
        
        return section.records[0]


class CanonicalDataInterfaceFactory:
    """Factory for creating canonical data interfaces."""
    
    @staticmethod
    def create_read_only_interface(backend_data: Dict[str, Any], field_mapper=None) -> CanonicalDataInterface:
        """Create a read-only canonical data interface."""
        return ReadOnlyCanonicalDataInterface(backend_data, field_mapper) 