# ✅ Markdown Formatting Rule (MD Compliance)

When editing or creating .md (Markdown) files:

**MD022 - Headings surrounded by blank lines:**

- Always insert a blank line before each heading (e.g., before #, ##, ###, etc.)
- Always insert a blank line after each heading
- Exception: No blank line needed before the first heading in a file

**MD031 & MD032 - Code blocks and lists surrounded by blank lines:**

- Always surround fenced code blocks with a blank line above and below
- Always surround lists (both ordered and unordered) with a blank line above and below
- This includes both ``` code blocks and list items starting with - or 1.

**MD040 - Fenced code blocks must specify language:**

- Always specify the language for syntax highlighting immediately after the opening triple backticks
- Use ```python,```bash,```json,```yaml,```javascript, etc.
- Never leave fenced code blocks blank (```language not just```)
- If no specific language, use ```text or```plaintext

**MD009 - No trailing spaces:**

- Remove all trailing whitespace at the end of lines
- Do not leave spaces or tabs after the final character on any line
- Exception: Two spaces at end of line for explicit line break (rare usage)

**MD036 - Use proper headings instead of emphasis:**

- Use # ## ### headings instead of **bold text** for section titles
- Use **bold** and *italic* only for emphasis within paragraphs, not as heading substitutes
- Structure documents with proper heading hierarchy

**File ending:**

- Always ensure there is exactly one newline at the end of the file
- Do not leave trailing spaces or multiple blank lines at the end
- The file must end with a clean newline character

**Example of correct formatting:**

```markdown

# Main Heading

This is a paragraph with some text.

## Sub Heading

Here's a list:

- Item 1
- Item 2
- Item 3

Here's some code:

```python

def hello_world():
    print("Hello, World!")

```text

Another paragraph here.

### Another Heading

More content.

```text

**Common violations to avoid:**

- ❌ No language: ```(should be```python)
- ❌ No spacing: ##Heading (should be ## Heading)
- ❌ Missing blanks: text\nlist item (should have blank line between)
- ❌ Trailing spaces: "text   " (should be "text")
- ❌ Bold as heading: **Section Title** (should be ## Section Title)
