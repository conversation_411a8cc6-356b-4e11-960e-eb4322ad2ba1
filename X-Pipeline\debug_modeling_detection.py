#!/usr/bin/env python3
"""Debug script to investigate modeling detection issue."""

from hdb_to_raw_pipeline import Backend, ModelTransformations

def debug_modeling_detection():
    """Debug modeling detection."""
    print("🔍 Debugging modeling detection...")
    
    backend = Backend.load('sample_nb.rawx')
    canonical_data = backend._backend.to_canonical()
    
    # Check model type detection
    model_type = ModelTransformations.detect_model_type(canonical_data)
    print(f"🏗️  Detected model type: {model_type}")
    
    # Check what sections exist
    print(f"\n📊 Available sections: {len(canonical_data)} total")
    for section in sorted(canonical_data.keys()):
        if isinstance(canonical_data[section], dict) and 'data' in canonical_data[section]:
            data_count = len(canonical_data[section]['data'])
            print(f"  {section}: {data_count} records")
    
    # Check specific sections for node-breaker detection
    print(f"\n🔧 Node-breaker specific sections:")
    node_breaker_sections = ['substation', 'node', 'switching_device', 'terminal', 'sub', 'subnode', 'subswd', 'subterm']
    
    for section in node_breaker_sections:
        if section in canonical_data:
            data_count = len(canonical_data[section].get('data', []))
            print(f"  ✅ {section}: {data_count} records")
        else:
            print(f"  ❌ {section}: NOT FOUND")
    
    # Check bus-branch sections
    print(f"\n🚌 Bus-branch specific sections:")
    bus_branch_sections = ['bus', 'ac_line', 'transformer']
    
    for section in bus_branch_sections:
        if section in canonical_data:
            data_count = len(canonical_data[section].get('data', []))
            print(f"  ✅ {section}: {data_count} records")
        else:
            print(f"  ❌ {section}: NOT FOUND")

if __name__ == "__main__":
    debug_modeling_detection() 