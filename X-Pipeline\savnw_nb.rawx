{"network": {"caseid": {"fields": ["ic", "sbase", "rev", "xfrrat", "nxfrat", "basfrq", "title1", "title2"], "data": [0, 100.0, 35, 0, 1, 60.0, "PSS(R)E PROGRAM APPLICATION GUIDE EXAMPLE", "BASE CASE INCLUDING SEQUENCE DATA"]}, "general": {"fields": ["thrshz", "pqbrak", "blowup", "maxisollvls", "camaxreptsln", "chkdupcntlbl"], "data": [0.0001, 0.7, 5.0, 4, 20, 0]}, "gauss": {"fields": ["itmx", "accp", "accq", "accm", "tol"], "data": [100, 1.6, 1.6, 1.0, 0.0001]}, "newton": {"fields": ["itmxn", "accn", "toln", "vctolq", "vctolv", "dvlim", "ndvfct"], "data": [100, 1.0, 0.1, 0.1, 1e-05, 0.99, 0.99]}, "adjust": {"fields": ["adjthr", "acctap", "taplim", "swvbnd", "mxtpss", "mxswim"], "data": [0.005, 1.0, 0.05, 100.0, 99, 10]}, "tysl": {"fields": ["itmxty", "accty", "tolty"], "data": [20, 1.0, 1e-05]}, "solver": {"fields": ["method", "actaps", "areain", "phshft", "dctaps", "swshnt", "flatst", "varlim", "nondiv"], "data": ["FDNS", 1, 0, 0, 1, 1, 0, 99, 0]}, "rating": {"fields": ["irate", "name", "desc"], "data": [[1, "CRCUS1", "Circus-Fibrous or threadlike    "], [2, "CRSTR2", "Cirrostrarus-Milky, translucent "], [3, "CRCUM3", "Cirrocumulus-small, white flakes"], [4, "ALTCU4", "Altocumulus-bundles or rollers  "], [5, "ALTST5", "Altostratus-dense, gray layer   "], [6, "STRTO6", "Stratocumulus-plaices or rollers"], [7, "STRTS7", "Stratus-Evenly grey, low layer  "], [8, "CMULS8", "Cumulus-Heap with flat basis    "], [9, "CMULO9", "Cumulonimbus-thunder, up-rises  "], [10, "NIBS10", "Nimbostratus-rain,grey, dark    "], [11, "CPLL11", "capillatus-haired, frayed       "], [12, "NBUL12", "nebulosus-fog, veil-like        "]]}, "bus": {"fields": ["ibus", "name", "baskv", "ide", "area", "zone", "owner", "vm", "va", "nvhi", "nvlo", "evhi", "evlo"], "data": [[101, "NUC-A       ", 21.6, 2, 1, 77, 11, 1.02, 14.876, 1.1, 0.9, 1.1, 0.9], [102, "NUC-B       ", 21.6, 2, 1, 77, 11, 1.02, 23.6767, 1.1, 0.9, 1.1, 0.9], [151, "NUCPANT     ", 500.0, 1, 1, 1, 1, 1.02468, 9.273, 1.1, 0.9, 1.1, 0.9], [152, "MID500      ", 500.0, 1, 1, 1, 1, 0.96664, -0.5216, 1.1, 0.9, 1.1, 0.9], [153, "MID230      ", 230.0, 1, 1, 1, 1, 0.98743, -2.6915, 1.1, 0.9, 1.1, 0.9], [154, "DOWNTN      ", 230.0, 1, 1, 1, 1, 0.78006, -17.1784, 1.1, 0.9, 1.1, 0.9], [201, "HYDRO       ", 500.0, 1, 2, 2, 22, 1.03545, 11.4041, 1.1, 0.9, 1.1, 0.9], [202, "EAST500     ", 500.0, 1, 2, 2, 2, 0.96741, 0.727, 1.1, 0.9, 1.1, 0.9], [203, "EAST230     ", 230.0, 1, 2, 2, 2, 0.92293, -4.9441, 1.1, 0.9, 1.1, 0.9], [204, "SUB500      ", 500.0, 1, 2, 2, 2, 0.97055, 1.928, 1.1, 0.9, 1.1, 0.9], [205, "SUB230      ", 230.0, 1, 2, 2, 2, 0.98, -2.9132, 1.1, 0.9, 1.1, 0.9], [206, "URBGEN      ", 18.0, 2, 2, 2, 22, 1.04718, 2.9708, 1.1, 0.9, 1.1, 0.9], [211, "HYDRO_G     ", 20.0, 2, 2, 2, 22, 1.10971, 17.6343, 1.1, 0.9, 1.1, 0.9], [3001, "MINE        ", 230.0, 1, 5, 5, 55, 1.01335, -1.6279, 1.1, 0.9, 1.1, 0.9], [3002, "E. MINE     ", 500.0, 1, 5, 5, 5, 1.00407, -2.1444, 1.1, 0.9, 1.1, 0.9], [3003, "S. MINE     ", 230.0, 1, 5, 5, 5, 0.9979, -2.7258, 1.1, 0.9, 1.1, 0.9], [3004, "WEST        ", 500.0, 1, 5, 5, 5, 0.96577, -3.9033, 1.1, 0.9, 1.1, 0.9], [3005, "WEST        ", 230.0, 1, 5, 5, 5, 0.93858, -6.363, 1.1, 0.9, 1.1, 0.9], [3006, "UPTOWN      ", 230.0, 1, 5, 5, 5, 0.97394, -3.722, 1.1, 0.9, 1.1, 0.9], [3007, "RURAL       ", 230.0, 1, 5, 5, 5, 0.87535, -11.4746, 1.1, 0.9, 1.1, 0.9], [3008, "CATDOG      ", 230.0, 1, 5, 5, 55, 0.84332, -13.4396, 1.1, 0.9, 1.1, 0.9], [3011, "MINE_G      ", 13.8, 3, 5, 5, 55, 1.04, 0.0, 1.1, 0.9, 1.1, 0.9], [3018, "CATDOG_G    ", 13.8, 2, 5, 5, 55, 0.9129, -7.1134, 1.1, 0.9, 1.1, 0.9]]}, "load": {"fields": ["ibus", "loadid", "stat", "area", "zone", "pl", "ql", "ip", "iq", "yp", "yq", "owner", "scale", "intrpt", "dgenp", "dgenq", "dgenm", "loadtype"], "data": [[153, "1 ", 1, 1, 1, 200.0, 100.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0.0, 0.0, 0, "            "], [154, "1 ", 1, 2, 1, 600.0, 450.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0.0, 0.0, 0, "            "], [154, "2 ", 1, 2, 1, 400.0, 350.0, 0.0, 0.0, 0.0, 0.0, 100, 1, 0, 0.0, 0.0, 0, "            "], [203, "1 ", 1, 2, 2, 300.0, 150.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [205, "1 ", 1, 2, 2, 1200.0, 700.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [3005, "1 ", 1, 5, 5, 100.0, 50.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3007, "1 ", 1, 5, 5, 200.0, 75.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3008, "1 ", 1, 5, 5, 200.0, 75.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "]]}, "fixshunt": {"fields": ["ibus", "s<PERSON><PERSON><PERSON>", "stat", "gl", "bl"], "data": [[151, "1 ", 1, 0.0, -600.0], [154, "1 ", 1, 0.0, 300.0], [201, "1 ", 1, 0.0, 300.0], [203, "1 ", 1, 0.0, 50.0], [205, "1 ", 1, 0.0, 300.0]]}, "generator": {"fields": ["ibus", "machid", "pg", "qg", "qt", "qb", "vs", "ireg", "nreg", "mbase", "zr", "zx", "rt", "xt", "gtap", "stat", "rmpct", "pt", "pb", "baslod", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "wmod", "wpf"], "data": [[101, "1 ", 750.0, -14.891, 600.0, -100.0, 1.02, 101, 0, 900.0, 0.01, 0.3, 0.0, 0.0, 1.0, 1, 100.0, 810.0, 0.0, 0, 11, 0.6667, 1, 0.3333, null, null, null, null, null, null], [102, "1 ", 750.0, 276.658, 600.0, -100.0, 1.02, 102, 0, 900.0, 0.01, 0.3, 0.0, 0.0, 1.0, 1, 100.0, 810.0, 0.0, 0, 11, 0.6667, 1, 0.3333, null, null, null, null, null, null], [206, "1 ", 800.0, 552.747, 600.0, 0.0, 0.98, 205, 0, 1000.0, 0.01, 0.25, 0.0, 0.0, 1.0, 1, 100.0, 900.0, 0.0, 0, 2, 0.4, 22, 0.6, null, null, null, null, null, null], [211, "1 ", 600.0, 400.0, 400.0, -100.0, 1.04, 201, 0, 725.0, 0.01, 0.26, 0.0, 0.0, 1.0, 1, 100.0, 616.25, 0.0, 0, 2, 0.4, 22, 0.6, null, null, null, null, null, null], [3011, "1 ", 304.899, 275.314, 600.0, -100.0, 1.04, 3011, 0, 1000.0, 0.01, 0.35, 0.0, 0.0, 1.0, 1, 100.0, 900.0, 0.0, 0, 55, 0.3846, 5, 0.3077, 22, 0.2308, 11, 0.0769, null, null], [3018, "1 ", 100.0, 80.0, 80.0, 0.0, 1.02, 3008, 0, 130.0, 0.01, 0.35, 0.0, 0.0, 1.0, 1, 100.0, 117.0, 0.0, 0, 55, 0.5556, 5, 0.4444, null, null, null, null, null, null]]}, "acline": {"fields": ["ibus", "jbus", "ckt", "rpu", "xpu", "bpu", "name", "rate1", "rate2", "rate3", "rate4", "rate5", "rate6", "rate7", "rate8", "rate9", "rate10", "rate11", "rate12", "gi", "bi", "gj", "bj", "stat", "met", "len", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4"], "data": [[151, 201, "1 ", 0.001, 0.015, 1.2, "BRANCH_FROM__151_TO__201___CIRCUIT_ID__1", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [151, 152, "1 ", 0.0026, 0.046, 3.5, "BRANCH_FROM__151_TO__152___CIRCUIT_ID__1", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [151, 152, "2 ", 0.0026, 0.046, 3.5, "BRANCH_FROM__151_TO__152___CIRCUIT_ID__2", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [152, 202, "1 ", 0.0008, 0.01, 0.95, "BRANCH_FROM__152_TO__202___CIRCUIT_ID__1", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 0.0, 1, 1.0, null, null, null, null, null, null], [152, 3004, "1 ", 0.003, 0.03, 2.5, "BRANCH_FROM__152_TO__3004__CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [153, 154, "1 ", 0.005, 0.045, 0.1, "BRANCH_FROM__153_TO__154___CIRCUIT_ID__1", 300.0, 350.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 0.75, 100, 0.25, null, null, null, null], [153, 154, "2 ", 0.006, 0.054, 0.15, "BRANCH_FROM__153_TO__154___CIRCUIT_ID__2", 300.0, 350.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [153, 3006, "1 ", 0.001, 0.012, 0.03, "BRANCH_FROM__153_TO__3006 _CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [154, 205, "1 ", 0.00033, 0.00333, 0.09, "BRANCH_FROM__154_TO__205___CIRCUIT_ID__1", 600.0, 660.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [154, 203, "1 ", 0.004, 0.04, 0.1, "BRANCH_FROM__154_TO__203___CIRCUIT_ID__1", 200.0, 250.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [154, 3008, "1 ", 0.0027, 0.022, 0.3, "BRANCH_FROM__154_TO__3008__CIRCUIT_ID__1", 400.0, 440.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 1, 1.0, null, null, null, null, null, null], [201, 202, "1 ", 0.002, 0.025, 2.0, "BRANCH_FROM__201_TO__202___CIRCUIT_ID__1", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 22, 1.0, null, null, null, null, null, null], [201, 204, "1 ", 0.003, 0.03, 2.5, "BRANCH_FROM__201_TO__204___CIRCUIT_ID__1", 1200.0, 1300.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 22, 1.0, null, null, null, null, null, null], [203, 205, "1 ", 0.005, 0.045, 0.08, "BRANCH_FROM__203_TO__205___CIRCUIT_ID__1", 200.0, 250.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 0.0, 2, 1.0, null, null, null, null, null, null], [203, 205, "2 ", 0.005, 0.045, 0.08, "BRANCH_FROM__203_TO__205___CIRCUIT_ID__2", 200.0, 250.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 0.0, 2, 1.0, null, null, null, null, null, null], [3001, 3003, "1 ", -0.0, 0.008, 0.0, "BRANCH_FROM__3001_TO__3003_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 55, 1.0, null, null, null, null, null, null], [3002, 3004, "1 ", 0.006, 0.054, 0.09, "BRANCH_FROM__3002_TO__3004_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3003, 3005, "1 ", 0.006, 0.054, 0.09, "BRANCH_FROM__3003_TO__3005_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3003, 3005, "2 ", 0.006, 0.054, 0.09, "BRANCH_FROM__3003_TO__3005_CIRCUIT_ID__2", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3005, 3006, "1 ", 0.0035, 0.03, 0.07, "BRANCH_FROM__3005_TO__3006_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3005, 3007, "1 ", 0.003, 0.025, 0.06, "BRANCH_FROM__3005_TO__3007_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3005, 3008, "1 ", 0.006, 0.05, 0.12, "BRANCH_FROM__3005_TO__3008_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null], [3007, 3008, "1 ", 0.003, 0.025, 0.06, "BRANCH_FROM__3007_TO__3008_CIRCUIT_ID__1", 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0.0, 5, 1.0, null, null, null, null, null, null]]}, "sysswd": {"fields": ["ibus", "jbus", "ckt", "xpu", "rate1", "rate2", "rate3", "rate4", "rate5", "rate6", "rate7", "rate8", "rate9", "rate10", "rate11", "rate12", "stat", "nstat", "met", "stype", "name"], "data": []}, "transformer": {"fields": ["ibus", "jbus", "kbus", "ckt", "cw", "cz", "cm", "mag1", "mag2", "nmet", "name", "stat", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "vecgrp", "zcod", "r1_2", "x1_2", "sbase1_2", "r2_3", "x2_3", "sbase2_3", "r3_1", "x3_1", "sbase3_1", "vmstar", "anstar", "windv1", "nomv1", "ang1", "wdg1rate1", "wdg1rate2", "wdg1rate3", "wdg1rate4", "wdg1rate5", "wdg1rate6", "wdg1rate7", "wdg1rate8", "wdg1rate9", "wdg1rate10", "wdg1rate11", "wdg1rate12", "cod1", "cont1", "node1", "rma1", "rmi1", "vma1", "vmi1", "ntp1", "tab1", "cr1", "cx1", "cnxa1", "windv2", "nomv2", "ang2", "wdg2rate1", "wdg2rate2", "wdg2rate3", "wdg2rate4", "wdg2rate5", "wdg2rate6", "wdg2rate7", "wdg2rate8", "wdg2rate9", "wdg2rate10", "wdg2rate11", "wdg2rate12", "cod2", "cont2", "node2", "rma2", "rmi2", "vma2", "vmi2", "ntp2", "tab2", "cr2", "cx2", "cnxa2", "windv3", "nomv3", "ang3", "wdg3rate1", "wdg3rate2", "wdg3rate3", "wdg3rate4", "wdg3rate5", "wdg3rate6", "wdg3rate7", "wdg3rate8", "wdg3rate9", "wdg3rate10", "wdg3rate11", "wdg3rate12", "cod3", "cont3", "node3", "rma3", "rmi3", "vma3", "vmi3", "ntp3", "tab3", "cr3", "cx3", "cnxa3"], "data": [[151, 101, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___151_TO___101_CIRCUIT_ID__1", 1, 1, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0003, 0.0136, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 1250.0, 1350.0, 1750.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [151, 102, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___151_TO___102_CIRCUIT_ID__1", 1, 1, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0003, 0.0136, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 1250.0, 1350.0, 1750.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [152, 153, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___152_TO___103_CIRCUIT_ID__1", 1, 1, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, -0.0, 0.005, 100.0, null, null, null, null, null, null, null, null, 0.95, 0.0, 0.0, 2500.0, 3000.0, 3500.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 154, 0, 1.05, 0.95, 1.0, 0.98, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [201, 211, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___201_TO___211_CIRCUIT_ID__1", 1, 22, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0007, 0.02125, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 800.0, 1000.0, 1120.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 201, 0, 1.1, 0.9, 1.05, 0.95, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [202, 203, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___202_TO___203_CIRCUIT_ID__1", 1, 2, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0004, 0.01625, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 800.0, 1040.0, 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3, 0, 0, 30.0, -30.0, 555.0, 545.0, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [204, 205, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___204_TO___205_CIRCUIT_ID__1", 1, 2, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0003, 0.015, 100.0, null, null, null, null, null, null, null, null, 0.95938, 0.0, 0.0, 800.0, 1040.0, 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 205, 0, 1.05, 0.95, 1.0, 0.98, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [205, 206, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM___205_TO___206_CIRCUIT_ID__1", 1, 2, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.00026, 0.01333, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 900.0, 1080.0, 1350.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3001, 3002, 0, "1 ", 1, 1, 1, 0.0, 0.0, 1, "TWOWDG_FROM__3001_TO__3002_CIRCUIT_ID__1", 1, 55, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0003, 0.015, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 800.0, 1040.0, 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.05, 0.95, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3001, 3011, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM__3001_TO__3011_CIRCUIT_ID__1", 1, 55, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0002, 0.01, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 1300.0, 1560.0, 1820.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.05, 0.95, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3004, 3005, 0, "1 ", 1, 1, 1, 0.0, 0.0, 1, "TWOWDG_FROM__3004_TO__3005_CIRCUIT_ID__1", 1, 5, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.0004, 0.01625, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 800.0, 1040.0, 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.05, 0.95, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3008, 3018, 0, "1 ", 1, 1, 1, 0.0, 0.0, 2, "TWOWDG_FROM__3008_TO__3018_CIRCUIT_ID__1", 1, 55, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "            ", null, 0.00021, 0.085, 100.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 150.0, 200.0, 250.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.05, 0.95, 5, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]]}, "area": {"fields": ["iarea", "isw", "pdes", "ptol", "arna<PERSON>"], "data": [[1, 101, 250.0, 10.0, "FLAPCO      "], [2, 206, -100.0, 10.0, "LIGHTCO     "], [5, 3011, -150.0, 10.0, "WORLD       "]]}, "twotermdc": {"fields": ["name", "mdc", "rdc", "setvl", "vschd", "vcmod", "rcomp", "<PERSON><PERSON>", "met", "dcvmin", "cccitmx", "cccacc", "ipr", "nbr", "anmxr", "anmnr", "rcr", "xcr", "ebasr", "trr", "tapr", "tmxr", "tmnr", "stpr", "icr", "ndr", "ifr", "itr", "idr", "xcapr", "ipi", "nbi", "anmxi", "anmni", "rci", "xci", "ebasi", "tri", "tapi", "tmxi", "tmni", "stpi", "ici", "ndi", "ifi", "iti", "idi", "xcapi"], "data": []}, "vscdc": {"fields": ["name", "mdc", "rdc", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "ibus1", "type1", "mode1", "dcset1", "acset1", "aloss1", "bloss1", "minloss1", "smax1", "imax1", "pwf1", "maxq1", "minq1", "vsreg1", "nreg1", "rmpct1", "ibus2", "type2", "mode2", "dcset2", "acset2", "aloss2", "bloss2", "minloss2", "smax2", "imax2", "pwf2", "maxq2", "minq2", "vsreg2", "nreg2", "rmpct2"], "data": []}, "impcor": {"fields": ["itable", "tap", "refact", "imfact"], "data": []}, "ntermdc": {"fields": ["name", "nconv", "ndcbs", "ndcln", "mdc", "vconv", "vcmod", "vconvn"], "data": []}, "ntermdcconv": {"fields": ["name", "ib", "nbrdg", "angmx", "angmn", "rc", "xc", "ebas", "tr", "tap", "tpmx", "tpmn", "tstp", "setvl", "dcpf", "marg", "cnvcod"], "data": []}, "ntermdcbus": {"fields": ["name", "idc", "ib", "area", "zone", "dcname", "idc2", "rgrnd", "owner"], "data": []}, "ntermdclink": {"fields": ["name", "idc", "jdc", "dcckt", "met", "rdc", "ldc"], "data": []}, "msline": {"fields": ["ibus", "jbus", "mslid", "met", "dum1", "dum2", "dum3", "dum4", "dum5", "dum6", "dum7", "dum8", "dum9"], "data": []}, "zone": {"fields": ["izone", "zoname"], "data": [[1, "FIRST       "], [2, "SECOND      "], [5, "FIFTH       "], [77, "PLANT       "]]}, "iatrans": {"fields": ["arfrom", "arto", "trid", "ptran"], "data": [[1, 2, "A", 70.0], [1, 2, "B", 30.0], [1, 5, "A", 100.0], [1, 5, "B", 50.0]]}, "owner": {"fields": ["iowner", "owname"], "data": [[1, "TRAN 1      "], [2, "TRAN 2      "], [5, "TRAN 5      "], [11, "GEN 1       "], [22, "GEN 2       "], [55, "GEN 5       "], [100, "NO BUSES    "]]}, "facts": {"fields": ["name", "ibus", "jbus", "mode", "pdes", "qdes", "vset", "shmx", "trmx", "vtmn", "vtmx", "vsmx", "imx", "linx", "rmpct", "owner", "set1", "set2", "vsref", "fcreg", "nreg", "mname"], "data": []}, "swshunt": {"fields": ["ibus", "s<PERSON><PERSON><PERSON>", "modsw", "adjm", "stat", "vswhi", "vswlo", "swreg", "nreg", "rmpct", "rmidnt", "binit", "s1", "n1", "b1", "s2", "n2", "b2", "s3", "n3", "b3", "s4", "n4", "b4", "s5", "n5", "b5", "s6", "n6", "b6", "s7", "n7", "b7", "s8", "n8", "b8"], "data": []}, "gne": {"fields": ["name", "model", "nterm", "bus1", "bus2", "nreal", "nintg", "nchar", "stat", "owner", "nmet", "real1", "real2", "real3", "real4", "real5", "real6", "real7", "real8", "real9", "real10", "intg1", "intg2", "intg3", "intg4", "intg5", "intg6", "intg7", "intg8", "intg9", "intg10", "char1", "char2", "char3", "char4", "char5", "char6", "char7", "char8", "char9", "char10"], "data": []}, "indmach": {"fields": ["ibus", "imid", "stat", "sc", "dc", "area", "zone", "owner", "tc", "bc", "mbase", "ratekv", "pcode", "pset", "hconst", "aconst", "bconst", "dconst", "econst", "ra", "xa", "xm", "r1", "x1", "r2", "x2", "x3", "e1", "se1", "e2", "se2", "ia1", "ia2", "xamult"], "data": []}, "sub": {"fields": ["isub", "name", "lati", "long", "srg"], "data": [[1, "SUBSTATION_NILE_TYPE_3_DBDB             ", 33.6134987, -87.373703, 0.21], [2, "SUBSTATION_YANGTZE_TYPE_6_MBTB          ", 34.3103981, -86.365799, 0.22], [3, "SUBSTATION_ARKANSAS_TYPE_4_BH           ", 33.955101, -84.6793976, 0.23], [4, "SUBSTATION_COLORADO_TYPE_5_DBSB         ", 33.5479012, -86.0746002, 1.0], [5, "SUBSTATION_MISSISSIPPI_TYPE_5_DBSB      ", 32.705101, -84.6633987, 0.11], [6, "SUBSTATION_VOLGA_TYPE_1_SB              ", 33.3773003, -82.6187973, 0.12], [8, "SUBSTATION_BRAHMAPUTRA_TYPE_4_BH        ", 34.2522011, -82.8363037, 0.13], [9, "SUBSTATION_INDUS_TYPE_6_MBTB            ", 34.1955986, -81.0979996, 0.14], [10, "SUBSTATION_DANUBE_TYPE_4_BH             ", 33.0122986, -80.0122986, 0.24], [17, "SUBSTATION_ZAMBEZI_TYPE_3_DBDB          ", 33.0203018, -80.0203018, 0.19], [18, "SUBSTATION_PILCOMAYO_TYPE_2_RB          ", 33.0213013, -80.0213013, 0.195], [19, "SUBSTATION_TIGRIS_TYPE_1_SB             ", 33.0223007, -80.0223007, 0.2]]}, "subnode": {"fields": ["isub", "inode", "name", "ibus", "stat", "vm", "va"], "data": [[1, 1, "SS_NILE_NODE_1                          ", 101, 1, null, null], [1, 2, "SS_NILE_NODE_2                          ", 101, 1, null, null], [1, 3, "SS_NILE_NODE_3                          ", 101, 1, null, null], [1, 4, "SS_NILE_NODE_4                          ", 101, 1, null, null], [1, 5, "SS_NILE_NODE_5                          ", 102, 1, null, null], [1, 6, "SS_NILE_NODE_6                          ", 102, 1, null, null], [1, 7, "SS_NILE_NODE_7                          ", 102, 1, null, null], [1, 8, "SS_NILE_NODE_8                          ", 102, 1, null, null], [1, 9, "SS_NILE_NODE_9                          ", 151, 1, 1.02468, 9.273], [1, 10, "SS_NILE_NODE_10                         ", 151, 1, 1.02468, 9.273], [1, 11, "SS_NILE_NODE_11                         ", 151, 1, 1.02468, 9.273], [1, 12, "SS_NILE_NODE_12                         ", 151, 1, 1.02468, 9.273], [1, 13, "SS_NILE_NODE_13                         ", 151, 1, 1.02468, 9.273], [1, 14, "SS_NILE_NODE_14                         ", 151, 1, 1.02468, 9.273], [1, 15, "SS_NILE_NODE_15                         ", 151, 1, 1.02468, 9.273], [1, 16, "SS_NILE_NODE_16                         ", 151, 1, 1.02468, 9.273], [2, 1, "SS_YANGTZE_NODE_1                       ", 152, 1, null, null], [2, 2, "SS_YANGTZE_NODE_2                       ", 152, 0, null, null], [2, 3, "SS_YANGTZE_NODE_3                       ", 152, 1, null, null], [2, 4, "SS_YANGTZE_NODE_4                       ", 152, 1, null, null], [2, 5, "SS_YANGTZE_NODE_5                       ", 152, 1, null, null], [2, 6, "SS_YANGTZE_NODE_6                       ", 152, 1, null, null], [2, 7, "SS_YANGTZE_NODE_7                       ", 152, 1, null, null], [2, 8, "SS_YANGTZE_NODE_8                       ", 153, 1, null, null], [2, 9, "SS_YANGTZE_NODE_9                       ", 153, 0, null, null], [2, 10, "SS_YANGTZE_NODE_10                      ", 153, 1, null, null], [2, 11, "SS_YANGTZE_NODE_11                      ", 153, 1, null, null], [2, 12, "SS_YANGTZE_NODE_12                      ", 153, 1, null, null], [2, 13, "SS_YANGTZE_NODE_13                      ", 153, 1, null, null], [2, 14, "SS_YANGTZE_NODE_14                      ", 153, 1, null, null], [3, 1, "SS_ARKANSAS_NODE_1                      ", 154, 1, 0.78006, -17.1784], [3, 2, "SS_ARKANSAS_NODE_2                      ", 154, 1, 0.78006, -17.1784], [3, 3, "SS_ARKANSAS_NODE_3                      ", 154, 1, 0.78006, -17.1784], [3, 4, "SS_ARKANSAS_NODE_4                      ", 154, 1, 0.78006, -17.1784], [3, 5, "SS_ARKANSAS_NODE_5                      ", 154, 1, 0.78006, -17.1784], [3, 6, "SS_ARKANSAS_NODE_6                      ", 154, 1, 0.78006, -17.1784], [3, 7, "SS_ARKANSAS_NODE_7                      ", 154, 1, 0.78006, -17.1784], [3, 8, "SS_ARKANSAS_NODE_8                      ", 154, 1, 0.78006, -17.1784], [3, 9, "SS_ARKANSAS_NODE_9                      ", 154, 1, 0.78006, -17.1784], [3, 10, "SS_ARKANSAS_NODE_10                     ", 154, 1, 0.78006, -17.1784], [4, 1, "SS_COLORADO_NODE_1                      ", 201, 1, null, null], [4, 2, "SS_COLORADO_NODE_2                      ", 201, 1, null, null], [4, 3, "SS_COLORADO_NODE_3                      ", 201, 1, null, null], [4, 4, "SS_COLORADO_NODE_4                      ", 201, 1, null, null], [4, 5, "SS_COLORADO_NODE_5                      ", 201, 1, null, null], [4, 6, "SS_COLORADO_NODE_6                      ", 201, 1, null, null], [4, 7, "SS_COLORADO_NODE_7                      ", 201, 1, null, null], [4, 8, "SS_COLORADO_NODE_8                      ", 201, 1, null, null], [4, 9, "SS_COLORADO_NODE_9                      ", 201, 1, null, null], [4, 10, "SS_COLORADO_NODE_10                     ", 201, 1, null, null], [4, 11, "SS_COLORADO_NODE_11                     ", 201, 1, null, null], [4, 12, "SS_COLORADO_NODE_12                     ", 201, 1, null, null], [4, 13, "SS_COLORADO_NODE_13                     ", 211, 1, null, null], [4, 14, "SS_COLORADO_NODE_14                     ", 211, 1, null, null], [4, 15, "SS_COLORADO_NODE_15                     ", 211, 1, null, null], [4, 16, "SS_COLORADO_NODE_16                     ", 211, 1, null, null], [4, 17, "SS_COLORADO_NODE_17                     ", 211, 1, null, null], [4, 18, "SS_COLORADO_NODE_18                     ", 211, 1, null, null], [5, 1, "SS_MISSISSIPPI_NODE_1                   ", 202, 1, null, null], [5, 2, "SS_MISSISSIPPI_NODE_2                   ", 202, 1, null, null], [5, 3, "SS_MISSISSIPPI_NODE_3                   ", 202, 1, null, null], [5, 4, "SS_MISSISSIPPI_NODE_4                   ", 202, 1, null, null], [5, 5, "SS_MISSISSIPPI_NODE_5                   ", 202, 1, null, null], [5, 6, "SS_MISSISSIPPI_NODE_6                   ", 202, 1, null, null], [5, 7, "SS_MISSISSIPPI_NODE_7                   ", 202, 1, null, null], [5, 8, "SS_MISSISSIPPI_NODE_8                   ", 202, 1, null, null], [5, 9, "SS_MISSISSIPPI_NODE_9                   ", 203, 1, null, null], [5, 10, "SS_MISSISSIPPI_NODE_10                  ", 203, 1, null, null], [5, 11, "SS_MISSISSIPPI_NODE_11                  ", 203, 1, null, null], [5, 12, "SS_MISSISSIPPI_NODE_12                  ", 203, 1, null, null], [5, 13, "SS_MISSISSIPPI_NODE_13                  ", 203, 1, null, null], [5, 14, "SS_MISSISSIPPI_NODE_14                  ", 203, 1, null, null], [5, 15, "SS_MISSISSIPPI_NODE_15                  ", 203, 1, null, null], [5, 16, "SS_MISSISSIPPI_NODE_16                  ", 203, 1, null, null], [5, 17, "SS_MISSISSIPPI_NODE_17                  ", 203, 1, null, null], [5, 18, "SS_MISSISSIPPI_NODE_18                  ", 203, 1, null, null], [5, 19, "SS_MISSISSIPPI_NODE_19                  ", 203, 1, null, null], [5, 20, "SS_MISSISSIPPI_NODE_20                  ", 203, 1, null, null], [5, 21, "SS_MISSISSIPPI_NODE_21                  ", 203, 1, null, null], [5, 22, "SS_MISSISSIPPI_NODE_22                  ", 203, 1, null, null], [6, 1, "SS_VOLGA_NODE_1                         ", 204, 1, null, null], [6, 2, "SS_VOLGA_NODE_2                         ", 204, 1, null, null], [6, 3, "SS_VOLGA_NODE_3                         ", 204, 1, null, null], [6, 4, "SS_VOLGA_NODE_4                         ", 205, 1, null, null], [6, 5, "SS_VOLGA_NODE_5                         ", 205, 1, null, null], [6, 6, "SS_VOLGA_NODE_6                         ", 205, 1, null, null], [6, 7, "SS_VOLGA_NODE_7                         ", 205, 1, null, null], [6, 8, "SS_VOLGA_NODE_8                         ", 205, 1, null, null], [6, 9, "SS_VOLGA_NODE_9                         ", 205, 1, null, null], [6, 10, "SS_VOLGA_NODE_10                        ", 205, 1, null, null], [6, 11, "SS_VOLGA_NODE_11                        ", 205, 1, null, null], [6, 12, "SS_VOLGA_NODE_12                        ", 206, 1, null, null], [6, 13, "SS_VOLGA_NODE_13                        ", 206, 1, null, null], [6, 14, "SS_VOLGA_NODE_14                        ", 206, 1, null, null], [8, 1, "SS_BRAHMAPUTRA_NODE_1                   ", 3001, 1, null, null], [8, 2, "SS_BRAHMAPUTRA_NODE_2                   ", 3001, 1, null, null], [8, 3, "SS_BRAHMAPUTRA_NODE_3                   ", 3001, 1, null, null], [8, 4, "SS_BRAHMAPUTRA_NODE_4                   ", 3001, 1, null, null], [8, 5, "SS_BRAHMAPUTRA_NODE_5                   ", 3001, 1, null, null], [8, 6, "SS_BRAHMAPUTRA_NODE_6                   ", 3001, 1, null, null], [8, 7, "SS_BRAHMAPUTRA_NODE_7                   ", 3002, 1, null, null], [8, 8, "SS_BRAHMAPUTRA_NODE_8                   ", 3002, 1, null, null], [8, 9, "SS_BRAHMAPUTRA_NODE_9                   ", 3002, 1, null, null], [8, 10, "SS_BRAHMAPUTRA_NODE_10                  ", 3002, 1, null, null], [8, 11, "SS_BRAHMAPUTRA_NODE_11                  ", 3011, 1, null, null], [8, 12, "SS_BRAHMAPUTRA_NODE_12                  ", 3011, 1, null, null], [8, 13, "SS_BRAHMAPUTRA_NODE_13                  ", 3011, 1, null, null], [8, 14, "SS_BRAHMAPUTRA_NODE_14                  ", 3011, 1, null, null], [9, 1, "SS_INDUS_NODE_1                         ", 3004, 1, null, null], [9, 2, "SS_INDUS_NODE_2                         ", 3004, 0, null, null], [9, 3, "SS_INDUS_NODE_3                         ", 3004, 1, null, null], [9, 4, "SS_INDUS_NODE_4                         ", 3004, 1, null, null], [9, 5, "SS_INDUS_NODE_5                         ", 3004, 1, null, null], [9, 6, "SS_INDUS_NODE_6                         ", 3005, 1, null, null], [9, 7, "SS_INDUS_NODE_7                         ", 3005, 0, null, null], [9, 8, "SS_INDUS_NODE_8                         ", 3005, 1, null, null], [9, 9, "SS_INDUS_NODE_9                         ", 3005, 1, null, null], [9, 10, "SS_INDUS_NODE_10                        ", 3005, 1, null, null], [9, 11, "SS_INDUS_NODE_11                        ", 3005, 1, null, null], [9, 12, "SS_INDUS_NODE_12                        ", 3005, 1, null, null], [9, 13, "SS_INDUS_NODE_13                        ", 3005, 1, null, null], [9, 14, "SS_INDUS_NODE_14                        ", 3005, 1, null, null], [10, 1, "SS_DANUBE_NODE_1                        ", 3008, 1, null, null], [10, 2, "SS_DANUBE_NODE_2                        ", 3008, 1, null, null], [10, 3, "SS_DANUBE_NODE_3                        ", 3008, 1, null, null], [10, 4, "SS_DANUBE_NODE_4                        ", 3008, 1, null, null], [10, 5, "SS_DANUBE_NODE_5                        ", 3008, 1, null, null], [10, 6, "SS_DANUBE_NODE_6                        ", 3008, 1, null, null], [10, 7, "SS_DANUBE_NODE_7                        ", 3008, 1, null, null], [10, 8, "SS_DANUBE_NODE_8                        ", 3008, 1, null, null], [10, 9, "SS_DANUBE_NODE_9                        ", 3018, 1, null, null], [10, 10, "SS_DANUBE_NODE_10                       ", 3018, 1, null, null], [10, 11, "SS_DANUBE_NODE_11                       ", 3018, 1, null, null], [10, 12, "SS_DANUBE_NODE_12                       ", 3018, 1, null, null], [17, 1, "SS_ZAMBEZI_NODE_1                       ", 3003, 1, null, null], [17, 2, "SS_ZAMBEZI_NODE_2                       ", 3003, 1, null, null], [17, 3, "SS_ZAMBEZI_NODE_3                       ", 3003, 1, null, null], [17, 4, "SS_ZAMBEZI_NODE_4                       ", 3003, 1, null, null], [17, 5, "SS_ZAMBEZI_NODE_5                       ", 3003, 1, null, null], [18, 1, "SS_PILCOMAYO_NODE_1                     ", 3007, 1, null, null], [18, 2, "SS_PILCOMAYO_NODE_2                     ", 3007, 1, null, null], [18, 3, "SS_PILCOMAYO_NODE_3                     ", 3007, 1, null, null], [19, 1, "SS_TIGRIS_NODE_1                        ", 3006, 1, null, null], [19, 2, "SS_TIGRIS_NODE_2                        ", 3006, 1, null, null], [19, 3, "SS_TIGRIS_NODE_3                        ", 3006, 1, null, null]]}, "subswd": {"fields": ["isub", "inode", "jnode", "swdid", "name", "type", "stat", "nstat", "xpu", "rate1", "rate2", "rate3"], "data": [[1, 1, 3, "1 ", "SS_NILE_SWD_1_3_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 1, 4, "1 ", "SS_NILE_SWD_1_4_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 2, 3, "1 ", "SS_NILE_SWD_2_3_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 2, 4, "1 ", "SS_NILE_SWD_2_4_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 5, 7, "1 ", "SS_NILE_SWD_5_7_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 5, 8, "1 ", "SS_NILE_SWD_5_8_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 6, 7, "1 ", "SS_NILE_SWD_6_7_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 6, 8, "1 ", "SS_NILE_SWD_6_8_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 11, "1 ", "SS_NILE_SWD_9_11_1                      ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 12, "1 ", "SS_NILE_SWD_9_12_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 13, "1 ", "SS_NILE_SWD_9_13_1                      ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 14, "1 ", "SS_NILE_SWD_9_14_1                      ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 15, "1 ", "SS_NILE_SWD_9_15_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 16, "1 ", "SS_NILE_SWD_9_16_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 11, "1 ", "SS_NILE_SWD_10_11_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 12, "1 ", "SS_NILE_SWD_10_12_1                     ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 13, "1 ", "SS_NILE_SWD_10_13_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 14, "1 ", "SS_NILE_SWD_10_14_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 15, "1 ", "SS_NILE_SWD_10_15_1                     ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 16, "1 ", "SS_NILE_SWD_10_16_1                     ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 2, "1 ", "SS_YANGTZE_SWD_1_2_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 1, 3, "1 ", "SS_YANGTZE_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 4, "1 ", "SS_YANGTZE_SWD_1_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 5, "1 ", "SS_YANGTZE_SWD_1_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 6, "1 ", "SS_YANGTZE_SWD_1_6_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 7, "1 ", "SS_YANGTZE_SWD_1_7_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 2, 3, "1 ", "SS_YANGTZE_SWD_2_3_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 4, "1 ", "SS_YANGTZE_SWD_2_4_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 5, "1 ", "SS_YANGTZE_SWD_2_5_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 6, "1 ", "SS_YANGTZE_SWD_2_6_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 7, "1 ", "SS_YANGTZE_SWD_2_7_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 8, 9, "1 ", "SS_YANGTZE_SWD_8_9_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 8, 10, "1 ", "SS_YANGTZE_SWD_8_10_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 8, 11, "1 ", "SS_YANGTZE_SWD_8_11_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 8, 12, "1 ", "SS_YANGTZE_SWD_8_12_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 8, 13, "1 ", "SS_YANGTZE_SWD_8_13_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 8, 14, "1 ", "SS_YANGTZE_SWD_8_14_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 9, 10, "1 ", "SS_YANGTZE_SWD_9_10_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 9, 11, "1 ", "SS_YANGTZE_SWD_9_11_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 9, 12, "1 ", "SS_YANGTZE_SWD_9_12_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 9, 13, "1 ", "SS_YANGTZE_SWD_9_13_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 9, 14, "1 ", "SS_YANGTZE_SWD_9_14_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [3, 1, 3, "1 ", "SS_ARKANSAS_SWD_1_3_1                   ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 4, "1 ", "SS_ARKANSAS_SWD_1_4_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 5, "1 ", "SS_ARKANSAS_SWD_1_5_1                   ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 6, "1 ", "SS_ARKANSAS_SWD_1_6_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 7, "1 ", "SS_ARKANSAS_SWD_2_7_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 8, "1 ", "SS_ARKANSAS_SWD_2_8_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 9, "1 ", "SS_ARKANSAS_SWD_2_9_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 10, "1 ", "SS_ARKANSAS_SWD_2_10_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 3, 7, "1 ", "SS_ARKANSAS_SWD_3_7_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 4, 8, "1 ", "SS_ARKANSAS_SWD_4_8_1                   ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [3, 5, 9, "1 ", "SS_ARKANSAS_SWD_5_9_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 6, 10, "1 ", "SS_ARKANSAS_SWD_6_10_1                  ", 2, 0, 1, 0.0001, 0.0, 0.0, 0.0], [4, 1, 2, "1 ", "SS_COLORADO_SWD_1_2_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 1, 8, "1 ", "SS_COLORADO_SWD_1_8_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 9, "1 ", "SS_COLORADO_SWD_1_9_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 10, "1 ", "SS_COLORADO_SWD_1_10_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 11, "1 ", "SS_COLORADO_SWD_1_11_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 12, "1 ", "SS_COLORADO_SWD_1_12_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 2, 8, "1 ", "SS_COLORADO_SWD_2_8_1                   ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 9, "1 ", "SS_COLORADO_SWD_2_9_1                   ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 10, "1 ", "SS_COLORADO_SWD_2_10_1                  ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 11, "1 ", "SS_COLORADO_SWD_2_11_1                  ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 12, "1 ", "SS_COLORADO_SWD_2_12_1                  ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 3, 8, "1 ", "SS_COLORADO_SWD_3_8_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 4, 9, "1 ", "SS_COLORADO_SWD_4_9_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 5, 10, "1 ", "SS_COLORADO_SWD_5_10_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 6, 11, "1 ", "SS_COLORADO_SWD_6_11_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 7, 12, "1 ", "SS_COLORADO_SWD_7_12_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 13, 14, "1 ", "SS_COLORADO_SWD_13_14_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 13, 17, "1 ", "SS_COLORADO_SWD_13_17_1                 ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 13, 18, "1 ", "SS_COLORADO_SWD_13_18_1                 ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 14, 17, "1 ", "SS_COLORADO_SWD_14_17_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 14, 18, "1 ", "SS_COLORADO_SWD_14_18_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 15, 17, "1 ", "SS_COLORADO_SWD_15_17_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 16, 18, "1 ", "SS_COLORADO_SWD_16_18_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 1, 2, "1 ", "SS_MISSISSIPPI_SWD_1_2_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 1, 6, "1 ", "SS_MISSISSIPPI_SWD_1_6_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 1, 7, "1 ", "SS_MISSISSIPPI_SWD_1_7_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 1, 8, "1 ", "SS_MISSISSIPPI_SWD_1_8_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 2, 6, "1 ", "SS_MISSISSIPPI_SWD_2_6_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 2, 7, "1 ", "SS_MISSISSIPPI_SWD_2_7_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 2, 8, "1 ", "SS_MISSISSIPPI_SWD_2_8_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 3, 6, "1 ", "SS_MISSISSIPPI_SWD_3_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 4, 7, "1 ", "SS_MISSISSIPPI_SWD_4_7_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 5, 8, "1 ", "SS_MISSISSIPPI_SWD_5_8_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 9, 10, "1 ", "SS_MISSISSIPPI_SWD_9_10_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 9, 17, "1 ", "SS_MISSISSIPPI_SWD_9_17_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 18, "1 ", "SS_MISSISSIPPI_SWD_9_18_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 19, "1 ", "SS_MISSISSIPPI_SWD_9_19_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 20, "1 ", "SS_MISSISSIPPI_SWD_9_20_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 21, "1 ", "SS_MISSISSIPPI_SWD_9_21_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 22, "1 ", "SS_MISSISSIPPI_SWD_9_22_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 10, 17, "1 ", "SS_MISSISSIPPI_SWD_10_17_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 18, "1 ", "SS_MISSISSIPPI_SWD_10_18_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 19, "1 ", "SS_MISSISSIPPI_SWD_10_19_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 20, "1 ", "SS_MISSISSIPPI_SWD_10_20_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 21, "1 ", "SS_MISSISSIPPI_SWD_10_21_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 22, "1 ", "SS_MISSISSIPPI_SWD_10_22_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 11, 17, "1 ", "SS_MISSISSIPPI_SWD_11_17_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 12, 18, "1 ", "SS_MISSISSIPPI_SWD_12_18_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 13, 19, "1 ", "SS_MISSISSIPPI_SWD_13_19_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 14, 20, "1 ", "SS_MISSISSIPPI_SWD_14_20_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 15, 21, "1 ", "SS_MISSISSIPPI_SWD_15_21_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 16, 22, "1 ", "SS_MISSISSIPPI_SWD_16_22_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 1, 2, "1 ", "SS_VOLGA_SWD_1_2_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 1, 3, "1 ", "SS_VOLGA_SWD_1_3_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 5, "1 ", "SS_VOLGA_SWD_4_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 6, "1 ", "SS_VOLGA_SWD_4_6_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 7, "1 ", "SS_VOLGA_SWD_4_7_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 8, "1 ", "SS_VOLGA_SWD_4_8_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 9, "1 ", "SS_VOLGA_SWD_4_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 10, "1 ", "SS_VOLGA_SWD_4_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 4, 11, "1 ", "SS_VOLGA_SWD_4_11_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 12, 13, "1 ", "SS_VOLGA_SWD_12_13_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 12, 14, "1 ", "SS_VOLGA_SWD_12_14_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 1, 3, "1 ", "SS_BRAHMAPUTRA_SWD_1_3_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 1, 4, "1 ", "SS_BRAHMAPUTRA_SWD_1_4_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 2, 5, "1 ", "SS_BRAHMAPUTRA_SWD_2_5_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 2, 6, "1 ", "SS_BRAHMAPUTRA_SWD_2_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 3, 5, "1 ", "SS_BRAHMAPUTRA_SWD_3_5_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 4, 6, "1 ", "SS_BRAHMAPUTRA_SWD_4_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 9, "1 ", "SS_BRAHMAPUTRA_SWD_7_9_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 10, "1 ", "SS_BRAHMAPUTRA_SWD_8_10_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 9, 10, "1 ", "SS_BRAHMAPUTRA_SWD_9_10_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 11, 13, "1 ", "SS_BRAHMAPUTRA_SWD_11_13_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 12, 14, "1 ", "SS_BRAHMAPUTRA_SWD_12_14_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 13, 14, "1 ", "SS_BRAHMAPUTRA_SWD_13_14_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 2, "1 ", "SS_INDUS_SWD_1_2_1                      ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 1, 3, "1 ", "SS_INDUS_SWD_1_3_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 4, "1 ", "SS_INDUS_SWD_1_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 5, "1 ", "SS_INDUS_SWD_1_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 2, 3, "1 ", "SS_INDUS_SWD_2_3_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 4, "1 ", "SS_INDUS_SWD_2_4_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 5, "1 ", "SS_INDUS_SWD_2_5_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 6, 7, "1 ", "SS_INDUS_SWD_6_7_1                      ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 6, 8, "1 ", "SS_INDUS_SWD_6_8_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 9, "1 ", "SS_INDUS_SWD_6_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 10, "1 ", "SS_INDUS_SWD_6_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 11, "1 ", "SS_INDUS_SWD_6_11_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 12, "1 ", "SS_INDUS_SWD_6_12_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 13, "1 ", "SS_INDUS_SWD_6_13_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 6, 14, "1 ", "SS_INDUS_SWD_6_14_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 7, 8, "1 ", "SS_INDUS_SWD_7_8_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 9, "1 ", "SS_INDUS_SWD_7_9_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 10, "1 ", "SS_INDUS_SWD_7_10_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 11, "1 ", "SS_INDUS_SWD_7_11_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 12, "1 ", "SS_INDUS_SWD_7_12_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 13, "1 ", "SS_INDUS_SWD_7_13_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 7, 14, "1 ", "SS_INDUS_SWD_7_14_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [10, 1, 3, "1 ", "SS_DANUBE_SWD_1_3_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 1, 4, "1 ", "SS_DANUBE_SWD_1_4_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 1, 5, "1 ", "SS_DANUBE_SWD_1_5_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 2, 6, "1 ", "SS_DANUBE_SWD_2_6_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 2, 7, "1 ", "SS_DANUBE_SWD_2_7_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 2, 8, "1 ", "SS_DANUBE_SWD_2_8_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 3, 6, "1 ", "SS_DANUBE_SWD_3_6_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 4, 7, "1 ", "SS_DANUBE_SWD_4_7_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 5, 8, "1 ", "SS_DANUBE_SWD_5_8_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 9, 11, "1 ", "SS_DANUBE_SWD_9_11_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 10, 12, "1 ", "SS_DANUBE_SWD_10_12_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 11, 12, "1 ", "SS_DANUBE_SWD_11_12_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 3, "1 ", "SS_ZAMBEZI_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 4, "1 ", "SS_ZAMBEZI_SWD_1_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 5, "1 ", "SS_ZAMBEZI_SWD_1_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 3, "1 ", "SS_ZAMBEZI_SWD_2_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 4, "1 ", "SS_ZAMBEZI_SWD_2_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 5, "1 ", "SS_ZAMBEZI_SWD_2_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [18, 1, 2, "1 ", "SS_PILCOMAYO_SWD_1_2_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [18, 1, 3, "1 ", "SS_PILCOMAYO_SWD_1_3_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [18, 2, 3, "1 ", "SS_PILCOMAYO_SWD_2_3_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [19, 1, 2, "1 ", "SS_TIGRIS_SWD_1_2_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [19, 1, 3, "1 ", "SS_TIGRIS_SWD_1_3_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0]]}, "subterm": {"fields": ["isub", "inode", "type", "eqid", "ibus", "jbus", "kbus"], "data": [[1, 4, "M", "1 ", 101, null, null], [1, 3, "2", "1 ", 101, 151, null], [1, 8, "M", "1 ", 102, null, null], [1, 7, "2", "1 ", 102, 151, null], [1, 16, "F", "1 ", 151, null, null], [1, 15, "2", "1 ", 151, 102, null], [1, 12, "B", "1 ", 151, 201, null], [1, 14, "2", "1 ", 151, 101, null], [1, 11, "B", "1 ", 151, 152, null], [1, 13, "B", "2 ", 151, 152, null], [2, 6, "B", "1 ", 152, 151, null], [2, 7, "B", "2 ", 152, 151, null], [2, 5, "2", "1 ", 152, 153, null], [2, 3, "B", "1 ", 152, 202, null], [2, 4, "B", "1 ", 152, 3004, null], [2, 14, "L", "1 ", 153, null, null], [2, 13, "2", "1 ", 153, 152, null], [2, 10, "B", "1 ", 153, 154, null], [2, 11, "B", "2 ", 153, 154, null], [2, 12, "B", "1 ", 153, 3006, null], [3, 6, "B", "1 ", 154, 153, null], [3, 4, "B", "1 ", 154, 205, null], [3, 8, "L", "1 ", 154, null, null], [3, 9, "L", "2 ", 154, null, null], [3, 10, "F", "1 ", 154, null, null], [3, 7, "B", "2 ", 154, 153, null], [3, 3, "B", "1 ", 154, 203, null], [3, 5, "B", "1 ", 154, 3008, null], [4, 7, "F", "1 ", 201, null, null], [4, 6, "B", "1 ", 201, 151, null], [4, 3, "B", "1 ", 201, 202, null], [4, 4, "B", "1 ", 201, 204, null], [4, 5, "2", "1 ", 201, 211, null], [4, 16, "M", "1 ", 211, null, null], [4, 15, "2", "1 ", 211, 201, null], [5, 4, "B", "1 ", 202, 152, null], [5, 5, "B", "1 ", 202, 201, null], [5, 3, "2", "1 ", 202, 203, null], [5, 15, "L", "1 ", 203, null, null], [5, 16, "F", "1 ", 203, null, null], [5, 13, "B", "1 ", 203, 154, null], [5, 14, "2", "1 ", 203, 202, null], [5, 11, "B", "1 ", 203, 205, null], [5, 12, "B", "2 ", 203, 205, null], [6, 3, "B", "1 ", 204, 201, null], [6, 2, "2", "1 ", 204, 205, null], [6, 10, "L", "1 ", 205, null, null], [6, 11, "F", "1 ", 205, null, null], [6, 6, "B", "1 ", 205, 154, null], [6, 7, "B", "1 ", 205, 203, null], [6, 8, "B", "2 ", 205, 203, null], [6, 9, "2", "1 ", 205, 204, null], [6, 5, "2", "1 ", 205, 206, null], [6, 14, "M", "1 ", 206, null, null], [6, 13, "2", "1 ", 206, 205, null], [8, 4, "2", "1 ", 3001, 3002, null], [8, 3, "B", "1 ", 3001, 3003, null], [8, 5, "2", "1 ", 3001, 3011, null], [8, 10, "2", "1 ", 3002, 3001, null], [8, 9, "B", "1 ", 3002, 3004, null], [8, 14, "M", "1 ", 3011, null, null], [8, 13, "2", "1 ", 3011, 3001, null], [9, 4, "B", "1 ", 3004, 152, null], [9, 5, "B", "1 ", 3004, 3002, null], [9, 3, "2", "1 ", 3004, 3005, null], [9, 14, "L", "1 ", 3005, null, null], [9, 11, "B", "1 ", 3005, 3003, null], [9, 12, "B", "2 ", 3005, 3003, null], [9, 13, "2", "1 ", 3005, 3004, null], [9, 8, "B", "1 ", 3005, 3006, null], [9, 9, "B", "1 ", 3005, 3007, null], [9, 10, "B", "1 ", 3005, 3008, null], [10, 7, "L", "1 ", 3008, null, null], [10, 4, "B", "1 ", 3008, 154, null], [10, 5, "B", "1 ", 3008, 3005, null], [10, 6, "B", "1 ", 3008, 3007, null], [10, 3, "2", "1 ", 3008, 3018, null], [10, 12, "M", "1 ", 3018, null, null], [10, 11, "2", "1 ", 3018, 3008, null], [17, 5, "B", "1 ", 3003, 3001, null], [17, 3, "B", "1 ", 3003, 3005, null], [17, 4, "B", "2 ", 3003, 3005, null], [18, 3, "L", "1 ", 3007, null, null], [18, 2, "B", "1 ", 3007, 3005, null], [18, 1, "B", "1 ", 3007, 3008, null], [19, 2, "B", "1 ", 3006, 153, null], [19, 3, "B", "1 ", 3006, 3005, null]]}}}