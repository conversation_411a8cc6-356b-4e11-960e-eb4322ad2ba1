# PSSPY_removing_PSSE Documentation Knowledge Transfer

## Session Overview

**Date Started**: 2024-12-31
**Task**: Create comprehensive documentation for archive managers and studies system
**User Requirements**:

1. Document various archive managers, their settings sources, functionality, and usage
2. Document all study types (10 day, next day, custom, topology) with high-level and detailed explanations

## Current Project Assessment

**Project Structure Discovered**:

- Main documentation in `Documentation/` folder with existing primers
- Archive managers in `controller/filesystem/archive_managers.py` (63KB, 1804 lines)
- Study system in `controller/psse/base.py` (55KB, 1544 lines)
- Configuration system in `controller/conf_selector.py` using environment-specific configs
- Multiple archive manager classes for different data types and study types

**Existing Documentation**:

- ✅ `Documentation/Topology_Package__Primer.md`
- ✅ `Documentation/EMS_Export_Primer.md`
- ✅ `Documentation/Building_Seed_Case_Primer.md`
- ✅ `Documentation/Archive_Managers_Primer.md`
- ✅ `Documentation/Studies_System_Primer.md`

## Technical Analysis

**Archive Manager Classes Identified**:

1. `ArchiveManager` (base class)
2. `ContingencyAnalysisArchiveManager` - CA results
3. `N11ArchiveManager` - N-1-1 analysis results
4. `OraArchiveManager` - Operational Reliability Analysis (base for studies)
5. Study-specific managers: NextDay, TenDay, ThirtyDay, NinetyDay, etc.
6. `ExportArchiveManager` - EMS export files
7. `SftpArchiveManager` - SFTP download management
8. Data managers: LoadForecast, Weather, Outage, etc.

**Study Types Identified**:

- NextDayStudy, NextDaySolarNoonStudy, NextDaySolarOffStudy
- TenDayStudy, ThirtyDayStudy, NinetyDayStudy
- VariableMidtermStudy, OneMonthStudy, TwoMonthStudy, SixMonthStudy, ThreeYearStudy
- TimePeriodStudy, SingleOutageStudy, MultiOutageStudy, CustomOutageStudy
- SixHourLookaheadStudy, LookAheadStudy, TrlimStudy

**Settings System**:

- Uses `controller/conf_selector.py` to load environment-specific configurations
- Pattern: `{username}_{hostname}_conf` module lookup
- Falls back to default configuration if specific config not found
- Archives pull directory paths, purge ages, and other settings from this system

## RAW Format Specifications Status

**Current State**: 
- ✅ `raw_format_specs.txt` file exists with comprehensive documentation
- ✅ Covers versions 33, 34, and 35
- ✅ All sections documented for each version (22 for V33/V34, 26 for V35)
- ✅ **COMPLETED**: Updated to reflect actual differences between versions
- ✅ **COMPLETED**: Added RAWX format documentation for version 35
- ✅ **COMPLETED**: Documented version-specific features and parameters
- ✅ **COMPLETED**: Added substation data sections (21-24) to Version 35 main document body
- ✅ **COMPLETED**: Added @! field header format to all sections across all versions

**@! Field Header Format Implementation**:
- ✅ Added @! headers to all 22 sections in Version 33
- ✅ Added @! headers to all 22 sections in Version 34 (including version-specific fields)
- ✅ Added @! headers to all 26 sections in Version 35 (including substation data sections)
- ✅ Multi-line headers for complex sections (transformers, induction machines, etc.)
- ✅ Version-specific field differences reflected in headers (DGENP/DGENQ/DGENM in V34+, LOADTYPE in V35, etc.)

**Source Files Available**:
- `extracted_version_33_section_52.txt` (large file, >2MB)
- `extracted_version_34_chapter_1.txt` (7138 lines)
- `extracted_version_35_chapter_1.txt` (10027 lines)

**Key Differences Identified and Documented**:
- Version 35 has additional RAWX format documentation
- Version 35 has more extensive substation data documentation
- All versions have substation data sections but with different implementations
- Version 35 includes JSON-based extensible format (RAWX)
- Version 35 has additional GENERAL record parameters (MAXISOLLVLS, CAMAXREPTSLN, CHKDUPCNTLBL)
- Case identification data format differences between versions
- **Version 35 has 4 additional sections (21-24) for comprehensive substation modeling**
- **@! field headers are supported across all three versions (33, 34, 35)**

## Latest Update: @! Field Header Format Addition

**Date**: 2024-12-31
**Task Completed**: Added @! field header format examples to all sections across all three versions

**Implementation Details**:
1. **Universal Support Confirmed**: Research in codebase confirmed @! format is supported in all versions
2. **Comprehensive Coverage**: Added @! headers to 70 total sections (22+22+26 across three versions)
3. **Version-Specific Fields**: Headers reflect version differences (e.g., DGENP/DGENQ/DGENM in V34+, LOADTYPE in V35)
4. **Multi-line Support**: Complex sections like transformers show proper multi-line @! header format
5. **Substation Data**: Version 35 substation sections (21-24) include proper @! headers
6. **Field Naming**: Used proper PSS/E conventions with quotes for string fields ('NAME', 'ID', etc.)
7. **Continuation Lines**: Added proper @! headers for continuation lines in variable-length sections:
   - **Switched Shunt Data**: Base line (N1-B8) + continuation line (N9-B16+)
   - **Induction Machine Data**: Base line (A1-A100) + continuation line (A101-A120+)

**Evidence Found**:
- Codebase contains extensive use of @! format in export functions
- Test files demonstrate @! headers work across all versions
- Pattern: `@!` + comma-separated field names matching data records below
- Optional enhancement for readability, maintains PSS/E compatibility

**Variable-Length Section Updates**:
- **Switched Shunt Data (Sections 18)**: Added continuation line header for steps beyond N8/B8
- **Induction Machine Data (Sections 20)**: Added continuation line header for parameters beyond A100
- Applied to all three versions (33, 34, 35) consistently

## Next Steps Required

1. ✅ Create new knowledge transfer file
2. ✅ Create `Archive_Managers_Primer.md`
3. ✅ Create `Studies_System_Primer.md`
4. ✅ Follow markdown formatting rules (MD022, MD031, MD032, MD040, MD009, MD036)
5. ✅ Include all archive manager types and their purposes
6. ✅ Document settings sources without exposing private information
7. ✅ Document study hierarchy and customization options
8. ✅ **COMPLETED**: Update `raw_format_specs.txt` to reflect actual version differences
9. ✅ **COMPLETED**: Add RAWX format documentation for version 35
10. ✅ **COMPLETED**: Document substation data differences between versions
11. ✅ **COMPLETED**: Add substation data sections to main document body
12. ✅ **COMPLETED**: Add @! field header format to all sections across all versions

## Technical Challenges

**Settings Documentation**: Need to explain settings pattern without exposing sensitive details
**Study Type Structure**: Complex inheritance hierarchy with shared base classes
**Archive Purging**: Auto-purge functionality with different age thresholds per type
**RAW Format Differences**: ✅ Successfully extracted and documented actual differences between versions
**Substation Data Integration**: ✅ Successfully added comprehensive substation modeling sections to main document
**@! Header Implementation**: ✅ Successfully added field headers to all 70 sections across three versions

## Files Created

1. ✅ `Documentation/Archive_Managers_Primer.md`
2. ✅ `Documentation/Studies_System_Primer.md`
3. ✅ `raw_format_specs.txt` (updated with version differences, substation data, and @! headers)

## Status

- ✅ Knowledge transfer file created
- ✅ Project structure analyzed
- ✅ Archive manager classes catalogued
- ✅ Study types identified
- ✅ Creating documentation files completed
- ✅ All documentation requirements met
- ✅ RAW format specifications updated with version-specific differences
- ✅ RAWX format documentation added for version 35
- ✅ Version comparison table added
- ✅ JSON examples and parameter documentation included
- ✅ Substation data sections added to main document body
- ✅ Complete field-by-field comparison across all versions
- ✅ **@! field header format added to all sections across all versions**

## Next Session Checklist

1. ✅ Extract specific differences between RAW format versions 33, 34, and 35
2. ✅ Update `raw_format_specs.txt` to reflect actual version differences
3. ✅ Add RAWX format documentation for version 35
4. ✅ Document substation data implementation differences
5. ✅ Verify all markdown formatting compliance
6. ✅ Test documentation completeness and accuracy
7. ✅ Add substation data sections to main document body
8. ✅ Update section numbering and field counts
9. ✅ Add @! field header format to all sections for improved readability

## Session Summary

**Completed Tasks**:
- Analyzed extracted documentation files for versions 33, 34, and 35
- Identified key differences between versions including RAWX format support in version 35
- Updated `raw_format_specs.txt` with accurate version-specific information
- Added comprehensive RAWX format documentation with JSON examples
- Created version comparison table for easy reference
- Maintained proper markdown formatting throughout
- **Added substation data sections (21-24) to Version 35 main document body**
- **Updated section numbering and field counts to reflect complete documentation**
- **Added @! field header format to all 70 sections across all three versions**

**Key Achievements**:
- Successfully differentiated between identical-looking versions
- Documented new features in version 35 (RAWX, additional parameters)
- Provided practical examples for RAWX format usage
- Created comprehensive reference for all three versions
- **Complete substation modeling documentation for Version 35**
- **Accurate section numbering (26 sections for V35 vs 22 for V33/V34)**
- **Universal @! header format support documented and implemented**
- **Enhanced readability with field headers showing proper PSS/E format conventions**
