#!/usr/bin/env python3
"""
Debug script to examine exactly what canonical sections the HDB backend produces.
"""

import logging
from pathlib import Path
import sys
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import HdbBackend

# Configure logging to write to file
log_file = f"debug_hdb_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def debug_hdb_backend_output():
    """Debug what canonical sections the HDB backend actually produces."""
    
    debug_file = f"debug_hdb_canonical_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    def debug_print(msg):
        """Print to both console and debug file."""
        print(msg)
        with open(debug_file, 'a', encoding='utf-8') as f:
            f.write(msg + '\n')
    
    debug_print("DEBUG: HDB Backend Canonical Output")
    debug_print("="*60)
    debug_print(f"Debug output will be saved to: {debug_file}")
    
    # Create HDB backend with specific file
    hdb_file = "hdbcontext_original.hdb"
    debug_print(f"Creating HDB backend with file: {hdb_file}...")
    try:
        hdb_backend = HdbBackend(file_path=hdb_file)
        debug_print("HDB backend created successfully")
    except Exception as e:
        debug_print(f"ERROR creating HDB backend: {e}")
        return
    
    # Get canonical data from HDB backend
    debug_print("Getting canonical data...")
    try:
        canonical_data = hdb_backend.to_canonical()
        debug_print(f"Canonical conversion successful")
    except Exception as e:
        debug_print(f"ERROR getting canonical data: {e}")
        return
    
    # Show all sections produced
    debug_print(f"\nTOTAL SECTIONS: {len(canonical_data)}")
    for section_name in sorted(canonical_data.keys()):
        section_data = canonical_data[section_name]
        if section_data and isinstance(section_data, dict) and 'data' in section_data:
            count = len(section_data['data'])
            fields = section_data.get('fields', [])
            debug_print(f"   {section_name}: {count} records, {len(fields)} fields")
            if count > 0:
                # Show field names for sections with data
                debug_print(f"      Fields: {fields}")
        elif section_data:
            debug_print(f"   {section_name}: {type(section_data)} (non-standard format)")
        else:
            debug_print(f"   {section_name}: Empty/None")
    
    # Specifically check for node/substation data
    debug_print(f"\nCHECKING KEY SECTIONS:")
    
    key_sections = ['substation', 'node', 'switching_device', 'terminal', 'subterm']
    for section in key_sections:
        if section in canonical_data:
            section_data = canonical_data[section]
            if section_data and isinstance(section_data, dict):
                count = len(section_data.get('data', []))
                fields = section_data.get('fields', [])
                debug_print(f"   {section}: FOUND - {count} records")
                if count > 0:
                    # Show first record
                    first_record = section_data['data'][0] if section_data['data'] else None
                    debug_print(f"      First record: {first_record}")
            else:
                debug_print(f"   {section}: EXISTS but wrong format: {type(section_data)}")
        else:
            debug_print(f"   {section}: NOT FOUND")
    
    # Check if any sections contain node/switching information
    debug_print(f"\nSEARCHING FOR NODE/SWITCHING DATA IN OTHER SECTIONS:")
    for section_name, section_data in canonical_data.items():
        if not isinstance(section_data, dict) or 'fields' not in section_data:
            continue
        
        fields = section_data.get('fields', [])
        field_str = ' '.join(fields).lower()
        
        # Look for node-related fields
        if ('node' in field_str or 'switching' in field_str or 
            'substation' in field_str or 'terminal' in field_str):
            count = len(section_data.get('data', []))
            debug_print(f"   {section_name}: Contains node/switching fields - {count} records")
            debug_print(f"      Relevant fields: {[f for f in fields if any(keyword in f.lower() for keyword in ['node', 'switch', 'substation', 'terminal'])]}")
    
    debug_print("\n" + "="*60)
    debug_print("DEBUG: Complete")
    debug_print(f"Complete debug output saved to: {debug_file}")

if __name__ == "__main__":
    debug_hdb_backend_output() 