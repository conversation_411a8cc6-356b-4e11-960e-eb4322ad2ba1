{"network": {"caseid": {"fields": ["ic", "sbase", "rev", "xfrrat", "nxfrat", "basfrq", "title1", "title2"], "data": [0, 100.0, 35, 0, 1, 60.0, "PSS(R)E SAMPLE CASE", "ALL DATA CATEGORIES WITH SEQUENCE DATA"]}, "general": {"fields": ["thrshz", "pqbrak", "blowup", "maxisollvls", "camaxreptsln", "chkdupcntlbl"], "data": [0.0001, 0.7, 5.0, 4, 20, 0]}, "gauss": {"fields": ["itmx", "accp", "accq", "accm", "tol"], "data": [100, 1.6, 1.6, 1.0, 0.0001]}, "newton": {"fields": ["itmxn", "accn", "toln", "vctolq", "vctolv", "dvlim", "ndvfct"], "data": [100, 1.0, 0.1, 0.1, 1e-05, 0.99, 0.99]}, "adjust": {"fields": ["adjthr", "acctap", "taplim", "swvbnd", "mxtpss", "mxswim"], "data": [0.005, 1.0, 0.05, 100.0, 99, 10]}, "tysl": {"fields": ["itmxty", "accty", "tolty"], "data": [20, 1.0, 1e-05]}, "solver": {"fields": ["method", "actaps", "areain", "phshft", "dctaps", "swshnt", "flatst", "varlim", "nondiv"], "data": ["FDNS", 0, 0, 0, 1, 1, 0, 99, 0]}, "rating": {"fields": ["irate", "name", "desc"], "data": [[1, "CRCUS1", "Circus-Fibrous or threadlike    "], [2, "CRSTR2", "Cirrostrarus-Milky, translucent "], [3, "CRCUM3", "Cirrocumulus-small, white flakes"], [4, "ALTCU4", "Altocumulus-bundles or rollers  "], [5, "ALTST5", "Altostratus-dense, gray layer   "], [6, "STRTO6", "Stratocumulus-plaices or rollers"], [7, "STRTS7", "Stratus-Evenly grey, low layer  "], [8, "CMULS8", "Cumulus-Heap with flat basis    "], [9, "CMULO9", "Cumulonimbus-thunder, up-rises  "], [10, "NIBS10", "Nimbostratus-rain,grey, dark    "], [11, "CPLL11", "capillatus-haired, frayed       "], [12, "NBUL12", "nebulosus-fog, veil-like        "]]}, "bus": {"fields": ["ibus", "name", "baskv", "ide", "area", "zone", "owner", "vm", "va", "nvhi", "nvlo", "evhi", "evlo"], "data": [[101, "NUC-A       ", 21.6, 2, 1, 1, 1, 1.01, -19.0142, 1.1, 0.9, 1.1, 0.9], [102, "NUC-B       ", 21.6, 2, 1, 1, 1, 1.01, -19.3678, 1.1, 0.9, 1.1, 0.9], [151, "NUCPLNT     ", 500.0, 1, 1, 1, 1, 0.99923, -22.1613, 1.1, 0.9, 1.1, 0.9], [152, "MID500      ", 500.0, 1, 1, 2, 1, 1.04393, -29.7672, 1.1, 0.9, 1.1, 0.9], [153, "MID230      ", 230.0, 1, 1, 3, 1, 1.05708, -31.4386, 1.1, 0.9, 1.1, 0.9], [154, "DOWNTN      ", 230.0, 1, 1, 3, 1, 0.99311, -36.8635, 1.1, 0.9, 1.1, 0.9], [155, "FACTS TE    ", 230.0, 1, 1, 4, 1, 1.01848, -28.0346, 1.1, 0.9, 1.1, 0.9], [201, "HYDRO       ", 500.0, 1, 2, 7, 2, 0.99923, -22.1613, 1.1, 0.9, 1.1, 0.9], [202, "EAST500     ", 500.0, 1, 2, 2, 2, 1.02225, -30.9424, 1.1, 0.9, 1.1, 0.9], [203, "EAST230     ", 230.0, 1, 2, 8, 2, 1.0, -34.1478, 1.1, 0.9, 1.1, 0.9], [204, "SUB500      ", 500.0, 1, 2, 8, 2, 1.03093, -34.9445, 1.1, 0.9, 1.1, 0.9], [205, "SUB230      ", 230.0, 1, 2, 8, 2, 1.0, -37.3467, 1.1, 0.9, 1.1, 0.9], [206, "URBGEN      ", 18.0, 2, 2, 8, 2, 1.0, -34.8918, 1.1, 0.9, 1.1, 0.9], [207, "DUPONT      ", 500.0, 1, 2, 7, 2, 1.01832, -28.7073, 1.1, 0.9, 1.1, 0.9], [208, "URBANEAST208", 230.0, 4, 2, 8, 2, 1.0, -31.5246, 1.1, 0.9, 1.1, 0.9], [209, "URBANEAST209", 230.0, 4, 2, 8, 2, 1.0, -31.5246, 1.1, 0.9, 1.1, 0.9], [211, "HYDRO_G     ", 20.0, 2, 2, 7, 2, 1.0, -17.5386, 1.1, 0.9, 1.1, 0.9], [212, "INVERT1     ", 230.0, 1, 2, 7, 2, 1.02695, -35.7231, 1.1, 0.9, 1.1, 0.9], [213, "INVERT2     ", 230.0, 1, 2, 7, 2, 1.10677, -38.9888, 1.1, 0.9, 1.1, 0.9], [214, "LOADER      ", 230.0, 1, 2, 7, 2, 1.07726, -40.4186, 1.1, 0.9, 1.1, 0.9], [215, "URBANEAST215", 18.0, 1, 2, 8, 2, 0.91817, -37.2809, 1.1, 0.9, 1.1, 0.9], [216, "URBANEAST216", 230.0, 1, 2, 8, 2, 0.99637, -37.3236, 1.1, 0.9, 1.1, 0.9], [217, "URBANEAST217", 230.0, 1, 2, 8, 2, 0.99733, -37.3298, 1.1, 0.9, 1.1, 0.9], [218, "URBANEAST218", 230.0, 1, 2, 8, 2, 0.99769, -37.332, 1.1, 0.9, 1.1, 0.9], [301, "NORTH       ", 765.0, 3, 3, 5, 3, 1.0, 0.0, 1.1, 0.9, 1.1, 0.9], [401, "COGEN-1     ", 500.0, 3, 4, 9, 4, 1.0, 0.0, 1.1, 0.9, 1.1, 0.9], [402, "COGEN-2     ", 500.0, 3, 6, 9, 4, 1.0, 0.0, 1.1, 0.9, 1.1, 0.9], [3001, "MINE        ", 230.0, 1, 5, 6, 5, 0.98441, -11.9797, 1.1, 0.9, 1.1, 0.9], [3002, "E. MINE     ", 500.0, 1, 5, 6, 5, 0.98662, -10.5569, 1.1, 0.9, 1.1, 0.9], [3003, "S. MINE     ", 230.0, 1, 5, 6, 5, 0.98742, -15.2068, 1.1, 0.9, 1.1, 0.9], [3004, "WEST        ", 500.0, 1, 5, 6, 5, 1.00898, -25.144, 1.1, 0.9, 1.1, 0.9], [3005, "WEST        ", 230.0, 1, 5, 2, 5, 0.99761, -26.0972, 1.1, 0.9, 1.1, 0.9], [3006, "UPTOWN      ", 230.0, 1, 5, 4, 5, 1.05708, -31.4386, 1.1, 0.9, 1.1, 0.9], [3007, "RURAL       ", 230.0, 1, 5, 4, 5, 0.98103, -29.4773, 1.1, 0.9, 1.1, 0.9], [3008, "CATDOG      ", 230.0, 1, 5, 4, 5, 0.99, -30.0543, 1.1, 0.9, 1.1, 0.9], [3009, "URBNWEST3009", 230.0, 1, 5, 4, 5, 0.99048, -30.0739, 1.1, 0.9, 1.1, 0.9], [3010, "INDMOTOR1   ", 21.6, 1, 5, 4, 5, 0.9639, -3.3103, 1.1, 0.9, 1.1, 0.9], [3011, "MINE_G      ", 19.4, 3, 5, 6, 5, 1.0, 0.0, 1.1, 0.9, 1.1, 0.9], [3012, "URBNWEST3012", 230.0, 4, 5, 4, 5, 0.99048, 0.0, 1.1, 0.9, 1.1, 0.9], [3018, "CATDOG_G    ", 13.8, 2, 5, 4, 5, 0.99, -26.2844, 1.1, 0.9, 1.1, 0.9], [3021, "WDUM        ", 18.0, 1, 3, 5, 3, 1.0, -26.2761, 1.1, 0.9, 1.1, 0.9], [3022, "EDUM        ", 18.0, 1, 3, 5, 3, 1.0, -25.5871, 1.1, 0.9, 1.1, 0.9], [9154, "INDGEN1     ", 4.16, 1, 1, 3, 1, 0.98605, -36.1817, 1.1, 0.9, 1.1, 0.9], [9204, "INDMOTOR2   ", 0.575, 1, 2, 8, 2, 0.98149, -38.6306, 1.1, 0.9, 1.1, 0.9], [93002, "INDGEN2     ", 0.69, 1, 5, 6, 5, 0.9482, -4.3678, 1.1, 0.9, 1.1, 0.9]]}, "load": {"fields": ["ibus", "loadid", "stat", "area", "zone", "pl", "ql", "ip", "iq", "yp", "yq", "owner", "scale", "intrpt", "dgenp", "dgenq", "dgenm", "loadtype"], "data": [[152, "1 ", 1, 1, 1, 1200.0, 360.0, 868.34, 360.502, 837.794, -351.338, 1, 1, 0, 0.0, 0.0, 0, "            "], [153, "1 ", 1, 1, 1, 300.0, 150.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 100.0, 50.0, 1, "            "], [154, "1 ", 1, 1, 1, 400.0, 200.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 30.0, 15.0, 1, "            "], [154, "2 ", 1, 1, 1, 250.0, 200.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 20.0, 8.0, 1, "            "], [154, "3 ", 1, 1, 1, 250.0, 100.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 7.5, 2.75, 1, "            "], [154, "MO", 1, 1, 1, 100.0, 80.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0.0, 0.0, 0, "            "], [201, "SC", 1, 2, 7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [203, "1 ", 1, 2, 2, 500.0, 250.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [205, "1 ", 1, 2, 2, 1800.0, 600.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 75.86, 46.786, 0, "            "], [205, "B ", 1, 2, 2, 90.0, 5.0, 110.0, 25.0, 20.0, 10.0, 2, 1, 0, 65.123, 20.456, 1, "            "], [205, "C ", 1, 2, 2, 60.0, 15.0, 45.0, 5.0, 35.0, 30.0, 2, 1, 0, 37.0, 13.0, 1, "            "], [214, "1 ", 1, 2, 2, 500.0, 75.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [215, "U1", 1, 2, 4, 0.0, 140.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [216, "U1", 1, 2, 4, 0.0, 12.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [217, "U1", 1, 2, 4, 0.0, 10.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [218, "U1", 1, 2, 4, 0.0, 9.0, 0.0, 0.0, 0.0, 0.0, 2, 1, 0, 0.0, 0.0, 0, "            "], [3005, "1 ", 1, 5, 5, 100.0, 50.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3007, "1 ", 1, 5, 5, 200.0, 75.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3008, "1 ", 1, 5, 5, 200.0, 75.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3009, "1 ", 1, 5, 4, 1.1, 0.9, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "], [3010, "1 ", 1, 5, 4, 12.0, 5.0, 0.0, 0.0, 0.0, 0.0, 5, 1, 0, 0.0, 0.0, 0, "            "]]}, "fixshunt": {"fields": ["ibus", "s<PERSON><PERSON><PERSON>", "stat", "gl", "bl"], "data": [[151, "F1", 1, 5.0, -400.0], [151, "F2", 1, 3.0, -400.0], [151, "F3", 1, 3.12, 200.0], [152, "1 ", 1, 1.63, 50.0], [154, "1 ", 1, 2.45, 200.0], [201, "1 ", 1, 3.67, -500.0], [203, "1 ", 1, -5.0, 30.0], [203, "2 ", 1, 5.0, 20.0], [205, "1 ", 1, 2.78, 300.0], [212, "1 ", 1, 5.13, 400.0], [213, "1 ", 1, 5.14, 400.0], [3021, "1 ", 1, 0.0, 1320.0], [3022, "1 ", 1, 0.0, 1080.0]]}, "generator": {"fields": ["ibus", "machid", "pg", "qg", "qt", "qb", "vs", "ireg", "nreg", "mbase", "zr", "zx", "rt", "xt", "gtap", "stat", "rmpct", "pt", "pb", "baslod", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "wmod", "wpf"], "data": [[101, "1 ", 750.0, 165.206, 400.0, -100.0, 1.01, 101, 0, 900.0, 0.01, 0.3, 0.0, 0.0, 1.0, 1, 100.0, 800.0, 50.0, 0, 1, 0.1289, 2, 0.2524, 3, 0.1031, 4, 0.5156, null, null], [102, "1 ", 650.0, 151.879, 410.0, -110.0, 1.01, 102, 0, 950.0, 0.0105, 0.32, 0.0, 0.0, 1.0, 1, 100.0, 700.0, 33.0, 0, 1, 0.3647, 2, 0.1838, 3, 0.0751, 4, 0.3764, null, null], [206, "1 ", 800.0, 283.913, 500.0, -400.0, 1.0, 206, 0, 1000.0, 0.0106, 0.251, 0.0, 0.0, 1.0, 1, 100.0, 850.0, 50.0, 0, 1, 0.1034, 2, 0.4006, 3, 0.0825, 4, 0.4135, null, null], [211, "1 ", 600.0, 18.35, 510.0, -100.0, 1.0, 211, 0, 725.0, 0.0108, 0.262, 0.0, 0.0, 1.0, 1, 100.0, 616.0, 30.0, 0, 1, 0.1423, 2, 0.221, 3, 0.1842, 4, 0.4525, null, null], [301, "1 ", 996.884, 299.541, 700.0, -650.0, 1.0, 301, 0, 1067.0, 0.0109, 0.23, 0.014, 0.126, 1.025, 1, 98.0, 1010.0, 320.0, 0, 1, 0.1118, 2, 0.2184, 3, 0.0893, 4, 0.5805, null, null], [301, "2 ", 996.884, 299.541, 710.0, -600.0, 1.0, 301, 0, 1070.0, 0.011, 0.24, 0.011, 0.127, 1.026, 1, 98.0, 1011.0, 321.0, 0, 1, 0.1173, 2, 0.32, 3, 0.0936, 4, 0.4691, null, null], [301, "3 ", 996.884, 299.541, 720.0, -600.0, 1.0, 301, 0, 1075.0, 0.008, 0.25, 0.012, 0.128, 1.027, 1, 98.0, 1012.0, 322.0, 0, 1, 0.126, 2, 0.2268, 3, 0.1827, 4, 0.4644, null, null], [401, "1 ", 321.0, 142.325, 600.0, -100.0, 1.0, 401, 0, 600.0, 0.0123, 0.2223, 0.0, 0.0, 1.0, 1, 90.0, 350.0, 25.0, 0, 1, 0.1625, 2, 0.2423, 3, 0.099, 4, 0.4962, null, null], [402, "1 ", 321.0, 142.325, 610.0, -110.0, 1.0, 402, 0, 610.0, 0.0045, 0.2432, 0.0, 0.0, 1.0, 1, 91.0, 351.0, 26.0, 0, 1, 0.1588, 2, 0.2117, 3, 0.1959, 4, 0.4335, null, null], [3011, "1 ", 1143.185, 176.798, 620.0, -120.0, 1.0, 3011, 0, 1050.0, 0.0076, 0.3543, 0.0, 0.0, 1.0, 1, 92.0, 1400.0, 100.0, 0, 1, 0.1452, 2, 0.1926, 3, 0.2677, 4, 0.3945, null, null], [3018, "1 ", 400.0, -0.628, 300.0, -150.0, 0.99, 3018, 0, 530.0, 0.087, 0.3563, 0.0, 0.0, 1.0, 1, 92.5, 500.0, 50.0, 0, 1, 0.1043, 2, 0.2037, 3, 0.2749, 4, 0.4171, null, null], [3018, "2 ", 100.0, -0.157, 75.0, -75.0, 0.99, 3018, 0, 120.0, 0.024, 0.3553, 0.0, 0.0, 1.0, 1, 92.5, 110.0, 20.0, 0, 1, 0.3003, 2, 0.1358, 3, 0.2857, 4, 0.2782, null, null]]}, "acline": {"fields": ["ibus", "jbus", "ckt", "rpu", "xpu", "bpu", "name", "rate1", "rate2", "rate3", "rate4", "rate5", "rate6", "rate7", "rate8", "rate9", "rate10", "rate11", "rate12", "gi", "bi", "gj", "bj", "stat", "met", "len", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4"], "data": [[151, 152, "1 ", 0.0026, 0.046, 3.5, "BRANCH_FROM__151_TO__152___CIRCUIT_ID__1", 1200.0, 1100.0, 1000.0, 1200.4, 1100.5, 1000.6, 1200.7, 1100.8, 1000.9, 1200.1, 1100.11, 1000.12, 0.01, -0.25, 0.011, -0.15, 1, 2, 150.0, 1, 0.2, 2, 0.3, 3, 0.4, 4, 0.1], [151, 152, "2 ", 0.00261, 0.0461, 3.51, "BRANCH_FROM__151_TO__152___CIRCUIT_ID__2", 1205.0, 1105.0, 1005.0, 1205.4, 1105.5, 1005.6, 1205.7, 1105.8, 1005.9, 1205.1, 1105.11, 1005.12, 0.013, -0.251, 0.012, -0.02, 1, 2, 149.0, 5, 0.2315, 1, 0.3056, 2, 0.3704, 3, 0.0926], [152, 202, "1 ", 0.0008, 0.01, 0.95, "BRANCH_FROM__152_TO__202___CIRCUIT_ID__1", 1207.0, 1107.0, 1007.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 200.0, 1, 0.3939, 2, 0.2273, 3, 0.303, 4, 0.0758], [152, 3004, "1 ", 0.003, 0.03, 2.5, "BRANCH_FROM__152_TO_3004___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 201.0, 5, 0.2379, 1, 0.3033, 2, 0.3667, 3, 0.0922], [153, 154, "2 ", 0.006, 0.054, 0.15, "BRANCH_FROM__153_TO__154___CIRCUIT_ID__2", 350.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 80.0, 2, 0.2727, 3, 0.2727, 4, 0.3636, 5, 0.0909], [154, 155, "1 ", 0.005, 0.045, 0.1, "BRANCH_FROM__154_TO__155___CIRCUIT_ID__1", 400.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 81.0, 5, 0.1914, 1, 0.2535, 2, 0.3065, 3, 0.2486], [154, 203, "1 ", 0.004, 0.04, 0.1, "BRANCH_FROM__154_TO__203___CIRCUIT_ID__1", 400.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 100.0, 2, 0.2, 3, 0.3, 4, 0.4, 5, 0.1], [154, 205, "1 ", 0.00033, 0.00333, 0.09, "BRANCH_FROM__154_TO__205___CIRCUIT_ID__1", 600.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 120.0, 1, 0.1667, 2, 0.25, 3, 0.3333, 4, 0.25], [154, 3008, "1 ", 0.0027, 0.022, 0.3, "BRANCH_FROM__154_TO_3008___CIRCUIT_ID__1", 800.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 119.0, 5, 0.1925, 1, 0.255, 2, 0.3083, 3, 0.2442], [201, 202, "1 ", 0.002, 0.025, 2.0, "BRANCH_FROM__201_TO__202___CIRCUIT_ID__1", 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 300.0, 2, 0.1905, 3, 0.2857, 4, 0.381, 5, 0.1429], [201, 207, "C1", 0.0015, 0.015, 1.25, "BRANCH_FROM__201_TO__207___CIRCUIT_ID_C1", 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 250.0, 1, 0.25, 2, 0.375, 3, 0.25, 4, 0.125], [203, 205, "1 ", 0.005, 0.045, 0.08, "BRANCH_FROM__203_TO__205___CIRCUIT_ID__1", 200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 70.0, 5, 0.1925, 1, 0.255, 2, 0.3083, 3, 0.2442], [204, 207, "C2", 0.0015, 0.015, 1.25, "BRANCH_FROM__204_TO__207___CIRCUIT_ID_C2", 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 300.0, 2, 0.2, 3, 0.3, 4, 0.4, 5, 0.1], [205, 212, "1 ", -0.0, 0.01, 0.0, "BRANCH_FROM__205_TO__212___CIRCUIT_ID__1", 1250.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 71.0, 1, 0.2, 2, 0.3, 3, 0.4, 4, 0.1], [205, 214, "2 ", 0.002, 0.025, 2.0, "BRANCH_FROM__205_TO__214___CIRCUIT_ID__2", 1200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 100.0, 5, 0.21, 1, 0.2782, 2, 0.3364, 3, 0.1755], [205, 216, "3 ", 0.005, 0.045, 0.08, "BRANCH_FROM__205_TO__216___CIRCUIT_ID__3", 200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 81.0, 2, 0.1818, 3, 0.2727, 4, 0.3636, 5, 0.1818], [205, 217, "4 ", 0.005, 0.045, 0.08, "BRANCH_FROM__205_TO__217___CIRCUIT_ID__4", 200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 80.0, 1, 0.1667, 2, 0.25, 3, 0.3333, 4, 0.25], [205, 218, "5 ", 0.005, 0.045, 0.08, "BRANCH_FROM__205_TO__218___CIRCUIT_ID__5", 200.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 59.0, 5, 0.165, 1, 0.2186, 2, 0.2643, 3, 0.3521], [213, 214, "1 ", -0.0, 0.01, 0.0, "BRANCH_FROM__213_TO__214___CIRCUIT_ID__1", 1250.0, 0.0, 0.0, 1250.4, 0.0, 0.0, 1250.7, 0.0, 0.0, 1250.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 0.5, 2, 0.1429, 3, 0.2143, 4, 0.2857, 5, 0.3571], [3001, 3003, "1 ", -0.0, 0.008, 0.0, "BRANCH_FROM_3001_TO_3003___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 70.0, 1, 0.1639, 2, 0.2459, 3, 0.3279, 4, 0.2623], [3002, 3004, "1 ", 0.006, 0.054, 0.09, "BRANCH_FROM_3002_TO_3004___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 200.0, 5, 0.1925, 1, 0.255, 2, 0.3083, 3, 0.2442], [3003, 3005, "1 ", 0.006, 0.054, 0.09, "BRANCH_FROM_3003_TO_3005___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 90.0, 2, 0.2, 3, 0.3, 4, 0.4, 5, 0.1], [3003, 3005, "2 ", 0.006, 0.054, 0.09, "BRANCH_FROM_3003_TO_3005___CIRCUIT_ID__2", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 90.0, 1, 0.1333, 2, 0.2, 3, 0.2667, 4, 0.4], [3005, 3006, "1 ", 0.0035, 0.03, 0.07, "BRANCH_FROM_3005_TO_3006___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 70.0, 5, 0.165, 1, 0.2186, 2, 0.2643, 3, 0.3521], [3005, 3007, "1 ", 0.003, 0.025, 0.06, "BRANCH_FROM_3005_TO_3007___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 80.0, 2, 0.1527, 3, 0.229, 4, 0.3053, 5, 0.313], [3005, 3008, "1 ", 0.006, 0.05, 0.12, "BRANCH_FROM_3005_TO_3008___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 2, 60.0, 1, 0.1802, 2, 0.2703, 3, 0.3604, 4, 0.1892], [3007, 3008, "1 ", 0.003, 0.025, 0.06, "BRANCH_FROM_3007_TO_3008___CIRCUIT_ID__1", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 60.0, 5, 0.1858, 1, 0.2462, 2, 0.4023, 3, 0.1657], [3008, 3009, "1 ", 0.003, 0.025, 0.06, "BRANCH_FROM_3008_TO_3009___CIRCUIT_ID__1", 25.0, 22.0, 18.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 2, 60.0, 1, 0.1797, 2, 0.2695, 3, 0.3594, 4, 0.1914]]}, "sysswd": {"fields": ["ibus", "jbus", "ckt", "xpu", "rate1", "rate2", "rate3", "rate4", "rate5", "rate6", "rate7", "rate8", "rate9", "rate10", "rate11", "rate12", "stat", "nstat", "met", "stype", "name"], "data": [[151, 201, "*1", 0.0001, 1206.0, 1106.0, 1006.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 2, 3, "                                        "], [153, 3006, "@1", 0.0001, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 2, 2, "                                        "]]}, "transformer": {"fields": ["ibus", "jbus", "kbus", "ckt", "cw", "cz", "cm", "mag1", "mag2", "nmet", "name", "stat", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "vecgrp", "zcod", "r1_2", "x1_2", "sbase1_2", "r2_3", "x2_3", "sbase2_3", "r3_1", "x3_1", "sbase3_1", "vmstar", "anstar", "windv1", "nomv1", "ang1", "wdg1rate1", "wdg1rate2", "wdg1rate3", "wdg1rate4", "wdg1rate5", "wdg1rate6", "wdg1rate7", "wdg1rate8", "wdg1rate9", "wdg1rate10", "wdg1rate11", "wdg1rate12", "cod1", "cont1", "node1", "rma1", "rmi1", "vma1", "vmi1", "ntp1", "tab1", "cr1", "cx1", "cnxa1", "windv2", "nomv2", "ang2", "wdg2rate1", "wdg2rate2", "wdg2rate3", "wdg2rate4", "wdg2rate5", "wdg2rate6", "wdg2rate7", "wdg2rate8", "wdg2rate9", "wdg2rate10", "wdg2rate11", "wdg2rate12", "cod2", "cont2", "node2", "rma2", "rmi2", "vma2", "vmi2", "ntp2", "tab2", "cr2", "cx2", "cnxa2", "windv3", "nomv3", "ang3", "wdg3rate1", "wdg3rate2", "wdg3rate3", "wdg3rate4", "wdg3rate5", "wdg3rate6", "wdg3rate7", "wdg3rate8", "wdg3rate9", "wdg3rate10", "wdg3rate11", "wdg3rate12", "cod3", "cont3", "node3", "rma3", "rmi3", "vma3", "vmi3", "ntp3", "tab3", "cr3", "cx3", "cnxa3"], "data": [[101, 151, 0, "T1", 2, 2, 1, 0.17147, -0.10288, 2, "EL DORADO NUCLEAR UNIT A GSU TRANSFORMER", 1, 1, 0.32, 2, 0.39, 3, 0.14, 4, 0.15, "Dyn1        ", null, 0.0011, 0.091, 1200.0, null, null, null, null, null, null, null, null, 21.6, 21.6, 0.0, 1200.0, 1100.0, 1000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 101, 0, 22.68, 20.52, 1.05, 0.95, 25, 0, 0.00021, 0.00051, 0.0, 500.0, 500.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [102, 151, 0, "T2", 2, 1, 2, 453750.0, 0.0026, 2, "EL DORADO NUCLEAR UNIT B GSU TRANSFORMER", 1, 1, 0.21, 2, 0.25, 3, 0.22, 4, 0.32, "Dyn1        ", null, 0.00012, 0.0076, 1210.0, null, null, null, null, null, null, null, null, 21.6, 21.6, 0.0, 1210.0, 1125.0, 1025.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 102, 0, 22.572, 20.628, 1.045, 0.955, 27, 0, 0.00022, 0.00052, 0.0, 500.0, 500.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [152, 153, 0, "T3", 2, 1, 1, 0.00021, -0.00012, 2, "MID LTC                                 ", 1, 1, 0.35, 2, 0.22, 3, 0.25, 4, 0.18, "YNyn0       ", null, 0.00017, 0.00775, 800.0, null, null, null, null, null, null, null, null, 475.0, 500.0, 0.0, 800.0, 750.0, 700.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 154, 0, 525.0, 475.0, 1.0, 0.98, 10, 0, 0.00023, 0.00053, 0.0, 230.0, 230.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [152, 3021, 0, "T4", 1, 2, 2, 562500.0, 0.0028, 2, "WDUM DC                                 ", 1, 1, 0.39, 2, 0.32, 3, 0.15, 4, 0.14, "YNyn0       ", null, 0.0013, 0.063, 1500.0, null, null, null, null, null, null, null, null, 1.1, 500.0, 0.0, 1500.0, 1400.0, 1350.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 2, 0.0, 0.0, 0.0, 1.0, 18.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [152, 3022, 0, "T5", 1, 2, 1, 0.0004, -0.00021, 2, "EDUM DC                                 ", 1, 1, 0.22, 2, 0.25, 3, 0.21, 4, 0.32, "YNyn0       ", null, 0.0017, 0.074, 1510.0, null, null, null, null, null, null, null, null, 1.1, 500.0, 0.0, 1510.0, 1410.0, 1393.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 1.0, 18.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [154, 9154, 0, "W1", 1, 1, 1, 0.0, 0.0, 2, "WTG1XMER                                ", 1, 1, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "YNd1        ", null, -0.0, 0.58333, 12.0, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 12.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [201, 211, 0, "T6", 2, 1, 2, 262500.0, 0.003, 2, "HYDRO_G XMER                            ", 1, 1, 0.14, 2, 0.15, 3, 0.32, 4, 0.39, "YNd1        ", null, 0.00026, 0.01343, 700.0, null, null, null, null, null, null, null, null, 500.0, 500.0, 0.0, 700.0, 650.0, 611.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 5, 0, 0.0, 0.0, 0.0, 20.0, 20.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [203, 202, 0, "T7", 1, 2, 1, 0.00095, -0.00046, 1, "EAST PS                                 ", 1, 1, 0.2754, 2, 0.3261, 3, 0.2899, 4, 0.1087, "YNyn0       ", null, 0.0021, 0.054, 750.0, null, null, null, null, null, null, null, null, 0.99, 230.0, 0.143, 750.0, 700.0, 657.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3, 0, 0, 12.0, -11.0, -900.0, -950.0, 33, 1, 0.0, 0.0, 0.0, 1.0, 500.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [204, 205, 0, "T8", 1, 2, 1, 0.00113, -0.00052, 2, "SUB LTC                                 ", 1, 1, 0.3019, 2, 0.4245, 3, 0.1321, 4, 0.1415, "YNyn0       ", null, 0.0037, 0.045, 800.0, null, null, null, null, null, null, null, null, 1.03667, 500.0, 0.0, 800.0, 775.0, 717.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 205, 0, 1.05, 0.95, 1.0, 0.98, 16, 0, 0.00024, 0.00054, 0.0, 1.0, 230.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [204, 9204, 0, "W2", 1, 1, 1, 0.0, 0.0, 2, "WTG2XMER                                ", 1, 2, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "YNd1        ", null, 0.088, 0.66171, 8.75, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 8.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [205, 206, 0, "T9", 1, 2, 1, 0.00113, -0.00052, 2, "URB TX                                  ", 1, 1, 0.1905, 2, 0.4643, 3, 0.1667, 4, 0.1786, "YNd1        ", null, 0.0016, 0.048, 900.0, null, null, null, null, null, null, null, null, 1.01591, 230.0, 0.0, 900.0, 850.0, 799.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2, 0, 0, 1.05, 0.975, -175.0, -250.0, 12, 0, 0.0, 0.0, 0.0, 1.0, 18.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [205, 215, 208, "3 ", 1, 1, 2, 34200.0, 0.00228, 2, "THREE WDG WITH COMPLEX IMP COR FACTORS 1", 3, 2, 0.254, 2, 0.1746, 3, 0.3333, 4, 0.2381, "YN0yn0y0    ", 1, 0.000752998, 0.053667, 150.0, 0.012479, 0.8735, 20.0, 0.020069, 1.34, 15.0, 0.60345, -36.786, 1.0, 230.0, 0.0, 150.0, 130.0, 120.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1, 215, 0, 1.1, 0.9, 1.1, 0.9, 33, 5, 0.00025, 0.00064, 0.0, 1.0, 18.0, 0.0, 20.0, 18.0, 15.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.2, 0.9012, 1.1, 0.9, 33, 0, 0.00012, 0.00046, 0.0, 1.0, 230.0, 0.0, 15.0, 13.0, 10.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 6, 0.0, 0.0, 0.0], [209, 217, 218, "4 ", 2, 3, 2, 41.8934, 9.98336e-06, 2, "3WNDSTAT4                               ", 4, 2, 0.4671, 2, 0.125, 3, 0.2379, 4, 0.17, "YN0yn0yn0   ", 0, 228000.0, 0.10001, 30.0, 190000.0, 0.09, 25.0, 266000.0, 0.075, 35.0, 0.99757, -37.331, 230.0, 230.0, 0.0, 30.0, 20.0, 10.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1, 0, 0, 253.0, 207.0, 1.1, 0.9, 33, 2, 0.00031, 0.00784, 0.0, 230.0, 230.0, 0.0, 25.0, 18.0, 12.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1, 217, 0, 253.0, 207.0, 1.1, 0.9, 33, 2, 0.00021, 0.00862, 0.0, 230.0, 230.0, 0.0, 35.0, 26.0, 14.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1, 0, 0, 253.0, 207.0, 1.1, 0.9, 33, 2, 0.00024, 0.00394, 0.0], [3001, 3002, 3011, "1 ", 1, 1, 1, 0.0012, -0.0042, 2, "THREE WDG WITH COMPLEX IMP COR FACTORS 2", 1, 1, 0.1238, 2, 0.2477, 3, 0.1548, 5, 0.4737, "YN0yn0d1    ", 1, 0.000113, 0.00805, 1000.0, 0.00025, 0.01747, 1000.0, 0.000301, 0.0201, 1000.0, 0.98588, -9.8267, 0.99884, 230.0, 0.0, 1200.0, 1150.0, 1090.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 3002, 0, 1.1002, 0.92, 1.16, 0.93, 33, 3, 0.00013, 0.00016, 0.0, 1.0, 500.0, 0.0, 1250.0, 1175.0, 1112.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1001, 0.9, 1.14, 0.91, 22, 0, 0.00012, 0.00015, 0.0, 1.0, 19.4, 0.0, 1280.0, 1200.0, 1157.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.91, 1.1, 0.92, 11, 4, 0.00011, 0.00014, 0.0], [3002, 93002, 0, "W3", 1, 1, 1, 0.0, 0.0, 2, "WTG3XMER                                ", 1, 5, 1.0, 0, 1.0, 0, 1.0, 0, 1.0, "YNd1        ", null, -0.0, 2.02703, 3.7, null, null, null, null, null, null, null, null, 1.0, 0.0, 0.0, 3.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 1.0, 0.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3005, 3004, 0, "10", 1, 1, 2, 206250.0, 0.0034, 1, "WEST TX                                 ", 1, 1, 0.2645, 2, 0.4959, 3, 0.1157, 4, 0.124, "YNa0        ", null, 0.00035, 0.00964, 550.0, null, null, null, null, null, null, null, null, 1.0, 230.0, 0.0, 550.0, 500.0, 455.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 3, 0, 0.0, 0.0, 0.0, 1.0, 500.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3008, 3018, 0, "11", 2, 1, 2, 196875.0, 0.0035, 2, "CATDOG_XMER                             ", 1, 1, 0.2051, 2, 0.25, 3, 0.4487, 4, 0.0962, "YNa0        ", null, 0.00044, 0.01276, 525.0, null, null, null, null, null, null, null, null, 230.0, 230.0, 0.0, 525.0, 475.0, 423.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 22, 2, 0.0, 0.0, 0.0, 13.8, 13.8, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3008, 3012, 3010, "2 ", 2, 1, 1, 0.00021, -0.00012, 1, "3WNDSTAT2                               ", 2, 5, 0.8, 3, 0.05, 2, 0.1, 1, 0.05, "D1y0y0      ", 0, 0.00515, 0.5, 20.0, 0.0092, 0.8, 15.0, 0.00375, 0.41667, 25.0, 0.98625, -30.5021, 230.0, 230.0, 0.0, 20.0, 18.0, 14.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 230.0, 230.0, 0.0, 15.0, 13.0, 9.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0, 21.6, 21.6, 30.0, 25.0, 19.0, 16.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1.1, 0.9, 1.1, 0.9, 33, 0, 0.0, 0.0, 0.0]]}, "area": {"fields": ["iarea", "isw", "pdes", "ptol", "arna<PERSON>"], "data": [[1, 101, -2800.0, 10.0, "CENTRAL     "], [2, 206, -1600.0, 10.0, "EAST        "], [3, 301, 2900.0, 55.0, "CENTRAL_DC  "], [4, 401, 300.0, 15.0, "EAST_COGEN1 "], [5, 3011, 900.0, 10.0, "WEST        "], [6, 402, 300.0, 20.0, "EAST_COGEN2 "]]}, "twotermdc": {"fields": ["name", "mdc", "rdc", "setvl", "vschd", "vcmod", "rcomp", "<PERSON><PERSON>", "met", "dcvmin", "cccitmx", "cccacc", "ipr", "nbr", "anmxr", "anmnr", "rcr", "xcr", "ebasr", "trr", "tapr", "tmxr", "tmnr", "stpr", "icr", "ndr", "ifr", "itr", "idr", "xcapr", "ipi", "nbi", "anmxi", "anmni", "rci", "xci", "ebasi", "tri", "tapi", "tmxi", "tmni", "stpi", "ici", "ndi", "ifi", "iti", "idi", "xcapi"], "data": [["TWO_TERM_DC1", 1, 7.8543, 1490.65, 525.0, 400.0, 3.942, 0.1556, "I", 0.0, 20, 1.0, 301, 2, 13.0, 7.5, 0.0111, 3.88, 500.0, 0.44, 1.06275, 1.1, 0.9, 0.00525, 0, 0, 0, 0, "1 ", 2.0034, 3021, 2, 21.0, 18.5, 0.0, 3.047, 230.0, 0.95652, 1.075, 1.1, 0.8, 0.00625, 0, 0, 152, 3021, "T4", 2.0], ["TWO_TERM_DC2", 1, 8.2, 1500.0, 525.0, 400.0, 4.1, 0.15, "I", 0.0, 20, 1.0, 301, 2, 12.0, 8.0, 0.011, 3.88, 500.0, 0.44, 1.0545, 1.112, 0.9, 0.00515, 0, 0, 0, 0, "1 ", 0.0098, 3022, 2, 20.0, 18.0, 0.012, 3.047, 230.0, 0.95652, 1.0625, 1.1, 0.8, 0.00625, 0, 0, 152, 3022, "T5", 0.0074]]}, "vscdc": {"fields": ["name", "mdc", "rdc", "o1", "f1", "o2", "f2", "o3", "f3", "o4", "f4", "ibus1", "type1", "mode1", "dcset1", "acset1", "aloss1", "bloss1", "minloss1", "smax1", "imax1", "pwf1", "maxq1", "minq1", "vsreg1", "nreg1", "rmpct1", "ibus2", "type2", "mode2", "dcset2", "acset2", "aloss2", "bloss2", "minloss2", "smax2", "imax2", "pwf2", "maxq2", "minq2", "vsreg2", "nreg2", "rmpct2"], "data": [["VDCLINE1    ", 1, 0.71, 1, 0.3204, 2, 0.3883, 3, 0.1942, 4, 0.0971, 3005, 2, 2, -209.0, 0.95, 100.0, 0.1, 50.0, 400.0, 1200.0, 0.1, 100.0, -110.0, 3005, 0, 100.0, 3008, 1, 1, 100.0, 0.99, 90.0, 0.15, 40.0, 350.0, 1200.0, 0.15, 150.0, -140.0, 3008, 0, 100.0], ["VDCLINE2    ", 1, 0.35, 1, 0.3705, 2, 0.3597, 3, 0.1799, 4, 0.0899, 203, 2, 1, -100.0, 1.0, 100.0, 0.15, 123.0, 200.0, 1200.0, 1.0, 200.0, -150.0, 203, 0, 100.0, 205, 1, 1, 100.0, 1.0, 93.0, 0.12, 98.0, 250.0, 1250.0, 1.0, 225.0, -250.0, 205, 0, 99.0]]}, "impcor": {"fields": ["itable", "tap", "refact", "imfact"], "data": [[1, -30.0, 1.1, 0.0], [1, -24.0, 1.091, 0.0], [1, -18.0, 1.084, 0.0], [1, -12.0, 1.063, 0.0], [1, -6.0, 1.032, 0.0], [1, 0.0, 1.0, 0.0], [1, 6.0, 1.03, 0.0], [1, 12.0, 1.06, 0.0], [1, 18.0, 1.08, 0.0], [1, 24.0, 1.09, 0.0], [1, 30.0, 1.11, 0.0], [2, 0.6, 1.06, 0.0], [2, 0.7, 1.05, 0.0], [2, 0.8, 1.04, 0.0], [2, 0.9, 1.03, 0.0], [2, 0.95, 1.02, 0.0], [2, 1.0, 1.01, 0.0], [2, 1.05, 0.99, 0.0], [2, 1.1, 0.98, 0.0], [2, 1.2, 0.97, 0.0], [2, 1.3, 0.96, 0.0], [2, 1.4, 0.95, 0.0], [3, 0.95, 1.0062, 0.00058], [3, 1.0, 1.0, 0.0], [3, 1.05, 0.99006, 0.00011], [4, 0.95, 0.96964, 0.00054], [4, 1.0, 1.0, 0.0], [4, 1.05, 1.02486, 0.00077], [5, 0.94, 1.0062, 0.00058], [5, 1.0, 1.0, 0.0], [5, 1.06, 0.99006, 0.00011], [6, 0.94, 0.96964, 0.00054], [6, 1.0, 1.0, 0.0], [6, 1.06, 1.02486, 0.00077]]}, "ntermdc": {"fields": ["name", "nconv", "ndcbs", "ndcln", "mdc", "vconv", "vcmod", "vconvn"], "data": [["MULTERM_DC_1", 4, 5, 4, 1, 212, 400.0, 0]]}, "ntermdcconv": {"fields": ["name", "ib", "nbrdg", "angmx", "angmn", "rc", "xc", "ebas", "tr", "tap", "tpmx", "tpmn", "tstp", "setvl", "dcpf", "marg", "cnvcod"], "data": [["MULTERM_DC_1", 402, 4, 10.0, 8.0, 0.0, 19.0, 500.0, 0.22, 1.01, 1.1, 0.97, 0.01, 321.0, 1.0, 0.15, 3], ["MULTERM_DC_1", 401, 4, 10.0, 8.0, 0.0, 19.0, 500.0, 0.22, 1.01, 1.1, 0.97, 0.01, 321.0, 1.0, 0.15, 3], ["MULTERM_DC_1", 212, 4, 20.0, 18.0, 0.0, 10.0, 230.0, 0.452, 1.04, 1.1, 0.9, 0.01, 500.0, 1.0, 0.0, 1], ["MULTERM_DC_1", 213, 4, 20.0, 18.0, 0.0, 10.0, 230.0, 0.452, 1.1, 1.1, 0.9, 0.01, -303.8, 1.0, 0.0, 4]]}, "ntermdcbus": {"fields": ["name", "idc", "ib", "area", "zone", "dcname", "idc2", "rgrnd", "owner"], "data": [["MULTERM_DC_1", 1, 401, 4, 4, "DC1         ", 0, 0.0, 4], ["MULTERM_DC_1", 2, 212, 2, 2, "DC2         ", 0, 0.0, 2], ["MULTERM_DC_1", 3, 402, 4, 4, "DC3         ", 0, 0.0, 4], ["MULTERM_DC_1", 4, 213, 2, 2, "DC4         ", 0, 0.0, 2], ["MULTERM_DC_1", 5, 0, 4, 4, "DC5         ", 0, 0.0, 4]]}, "ntermdclink": {"fields": ["name", "idc", "jdc", "dcckt", "met", "rdc", "ldc"], "data": [["MULTERM_DC_1", 1, 5, "1", 1, 29.0, 0.0], ["MULTERM_DC_1", 2, 5, "1", 1, 29.0, 0.0], ["MULTERM_DC_1", 3, 5, "1", 1, 29.0, 0.0], ["MULTERM_DC_1", 4, 5, "1", 1, 29.0, 0.0]]}, "msline": {"fields": ["ibus", "jbus", "mslid", "met", "dum1", "dum2", "dum3", "dum4", "dum5", "dum6", "dum7", "dum8", "dum9"], "data": [[201, 204, "&1", 2, 207, null, null, null, null, null, null, null, null]]}, "zone": {"fields": ["izone", "zoname"], "data": [[1, "NORTH_A1    "], [2, "MID_A1_A2_A5"], [3, "DISCNT_IN_A1"], [4, "SOUTH_A1_A5 "], [5, "ALL_A3      "], [6, "NORTH_A5    "], [7, "NORTH_A2    "], [8, "SOUTH_A2    "], [9, "ALL_A4_A6   "]]}, "iatrans": {"fields": ["arfrom", "arto", "trid", "ptran"], "data": [[1, 2, "A", 1000.0], [1, 5, "B", -3800.0], [2, 4, "C", -300.0], [2, 6, "E", -300.0], [3, 5, "D", 2900.0]]}, "owner": {"fields": ["iowner", "owname"], "data": [[1, "OWNER 1     "], [2, "OWNER 2     "], [3, "OWNER 3     "], [4, "OWNER 4     "], [5, "OWNER 5     "]]}, "facts": {"fields": ["name", "ibus", "jbus", "mode", "pdes", "qdes", "vset", "shmx", "trmx", "vtmn", "vtmx", "vsmx", "imx", "linx", "rmpct", "owner", "set1", "set2", "vsref", "fcreg", "nreg", "mname"], "data": [["FACTS_DVCE_1", 153, 0, 1, 0.0, 0.0, 1.015, 50.0, 100.0, 0.9263, 1.134, 1.0, 0.0, 0.05652, 100.0, 1, 0.0, 0.0, 0, 153, 0, "            "], ["FACTS_DVCE_2", 153, 155, 1, 350.0, 40.0, 1.015, 25.0, 9999.0, 0.9, 1.1, 1.0, 0.0, 0.05, 100.0, 1, 0.0, 0.0, 0, 153, 0, "            "]]}, "swshunt": {"fields": ["ibus", "s<PERSON><PERSON><PERSON>", "modsw", "adjm", "stat", "vswhi", "vswlo", "swreg", "nreg", "rmpct", "rmidnt", "binit", "s1", "n1", "b1", "s2", "n2", "b2", "s3", "n3", "b3", "s4", "n4", "b4", "s5", "n5", "b5", "s6", "n6", "b6", "s7", "n7", "b7", "s8", "n8", "b8"], "data": [[152, "1 ", 1, 0, 1, 1.045, 0.955, 152, 0, 100.0, "            ", -233.0, 1, 1, -15.0, 1, 2, -5.0, 1, 3, -10.0, 1, 4, -8.0, 1, 5, -7.0, 1, 6, -5.0, 1, 7, -7.0, 1, 8, -4.0], [154, "1 ", 1, 0, 1, 1.0448, 0.965, 154, 0, 100.0, "            ", 124.0, 1, 1, 25.0, 1, 2, 10.0, 1, 2, 15.0, 1, 1, 15.0, 1, 2, 5.0, 1, 3, 3.0, 1, 2, 4.0, 1, 1, 7.0], [3005, "1 ", 4, 0, 1, 0.98, 0.64, 3005, 0, 100.0, "VDCLINE1    ", 0.0, 1, 1, 33.35, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [3021, "1 ", 2, 0, 1, 1.0, 1.0, 3021, 0, 100.0, "            ", 493.4, 1, 2, 200.0, 1, 1, 100.0, 1, 2, 50.0, 1, 4, 25.0, null, null, null, null, null, null, null, null, null, null, null, null], [3022, "1 ", 2, 0, 1, 1.0, 1.0, 3022, 0, 100.0, "            ", 594.28, 1, 4, 100.0, 1, 2, 50.0, 1, 4, 25.0, 1, 3, 20.0, 1, 2, 20.0, null, null, null, null, null, null, null, null, null], [93002, "1 ", 1, 0, 1, 0.99628, 0.99628, 93002, 0, 100.0, "            ", 1.44, 1, 1, 1.44, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]]}, "gne": {"fields": ["name", "model", "nterm", "bus1", "bus2", "nreal", "nintg", "nchar", "stat", "owner", "nmet", "real1", "real2", "real3", "real4", "real5", "real6", "real7", "real8", "real9", "real10", "intg1", "intg2", "intg3", "intg4", "intg5", "intg6", "intg7", "intg8", "intg9", "intg10", "char1", "char2", "char3", "char4", "char5", "char6", "char7", "char8", "char9", "char10"], "data": []}, "indmach": {"fields": ["ibus", "imid", "stat", "sc", "dc", "area", "zone", "owner", "tc", "bc", "mbase", "ratekv", "pcode", "pset", "hconst", "aconst", "bconst", "dconst", "econst", "ra", "xa", "xm", "r1", "x1", "r2", "x2", "x3", "e1", "se1", "e2", "se2", "ia1", "ia2", "xamult"], "data": [[3010, "1 ", 1, 1, 2, 5, 4, 5, 1, 1, 1.0, 21.6, 1, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [9154, "1 ", 1, 1, 2, 1, 3, 1, 1, 2, 2.27, 4.16, 2, -2.0, 1.0, 1.0, 1.0, -1.0, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [9204, "1 ", 1, 1, 2, 2, 8, 2, 1, 1, 10.0, 0.575, 1, 10.0, 1.0, 1.0, 1.0, 1.0, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], [93002, "1 ", 1, 1, 2, 5, 6, 5, 1, 2, 5.62, 0.69, 2, -5.0, 1.0, 1.0, 1.0, -1.0, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]]}, "sub": {"fields": ["isub", "name", "lati", "long", "srg"], "data": [[1, "SS01_NILE_TYP_3_DBDB                    ", 34.6134987, -86.6737137, 0.11], [2, "SS02_YANGTZE_TYP_6_MBTB                 ", 32.5103989, -86.365799, 0.12], [3, "SS03_ARKANSAS_TYP_4_BH                  ", 32.1551018, -83.6793976, 0.13], [4, "SS04_COLORADO_TYP_5_DBSB                ", 33.705101, -84.6633987, 0.15], [5, "SS05_MISSISSIPPI_TYP_5_DBSB             ", 33.3773003, -82.6187973, 0.16], [6, "SS06_VOLGA_TYP_1_SB                     ", 34.2522011, -82.8363037, 0.17], [7, "SS07_YUKON_TYP_4_BH                     ", 33.5956001, -88.7979965, 0.18], [8, "SS08_BRAHMAPUTRA_TYP_4_BH               ", 31.9123001, -88.3123016, 0.19], [9, "SS09_INDUS_TYP_6_MBTB                   ", 31.0132999, -82.013298, 0.2], [10, "SS10_DANUBE_TYP_4_BH                    ", 32.0143013, -82.5142975, 0.21], [11, "SS11_ALLEGHENY_TYP_2_RB                 ", 35.2153015, -86.0152969, 0.22], [12, "SS12_GANGES_TYP_2_RB                    ", 33.5163002, -81.0162964, 0.23], [13, "SS13_OXUS_TYP_1_SB                      ", 35.0172997, -82.0173035, 0.24], [14, "SS14_SALWEEN_TYP_1_SB                   ", 34.7182999, -81.0183029, 0.25], [15, "SS15_HEILONG_TYP_3_DBDB                 ", 35.0192986, -84.0193024, 0.26], [16, "SS16_ZAIRE_TYP_4_BH                     ", 35.0029984, -87.5203018, 0.27], [17, "SS17_ZAMBEZI_TYP_3_DBDB                 ", 31.4213028, -85.7212982, 0.28], [18, "SS18_PILCOMAYO_TYP_2_RB                 ", 31.4223003, -81.0223007, 0.29]]}, "subnode": {"fields": ["isub", "inode", "name", "ibus", "stat", "vm", "va"], "data": [[1, 1, "SS_NILE_NODE_1                          ", 101, 1, null, null], [1, 2, "SS_NILE_NODE_2                          ", 101, 1, null, null], [1, 3, "SS_NILE_NODE_3                          ", 101, 1, null, null], [1, 4, "SS_NILE_NODE_4                          ", 101, 1, null, null], [1, 5, "SS_NILE_NODE_5                          ", 102, 1, null, null], [1, 6, "SS_NILE_NODE_6                          ", 102, 1, null, null], [1, 7, "SS_NILE_NODE_7                          ", 102, 1, null, null], [1, 8, "SS_NILE_NODE_8                          ", 102, 1, null, null], [1, 9, "SS_NILE_NODE_9                          ", 151, 1, null, null], [1, 10, "SS_NILE_NODE_10                         ", 151, 1, null, null], [1, 11, "SS_NILE_NODE_11                         ", 151, 1, null, null], [1, 12, "SS_NILE_NODE_12                         ", 151, 1, null, null], [1, 13, "SS_NILE_NODE_13                         ", 151, 1, null, null], [1, 14, "SS_NILE_NODE_14                         ", 151, 1, null, null], [1, 15, "SS_NILE_NODE_15                         ", 151, 1, null, null], [1, 16, "SS_NILE_NODE_16                         ", 151, 1, null, null], [1, 17, "SS_NILE_NODE_17                         ", 151, 1, null, null], [1, 18, "SS_NILE_NODE_18                         ", 151, 1, null, null], [1, 19, "SS_NILE_NODE_19                         ", 201, 1, null, null], [1, 20, "SS_NILE_NODE_20                         ", 201, 1, null, null], [1, 21, "SS_NILE_NODE_21                         ", 201, 1, null, null], [1, 22, "SS_NILE_NODE_22                         ", 201, 1, null, null], [1, 23, "SS_NILE_NODE_23                         ", 201, 1, null, null], [1, 24, "SS_NILE_NODE_24                         ", 201, 1, null, null], [1, 25, "SS_NILE_NODE_25                         ", 201, 1, null, null], [1, 26, "SS_NILE_NODE_26                         ", 201, 1, null, null], [1, 27, "SS_NILE_NODE_27                         ", 211, 1, null, null], [1, 28, "SS_NILE_NODE_28                         ", 211, 1, null, null], [1, 29, "SS_NILE_NODE_29                         ", 211, 1, null, null], [1, 30, "SS_NILE_NODE_30                         ", 211, 1, null, null], [2, 1, "SS_YANGTZE_NODE_1                       ", 152, 1, null, null], [2, 2, "SS_YANGTZE_NODE_2                       ", 152, 0, null, null], [2, 3, "SS_YANGTZE_NODE_3                       ", 152, 1, null, null], [2, 4, "SS_YANGTZE_NODE_4                       ", 152, 1, null, null], [2, 5, "SS_YANGTZE_NODE_5                       ", 152, 1, null, null], [2, 6, "SS_YANGTZE_NODE_6                       ", 152, 1, null, null], [2, 7, "SS_YANGTZE_NODE_7                       ", 152, 1, null, null], [2, 8, "SS_YANGTZE_NODE_8                       ", 152, 1, null, null], [2, 9, "SS_YANGTZE_NODE_9                       ", 152, 1, null, null], [2, 10, "SS_YANGTZE_NODE_10                      ", 152, 1, null, null], [2, 11, "SS_YANGTZE_NODE_11                      ", 152, 1, null, null], [2, 12, "SS_YANGTZE_NODE_12                      ", 152, 1, null, null], [2, 13, "SS_YANGTZE_NODE_13                      ", 153, 1, null, null], [2, 14, "SS_YANGTZE_NODE_14                      ", 153, 0, null, null], [2, 15, "SS_YANGTZE_NODE_15                      ", 153, 1, null, null], [2, 16, "SS_YANGTZE_NODE_16                      ", 153, 1, null, null], [2, 17, "SS_YANGTZE_NODE_17                      ", 153, 1, null, null], [2, 18, "SS_YANGTZE_NODE_18                      ", 153, 1, null, null], [2, 19, "SS_YANGTZE_NODE_19                      ", 153, 1, null, null], [2, 20, "SS_YANGTZE_NODE_20                      ", 153, 1, null, null], [2, 21, "SS_YANGTZE_NODE_21                      ", 3006, 1, null, null], [2, 22, "SS_YANGTZE_NODE_22                      ", 3006, 0, null, null], [2, 23, "SS_YANGTZE_NODE_23                      ", 3006, 1, null, null], [2, 24, "SS_YANGTZE_NODE_24                      ", 3006, 1, null, null], [2, 25, "SS_YANGTZE_NODE_25                      ", 3021, 1, null, null], [2, 26, "SS_YANGTZE_NODE_26                      ", 3021, 0, null, null], [2, 27, "SS_YANGTZE_NODE_27                      ", 3021, 1, null, null], [2, 28, "SS_YANGTZE_NODE_28                      ", 3021, 1, null, null], [2, 29, "SS_YANGTZE_NODE_29                      ", 3021, 1, null, null], [2, 30, "SS_YANGTZE_NODE_30                      ", 3021, 1, null, null], [2, 31, "SS_YANGTZE_NODE_31                      ", 3022, 1, null, null], [2, 32, "SS_YANGTZE_NODE_32                      ", 3022, 0, null, null], [2, 33, "SS_YANGTZE_NODE_33                      ", 3022, 1, null, null], [2, 34, "SS_YANGTZE_NODE_34                      ", 3022, 1, null, null], [2, 35, "SS_YANGTZE_NODE_35                      ", 3022, 1, null, null], [2, 36, "SS_YANGTZE_NODE_36                      ", 3022, 1, null, null], [3, 1, "SS_ARKANSAS_NODE_1                      ", 154, 1, null, null], [3, 2, "SS_ARKANSAS_NODE_2                      ", 154, 1, null, null], [3, 3, "SS_ARKANSAS_NODE_3                      ", 154, 1, null, null], [3, 4, "SS_ARKANSAS_NODE_4                      ", 154, 1, null, null], [3, 5, "SS_ARKANSAS_NODE_5                      ", 154, 1, null, null], [3, 6, "SS_ARKANSAS_NODE_6                      ", 154, 1, null, null], [3, 7, "SS_ARKANSAS_NODE_7                      ", 154, 1, null, null], [3, 8, "SS_ARKANSAS_NODE_8                      ", 154, 1, null, null], [3, 9, "SS_ARKANSAS_NODE_9                      ", 154, 1, null, null], [3, 10, "SS_ARKANSAS_NODE_10                     ", 154, 1, null, null], [3, 11, "SS_ARKANSAS_NODE_11                     ", 154, 1, null, null], [3, 12, "SS_ARKANSAS_NODE_12                     ", 154, 1, null, null], [3, 13, "SS_ARKANSAS_NODE_13                     ", 154, 1, null, null], [3, 14, "SS_ARKANSAS_NODE_14                     ", 154, 1, null, null], [3, 15, "SS_ARKANSAS_NODE_15                     ", 9154, 1, null, null], [3, 16, "SS_ARKANSAS_NODE_16                     ", 9154, 1, null, null], [3, 17, "SS_ARKANSAS_NODE_17                     ", 9154, 1, null, null], [3, 18, "SS_ARKANSAS_NODE_18                     ", 9154, 1, null, null], [4, 1, "SS_COLORADO_NODE_1                      ", 202, 1, null, null], [4, 2, "SS_COLORADO_NODE_2                      ", 202, 1, null, null], [4, 3, "SS_COLORADO_NODE_3                      ", 202, 1, null, null], [4, 4, "SS_COLORADO_NODE_4                      ", 202, 1, null, null], [4, 5, "SS_COLORADO_NODE_5                      ", 202, 1, null, null], [4, 6, "SS_COLORADO_NODE_6                      ", 202, 1, null, null], [4, 7, "SS_COLORADO_NODE_7                      ", 202, 1, null, null], [4, 8, "SS_COLORADO_NODE_8                      ", 202, 1, null, null], [4, 9, "SS_COLORADO_NODE_9                      ", 203, 1, null, null], [4, 10, "SS_COLORADO_NODE_10                     ", 203, 1, null, null], [4, 11, "SS_COLORADO_NODE_11                     ", 203, 1, null, null], [4, 12, "SS_COLORADO_NODE_12                     ", 203, 1, null, null], [4, 13, "SS_COLORADO_NODE_13                     ", 203, 1, null, null], [4, 14, "SS_COLORADO_NODE_14                     ", 203, 1, null, null], [4, 15, "SS_COLORADO_NODE_15                     ", 203, 1, null, null], [4, 16, "SS_COLORADO_NODE_16                     ", 203, 1, null, null], [4, 17, "SS_COLORADO_NODE_17                     ", 203, 1, null, null], [4, 18, "SS_COLORADO_NODE_18                     ", 203, 1, null, null], [4, 19, "SS_COLORADO_NODE_19                     ", 203, 1, null, null], [4, 20, "SS_COLORADO_NODE_20                     ", 203, 1, null, null], [4, 21, "SS_COLORADO_NODE_21                     ", 203, 1, null, null], [4, 22, "SS_COLORADO_NODE_22                     ", 203, 1, null, null], [4, 23, "SS_COLORADO_NODE_23                     ", 203, 1, null, null], [4, 24, "SS_COLORADO_NODE_24                     ", 203, 1, null, null], [5, 1, "SS_MISSISSIPPI_NODE_1                   ", 204, 1, null, null], [5, 2, "SS_MISSISSIPPI_NODE_2                   ", 204, 1, null, null], [5, 3, "SS_MISSISSIPPI_NODE_3                   ", 204, 1, null, null], [5, 4, "SS_MISSISSIPPI_NODE_4                   ", 204, 1, null, null], [5, 5, "SS_MISSISSIPPI_NODE_5                   ", 204, 1, null, null], [5, 6, "SS_MISSISSIPPI_NODE_6                   ", 204, 1, null, null], [5, 7, "SS_MISSISSIPPI_NODE_7                   ", 204, 1, null, null], [5, 8, "SS_MISSISSIPPI_NODE_8                   ", 204, 1, null, null], [5, 9, "SS_MISSISSIPPI_NODE_9                   ", 205, 1, null, null], [5, 10, "SS_MISSISSIPPI_NODE_10                  ", 205, 1, null, null], [5, 11, "SS_MISSISSIPPI_NODE_11                  ", 205, 1, null, null], [5, 12, "SS_MISSISSIPPI_NODE_12                  ", 205, 1, null, null], [5, 13, "SS_MISSISSIPPI_NODE_13                  ", 205, 1, null, null], [5, 14, "SS_MISSISSIPPI_NODE_14                  ", 205, 1, null, null], [5, 15, "SS_MISSISSIPPI_NODE_15                  ", 205, 1, null, null], [5, 16, "SS_MISSISSIPPI_NODE_16                  ", 205, 1, null, null], [5, 17, "SS_MISSISSIPPI_NODE_17                  ", 205, 1, null, null], [5, 18, "SS_MISSISSIPPI_NODE_18                  ", 205, 1, null, null], [5, 19, "SS_MISSISSIPPI_NODE_19                  ", 205, 1, null, null], [5, 20, "SS_MISSISSIPPI_NODE_20                  ", 205, 1, null, null], [5, 21, "SS_MISSISSIPPI_NODE_21                  ", 205, 1, null, null], [5, 22, "SS_MISSISSIPPI_NODE_22                  ", 205, 1, null, null], [5, 23, "SS_MISSISSIPPI_NODE_23                  ", 205, 1, null, null], [5, 24, "SS_MISSISSIPPI_NODE_24                  ", 205, 1, null, null], [5, 25, "SS_MISSISSIPPI_NODE_25                  ", 205, 1, null, null], [5, 26, "SS_MISSISSIPPI_NODE_26                  ", 205, 1, null, null], [5, 27, "SS_MISSISSIPPI_NODE_27                  ", 205, 1, null, null], [5, 28, "SS_MISSISSIPPI_NODE_28                  ", 205, 1, null, null], [5, 29, "SS_MISSISSIPPI_NODE_29                  ", 205, 1, null, null], [5, 30, "SS_MISSISSIPPI_NODE_30                  ", 205, 1, null, null], [5, 31, "SS_MISSISSIPPI_NODE_31                  ", 205, 1, null, null], [5, 32, "SS_MISSISSIPPI_NODE_32                  ", 205, 1, null, null], [5, 33, "SS_MISSISSIPPI_NODE_33                  ", 205, 1, null, null], [5, 34, "SS_MISSISSIPPI_NODE_34                  ", 205, 1, null, null], [5, 35, "SS_MISSISSIPPI_NODE_35                  ", 205, 1, null, null], [5, 36, "SS_MISSISSIPPI_NODE_36                  ", 205, 1, null, null], [5, 37, "SS_MISSISSIPPI_NODE_37                  ", 205, 1, null, null], [5, 38, "SS_MISSISSIPPI_NODE_38                  ", 205, 1, null, null], [5, 39, "SS_MISSISSIPPI_NODE_39                  ", 205, 1, null, null], [5, 40, "SS_MISSISSIPPI_NODE_40                  ", 205, 1, null, null], [5, 41, "SS_MISSISSIPPI_NODE_41                  ", 206, 1, null, null], [5, 42, "SS_MISSISSIPPI_NODE_42                  ", 206, 1, null, null], [5, 43, "SS_MISSISSIPPI_NODE_43                  ", 206, 1, null, null], [5, 44, "SS_MISSISSIPPI_NODE_44                  ", 206, 1, null, null], [5, 45, "SS_MISSISSIPPI_NODE_45                  ", 206, 1, null, null], [5, 46, "SS_MISSISSIPPI_NODE_46                  ", 206, 1, null, null], [5, 47, "SS_MISSISSIPPI_NODE_47                  ", 208, 0, null, null], [5, 48, "SS_MISSISSIPPI_NODE_48                  ", 208, 0, null, null], [5, 49, "SS_MISSISSIPPI_NODE_49                  ", 208, 0, null, null], [5, 50, "SS_MISSISSIPPI_NODE_50                  ", 208, 0, null, null], [5, 51, "SS_MISSISSIPPI_NODE_51                  ", 215, 1, null, null], [5, 52, "SS_MISSISSIPPI_NODE_52                  ", 215, 1, null, null], [5, 53, "SS_MISSISSIPPI_NODE_53                  ", 215, 1, null, null], [5, 54, "SS_MISSISSIPPI_NODE_54                  ", 215, 1, null, null], [5, 55, "SS_MISSISSIPPI_NODE_55                  ", 215, 1, null, null], [5, 56, "SS_MISSISSIPPI_NODE_56                  ", 215, 1, null, null], [5, 57, "SS_MISSISSIPPI_NODE_57                  ", 9204, 1, null, null], [5, 58, "SS_MISSISSIPPI_NODE_58                  ", 9204, 1, null, null], [5, 59, "SS_MISSISSIPPI_NODE_59                  ", 9204, 1, null, null], [5, 60, "SS_MISSISSIPPI_NODE_60                  ", 9204, 1, null, null], [5, 61, "SS_MISSISSIPPI_NODE_61                  ", 9204, 1, null, null], [5, 62, "SS_MISSISSIPPI_NODE_62                  ", 9204, 1, null, null], [6, 1, "SS_VOLGA_NODE_1                         ", 209, 0, null, null], [6, 2, "SS_VOLGA_NODE_2                         ", 209, 0, null, null], [6, 3, "SS_VOLGA_NODE_3                         ", 217, 1, null, null], [6, 4, "SS_VOLGA_NODE_4                         ", 217, 1, null, null], [6, 5, "SS_VOLGA_NODE_5                         ", 217, 1, null, null], [6, 6, "SS_VOLGA_NODE_6                         ", 217, 1, null, null], [6, 7, "SS_VOLGA_NODE_7                         ", 218, 1, null, null], [6, 8, "SS_VOLGA_NODE_8                         ", 218, 1, null, null], [6, 9, "SS_VOLGA_NODE_9                         ", 218, 1, null, null], [6, 10, "SS_VOLGA_NODE_10                        ", 218, 1, null, null], [7, 1, "SS_YUKON_NODE_1                         ", 3001, 1, null, null], [7, 2, "SS_YUKON_NODE_2                         ", 3001, 1, null, null], [7, 3, "SS_YUKON_NODE_3                         ", 3001, 1, null, null], [7, 4, "SS_YUKON_NODE_4                         ", 3001, 1, null, null], [7, 5, "SS_YUKON_NODE_5                         ", 3002, 1, null, null], [7, 6, "SS_YUKON_NODE_6                         ", 3002, 1, null, null], [7, 7, "SS_YUKON_NODE_7                         ", 3002, 1, null, null], [7, 8, "SS_YUKON_NODE_8                         ", 3002, 1, null, null], [7, 9, "SS_YUKON_NODE_9                         ", 3002, 1, null, null], [7, 10, "SS_YUKON_NODE_10                        ", 3002, 1, null, null], [7, 11, "SS_YUKON_NODE_11                        ", 3011, 1, null, null], [7, 12, "SS_YUKON_NODE_12                        ", 3011, 1, null, null], [7, 13, "SS_YUKON_NODE_13                        ", 3011, 1, null, null], [7, 14, "SS_YUKON_NODE_14                        ", 3011, 1, null, null], [7, 15, "SS_YUKON_NODE_15                        ", 93002, 1, null, null], [7, 16, "SS_YUKON_NODE_16                        ", 93002, 1, null, null], [7, 17, "SS_YUKON_NODE_17                        ", 93002, 1, null, null], [7, 18, "SS_YUKON_NODE_18                        ", 93002, 1, null, null], [7, 19, "SS_YUKON_NODE_19                        ", 93002, 1, null, null], [7, 20, "SS_YUKON_NODE_20                        ", 93002, 1, null, null], [8, 1, "SS_BRAHMAPUTRA_NODE_1                   ", 3004, 1, null, null], [8, 2, "SS_BRAHMAPUTRA_NODE_2                   ", 3004, 1, null, null], [8, 3, "SS_BRAHMAPUTRA_NODE_3                   ", 3004, 1, null, null], [8, 4, "SS_BRAHMAPUTRA_NODE_4                   ", 3004, 1, null, null], [8, 5, "SS_BRAHMAPUTRA_NODE_5                   ", 3004, 1, null, null], [8, 6, "SS_BRAHMAPUTRA_NODE_6                   ", 3004, 1, null, null], [8, 7, "SS_BRAHMAPUTRA_NODE_7                   ", 3005, 1, null, null], [8, 8, "SS_BRAHMAPUTRA_NODE_8                   ", 3005, 1, null, null], [8, 9, "SS_BRAHMAPUTRA_NODE_9                   ", 3005, 1, null, null], [8, 10, "SS_BRAHMAPUTRA_NODE_10                  ", 3005, 1, null, null], [8, 11, "SS_BRAHMAPUTRA_NODE_11                  ", 3005, 1, null, null], [8, 12, "SS_BRAHMAPUTRA_NODE_12                  ", 3005, 1, null, null], [8, 13, "SS_BRAHMAPUTRA_NODE_13                  ", 3005, 1, null, null], [8, 14, "SS_BRAHMAPUTRA_NODE_14                  ", 3005, 1, null, null], [8, 15, "SS_BRAHMAPUTRA_NODE_15                  ", 3005, 1, null, null], [8, 16, "SS_BRAHMAPUTRA_NODE_16                  ", 3005, 1, null, null], [8, 17, "SS_BRAHMAPUTRA_NODE_17                  ", 3005, 1, null, null], [8, 18, "SS_BRAHMAPUTRA_NODE_18                  ", 3005, 1, null, null], [9, 1, "SS_INDUS_NODE_1                         ", 3008, 1, null, null], [9, 2, "SS_INDUS_NODE_2                         ", 3008, 0, null, null], [9, 3, "SS_INDUS_NODE_3                         ", 3008, 1, null, null], [9, 4, "SS_INDUS_NODE_4                         ", 3008, 1, null, null], [9, 5, "SS_INDUS_NODE_5                         ", 3008, 1, null, null], [9, 6, "SS_INDUS_NODE_6                         ", 3008, 1, null, null], [9, 7, "SS_INDUS_NODE_7                         ", 3008, 1, null, null], [9, 8, "SS_INDUS_NODE_8                         ", 3008, 1, null, null], [9, 9, "SS_INDUS_NODE_9                         ", 3008, 1, null, null], [9, 10, "SS_INDUS_NODE_10                        ", 3008, 1, null, null], [9, 11, "SS_INDUS_NODE_11                        ", 3010, 1, null, null], [9, 12, "SS_INDUS_NODE_12                        ", 3010, 0, null, null], [9, 13, "SS_INDUS_NODE_13                        ", 3010, 1, null, null], [9, 14, "SS_INDUS_NODE_14                        ", 3010, 1, null, null], [9, 15, "SS_INDUS_NODE_15                        ", 3010, 1, null, null], [9, 16, "SS_INDUS_NODE_16                        ", 3012, 0, null, null], [9, 17, "SS_INDUS_NODE_17                        ", 3012, 0, null, null], [9, 18, "SS_INDUS_NODE_18                        ", 3012, 0, null, null], [9, 19, "SS_INDUS_NODE_19                        ", 3018, 1, null, null], [9, 20, "SS_INDUS_NODE_20                        ", 3018, 0, null, null], [9, 21, "SS_INDUS_NODE_21                        ", 3018, 1, null, null], [9, 22, "SS_INDUS_NODE_22                        ", 3018, 1, null, null], [9, 23, "SS_INDUS_NODE_23                        ", 3018, 1, null, null], [10, 1, "SS_DANUBE_NODE_1                        ", 155, 1, null, null], [10, 2, "SS_DANUBE_NODE_2                        ", 155, 1, null, null], [10, 3, "SS_DANUBE_NODE_3                        ", 155, 1, null, null], [10, 4, "SS_DANUBE_NODE_4                        ", 155, 1, null, null], [11, 1, "SS_ALLEGHENY_NODE_1                     ", 207, 1, null, null], [11, 2, "SS_ALLEGHENY_NODE_2                     ", 207, 1, null, null], [12, 1, "SS_GANGES_NODE_1                        ", 212, 1, null, null], [12, 2, "SS_GANGES_NODE_2                        ", 212, 1, null, null], [12, 3, "SS_GANGES_NODE_3                        ", 212, 1, null, null], [13, 1, "SS_OXUS_NODE_1                          ", 214, 1, null, null], [13, 2, "SS_OXUS_NODE_2                          ", 214, 1, null, null], [13, 3, "SS_OXUS_NODE_3                          ", 214, 1, null, null], [13, 4, "SS_OXUS_NODE_4                          ", 214, 1, null, null], [14, 1, "SS_SALWEEN_NODE_1                       ", 216, 1, null, null], [14, 2, "SS_SALWEEN_NODE_2                       ", 216, 1, null, null], [14, 3, "SS_SALWEEN_NODE_3                       ", 216, 1, null, null], [15, 1, "SS_HEILONG_NODE_1                       ", 213, 1, null, null], [15, 2, "SS_HEILONG_NODE_2                       ", 213, 1, null, null], [15, 3, "SS_HEILONG_NODE_3                       ", 213, 1, null, null], [15, 4, "SS_HEILONG_NODE_4                       ", 213, 1, null, null], [15, 5, "SS_HEILONG_NODE_5                       ", 213, 1, null, null], [16, 1, "SS_ZAIRE_NODE_1                         ", 3003, 1, null, null], [16, 2, "SS_ZAIRE_NODE_2                         ", 3003, 1, null, null], [16, 3, "SS_ZAIRE_NODE_3                         ", 3003, 1, null, null], [16, 4, "SS_ZAIRE_NODE_4                         ", 3003, 1, null, null], [16, 5, "SS_ZAIRE_NODE_5                         ", 3003, 1, null, null], [16, 6, "SS_ZAIRE_NODE_6                         ", 3003, 1, null, null], [17, 1, "SS_ZAMBEZI_NODE_1                       ", 3007, 1, null, null], [17, 2, "SS_ZAMBEZI_NODE_2                       ", 3007, 1, null, null], [17, 3, "SS_ZAMBEZI_NODE_3                       ", 3007, 1, null, null], [17, 4, "SS_ZAMBEZI_NODE_4                       ", 3007, 1, null, null], [17, 5, "SS_ZAMBEZI_NODE_5                       ", 3007, 1, null, null], [18, 1, "SS_PILCOMAYO_NODE_1                     ", 3009, 1, null, null], [18, 2, "SS_PILCOMAYO_NODE_2                     ", 3009, 1, null, null]]}, "subswd": {"fields": ["isub", "inode", "jnode", "swdid", "name", "type", "stat", "nstat", "xpu", "rate1", "rate2", "rate3"], "data": [[1, 1, 3, "1 ", "SS_NILE_SWD_1_3_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 1, 4, "1 ", "SS_NILE_SWD_1_4_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 2, 3, "1 ", "SS_NILE_SWD_2_3_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 2, 4, "1 ", "SS_NILE_SWD_2_4_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 5, 7, "1 ", "SS_NILE_SWD_5_7_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 5, 8, "1 ", "SS_NILE_SWD_5_8_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 6, 7, "1 ", "SS_NILE_SWD_6_7_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 6, 8, "1 ", "SS_NILE_SWD_6_8_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 11, "1 ", "SS_NILE_SWD_9_11_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 12, "1 ", "SS_NILE_SWD_9_12_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 13, "1 ", "SS_NILE_SWD_9_13_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 14, "1 ", "SS_NILE_SWD_9_14_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 15, "1 ", "SS_NILE_SWD_9_15_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 16, "1 ", "SS_NILE_SWD_9_16_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 17, "1 ", "SS_NILE_SWD_9_17_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 9, 18, "1 ", "SS_NILE_SWD_9_18_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 11, "1 ", "SS_NILE_SWD_10_11_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 12, "1 ", "SS_NILE_SWD_10_12_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 13, "1 ", "SS_NILE_SWD_10_13_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 14, "1 ", "SS_NILE_SWD_10_14_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 15, "1 ", "SS_NILE_SWD_10_15_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 16, "1 ", "SS_NILE_SWD_10_16_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 17, "1 ", "SS_NILE_SWD_10_17_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 10, 18, "1 ", "SS_NILE_SWD_10_18_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 21, "1 ", "SS_NILE_SWD_19_21_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 22, "1 ", "SS_NILE_SWD_19_22_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 23, "1 ", "SS_NILE_SWD_19_23_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 24, "1 ", "SS_NILE_SWD_19_24_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 25, "1 ", "SS_NILE_SWD_19_25_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 19, 26, "1 ", "SS_NILE_SWD_19_26_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 21, "1 ", "SS_NILE_SWD_20_21_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 22, "1 ", "SS_NILE_SWD_20_22_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 23, "1 ", "SS_NILE_SWD_20_23_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 24, "1 ", "SS_NILE_SWD_20_24_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 25, "1 ", "SS_NILE_SWD_20_25_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 20, 26, "1 ", "SS_NILE_SWD_20_26_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 27, 29, "1 ", "SS_NILE_SWD_27_29_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 27, 30, "1 ", "SS_NILE_SWD_27_30_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 28, 29, "1 ", "SS_NILE_SWD_28_29_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [1, 28, 30, "1 ", "SS_NILE_SWD_28_30_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 2, "1 ", "SS_YANGTZE_SWD_1_2_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 1, 3, "1 ", "SS_YANGTZE_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 4, "1 ", "SS_YANGTZE_SWD_1_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 5, "1 ", "SS_YANGTZE_SWD_1_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 6, "1 ", "SS_YANGTZE_SWD_1_6_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 7, "1 ", "SS_YANGTZE_SWD_1_7_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 8, "1 ", "SS_YANGTZE_SWD_1_8_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 9, "1 ", "SS_YANGTZE_SWD_1_9_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 10, "1 ", "SS_YANGTZE_SWD_1_10_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 11, "1 ", "SS_YANGTZE_SWD_1_11_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 1, 12, "1 ", "SS_YANGTZE_SWD_1_12_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 2, 3, "1 ", "SS_YANGTZE_SWD_2_3_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 4, "1 ", "SS_YANGTZE_SWD_2_4_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 5, "1 ", "SS_YANGTZE_SWD_2_5_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 6, "1 ", "SS_YANGTZE_SWD_2_6_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 7, "1 ", "SS_YANGTZE_SWD_2_7_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 8, "1 ", "SS_YANGTZE_SWD_2_8_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 9, "1 ", "SS_YANGTZE_SWD_2_9_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 10, "1 ", "SS_YANGTZE_SWD_2_10_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 11, "1 ", "SS_YANGTZE_SWD_2_11_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 2, 12, "1 ", "SS_YANGTZE_SWD_2_12_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 13, 14, "1 ", "SS_YANGTZE_SWD_13_14_1                  ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 13, 15, "1 ", "SS_YANGTZE_SWD_13_15_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 13, 16, "1 ", "SS_YANGTZE_SWD_13_16_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 13, 17, "1 ", "SS_YANGTZE_SWD_13_17_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 13, 18, "1 ", "SS_YANGTZE_SWD_13_18_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 13, 19, "1 ", "SS_YANGTZE_SWD_13_19_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 13, 20, "1 ", "SS_YANGTZE_SWD_13_20_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 14, 15, "1 ", "SS_YANGTZE_SWD_14_15_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 14, 16, "1 ", "SS_YANGTZE_SWD_14_16_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 14, 17, "1 ", "SS_YANGTZE_SWD_14_17_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 14, 18, "1 ", "SS_YANGTZE_SWD_14_18_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 14, 19, "1 ", "SS_YANGTZE_SWD_14_19_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 14, 20, "1 ", "SS_YANGTZE_SWD_14_20_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 21, 22, "1 ", "SS_YANGTZE_SWD_21_22_1                  ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 21, 23, "1 ", "SS_YANGTZE_SWD_21_23_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 21, 24, "1 ", "SS_YANGTZE_SWD_21_24_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 22, 23, "1 ", "SS_YANGTZE_SWD_22_23_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 22, 24, "1 ", "SS_YANGTZE_SWD_22_24_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 25, 26, "1 ", "SS_YANGTZE_SWD_25_26_1                  ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 25, 27, "1 ", "SS_YANGTZE_SWD_25_27_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 25, 28, "1 ", "SS_YANGTZE_SWD_25_28_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 25, 29, "1 ", "SS_YANGTZE_SWD_25_29_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 25, 30, "1 ", "SS_YANGTZE_SWD_25_30_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 26, 27, "1 ", "SS_YANGTZE_SWD_26_27_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 26, 28, "1 ", "SS_YANGTZE_SWD_26_28_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 26, 29, "1 ", "SS_YANGTZE_SWD_26_29_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 26, 30, "1 ", "SS_YANGTZE_SWD_26_30_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 31, 32, "1 ", "SS_YANGTZE_SWD_31_32_1                  ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 31, 33, "1 ", "SS_YANGTZE_SWD_31_33_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 31, 34, "1 ", "SS_YANGTZE_SWD_31_34_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 31, 35, "1 ", "SS_YANGTZE_SWD_31_35_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 31, 36, "1 ", "SS_YANGTZE_SWD_31_36_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [2, 32, 33, "1 ", "SS_YANGTZE_SWD_32_33_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 32, 34, "1 ", "SS_YANGTZE_SWD_32_34_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 32, 35, "1 ", "SS_YANGTZE_SWD_32_35_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [2, 32, 36, "1 ", "SS_YANGTZE_SWD_32_36_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [3, 1, 3, "1 ", "SS_ARKANSAS_SWD_1_3_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 4, "1 ", "SS_ARKANSAS_SWD_1_4_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 5, "1 ", "SS_ARKANSAS_SWD_1_5_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 6, "1 ", "SS_ARKANSAS_SWD_1_6_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 7, "1 ", "SS_ARKANSAS_SWD_1_7_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 1, 8, "1 ", "SS_ARKANSAS_SWD_1_8_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 9, "1 ", "SS_ARKANSAS_SWD_2_9_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 10, "1 ", "SS_ARKANSAS_SWD_2_10_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 11, "1 ", "SS_ARKANSAS_SWD_2_11_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 12, "1 ", "SS_ARKANSAS_SWD_2_12_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 13, "1 ", "SS_ARKANSAS_SWD_2_13_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 2, 14, "1 ", "SS_ARKANSAS_SWD_2_14_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 3, 9, "1 ", "SS_ARKANSAS_SWD_3_9_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 4, 10, "1 ", "SS_ARKANSAS_SWD_4_10_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 5, 11, "1 ", "SS_ARKANSAS_SWD_5_11_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 6, 12, "1 ", "SS_ARKANSAS_SWD_6_12_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 7, 13, "1 ", "SS_ARKANSAS_SWD_7_13_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 8, 14, "1 ", "SS_ARKANSAS_SWD_8_14_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 15, 17, "1 ", "SS_ARKANSAS_SWD_15_17_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 16, 18, "1 ", "SS_ARKANSAS_SWD_16_18_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [3, 17, 18, "1 ", "SS_ARKANSAS_SWD_17_18_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 1, 2, "1 ", "SS_COLORADO_SWD_1_2_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 1, 6, "1 ", "SS_COLORADO_SWD_1_6_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 7, "1 ", "SS_COLORADO_SWD_1_7_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 1, 8, "1 ", "SS_COLORADO_SWD_1_8_1                   ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 2, 6, "1 ", "SS_COLORADO_SWD_2_6_1                   ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 7, "1 ", "SS_COLORADO_SWD_2_7_1                   ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 2, 8, "1 ", "SS_COLORADO_SWD_2_8_1                   ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 3, 6, "1 ", "SS_COLORADO_SWD_3_6_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 4, 7, "1 ", "SS_COLORADO_SWD_4_7_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 5, 8, "1 ", "SS_COLORADO_SWD_5_8_1                   ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 9, 10, "1 ", "SS_COLORADO_SWD_9_10_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 9, 18, "1 ", "SS_COLORADO_SWD_9_18_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 19, "1 ", "SS_COLORADO_SWD_9_19_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 20, "1 ", "SS_COLORADO_SWD_9_20_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 21, "1 ", "SS_COLORADO_SWD_9_21_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 22, "1 ", "SS_COLORADO_SWD_9_22_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 23, "1 ", "SS_COLORADO_SWD_9_23_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 9, 24, "1 ", "SS_COLORADO_SWD_9_24_1                  ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [4, 10, 18, "1 ", "SS_COLORADO_SWD_10_18_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 19, "1 ", "SS_COLORADO_SWD_10_19_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 20, "1 ", "SS_COLORADO_SWD_10_20_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 21, "1 ", "SS_COLORADO_SWD_10_21_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 22, "1 ", "SS_COLORADO_SWD_10_22_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 23, "1 ", "SS_COLORADO_SWD_10_23_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 10, 24, "1 ", "SS_COLORADO_SWD_10_24_1                 ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 11, 18, "1 ", "SS_COLORADO_SWD_11_18_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 12, 19, "1 ", "SS_COLORADO_SWD_12_19_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 13, 20, "1 ", "SS_COLORADO_SWD_13_20_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 14, 21, "1 ", "SS_COLORADO_SWD_14_21_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 15, 22, "1 ", "SS_COLORADO_SWD_15_22_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 16, 23, "1 ", "SS_COLORADO_SWD_16_23_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [4, 17, 24, "1 ", "SS_COLORADO_SWD_17_24_1                 ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 1, 2, "1 ", "SS_MISSISSIPPI_SWD_1_2_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 1, 6, "1 ", "SS_MISSISSIPPI_SWD_1_6_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 1, 7, "1 ", "SS_MISSISSIPPI_SWD_1_7_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 1, 8, "1 ", "SS_MISSISSIPPI_SWD_1_8_1                ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 2, 6, "1 ", "SS_MISSISSIPPI_SWD_2_6_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 2, 7, "1 ", "SS_MISSISSIPPI_SWD_2_7_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 2, 8, "1 ", "SS_MISSISSIPPI_SWD_2_8_1                ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 3, 6, "1 ", "SS_MISSISSIPPI_SWD_3_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 4, 7, "1 ", "SS_MISSISSIPPI_SWD_4_7_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 5, 8, "1 ", "SS_MISSISSIPPI_SWD_5_8_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 9, 10, "1 ", "SS_MISSISSIPPI_SWD_9_10_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 9, 26, "1 ", "SS_MISSISSIPPI_SWD_9_26_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 27, "1 ", "SS_MISSISSIPPI_SWD_9_27_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 28, "1 ", "SS_MISSISSIPPI_SWD_9_28_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 29, "1 ", "SS_MISSISSIPPI_SWD_9_29_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 30, "1 ", "SS_MISSISSIPPI_SWD_9_30_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 31, "1 ", "SS_MISSISSIPPI_SWD_9_31_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 32, "1 ", "SS_MISSISSIPPI_SWD_9_32_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 33, "1 ", "SS_MISSISSIPPI_SWD_9_33_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 34, "1 ", "SS_MISSISSIPPI_SWD_9_34_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 35, "1 ", "SS_MISSISSIPPI_SWD_9_35_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 36, "1 ", "SS_MISSISSIPPI_SWD_9_36_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 37, "1 ", "SS_MISSISSIPPI_SWD_9_37_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 38, "1 ", "SS_MISSISSIPPI_SWD_9_38_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 39, "1 ", "SS_MISSISSIPPI_SWD_9_39_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 9, 40, "1 ", "SS_MISSISSIPPI_SWD_9_40_1               ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 10, 26, "1 ", "SS_MISSISSIPPI_SWD_10_26_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 27, "1 ", "SS_MISSISSIPPI_SWD_10_27_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 28, "1 ", "SS_MISSISSIPPI_SWD_10_28_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 29, "1 ", "SS_MISSISSIPPI_SWD_10_29_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 30, "1 ", "SS_MISSISSIPPI_SWD_10_30_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 31, "1 ", "SS_MISSISSIPPI_SWD_10_31_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 32, "1 ", "SS_MISSISSIPPI_SWD_10_32_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 33, "1 ", "SS_MISSISSIPPI_SWD_10_33_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 34, "1 ", "SS_MISSISSIPPI_SWD_10_34_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 35, "1 ", "SS_MISSISSIPPI_SWD_10_35_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 36, "1 ", "SS_MISSISSIPPI_SWD_10_36_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 37, "1 ", "SS_MISSISSIPPI_SWD_10_37_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 38, "1 ", "SS_MISSISSIPPI_SWD_10_38_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 39, "1 ", "SS_MISSISSIPPI_SWD_10_39_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 10, 40, "1 ", "SS_MISSISSIPPI_SWD_10_40_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 11, 26, "1 ", "SS_MISSISSIPPI_SWD_11_26_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 12, 27, "1 ", "SS_MISSISSIPPI_SWD_12_27_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 13, 28, "1 ", "SS_MISSISSIPPI_SWD_13_28_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 14, 29, "1 ", "SS_MISSISSIPPI_SWD_14_29_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 15, 30, "1 ", "SS_MISSISSIPPI_SWD_15_30_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 16, 31, "1 ", "SS_MISSISSIPPI_SWD_16_31_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 17, 32, "1 ", "SS_MISSISSIPPI_SWD_17_32_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 18, 33, "1 ", "SS_MISSISSIPPI_SWD_18_33_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 19, 34, "1 ", "SS_MISSISSIPPI_SWD_19_34_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 20, 35, "1 ", "SS_MISSISSIPPI_SWD_20_35_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 21, 36, "1 ", "SS_MISSISSIPPI_SWD_21_36_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 22, 37, "1 ", "SS_MISSISSIPPI_SWD_22_37_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 23, 38, "1 ", "SS_MISSISSIPPI_SWD_23_38_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 24, 39, "1 ", "SS_MISSISSIPPI_SWD_24_39_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 25, 40, "1 ", "SS_MISSISSIPPI_SWD_25_40_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 41, 42, "1 ", "SS_MISSISSIPPI_SWD_41_42_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 41, 45, "1 ", "SS_MISSISSIPPI_SWD_41_45_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 41, 46, "1 ", "SS_MISSISSIPPI_SWD_41_46_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 42, 45, "1 ", "SS_MISSISSIPPI_SWD_42_45_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 42, 46, "1 ", "SS_MISSISSIPPI_SWD_42_46_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 43, 45, "1 ", "SS_MISSISSIPPI_SWD_43_45_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 44, 46, "1 ", "SS_MISSISSIPPI_SWD_44_46_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 47, 48, "1 ", "SS_MISSISSIPPI_SWD_47_48_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 47, 50, "1 ", "SS_MISSISSIPPI_SWD_47_50_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 48, 50, "1 ", "SS_MISSISSIPPI_SWD_48_50_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 49, 50, "1 ", "SS_MISSISSIPPI_SWD_49_50_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 51, 52, "1 ", "SS_MISSISSIPPI_SWD_51_52_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 51, 55, "1 ", "SS_MISSISSIPPI_SWD_51_55_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 51, 56, "1 ", "SS_MISSISSIPPI_SWD_51_56_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 52, 55, "1 ", "SS_MISSISSIPPI_SWD_52_55_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 52, 56, "1 ", "SS_MISSISSIPPI_SWD_52_56_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 53, 55, "1 ", "SS_MISSISSIPPI_SWD_53_55_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 54, 56, "1 ", "SS_MISSISSIPPI_SWD_54_56_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 57, 58, "1 ", "SS_MISSISSIPPI_SWD_57_58_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 57, 61, "1 ", "SS_MISSISSIPPI_SWD_57_61_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 57, 62, "1 ", "SS_MISSISSIPPI_SWD_57_62_1              ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [5, 58, 61, "1 ", "SS_MISSISSIPPI_SWD_58_61_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 58, 62, "1 ", "SS_MISSISSIPPI_SWD_58_62_1              ", 3, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 59, 61, "1 ", "SS_MISSISSIPPI_SWD_59_61_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [5, 60, 62, "1 ", "SS_MISSISSIPPI_SWD_60_62_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 1, 2, "1 ", "SS_VOLGA_SWD_1_2_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 3, 4, "1 ", "SS_VOLGA_SWD_3_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 3, 5, "1 ", "SS_VOLGA_SWD_3_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 3, 6, "1 ", "SS_VOLGA_SWD_3_6_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 7, 8, "1 ", "SS_VOLGA_SWD_7_8_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 7, 9, "1 ", "SS_VOLGA_SWD_7_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [6, 7, 10, "1 ", "SS_VOLGA_SWD_7_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 1, 3, "1 ", "SS_YUKON_SWD_1_3_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 2, 4, "1 ", "SS_YUKON_SWD_2_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 3, 4, "1 ", "SS_YUKON_SWD_3_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 5, 7, "1 ", "SS_YUKON_SWD_5_7_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 5, 8, "1 ", "SS_YUKON_SWD_5_8_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 6, 9, "1 ", "SS_YUKON_SWD_6_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 6, 10, "1 ", "SS_YUKON_SWD_6_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 7, 9, "1 ", "SS_YUKON_SWD_7_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 8, 10, "1 ", "SS_YUKON_SWD_8_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 11, 13, "1 ", "SS_YUKON_SWD_11_13_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 12, 14, "1 ", "SS_YUKON_SWD_12_14_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 13, 14, "1 ", "SS_YUKON_SWD_13_14_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 15, 17, "1 ", "SS_YUKON_SWD_15_17_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 15, 18, "1 ", "SS_YUKON_SWD_15_18_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 16, 19, "1 ", "SS_YUKON_SWD_16_19_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 16, 20, "1 ", "SS_YUKON_SWD_16_20_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 17, 19, "1 ", "SS_YUKON_SWD_17_19_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [7, 18, 20, "1 ", "SS_YUKON_SWD_18_20_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 1, 3, "1 ", "SS_BRAHMAPUTRA_SWD_1_3_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 1, 4, "1 ", "SS_BRAHMAPUTRA_SWD_1_4_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 2, 5, "1 ", "SS_BRAHMAPUTRA_SWD_2_5_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 2, 6, "1 ", "SS_BRAHMAPUTRA_SWD_2_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 3, 5, "1 ", "SS_BRAHMAPUTRA_SWD_3_5_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 4, 6, "1 ", "SS_BRAHMAPUTRA_SWD_4_6_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 9, "1 ", "SS_BRAHMAPUTRA_SWD_7_9_1                ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 10, "1 ", "SS_BRAHMAPUTRA_SWD_7_10_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 11, "1 ", "SS_BRAHMAPUTRA_SWD_7_11_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 12, "1 ", "SS_BRAHMAPUTRA_SWD_7_12_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 7, 13, "1 ", "SS_BRAHMAPUTRA_SWD_7_13_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 14, "1 ", "SS_BRAHMAPUTRA_SWD_8_14_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 15, "1 ", "SS_BRAHMAPUTRA_SWD_8_15_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 16, "1 ", "SS_BRAHMAPUTRA_SWD_8_16_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 17, "1 ", "SS_BRAHMAPUTRA_SWD_8_17_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 8, 18, "1 ", "SS_BRAHMAPUTRA_SWD_8_18_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 9, 14, "1 ", "SS_BRAHMAPUTRA_SWD_9_14_1               ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 10, 15, "1 ", "SS_BRAHMAPUTRA_SWD_10_15_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 11, 16, "1 ", "SS_BRAHMAPUTRA_SWD_11_16_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 12, 17, "1 ", "SS_BRAHMAPUTRA_SWD_12_17_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [8, 13, 18, "1 ", "SS_BRAHMAPUTRA_SWD_13_18_1              ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 2, "1 ", "SS_INDUS_SWD_1_2_1                      ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 1, 3, "1 ", "SS_INDUS_SWD_1_3_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 4, "1 ", "SS_INDUS_SWD_1_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 5, "1 ", "SS_INDUS_SWD_1_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 6, "1 ", "SS_INDUS_SWD_1_6_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 7, "1 ", "SS_INDUS_SWD_1_7_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 8, "1 ", "SS_INDUS_SWD_1_8_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 9, "1 ", "SS_INDUS_SWD_1_9_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 1, 10, "1 ", "SS_INDUS_SWD_1_10_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 2, 3, "1 ", "SS_INDUS_SWD_2_3_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 4, "1 ", "SS_INDUS_SWD_2_4_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 5, "1 ", "SS_INDUS_SWD_2_5_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 6, "1 ", "SS_INDUS_SWD_2_6_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 7, "1 ", "SS_INDUS_SWD_2_7_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 8, "1 ", "SS_INDUS_SWD_2_8_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 9, "1 ", "SS_INDUS_SWD_2_9_1                      ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 2, 10, "1 ", "SS_INDUS_SWD_2_10_1                     ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 11, 12, "1 ", "SS_INDUS_SWD_11_12_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 11, 13, "1 ", "SS_INDUS_SWD_11_13_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 11, 14, "1 ", "SS_INDUS_SWD_11_14_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 11, 15, "1 ", "SS_INDUS_SWD_11_15_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 12, 13, "1 ", "SS_INDUS_SWD_12_13_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 12, 14, "1 ", "SS_INDUS_SWD_12_14_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 12, 15, "1 ", "SS_INDUS_SWD_12_15_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 16, 17, "1 ", "SS_INDUS_SWD_16_17_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 16, 18, "1 ", "SS_INDUS_SWD_16_18_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 17, 18, "1 ", "SS_INDUS_SWD_17_18_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 19, 20, "1 ", "SS_INDUS_SWD_19_20_1                    ", 2, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 19, 21, "1 ", "SS_INDUS_SWD_19_21_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 19, 22, "1 ", "SS_INDUS_SWD_19_22_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 19, 23, "1 ", "SS_INDUS_SWD_19_23_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [9, 20, 21, "1 ", "SS_INDUS_SWD_20_21_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 20, 22, "1 ", "SS_INDUS_SWD_20_22_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [9, 20, 23, "1 ", "SS_INDUS_SWD_20_23_1                    ", 3, 0, 0, 0.0001, 0.0, 0.0, 0.0], [10, 1, 3, "1 ", "SS_DANUBE_SWD_1_3_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 2, 4, "1 ", "SS_DANUBE_SWD_2_4_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [10, 3, 4, "1 ", "SS_DANUBE_SWD_3_4_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [11, 1, 2, "1 ", "SS_ALLEGHENY_SWD_1_2_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [11, 1, 2, "2 ", "SS_ALLEGHENY_SWD_1_2_2                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [12, 1, 2, "1 ", "SS_GANGES_SWD_1_2_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [12, 1, 3, "1 ", "SS_GANGES_SWD_1_3_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [12, 2, 3, "1 ", "SS_GANGES_SWD_2_3_1                     ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [13, 1, 2, "1 ", "SS_OXUS_SWD_1_2_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [13, 1, 3, "1 ", "SS_OXUS_SWD_1_3_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [13, 1, 4, "1 ", "SS_OXUS_SWD_1_4_1                       ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [14, 1, 2, "1 ", "SS_SALWEEN_SWD_1_2_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [14, 1, 3, "1 ", "SS_SALWEEN_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 1, 3, "1 ", "SS_HEILONG_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 1, 4, "1 ", "SS_HEILONG_SWD_1_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 1, 5, "1 ", "SS_HEILONG_SWD_1_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 2, 3, "1 ", "SS_HEILONG_SWD_2_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 2, 4, "1 ", "SS_HEILONG_SWD_2_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [15, 2, 5, "1 ", "SS_HEILONG_SWD_2_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 1, 3, "1 ", "SS_ZAIRE_SWD_1_3_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 1, 4, "1 ", "SS_ZAIRE_SWD_1_4_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 2, 5, "1 ", "SS_ZAIRE_SWD_2_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 2, 6, "1 ", "SS_ZAIRE_SWD_2_6_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 3, 5, "1 ", "SS_ZAIRE_SWD_3_5_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [16, 4, 6, "1 ", "SS_ZAIRE_SWD_4_6_1                      ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 3, "1 ", "SS_ZAMBEZI_SWD_1_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 4, "1 ", "SS_ZAMBEZI_SWD_1_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 1, 5, "1 ", "SS_ZAMBEZI_SWD_1_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 3, "1 ", "SS_ZAMBEZI_SWD_2_3_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 4, "1 ", "SS_ZAMBEZI_SWD_2_4_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [17, 2, 5, "1 ", "SS_ZAMBEZI_SWD_2_5_1                    ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [18, 1, 2, "1 ", "SS_PILCOMAYO_SWD_1_2_1                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0], [18, 1, 2, "2 ", "SS_PILCOMAYO_SWD_1_2_2                  ", 2, 1, 1, 0.0001, 0.0, 0.0, 0.0]]}, "subterm": {"fields": ["isub", "inode", "type", "eqid", "ibus", "jbus", "kbus"], "data": [[1, 4, "M", "1 ", 101, null, null], [1, 3, "2", "T1", 101, 151, null], [1, 8, "M", "1 ", 102, null, null], [1, 7, "2", "T2", 102, 151, null], [1, 16, "F", "F1", 151, null, null], [1, 17, "F", "F2", 151, null, null], [1, 18, "F", "F3", 151, null, null], [1, 14, "2", "T1", 151, 101, null], [1, 15, "2", "T2", 151, 102, null], [1, 11, "B", "1 ", 151, 152, null], [1, 12, "B", "2 ", 151, 152, null], [1, 13, "B", "*1", 151, 201, null], [1, 25, "L", "SC", 201, null, null], [1, 26, "F", "1 ", 201, null, null], [1, 24, "B", "*1", 201, 151, null], [1, 21, "B", "1 ", 201, 202, null], [1, 22, "B", "C1", 201, 207, null], [1, 23, "2", "T6", 201, 211, null], [1, 30, "M", "1 ", 211, null, null], [1, 29, "2", "T6", 211, 201, null], [2, 10, "L", "1 ", 152, null, null], [2, 11, "F", "1 ", 152, null, null], [2, 12, "S", "1 ", 152, null, null], [2, 8, "B", "1 ", 152, 151, null], [2, 9, "B", "2 ", 152, 151, null], [2, 5, "2", "T3", 152, 153, null], [2, 3, "B", "1 ", 152, 202, null], [2, 4, "B", "1 ", 152, 3004, null], [2, 6, "2", "T4", 152, 3021, null], [2, 7, "2", "T5", 152, 3022, null], [2, 18, "L", "1 ", 153, null, null], [2, 17, "2", "T3", 153, 152, null], [2, 15, "B", "2 ", 153, 154, null], [2, 16, "B", "@1", 153, 3006, null], [2, 19, "A", "FACTS_DVCE_1", 153, null, null], [2, 20, "A", "FACTS_DVCE_2", 153, null, null], [2, 23, "B", "@1", 3006, 153, null], [2, 24, "B", "1 ", 3006, 3005, null], [2, 28, "F", "1 ", 3021, null, null], [2, 29, "S", "1 ", 3021, null, null], [2, 27, "2", "T4", 3021, 152, null], [2, 30, "D", "TWO_TERM_DC1", 3021, null, null], [2, 34, "F", "1 ", 3022, null, null], [2, 35, "S", "1 ", 3022, null, null], [2, 33, "2", "T5", 3022, 152, null], [2, 36, "D", "TWO_TERM_DC2", 3022, null, null], [3, 9, "L", "1 ", 154, null, null], [3, 10, "L", "2 ", 154, null, null], [3, 11, "L", "3 ", 154, null, null], [3, 12, "L", "MO", 154, null, null], [3, 13, "F", "1 ", 154, null, null], [3, 14, "S", "1 ", 154, null, null], [3, 8, "B", "2 ", 154, 153, null], [3, 3, "B", "1 ", 154, 155, null], [3, 4, "B", "1 ", 154, 203, null], [3, 5, "B", "1 ", 154, 205, null], [3, 6, "B", "1 ", 154, 3008, null], [3, 7, "2", "W1", 154, 9154, null], [3, 18, "I", "1 ", 9154, null, null], [3, 17, "2", "W1", 9154, 154, null], [4, 4, "B", "1 ", 202, 152, null], [4, 5, "B", "1 ", 202, 201, null], [4, 3, "2", "T7", 202, 203, null], [4, 14, "L", "1 ", 203, null, null], [4, 15, "F", "1 ", 203, null, null], [4, 16, "F", "2 ", 203, null, null], [4, 12, "B", "1 ", 203, 154, null], [4, 13, "2", "T7", 203, 202, null], [4, 11, "B", "1 ", 203, 205, null], [4, 17, "V", "VDCLINE2    ", 203, null, null], [5, 4, "2", "T8", 204, 205, null], [5, 3, "B", "C2", 204, 207, null], [5, 5, "2", "W2", 204, 9204, null], [5, 21, "L", "1 ", 205, null, null], [5, 22, "L", "B ", 205, null, null], [5, 23, "L", "C ", 205, null, null], [5, 24, "F", "1 ", 205, null, null], [5, 18, "B", "1 ", 205, 154, null], [5, 19, "B", "1 ", 205, 203, null], [5, 20, "2", "T8", 205, 204, null], [5, 16, "2", "T9", 205, 206, null], [5, 11, "B", "1 ", 205, 212, null], [5, 12, "B", "2 ", 205, 214, null], [5, 13, "B", "3 ", 205, 216, null], [5, 14, "B", "4 ", 205, 217, null], [5, 15, "B", "5 ", 205, 218, null], [5, 17, "3", "3 ", 205, 208, 215], [5, 25, "V", "VDCLINE2    ", 205, null, null], [5, 44, "M", "1 ", 206, null, null], [5, 43, "2", "T9", 206, 205, null], [5, 49, "3", "3 ", 208, 205, 215], [5, 54, "L", "U1", 215, null, null], [5, 53, "3", "3 ", 215, 205, 208], [5, 60, "I", "1 ", 9204, null, null], [5, 59, "2", "W2", 9204, 204, null], [6, 2, "3", "4 ", 209, 217, 218], [6, 6, "L", "U1", 217, null, null], [6, 5, "B", "4 ", 217, 205, null], [6, 4, "3", "4 ", 217, 209, 218], [6, 10, "L", "U1", 218, null, null], [6, 9, "B", "5 ", 218, 205, null], [6, 8, "3", "4 ", 218, 209, 217], [7, 3, "B", "1 ", 3001, 3003, null], [7, 4, "3", "1 ", 3001, 3002, 3011], [7, 7, "B", "1 ", 3002, 3004, null], [7, 9, "2", "W3", 3002, 93002, null], [7, 8, "3", "1 ", 3002, 3001, 3011], [7, 14, "M", "1 ", 3011, null, null], [7, 13, "3", "1 ", 3011, 3001, 3002], [7, 19, "S", "1 ", 93002, null, null], [7, 18, "I", "1 ", 93002, null, null], [7, 17, "2", "W3", 93002, 3002, null], [8, 4, "B", "1 ", 3004, 152, null], [8, 5, "B", "1 ", 3004, 3002, null], [8, 3, "2", "10", 3004, 3005, null], [8, 15, "L", "1 ", 3005, null, null], [8, 16, "S", "1 ", 3005, null, null], [8, 12, "B", "1 ", 3005, 3003, null], [8, 13, "B", "2 ", 3005, 3003, null], [8, 14, "2", "10", 3005, 3004, null], [8, 9, "B", "1 ", 3005, 3006, null], [8, 10, "B", "1 ", 3005, 3007, null], [8, 11, "B", "1 ", 3005, 3008, null], [8, 17, "V", "VDCLINE1    ", 3005, null, null], [9, 9, "L", "1 ", 3008, null, null], [9, 6, "B", "1 ", 3008, 154, null], [9, 7, "B", "1 ", 3008, 3005, null], [9, 8, "B", "1 ", 3008, 3007, null], [9, 3, "B", "1 ", 3008, 3009, null], [9, 4, "2", "11", 3008, 3018, null], [9, 5, "3", "2 ", 3008, 3010, 3012], [9, 10, "V", "VDCLINE1    ", 3008, null, null], [9, 14, "L", "1 ", 3010, null, null], [9, 15, "I", "1 ", 3010, null, null], [9, 13, "3", "2 ", 3010, 3008, 3012], [9, 18, "3", "2 ", 3012, 3008, 3010], [9, 22, "M", "1 ", 3018, null, null], [9, 23, "M", "2 ", 3018, null, null], [9, 21, "2", "11", 3018, 3008, null], [10, 3, "B", "1 ", 155, 154, null], [10, 4, "A", "FACTS_DVCE_2", 155, null, null], [11, 1, "B", "C1", 207, 201, null], [11, 2, "B", "C2", 207, 204, null], [12, 2, "F", "1 ", 212, null, null], [12, 1, "B", "1 ", 212, 205, null], [12, 3, "N", "MULTERM_DC_1", 212, null, null], [13, 4, "L", "1 ", 214, null, null], [13, 2, "B", "2 ", 214, 205, null], [13, 3, "B", "1 ", 214, 213, null], [14, 3, "L", "U1", 216, null, null], [14, 2, "B", "3 ", 216, 205, null], [15, 4, "F", "1 ", 213, null, null], [15, 3, "B", "1 ", 213, 214, null], [15, 5, "N", "MULTERM_DC_1", 213, null, null], [16, 5, "B", "1 ", 3003, 3001, null], [16, 3, "B", "1 ", 3003, 3005, null], [16, 4, "B", "2 ", 3003, 3005, null], [17, 5, "L", "1 ", 3007, null, null], [17, 4, "B", "1 ", 3007, 3005, null], [17, 3, "B", "1 ", 3007, 3008, null], [18, 2, "L", "1 ", 3009, null, null], [18, 1, "B", "1 ", 3009, 3008, null]]}}}