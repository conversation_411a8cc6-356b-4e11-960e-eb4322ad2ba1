# =========================
# Section: Bus Operations
# Equipment Type: Bus
# =========================

ERROR_CODES_BUS_DATA = {
    0: "No errors occurred.",
    1: "Invalid bus number.",
    2: "Bus table is full.",
    3: "Working case is not initialized.",
    5: "Prerequisite requirements for API are not met.",
    -1: (
        "Data error, warning and information messages; one or more of: "
        "Invalid base voltage. Invalid bus type code. Invalid area, zone or owner number. "
        "Bus name is more than 12 characters. New area number but bus is the area swing for its old area. "
        "Bus type code is 3 but is not the swing bus for its area. Area table is full. Zone table is full. "
        "Owner table is full. High voltage limit is not greater than the low voltage limit. "
        "Normal voltage limit is less restrictive than the emergency limit."
    ),
}

def bus_data(
    ibus: int,
    intgar: list[int] = [1, 1, 1, 1],  # [IDE, AREA, ZONE, OWNER]
    realar: list[float] = [0.0, 1.0, 0.0, 1.1, 0.9, 1.1, 0.9],  # [BASKV, VM, VA, NMAXV, NMINV, EMAXV, EMINV]
    name: str = ""
) -> int:
    """
    Official PSSPY API: bus_data

    Adds or modifies a bus in the network.

    Parameters:
        ibus (int): Bus number.
        intgar (list[int]): [IDE, AREA, ZONE, OWNER]
            IDE (int, default=1): Bus type code.
            AREA (int, default=1): Area number.
            ZONE (int, default=1): Zone number.
            OWNER (int, default=1): Owner number.
        realar (list[float]): [BASKV, VM, VA, NMAXV, NMINV, EMAXV, EMINV]
            BASKV (float, default=0.0): Bus base voltage in kV.
            VM (float, default=1.0): Bus voltage magnitude in pu.
            VA (float, default=0.0): Bus voltage phase angle.
            NMAXV (float, default=1.1): Normal bus voltage magnitude high limit in pu.
            NMINV (float, default=0.9): Normal bus voltage magnitude low limit in pu.
            EMAXV (float, default=1.1): Emergency bus voltage magnitude high limit in pu.
            EMINV (float, default=0.9): Emergency bus voltage magnitude low limit in pu.
        name (str): Bus name (max 12 characters).

    Returns:
        ierr (int): Return code. See ERROR_CODES_BUS_DATA for details.

    Example canonical call:
        ierr = bus_data(
            ibus,
            intgar=[ide, area, zone, owner],
            realar=[baskv, vm, va, nmaxv, nminv, emaxv, eminv],
            name=name
        )
    """
    return add_or_update_bus(
        bus_number=ibus,
        bus_type_code=intgar[0],
        area_number=intgar[1],
        zone_number=intgar[2],
        owner_number=intgar[3],
        base_kv=realar[0],
        voltage_mag=realar[1],
        voltage_angle=realar[2],
        normal_v_hi=realar[3],
        normal_v_lo=realar[4],
        emerg_v_hi=realar[5],
        emerg_v_lo=realar[6],
        bus_name=name
    )

def add_or_update_bus(
    bus_number: int,
    bus_type_code: int = 1,
    area_number: int = 1,
    zone_number: int = 1,
    owner_number: int = 1,
    base_kv: float = 0.0,
    voltage_mag: float = 1.0,
    voltage_angle: float = 0.0,
    normal_v_hi: float = 1.1,
    normal_v_lo: float = 0.9,
    emerg_v_hi: float = 1.1,
    emerg_v_lo: float = 0.9,
    bus_name: str = ""
) -> int:
    """
    Adds or updates a bus in the network (human-readable version).

    Parameters:
        bus_number (int): Bus number.
        bus_type_code (int, default=1): Bus type code.
        area_number (int, default=1): Area number.
        zone_number (int, default=1): Zone number.
        owner_number (int, default=1): Owner number.
        base_kv (float, default=0.0): Bus base voltage in kV.
        voltage_mag (float, default=1.0): Bus voltage magnitude in pu.
        voltage_angle (float, default=0.0): Bus voltage phase angle.
        normal_v_hi (float, default=1.1): Normal bus voltage magnitude high limit in pu.
        normal_v_lo (float, default=0.9): Normal bus voltage magnitude low limit in pu.
        emerg_v_hi (float, default=1.1): Emergency bus voltage magnitude high limit in pu.
        emerg_v_lo (float, default=0.9): Emergency bus voltage magnitude low limit in pu.
        bus_name (str): Bus name (max 12 characters).

    Returns:
        return_code (int): Return code. See ERROR_CODES_BUS_DATA for details.

    Example canonical call:
        ierr = bus_data(
            ibus=bus_number,
            intgar=[bus_type_code, area_number, zone_number, owner_number],
            realar=[base_kv, voltage_mag, voltage_angle, normal_v_hi, normal_v_lo, emerg_v_hi, emerg_v_lo],
            name=bus_name
        )
    """
    return bus_data(
        ibus=bus_number,
        intgar=[bus_type_code, area_number, zone_number, owner_number],
        realar=[base_kv, voltage_mag, voltage_angle, normal_v_hi, normal_v_lo, emerg_v_hi, emerg_v_lo],
        name=bus_name
    )

# Reference from .md:
# def bus_data(ibus, intgar, realar, name):
#     :param ibus: (undocumented)
#     :param intgar: (undocumented)
#     :param realar: (undocumented)
#     :param name: (undocumented)
#     :return: ierr
# error_codes = {'IERR': '-1 data error, warning and information mes-'} 