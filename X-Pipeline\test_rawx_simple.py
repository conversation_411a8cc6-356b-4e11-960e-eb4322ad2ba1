#!/usr/bin/env python3

import sys
sys.path.append('.')

from hdb_to_raw_pipeline import Rawx<PERSON>ackend, configure_logging

# Configure logging
configure_logging()

print("Testing RAWX backend...")

try:
    # Test constructor-based loading
    print("=== Constructor-based loading ===")
    print("Creating RawxBackend...")
    rawx_backend = RawxBackend(file_path="savnw_nb.rawx")
    print("Backend created successfully")
    print(f"Backend data sections: {len(rawx_backend.data)}")

    key_sections = ['sub', 'subnode', 'subswd']
    print("Key sections in backend.data:")
    for section in key_sections:
        if section in rawx_backend.data:
            section_data = rawx_backend.data[section]
            if isinstance(section_data, dict) and 'data' in section_data:
                print(f"  {section}: {len(section_data['data'])} records")

    # Test canonical conversion
    print("\n=== Canonical conversion ===")
    print("Calling to_canonical()...")
    canonical_data = rawx_backend.to_canonical()
    print("Canonical conversion completed")
    print(f"Canonical data sections: {len(canonical_data)}")

    canonical_key_sections = ['substation', 'node', 'switching_device', 'sub', 'subnode', 'subswd']
    print("Key sections in canonical data:")
    for section in canonical_key_sections:
        if section in canonical_data:
            section_data = canonical_data[section]
            if isinstance(section_data, dict) and 'data' in section_data:
                print(f"  {section}: {len(section_data['data'])} records")

    print("\nTest complete.")

except Exception as e:
    print(f"Error occurred: {e}")
    import traceback
    traceback.print_exc() 