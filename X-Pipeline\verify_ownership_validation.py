#!/usr/bin/env python3
"""
Verification script to test that ownership validation properly handles zero impedance branches.
"""

import sys
from pathlib import Path

# Add the current directory to the path so we can import from hdb_to_raw_pipeline
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import Backend

def test_ownership_validation_with_zbr():
    """Test that ownership validation works with zero impedance branches."""
    print("Testing ownership validation with zero impedance branches...")
    
    # Create sample data with zero impedance branches that have invalid ownership fractions
    test_data = {
        'ac_line': {
            'fields': ['ibus', 'jbus', 'ckt', 'r', 'x', 'b', 'name', 'rate1', 'rate2', 'rate3', 'stat', 'o1', 'f1', 'o2', 'f2', 'o3', 'f3', 'o4', 'f4'],
            'data': [
                # Regular branch with valid ownership
                [101, 102, '1', 0.01, 0.1, 0.0, 'BRANCH1', 100.0, 100.0, 100.0, 1, 1, 1.0, 0, 0.0, 0, 0.0, 0, 0.0],
                # Zero impedance branch with invalid ownership (sum = 0.8)
                [103, 104, 'ZZ', 0.0, 0.0001, 0.0, 'ZBR1', 0.0, 0.0, 0.0, 1, 1, 0.8, 0, 0.0, 0, 0.0, 0, 0.0],
                # Zero impedance branch with invalid ownership (sum = 1.2)
                [105, 106, 'ZZ', 0.0, 0.0001, 0.0, 'ZBR2', 0.0, 0.0, 0.0, 1, 1, 1.2, 0, 0.0, 0, 0.0, 0, 0.0],
            ]
        }
    }
    
    # Create a backend instance to access the validation method
    backend = Backend()
    
    # Run ownership validation
    validated_data = backend._validate_ownership_fractions(test_data)
    
    # Check results
    ac_line = validated_data['ac_line']
    data = ac_line['data']
    
    print("Original ownership fractions:")
    print(f"  ZBR1: f1 = 0.8 (sum = 0.8)")
    print(f"  ZBR2: f1 = 1.2 (sum = 1.2)")
    
    print("\nValidated ownership fractions:")
    zbr1_f1 = data[1][11]  # f1 for ZBR1
    zbr2_f1 = data[2][11]  # f1 for ZBR2
    
    print(f"  ZBR1: f1 = {zbr1_f1}")
    print(f"  ZBR2: f1 = {zbr2_f1}")
    
    # Verify that fractions sum to 1.0
    zbr1_sum = zbr1_f1
    zbr2_sum = zbr2_f1
    
    print(f"\nValidation results:")
    print(f"  ZBR1 sum: {zbr1_sum} (should be 1.0)")
    print(f"  ZBR2 sum: {zbr2_sum} (should be 1.0)")
    
    if abs(zbr1_sum - 1.0) < 0.001 and abs(zbr2_sum - 1.0) < 0.001:
        print("✅ Ownership validation correctly fixed zero impedance branch fractions")
        return True
    else:
        print("❌ Ownership validation failed to fix fractions properly")
        return False

def main():
    """Run the verification."""
    print("Verifying Zero Impedance Branch Ownership Validation")
    print("=" * 60)
    
    success = test_ownership_validation_with_zbr()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Verification passed!")
    else:
        print("❌ Verification failed!")
        sys.exit(1)

if __name__ == "__main__":
    main() 