#!/usr/bin/env python3
"""
Convert Architecture Documentation to PDF
"""

import markdown
import os
import sys
import re
from pathlib import Path

def convert_markdown_to_html(md_file, html_file):
    """Convert markdown file to HTML with styling."""
    
    # Read the markdown file
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Configure markdown with extensions
    md = markdown.Markdown(
        extensions=[
            'markdown.extensions.toc',
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.codehilite',
            'markdown.extensions.extra'
        ],
        extension_configs={
            'markdown.extensions.toc': {
                'title': 'Table of Contents',
                'anchorlink': True
            },
            'markdown.extensions.codehilite': {
                'use_pygments': False,
                'noclasses': True
            }
        }
    )
    
    # Convert markdown to HTML
    html_content = md.convert(md_content)
    
    # Post-process to convert code blocks with language 'mermaid' to proper mermaid divs
    def mermaid_replacer(match):
        mermaid_code = match.group(1).strip()
        return f'<div class="mermaid">\n{mermaid_code}\n</div>'
    
    # Find and replace mermaid code blocks
    # Pattern matches: <pre><code class="language-mermaid">...content...</code></pre>
    mermaid_pattern = r'<pre><code class="language-mermaid">(.*?)</code></pre>'
    html_content = re.sub(mermaid_pattern, mermaid_replacer, html_content, flags=re.DOTALL)
    
    # Also handle plain <code class="language-mermaid"> blocks
    mermaid_pattern2 = r'<code class="language-mermaid">(.*?)</code>'
    html_content = re.sub(mermaid_pattern2, mermaid_replacer, html_content, flags=re.DOTALL)
    
    # Add Mermaid support to HTML
    mermaid_script = """
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                },
                sequence: {
                    useMaxWidth: true
                },
                gantt: {
                    useMaxWidth: true
                }
            });
        });
    </script>
    """
    
    # Create full HTML document with styling
    full_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDB to RAW Pipeline Architecture Documentation</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        
        h1 {{
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 2.2em;
        }}
        
        h2 {{
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            font-size: 1.8em;
        }}
        
        h3 {{
            color: #34495e;
            font-size: 1.4em;
        }}
        
        h4 {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        
        code {{
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }}
        
        pre {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
        }}
        
        pre code {{
            background-color: transparent;
            padding: 0;
            color: #333;
        }}
        
        blockquote {{
            border-left: 4px solid #3498db;
            padding-left: 20px;
            margin-left: 0;
            font-style: italic;
            color: #7f8c8d;
        }}
        
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }}
        
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        
        ul, ol {{
            padding-left: 25px;
        }}
        
        li {{
            margin-bottom: 5px;
        }}
        
        .mermaid {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }}
        
        .toc {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .toc ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        
        .toc li {{
            margin-bottom: 8px;
        }}
        
        .toc a {{
            text-decoration: none;
            color: #3498db;
        }}
        
        .toc a:hover {{
            text-decoration: underline;
        }}
        
        strong {{
            color: #2c3e50;
        }}
        
        em {{
            color: #7f8c8d;
        }}
        
        .page-break {{
            page-break-before: always;
        }}
        
        @media print {{
            body {{
                max-width: none;
                margin: 0;
                padding: 15px;
            }}
            
            h1, h2, h3 {{
                page-break-after: avoid;
            }}
            
            pre, blockquote {{
                page-break-inside: avoid;
            }}
            
            .mermaid {{
                page-break-inside: avoid;
                background-color: white !important;
            }}
        }}
    </style>
    {mermaid_script}
</head>
<body>
    {html_content}
</body>
</html>
"""
    
    # Write HTML to file
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(full_html)
    
    print(f"HTML file created: {html_file}")

def convert_html_to_pdf_weasyprint(html_file, pdf_file):
    """Convert HTML to PDF using WeasyPrint."""
    try:
        from weasyprint import HTML
        
        # Convert HTML to PDF
        HTML(filename=html_file).write_pdf(pdf_file)
        print(f"PDF created using WeasyPrint: {pdf_file}")
        return True
    except ImportError:
        print("WeasyPrint not available")
        return False
    except Exception as e:
        print(f"Error with WeasyPrint: {e}")
        return False

def convert_html_to_pdf_pdfkit(html_file, pdf_file):
    """Convert HTML to PDF using pdfkit/wkhtmltopdf."""
    try:
        import pdfkit
        
        # Configure options for better PDF output
        options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'javascript-delay': 5000,  # Wait longer for Mermaid to render
            'no-stop-slow-scripts': None,
            'disable-smart-shrinking': None
        }
        
        # Convert HTML to PDF
        pdfkit.from_file(html_file, pdf_file, options=options)
        print(f"PDF created using pdfkit: {pdf_file}")
        return True
    except ImportError:
        print("pdfkit not available")
        return False
    except Exception as e:
        print(f"Error with pdfkit: {e}")
        return False

def main():
    """Main conversion function."""
    # File paths
    md_file = Path("Architecture_Documentation.md")
    html_file = Path("Architecture_Documentation.html")
    pdf_file = Path("Architecture_Documentation.pdf")
    
    # Check if markdown file exists
    if not md_file.exists():
        print(f"Error: Markdown file {md_file} not found")
        sys.exit(1)
    
    print(f"Converting {md_file} to PDF with Mermaid diagram support...")
    
    # Step 1: Convert markdown to HTML
    convert_markdown_to_html(md_file, html_file)
    
    # Step 2: Convert HTML to PDF (try multiple methods)
    pdf_created = False
    
    # Try WeasyPrint first
    if convert_html_to_pdf_weasyprint(html_file, pdf_file):
        pdf_created = True
    
    # Try pdfkit if WeasyPrint failed
    if not pdf_created:
        if convert_html_to_pdf_pdfkit(html_file, pdf_file):
            pdf_created = True
    
    if not pdf_created:
        print("Warning: PDF conversion failed. HTML file created successfully.")
        print(f"You can manually convert {html_file} to PDF using a web browser:")
        print("1. Open the HTML file in a web browser")
        print("2. Wait for diagrams to render (may take 5-10 seconds)")
        print("3. Press Ctrl+P to print")
        print("4. Select 'Save as PDF' as destination")
        print("5. Use landscape orientation for better diagram visibility")
        print("6. Make sure 'Background graphics' is enabled in print options")
    
    # Keep HTML file for manual conversion
    print(f"HTML file available for manual conversion: {html_file}")
    print("Conversion complete!")

if __name__ == "__main__":
    main() 