import os
import re
import logging
from collections import defaultdict
from typing import List, Dict, Tuple, Optional

# Setup logging
LOG_DIR = 'log'
LOG_FILE = os.path.join(LOG_DIR, 'psspy_api_chapter_split.log')
os.makedirs(LOG_DIR, exist_ok=True)
logging.basicConfig(filename=LOG_FILE, level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

API_DOC_DIR = os.path.join('Documentation', 'docs')
CHAPTERS_DIR = 'Chapters'
MASTER_PY_FILE = os.path.join(CHAPTERS_DIR, 'psspy_api_master.py')
LOOKUP_DICT_FILE = os.path.join(CHAPTERS_DIR, 'psspy_api_var_lookup.py')
os.makedirs(CHAPTERS_DIR, exist_ok=True)

API_MD_FILES = [f for f in os.listdir(API_DOC_DIR) if re.match(r'API\d+\.md', f)]
API_MD_FILES.sort(key=lambda x: int(re.search(r'API(\d+)', x).group(1)))

# Regex patterns
chapter_header_re = re.compile(r'^# Chapter (\d+)')
section_header_re = re.compile(r'^(\d+(?:\.\d+)+)\.\s+([A-Z0-9_]+)')
python_syntax_re = re.compile(r'^```\s*\n(.*?)\n```', re.DOTALL)
python_cmd_re = re.compile(r'^(.*?)\s*=\s*([a-zA-Z0-9_]+)\((.*?)\)')
where_re = re.compile(r'^Where:', re.IGNORECASE)
error_code_re = re.compile(r'^(IERR|IVAL|RVAL|CVAL|CMPVAL)\s*=\s*(.*)')
array_arg_re = re.compile(r'([A-Z0-9_]+)\((\d+)\)')

# Utility functions

def log(msg):
    print(msg)
    logging.info(msg)

def parse_python_syntax_block(lines: List[str]) -> Optional[Tuple[str, str, List[str]]]:
    """Extract return vars, function name, and argument list from python syntax block."""
    for line in lines:
        m = python_cmd_re.match(line.strip())
        if m:
            ret_vars, func, args = m.groups()
            arg_list = [a.strip() for a in args.split(',')] if args.strip() else []
            return ret_vars, func, arg_list
    return None

def extract_arg_info(arg_block):
    """Extracts (official_name, human_readable, type, options, docstring) from a multi-line argument block."""
    import re
    lines = arg_block if isinstance(arg_block, list) else arg_block.split('\n')
    arg_type = None
    official = None
    human = None
    options = []
    doc_lines = []
    array_elements = []
    # First line: type and main name
    m = re.match(r'^(Integer|Real|Character\*\d+|Character|String|Float|Double|Logical|Boolean)\s+([A-Z0-9_\(\)]+)\s*(.*)', lines[0], re.IGNORECASE)
    if m:
        arg_type = m.group(1)
        official = m.group(2)
        rest = m.group(3).strip()
        if rest:
            rest = re.sub(r'\(.*?\)', '', rest).strip('. ').strip()
            if rest:
                human = rest
        doc_lines.append(f"{arg_type} {official}: {human if human else ''}")
    else:
        m2 = re.match(r'^([A-Z0-9_]+\(\d+\))\s+(.*)', lines[0])
        if m2:
            official = m2.group(1)
            human = m2.group(2).strip()
            doc_lines.append(f"{official}: {human}")
        else:
            parts = lines[0].split(' ', 1)
            if len(parts) == 2:
                official, human = parts[0], parts[1].strip()
                doc_lines.append(f"{official}: {human}")
    # Process additional lines for array elements and options
    for line in lines[1:]:
        line = line.strip()
        if not line:
            continue
        m_elem = re.match(r'^([A-Z0-9_]+\(\d+\))\s+(.*)', line)
        if m_elem:
            array_elements.append(f"{m_elem.group(1)}: {m_elem.group(2)}")
            continue
        m_opt = re.match(r'^([A-Z0-9_\(\)]+)\s*=\s*([0-9]+)\s*(.*)', line)
        if m_opt:
            options.append(f"{m_opt.group(1)} = {m_opt.group(2)} {m_opt.group(3)}")
            continue
        m_opt2 = re.match(r'^(\d+)\s*=\s*(.*)', line)
        if m_opt2:
            options.append(f"{m_opt2.group(1)} = {m_opt2.group(2)}")
            continue
        doc_lines.append(line)
    # Add array elements and options to docstring if present
    if array_elements:
        doc_lines.append('Array elements:')
        doc_lines.extend(array_elements)
    if options:
        doc_lines.append('Options:')
        doc_lines.extend(options)
    # Ensure docstring is not empty if there is info
    docstring = ' '.join(doc_lines).strip()
    if not docstring and (array_elements or options):
        docstring = ' '.join(array_elements + options)
    if not docstring:
        docstring = '(undocumented)'
    return official, human, arg_type, options, docstring

def parse_args_block(args_block_lines):
    """Parse the 'Where:' block into argument info and error codes."""
    import re
    args = []
    error_codes = []
    current_arg = []
    in_error = False
    for line in args_block_lines:
        line = line.rstrip()
        if not line.strip():
            continue
        # Error code lines
        if re.match(r'^[A-Z]+\s*=\s*', line):
            in_error = True
            if current_arg:
                args.append(current_arg)
                current_arg = []
            error_codes.append(line)
            continue
        if in_error:
            # If error code block continues, keep adding
            if re.match(r'^[A-Z]+\s*=\s*', line):
                error_codes.append(line)
            else:
                in_error = False
        # Argument lines
        if not in_error:
            # New argument line: starts with type or variable name
            if re.match(r'^(Integer|Real|Character\*?\d*|Complex|Logical|Boolean|String|[A-Z0-9_]+\(\d+\)|[A-Z0-9_]+)\s', line):
                if current_arg:
                    args.append(current_arg)
                    current_arg = []
                current_arg.append(line)
            else:
                # Indented or continued line for current argument
                if current_arg:
                    current_arg.append(line)
    if current_arg:
        args.append(current_arg)
    return args, error_codes

def parse_api_file(filepath: str) -> List[Dict]:
    """Parse an APIx.md file and return a list of chapter dicts with commands."""
    chapters = []
    with open(filepath, encoding='utf-8') as f:
        lines = f.readlines()
    chapter = None
    command = None
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        chap_match = chapter_header_re.match(line)
        sect_match = section_header_re.match(line)
        if chap_match:
            if chapter:
                chapters.append(chapter)
            chapter = {'number': chap_match.group(1), 'name': '', 'commands': []}
        elif sect_match:
            if command:
                chapter['commands'].append(command)
            command = {
                'section': sect_match.group(1),
                'name': sect_match.group(2),
                'desc': '',
                'python_syntax': [],
                'args': [],
                'error_codes': [],
                'raw_block': []
            }
            # Grab description and syntax blocks
            j = i + 1
            while j < len(lines) and not section_header_re.match(lines[j]) and not chapter_header_re.match(lines[j]):
                command['raw_block'].append(lines[j])
                if 'Python command syntax:' in lines[j]:
                    # Parse python syntax block
                    k = j + 1
                    while k < len(lines) and (lines[k].strip().startswith('```') or lines[k].strip() == '' or lines[k].strip().startswith('ierr')):
                        if lines[k].strip().startswith('```'):
                            k += 1
                            continue
                        command['python_syntax'].append(lines[k].strip())
                        k += 1
                    j = k - 1
                elif where_re.match(lines[j]):
                    # Parse argument list
                    k = j + 1
                    while k < len(lines) and lines[k].strip() != '' and not section_header_re.match(lines[k]) and not chapter_header_re.match(lines[k]):
                        command['args'].append(lines[k].strip())
                        k += 1
                    j = k - 1
                elif error_code_re.match(lines[j]):
                    command['error_codes'].append(lines[j].strip())
                j += 1
            i = j - 1
        i += 1
    if command:
        chapter['commands'].append(command)
    if chapter:
        chapters.append(chapter)
    return chapters

def generate_code_blocks(command: Dict) -> Tuple[str, str, str, Dict[str, str]]:
    """Generate official, human-readable code blocks, error_codes dict, and lookup dict for a command."""
    py_syntax = parse_python_syntax_block(command['python_syntax'])
    if not py_syntax:
        return '', '', '', {}
    ret_vars, func, arg_list = py_syntax
    # Build a mapping from official arg to (type, desc, options, human)
    arg_info_map = {}
    for arg in command['args']:
        var, hr, arg_type, options, docstring = extract_arg_info(arg)
        if var:
            # Normalize: uppercase, strip array indices, underscores
            norm_var = re.sub(r'\(.*?\)', '', var).replace('_', '').upper()
            arg_info_map[norm_var] = (arg_type, arg, options, hr, docstring)
            # Also store lowercase for fallback
            arg_info_map[norm_var.lower()] = (arg_type, arg, options, hr, docstring)
    # Build official and human-readable arg lists
    official_args = []
    human_args = []
    doc_lines = []
    lookup = {}
    for arg in arg_list:
        # Try several normalizations
        norm_arg = re.sub(r'\(.*?\)', '', arg).replace('_', '').upper()
        norm_arg_lc = norm_arg.lower()
        found = False
        for key in [norm_arg, norm_arg_lc, arg.upper(), arg.lower()]:
            if key in arg_info_map:
                arg_type, desc, options, hr, docstring = arg_info_map[key]
                official_args.append(arg)
                human_name = hr if hr and hr != arg else arg
                human_args.append(human_name)
                lookup[arg] = human_name
                doc_lines.append(f':param {arg}: {docstring}')
                found = True
                break
        if not found:
            # Try partial match (e.g., ival matches IVAL)
            for key in arg_info_map:
                if key.endswith(norm_arg) or key.endswith(norm_arg_lc):
                    arg_type, desc, options, hr, docstring = arg_info_map[key]
                    official_args.append(arg)
                    human_name = hr if hr and hr != arg else arg
                    human_args.append(human_name)
                    lookup[arg] = human_name
                    doc_lines.append(f':param {arg}: {docstring}')
                    found = True
                    log(f'Partial match: {arg} -> {key} in {func}')
                    break
        if not found:
            official_args.append(arg)
            human_args.append(arg)
            lookup[arg] = arg
            doc_lines.append(f':param {arg}: (undocumented)')
            log(f'Could not match argument: {arg} in {func}')
    # Error codes: only include lines that look like error codes
    error_dict = {}
    for err in command['error_codes']:
        m = re.match(r'^(IERR|IVAL|RVAL|CVAL|CMPVAL)\s*=\s*(.*)', err)
        if m and m.group(1).startswith('IERR'):
            code, meaning = m.groups()
            error_dict[code] = meaning
    # Official code block
    official_code = f"""def {func}({', '.join(official_args)}):\n    """
    if doc_lines:
        official_code += '    ' + '\n    '.join(doc_lines) + '\n'
    official_code += f"    :return: {ret_vars}\n    \n"
    # Human-readable code block
    human_code = f"""def {func}({', '.join(human_args)}):\n    """
    if doc_lines:
        human_code += '    ' + '\n    '.join(doc_lines) + '\n'
    human_code += f"    :return: {ret_vars}\n    \n"
    # Error codes dict
    error_code_block = f"error_codes = {error_dict}\n"
    return official_code, human_code, error_code_block, lookup

def main():
    master_blocks = []
    lookup_dict = {}
    for api_file in API_MD_FILES:
        filepath = os.path.join(API_DOC_DIR, api_file)
        log(f'Parsing {filepath}')
        chapters = parse_api_file(filepath)
        for chapter in chapters:
            chap_num = chapter['number']
            chap_name = chapter.get('name', f'chapter_{chap_num}')
            out_path = os.path.join(CHAPTERS_DIR, f'chapter_{chap_num}_{chap_name}.md')
            with open(out_path, 'w', encoding='utf-8') as f:
                for cmd in chapter['commands']:
                    official, human, errblock, lookup = generate_code_blocks(cmd)
                    f.write(f'\n```python\n{official}\n```\n')
                    f.write(f'\n```python\n{human}\n```\n')
                    f.write(f'\n```python\n{errblock}\n```\n')
                    master_blocks.append(official + '\n' + human + '\n' + errblock)
                    lookup_dict.update(lookup)
    # Write master python file
    with open(MASTER_PY_FILE, 'w', encoding='utf-8') as f:
        for block in master_blocks:
            f.write(block + '\n')
    # Write lookup dict
    with open(LOOKUP_DICT_FILE, 'w', encoding='utf-8') as f:
        f.write('official_to_human = ' + repr(lookup_dict) + '\n')

if __name__ == '__main__':
    main() 