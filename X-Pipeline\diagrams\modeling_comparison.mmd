graph TB
    subgraph "Bus-Branch Modeling"
        BusBranchData["Equipment Data<br/>- Direct bus references<br/>- No substation topology<br/>- Traditional PSS/E format"]
        BusBranchOutput["RAW Output<br/>- Flat bus structure<br/>- Standard equipment sections<br/>- Ends with 'Q'"]
    end

    subgraph "Node-Breaker Modeling"
        NodeBreakerData["Equipment Data<br/>- Substation topology<br/>- Node connections<br/>- Switching devices"]
        NodeBreakerOutput["RAW Output<br/>- Hierarchical substation blocks<br/>- Node/switching device sections<br/>- Ends with 'Q'"]
    end

    subgraph "Hybrid Modeling"
        HybridData["Equipment Data<br/>- Node info embedded<br/>- Mapped to bus numbers<br/>- Simplified topology"]
        HybridOutput["RAW Output<br/>- Simplified bus structure<br/>- Standard equipment sections<br/>- Optional substation data"]
    end

    BusBranchData --> BusBranchOutput
    NodeBreakerData --> NodeBreakerOutput
    HybridData --> HybridOutput 