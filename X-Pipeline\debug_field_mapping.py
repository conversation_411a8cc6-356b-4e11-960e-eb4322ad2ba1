#!/usr/bin/env python3

from hdb_to_raw_pipeline import FIELD_MAP

print("Checking RAWX terminal field mapping...")

terminal_spec = FIELD_MAP.get('terminal', {})
print("Canonical terminal fields and their aliases:")
for canonical_name, spec in terminal_spec.items():
    print(f"  {canonical_name}: aliases = {spec.aliases}")

print("\nRAWX terminal fields found in file:")
rawx_fields = ["isub", "inode", "type", "eqid", "ibus", "jbus", "kbus"]
print(f"  {rawx_fields}")

print("\nField mapping check:")
for rawx_field in rawx_fields:
    found_canonical = None
    for canonical_name, spec in terminal_spec.items():
        if rawx_field in spec.aliases:
            found_canonical = canonical_name
            break
    status = found_canonical or "NOT MAPPED"
    print(f"  {rawx_field} -> {status}")
    
print("\nChecking if all RAWX fields are properly aliased...")
unmapped_fields = []
for rawx_field in rawx_fields:
    found = False
    for canonical_name, spec in terminal_spec.items():
        if rawx_field in spec.aliases:
            found = True
            break
    if not found:
        unmapped_fields.append(rawx_field)

if unmapped_fields:
    print(f"❌ Unmapped RAWX fields: {unmapped_fields}")
else:
    print("✅ All RAWX fields are properly mapped to canonical names") 