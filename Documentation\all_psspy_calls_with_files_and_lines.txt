psspy.a2trmdcchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:301)
psspy.aareaint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:321)
psspy.abrnchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:271)
psspy.abrnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:265)
psspy.abrnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:268)
psspy.abuschar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:225)
psspy.abuscount (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:74)
psspy.abuscount (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:78)
psspy.abusint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:222)
psspy.afactschar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:316)
psspy.afxshuntchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:255)
psspy.afxshuntint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:252)
psspy.agenbusint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:230)
psspy.aindmacchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:339)
psspy.aindmacint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:336)
psspy.alert_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\output.py:39)
psspy.aloadchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:247)
psspy.aloadint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:244)
psspy.amachchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:239)
psspy.amachcount (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:47)
psspy.amachint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:236)
psspy.amachint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:53)
psspy.amultitrmdcchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:306)
psspy.anodechar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:78)
psspy.anodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:77)
psspy.aownerint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:326)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:170)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:174)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:184)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:188)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:198)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:202)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:212)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:216)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:226)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:230)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:241)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:245)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:257)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:260)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:271)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:275)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:288)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:291)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:302)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:306)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:318)
psspy.ardat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:321)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:102)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:113)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:122)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:133)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:142)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:153)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:161)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:93)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:128)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:155)
psspy.area_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:58)
psspy.aredat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:111)
psspy.aredat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:117)
psspy.aredat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:131)
psspy.aredat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:137)
psspy.aredat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:102)
psspy.areint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:91)
psspy.areint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:97)
psspy.areint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:100)
psspy.areint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:153)
psspy.arenam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:151)
psspy.arenam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:156)
psspy.arenam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:38)
psspy.arenam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:98)
psspy.aritoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:372)
psspy.aritoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:374)
psspy.aritoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:396)
psspy.aritoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:400)
psspy.astaswdevchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:87)
psspy.astaswdevint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:86)
psspy.aswshint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:260)
psspy.atr3char (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:296)
psspy.atr3int (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:287)
psspy.atr3int (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:290)
psspy.atr3int (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:293)
psspy.atrnchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:282)
psspy.atrnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:276)
psspy.atrnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:279)
psspy.avscdcchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:311)
psspy.azoneint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case.py:331)
psspy.branch_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Contingency.py:91)
psspy.branch_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:83)
psspy.branch_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_LineParameter.py:88)
psspy.branch_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:72)
psspy.branch_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:937)
psspy.branch_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:996)
psspy.branch_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:137)
psspy.branch_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:279)
psspy.brk_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_ProtectionDevice.py:73)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:234)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:336)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:341)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:935)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:507)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:106)
psspy.brndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:84)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1060)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1063)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1074)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1077)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1088)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1091)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:357)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:367)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:511)
psspy.brndt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_LineParameter.py:50)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1102)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1106)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1118)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1122)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1149)
psspy.brnflo (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1153)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:317)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:322)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:994)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:415)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:426)
psspy.brnint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:503)
psspy.brnmsc (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1135)
psspy.brnmsc (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1138)
psspy.brnnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:392)
psspy.brnnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:396)
psspy.bsys (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:165)
psspy.bsys (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:168)
psspy.bsysadd (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:186)
psspy.bsysadd (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:102)
psspy.bsysadd (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:106)
psspy.bsysadd (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:110)
psspy.bsysadd (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:52)
psspy.bsysclr (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:70)
psspy.bsysdef (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:322)
psspy.bsysdef (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:326)
psspy.bsysdel (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:63)
psspy.bsysinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:348)
psspy.bsysinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:22)
psspy.bsysinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:98)
psspy.bsyslist (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:80)
psspy.bsysmem (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:359)
psspy.bsysmem (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:362)
psspy.bsysrcl (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:375)
psspy.bsysrcl (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_subsystem_definition.py:379)
psspy.bus_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:102)
psspy.bus_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:104)
psspy.bus_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:106)
psspy.bus_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:108)
psspy.bus_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:110)
psspy.bus_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:50)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:107)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:117)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:144)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:163)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:178)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:187)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:202)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:211)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:226)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:235)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:267)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:276)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:289)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:298)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:311)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:320)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:335)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:344)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:358)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:367)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:381)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:390)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:404)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:413)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:427)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:436)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:450)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:459)
psspy.bus_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:766)
psspy.bus_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:123)
psspy.bus_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:86)
psspy.bus_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:95)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:265)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:271)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:287)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:293)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:309)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:315)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:333)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:339)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:356)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:362)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:379)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:385)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:402)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:408)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:425)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:431)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:448)
psspy.busdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:454)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:471)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:475)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:486)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:490)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:501)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:505)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:516)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:520)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:531)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:535)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:547)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:551)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:563)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:567)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:578)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:582)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:593)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:597)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:609)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:613)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:625)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:629)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:640)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:644)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:654)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:659)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:668)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:677)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:687)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:691)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:700)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:709)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:718)
psspy.busdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:727)
psspy.busexs (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:1733)
psspy.busexs (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:93)
psspy.busexs (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2144)
psspy.busexs (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:97)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:146)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:149)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:176)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:182)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:200)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:206)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:224)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:230)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:246)
psspy.busint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:250)
psspy.busmsm (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:737)
psspy.busmsm (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:740)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:2108)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:76)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:321)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2541)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:83)
psspy.case (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:126)
psspy.case_title_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:50)
psspy.case_title_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:59)
psspy.case_title_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:69)
psspy.case_title_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:78)
psspy.dist_branch_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Fault.py:53)
psspy.dist_bus_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Contingency.py:97)
psspy.dist_bus_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:76)
psspy.dist_bus_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Fault.py:51)
psspy.dist_bus_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:65)
psspy.dist_clear_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Contingency.py:106)
psspy.dist_clear_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:87)
psspy.dist_clear_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Fault.py:66)
psspy.dist_trans_fault (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Fault.py:55)
psspy.dsrval (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:104)
psspy.dsrval (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:111)
psspy.fdns (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:145)
psspy.fdns (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:104)
psspy.fdns (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:105)
psspy.fnsl (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:199)
psspy.fxsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:108)
psspy.fxsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:55)
psspy.fxsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:78)
psspy.fxsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:93)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:103)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:45)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:50)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:60)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:69)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:83)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:88)
psspy.fxsdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:98)
psspy.fxsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:113)
psspy.fxsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:36)
psspy.gendat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:38)
psspy.gendat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:43)
psspy.gendat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:53)
psspy.gendt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:48)
psspy.getbatdefaults (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:47)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:48)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:340)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1025)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:88)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:26)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:755)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:140)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:491)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:517)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:50)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:276)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:59)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:35)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:334)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:13)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:21)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:521)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:278)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:16)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:22)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:51)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\limits.py:29)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:20)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:58)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:13)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:11)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:29)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:29)
psspy.getdefaultchar (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:48)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:333)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:338)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1018)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1023)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:86)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:20)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:748)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:753)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:138)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:484)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:489)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:515)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:48)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:269)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:274)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:57)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:33)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:332)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:11)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:17)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:519)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:271)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:276)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:14)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:20)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:18)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:11)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:10)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:27)
psspy.getdefaultint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:27)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:47)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:334)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\area.py:339)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1019)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1024)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:87)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:23)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:749)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:754)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:139)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:485)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:490)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:516)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:49)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:270)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:275)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\plant.py:58)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:34)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:333)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:12)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:20)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:520)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:272)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:277)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:15)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:21)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:50)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\limits.py:28)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:19)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:57)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:12)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:28)
psspy.getdefaultreal (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:28)
psspy.infl (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:90)
psspy.iterat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:66)
psspy.load_chng_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Load.py:93)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:101)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:110)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:152)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:161)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:176)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:185)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:200)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:209)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:225)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:234)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:239)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:276)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:284)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:298)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:306)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:335)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:343)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:357)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:365)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:394)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:402)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:416)
psspy.load_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:424)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:259)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:262)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:318)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:321)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:377)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:380)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:432)
psspy.loddt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:458)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:274)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:279)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:296)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:301)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:333)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:338)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:355)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:360)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:392)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:397)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:414)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:419)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:440)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:449)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:466)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:475)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Load.py:33)
psspy.loddt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Load.py:75)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:105)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:115)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:150)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:156)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:174)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:180)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:198)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:204)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:223)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:229)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:247)
psspy.lodint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:99)
psspy.losses (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:72)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:122)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:136)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:150)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:164)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:178)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:192)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:197)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:211)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:225)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:230)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:245)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:254)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:268)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:282)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:296)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:310)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:324)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:333)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:338)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:343)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:348)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:57)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:58)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Generator.py:33)
psspy.macdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Generator.py:75)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:353)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:358)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:363)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:368)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:382)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:396)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:401)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:415)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:429)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:434)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:439)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:444)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:449)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:454)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:459)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:464)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:469)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:474)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:479)
psspy.macdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:484)
psspy.machine_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Contingency.py:94)
psspy.machine_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:78)
psspy.machine_chng_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Generator.py:93)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:127)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:141)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:155)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:169)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:183)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:202)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:216)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:235)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:259)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:273)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:287)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:301)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:315)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:373)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:387)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:406)
psspy.machine_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:420)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:100)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:111)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:42)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:66)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:78)
psspy.machine_data_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:89)
psspy.machine_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:527)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:106)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:117)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:37)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:47)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:489)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:56)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:61)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:73)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:84)
psspy.macint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:95)
psspy.maxmsm (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:69)
psspy.mslv (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:122)
psspy.newcase_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:201)
psspy.newcase_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:2093)
psspy.newcase_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:213)
psspy.newcase_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2525)
psspy.notano (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:131)
psspy.notona (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:109)
psspy.notona (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:134)
psspy.nsol (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:166)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:111)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:115)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:125)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:129)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:139)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:143)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:153)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:157)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:167)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:171)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:182)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:186)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:198)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:201)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:213)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:216)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:228)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:231)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:243)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:246)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:258)
psspy.owndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:261)
psspy.owner_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:102)
psspy.owner_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:94)
psspy.owner_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Owner.py:90)
psspy.ownnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:92)
psspy.ownnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\owner.py:97)
psspy.ownname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Owner.py:28)
psspy.ownname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Owner.py:69)
psspy.plant_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:52)
psspy.plant_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:250)
psspy.plant_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:329)
psspy.plant_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:524)
psspy.progress_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\output.py:52)
psspy.progress_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:113)
psspy.progress_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:45)
psspy.prompt_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\output.py:26)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:1172)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:320)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:376)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:381)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:395)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Area.py:184)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:184)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Bus.py:190)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Contingency.py:123)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_CostAnalysis.py:130)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:129)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Fault.py:106)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Generator.py:130)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_LineParameter.py:128)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Load.py:130)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Owner.py:109)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_ProtectionDevice.py:109)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Shunt.py:116)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:137)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Subsystem.py:121)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:159)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:131)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Zone.py:109)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:290)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:125)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:361)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:366)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:361)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:366)
psspy.psseinit (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:43)
psspy.purgbrn (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:1498)
psspy.purgbrn (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:1519)
psspy.purgload (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:1537)
psspy.purgshunt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:1186)
psspy.purgshunt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:1608)
psspy.ratingx (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:110)
psspy.ratingx (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Branch.py:140)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:1755)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:89)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2167)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:92)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:108)
psspy.rawd_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:98)
psspy.read (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:2111)
psspy.read (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:78)
psspy.read (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2544)
psspy.read (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:85)
psspy.read (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:50)
psspy.recn (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\bus_nb.py:158)
psspy.report_output (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\output.py:65)
psspy.run (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:51)
psspy.run (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:59)
psspy.run (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:91)
psspy.run (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:95)
psspy.save (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:1746)
psspy.save (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\case_utilities.py:87)
psspy.save (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:2157)
psspy.save (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:90)
psspy.set_stanode_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:174)
psspy.shunt_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Shunt.py:88)
psspy.shunt_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:70)
psspy.shunt_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:78)
psspy.shunt_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:86)
psspy.shunt_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:41)
psspy.shunt_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:65)
psspy.shunt_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:74)
psspy.shuntdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:109)
psspy.shuntdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Shunt.py:31)
psspy.shuntdt2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Shunt.py:72)
psspy.shuntint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:97)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:216)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:225)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:234)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:244)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:254)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:263)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:272)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:281)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:290)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:299)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:309)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:318)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:327)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:336)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:345)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:354)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:363)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:372)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:381)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:390)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:399)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:409)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:419)
psspy.solution_parameters_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:429)
psspy.solv (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\power_flow.py:106)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:137)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:139)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:141)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:124)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:126)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:128)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:124)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:126)
psspy.stadat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:128)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:135)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:58)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:122)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:53)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:122)
psspy.staname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:53)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:135)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:148)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:149)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:179)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:180)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:50)
psspy.stanodeint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:52)
psspy.stanodename (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:133)
psspy.stanodename (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:79)
psspy.stanodename (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:48)
psspy.staswdevdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:101)
psspy.staswdevdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:102)
psspy.staswdevint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:99)
psspy.staswdevint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:100)
psspy.staswdevname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:56)
psspy.staswdevname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:97)
psspy.staswdevname (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:98)
psspy.station_ampout (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:300)
psspy.station_ampout (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:285)
psspy.station_ampout (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:285)
psspy.station_branch_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:488)
psspy.station_branch_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:448)
psspy.station_branch_term_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\branch_nb.py:511)
psspy.station_branch_term_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:471)
psspy.station_build_config (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:293)
psspy.station_build_config (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:278)
psspy.station_build_config (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:278)
psspy.station_build_config (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:70)
psspy.station_bus_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:279)
psspy.station_bus_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:264)
psspy.station_bus_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:264)
psspy.station_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:107)
psspy.station_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:94)
psspy.station_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:94)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:106)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:91)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:78)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:93)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:78)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:93)
psspy.station_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:72)
psspy.station_load_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\load_nb.py:124)
psspy.station_machine_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\machine_nb.py:498)
psspy.station_machine_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\case_utilities_nb.py:631)
psspy.station_node_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:84)
psspy.station_node_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:175)
psspy.station_node_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:67)
psspy.station_node_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\node_nb.py:164)
psspy.station_node_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\node.py:90)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:189)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:197)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:174)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:182)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:174)
psspy.station_number (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:182)
psspy.station_shunt_term_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\fixed_shunt_nb.py:121)
psspy.station_swd_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\RawEditor\examples\sample_add_substations.py:93)
psspy.station_swd_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:154)
psspy.station_swd_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:150)
psspy.station_swd_mbid (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:362)
psspy.station_swd_mbid (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:256)
psspy.station_swd_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:286)
psspy.station_swd_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:335)
psspy.station_swd_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:230)
psspy.station_swd_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:271)
psspy.station_swd_move (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:271)
psspy.station_swd_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:385)
psspy.station_swd_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\Final_Switching_Devices.py:279)
psspy.station_tree (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\station_nb.py:307)
psspy.station_tree (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station.py:292)
psspy.station_tree (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\Obsolete\station_final.py:292)
psspy.strt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_DynamicSimulation.py:48)
psspy.strt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_StabilityAnalysis.py:88)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:113)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:135)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:157)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:179)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:201)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:223)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:245)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:268)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:282)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:296)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:310)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:324)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:47)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:60)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:73)
psspy.switched_shunt_data_3 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:86)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:104)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:121)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:126)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:143)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:148)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:165)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:170)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:187)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:192)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:209)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:214)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:231)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:236)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:254)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:259)
psspy.swsblk (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:99)
psspy.swsdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:277)
psspy.swsdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:291)
psspy.swsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:305)
psspy.swsdt1 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:319)
psspy.swsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:42)
psspy.swsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:55)
psspy.swsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:68)
psspy.swsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:81)
psspy.swsint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switched_shunt.py:94)
psspy.system_swd_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\switching_device_nb.py:185)
psspy.tcsc_chng_2 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:57)
psspy.tcscint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_VoltageControlDevice.py:123)
psspy.titldt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:48)
psspy.titldt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:67)
psspy.titldt (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\case_title_data.py:85)
psspy.tr3_winding_chng (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:108)
psspy.tr3_winding_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:149)
psspy.tr3nam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:67)
psspy.tr3nam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:88)
psspy.tree (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:1066)
psspy.tree (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\modelling\psse\topology.py:1068)
psspy.two_winding_chng_6 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:110)
psspy.two_winding_chng_6 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:69)
psspy.two_winding_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:91)
psspy.two_winding_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:92)
psspy.two_winding_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:93)
psspy.two_winding_data_4 (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:499)
psspy.two_winding_purg (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Transformers.py:151)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:108)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:293)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:302)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:311)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:320)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:329)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:338)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:347)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:356)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:361)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:370)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:379)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:388)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:397)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:406)
psspy.xfrdat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:99)
psspy.xfrint (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:515)
psspy.xfrnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\two_winding_transformer_nb.py:46)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:112)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:116)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:126)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:130)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:140)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:144)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:154)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:158)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:168)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:172)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:183)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:187)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:199)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:202)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:213)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:217)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:230)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:233)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:244)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:248)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:260)
psspy.zndat (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:263)
psspy.znitoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:305)
psspy.znitoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:307)
psspy.znitoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:329)
psspy.znitoj (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:333)
psspy.zone_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:103)
psspy.zone_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:95)
psspy.zone_data (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Zone.py:90)
psspy.zonename (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Zone.py:28)
psspy.zonename (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\GPT\GPT_Zone.py:69)
psspy.zonnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:93)
psspy.zonnam (C:\Users\<USER>\Documents\CursorCreatedPrograms\PSSPY_removing_PSSE\controller\psse\zone.py:98)
