#!/usr/bin/env python3
"""
Backend Modeling Consistency Test (Windows Safe)
================================================

This script tests whether any backend applies different data transformations
based on the modeling_approach parameter. It loads the same input file with all
available modeling approaches and compares the canonical outputs for any differences.

If the backend is working correctly, all outputs should be identical
since the modeling approach should only affect export formatting, not data loading.

Supports multiple backend types: HDB, RAWX, JSON, etc.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List

# Add the parent directory to the path to access the pipeline
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from hdb_to_raw_pipeline import ModelingApproach, HdbBackend, RawxBackend
    print("SUCCESS: Imported backend components")
except ImportError as e:
    print(f"ERROR: Failed to import components: {e}")
    print("Please ensure you're running from the X-Pipeline directory")
    sys.exit(1)

# Import the verification module
from verify_canonical_outputs import CanonicalOutputVerifier


class BackendModelingConsistencyTester:
    """Test any backend's consistency across modeling approaches."""
    
    # Backend type mapping
    BACKEND_TYPES = {
        'hdb': HdbBackend,
        'rawx': RawxBackend,
        # Add more backend types as needed
    }
    
    # File extension to backend type mapping
    FILE_EXTENSION_MAP = {
        '.hdb': 'hdb',
        '.hdbcontext': 'hdb',
        '.rawx': 'rawx',
        '.json': 'hdb',  # Assume JSON is canonical/HDB format
    }
    
    def __init__(self, input_file_path: str, backend_type: Optional[str] = None, 
                 output_dir: str = "test_modeling_consistency"):
        """
        Initialize the tester with input file and backend type.
        
        Args:
            input_file_path: Path to input file to test
            backend_type: Backend type ('hdb', 'rawx', etc.) - auto-detected if None
            output_dir: Output directory for test results
        """
        self.input_file_path = Path(input_file_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Auto-detect backend type if not specified
        if backend_type is None:
            backend_type = self._detect_backend_type()
        
        self.backend_type = backend_type
        self.backend_class = self.BACKEND_TYPES.get(backend_type)
        
        if self.backend_class is None:
            raise ValueError(f"Unsupported backend type: {backend_type}")
        
        # Timestamp for this test run
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Results storage
        self.results: Dict[str, Dict[str, Any]] = {}
        self.output_files: List[Path] = []
        
        print(f"Backend Modeling Consistency Test")
        print(f"   Backend type: {self.backend_type.upper()}")
        print(f"   Input file: {self.input_file_path}")
        print(f"   Output directory: {self.output_dir}")
        print(f"   Test timestamp: {self.timestamp}")
    
    def _detect_backend_type(self) -> str:
        """Auto-detect backend type from file extension."""
        file_extension = self.input_file_path.suffix.lower()
        backend_type = self.FILE_EXTENSION_MAP.get(file_extension)
        
        if backend_type is None:
            raise ValueError(f"Cannot auto-detect backend type for file extension: {file_extension}")
        
        return backend_type
    
    def load_with_modeling_approach(self, modeling_approach: ModelingApproach) -> Optional[Dict[str, Any]]:
        """Load input file with specific modeling approach and return canonical data."""
        approach_name = modeling_approach.value
        print(f"\nLoading {self.backend_type.upper()} with modeling approach: {approach_name}")
        
        try:
            # Create backend with specific modeling approach
            if self.backend_type == 'hdb':
                backend = self.backend_class(
                    file_path=str(self.input_file_path),
                    modeling_approach=modeling_approach
                )
            elif self.backend_type == 'rawx':
                backend = self.backend_class(
                    file_path=str(self.input_file_path),
                    modeling_approach=modeling_approach
                )
            else:
                # Generic backend creation
                backend = self.backend_class(
                    file_path=str(self.input_file_path),
                    modeling_approach=modeling_approach
                )
            
            print(f"   Backend created successfully")
            
            # Load all data to canonical format
            canonical_data = backend.to_canonical()
            print(f"   Data loaded to canonical format")
            
            # Count records in each section
            record_counts = {}
            total_records = 0
            
            for section_name, section_data in canonical_data.items():
                if isinstance(section_data, dict) and 'data' in section_data:
                    # Standard canonical format with 'data' key
                    count = len(section_data['data']) if section_data['data'] else 0
                elif isinstance(section_data, list):
                    # Direct list format
                    count = len(section_data)
                elif isinstance(section_data, dict):
                    # Dictionary format - count as 1 if non-empty
                    count = 1 if section_data else 0
                else:
                    count = 1 if section_data else 0
                
                record_counts[section_name] = count
                total_records += count
            
            print(f"   Total records: {total_records:,}")
            print(f"   Sections: {len(record_counts)}")
            
            # Store results
            self.results[approach_name] = {
                'canonical_data': canonical_data,
                'record_counts': record_counts,
                'total_records': total_records,
                'load_success': True,
                'error': None
            }
            
            return canonical_data
            
        except Exception as e:
            print(f"   ERROR: Failed to load with {approach_name}: {e}")
            self.results[approach_name] = {
                'canonical_data': None,
                'record_counts': {},
                'total_records': 0,
                'load_success': False,
                'error': str(e)
            }
            return None
    
    def save_canonical_output(self, canonical_data: Dict[str, Any], approach_name: str) -> Optional[Path]:
        """Save canonical data to JSON file."""
        if canonical_data is None:
            return None
            
        output_file = self.output_dir / f"canonical_{self.backend_type}_{approach_name}_{self.timestamp}.json"
        
        try:
            # Custom JSON encoder for non-serializable objects
            def json_encoder(obj):
                if hasattr(obj, '__dict__'):
                    return obj.__dict__
                return str(obj)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(canonical_data, f, indent=2, default=json_encoder)
            
            file_size = output_file.stat().st_size
            
            print(f"   Saved to: {output_file}")
            print(f"   File size: {file_size:,} bytes")
            
            self.output_files.append(output_file)
            return output_file
            
        except Exception as e:
            print(f"   ERROR: Failed to save {approach_name}: {e}")
            return None
    
    def run_verification(self) -> bool:
        """Run verification using the separate verification module."""
        if len(self.output_files) < 2:
            print("ERROR: Need at least 2 output files to verify")
            return False
        
        print(f"\nRUNNING VERIFICATION")
        print(f"=" * 50)
        
        # Use the verification module
        verifier = CanonicalOutputVerifier(self.output_dir)
        return verifier.verify_all_files()
    
    def generate_summary_report(self) -> Path:
        """Generate comprehensive summary report."""
        report_file = self.output_dir / f"{self.backend_type}_consistency_test_report_{self.timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"{self.backend_type.upper()} Backend Modeling Consistency Test Report\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Backend Type: {self.backend_type.upper()}\n")
            f.write(f"Input File: {self.input_file_path}\n")
            f.write(f"Output Directory: {self.output_dir}\n\n")
            
            f.write("MODELING APPROACHES TESTED:\n")
            f.write("-" * 30 + "\n")
            for approach in ModelingApproach:
                result = self.results.get(approach.value, {})
                status = "SUCCESS" if result.get('load_success', False) else "FAILED"
                f.write(f"{approach.value:25} {status}\n")
                if not result.get('load_success', False):
                    f.write(f"                          Error: {result.get('error', 'Unknown')}\n")
            
            f.write(f"\nRECORD COUNT SUMMARY:\n")
            f.write("-" * 20 + "\n")
            successful_results = {
                name: result for name, result in self.results.items() 
                if result['load_success']
            }
            
            if successful_results:
                all_sections = set()
                for result in successful_results.values():
                    all_sections.update(result['record_counts'].keys())
                
                for section in sorted(all_sections):
                    counts = [
                        result['record_counts'].get(section, 0)
                        for result in successful_results.values()
                    ]
                    unique_counts = set(counts)
                    if len(unique_counts) == 1:
                        f.write(f"   {section:30} {list(unique_counts)[0]:>8,} records\n")
                    else:
                        f.write(f"   {section:30} INCONSISTENT: {counts}\n")
            
            f.write(f"\nOUTPUT FILES GENERATED:\n")
            f.write("-" * 23 + "\n")
            for file_path in self.output_files:
                size = file_path.stat().st_size
                f.write(f"   {file_path.name} ({size:,} bytes)\n")
        
        print(f"\nSummary report saved to: {report_file}")
        return report_file
    
    def run_full_test(self) -> bool:
        """Run the complete consistency test."""
        print(f"\nSTARTING FULL CONSISTENCY TEST")
        print(f"=" * 70)
        
        # Test all modeling approaches
        all_approaches = list(ModelingApproach)
        print(f"Testing {len(all_approaches)} modeling approaches: {[a.value for a in all_approaches]}")
        
        # Load and save outputs for each approach
        for approach in all_approaches:
            canonical_data = self.load_with_modeling_approach(approach)
            if canonical_data:
                self.save_canonical_output(canonical_data, approach.value)
        
        # Run verification
        verification_passed = self.run_verification()
        
        # Generate summary report
        self.generate_summary_report()
        
        # Final assessment
        print(f"\nFINAL ASSESSMENT:")
        print(f"=" * 20)
        
        successful_loads = sum(1 for r in self.results.values() if r['load_success'])
        total_approaches = len(all_approaches)
        
        print(f"Successful loads: {successful_loads}/{total_approaches}")
        
        if successful_loads < 2:
            print("INSUFFICIENT DATA - Need at least 2 successful loads to compare")
            return False
        elif verification_passed:
            print(f"SUCCESS - All modeling approaches produce identical canonical data")
            print(f"   The {self.backend_type.upper()} backend is NOT applying unwanted transformations")
            return True
        else:
            print(f"WARNING - Differences detected between modeling approaches")
            print(f"   The {self.backend_type.upper()} backend may be applying different transformations")
            print("   Check the verification output above and summary report")
            return False


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Test backend consistency across modeling approaches')
    parser.add_argument('input_file', help='Path to input file to test')
    parser.add_argument('--backend-type', '-b', choices=['hdb', 'rawx'], 
                       help='Backend type (auto-detected if not specified)')
    parser.add_argument('--output-dir', '-o', default='test_modeling_consistency', 
                       help='Output directory for test results')
    
    args = parser.parse_args()
    
    # Validate input file
    input_file = Path(args.input_file)
    if not input_file.exists():
        print(f"ERROR: Input file not found: {input_file}")
        sys.exit(1)
    
    try:
        # Run the test
        tester = BackendModelingConsistencyTester(
            str(input_file), 
            args.backend_type, 
            args.output_dir
        )
        success = tester.run_full_test()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)


 