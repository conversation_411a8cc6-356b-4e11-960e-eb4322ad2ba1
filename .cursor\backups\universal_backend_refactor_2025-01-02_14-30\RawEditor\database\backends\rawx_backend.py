"""
Rawx Backend for RawEditor ecosystem.
"""
import json
import copy
import logging
from typing import Optional, Any, Dict, List, Union
from pathlib import Path

from ..base_backend import BaseBackend, ModelingApproach, SystemState

# Use clean field mapping system - no business logic
from RawEditor.database.field_mapping_only import PureFieldMapper


class RawxBackend(BaseBackend):
    """
    RAWX (JSON) backend for RawEditor ecosystem.
    
    Handles .rawx (JSON) files following Siemens documentation format.
    Supports modeling approach conversions and system state management.
    """
    
    def __init__(self, enable_quality_check: bool = True, auto_fix: bool = True,
                 modeling_approach: Optional[ModelingApproach] = None,
                 system_state: SystemState = SystemState.MAINTAIN_CURRENT,
                 file_path: Optional[str] = None):
        """
        Initialize Rawx backend.
        
        Args:
            enable_quality_check: Enable data quality validation
            auto_fix: Enable automatic fixing of data quality issues
            modeling_approach: Power system modeling approach (auto-detected if None)
            system_state: System operational state configuration
            file_path: Path to RAWX file to load
        """
        super().__init__(enable_quality_check, auto_fix, modeling_approach, system_state)
        
        self.file_path = file_path
        self.data: Dict[str, Any] = {}
        
        # System state backup for restoration
        self._original_data_backup: Optional[Dict[str, Any]] = None
        
        # Initialize pure field mapper (no business logic)
        self.field_mapper = PureFieldMapper()
        
        if file_path:
            self.load(file_path)
    
    def load(self, file_path: Union[str, Path]) -> None:
        """Load RAWX file and extract network data."""
        file_path = Path(file_path)
        self.logger.info(f"Loading RAWX file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                rawx_data = json.load(f)
            
            # Extract network data from RAWX structure
            if 'network' in rawx_data:
                self.data = rawx_data['network']
            else:
                self.data = rawx_data
            
            self.file_path = str(file_path)
            
            # Create backup for system state restoration
            self._original_data_backup = copy.deepcopy(self.data)
            
            # Detect normal state availability
            self.detect_normal_state_availability()
            
            self.logger.info(f"Successfully loaded RAWX with {len(self.data)} sections")
            
        except Exception as e:
            self.logger.error(f"Error loading RAWX file {file_path}: {e}")
            raise
    
    def save(self, file_path: Optional[Union[str, Path]] = None) -> None:
        """Save data to RAWX format."""
        file_path = Path(file_path) if file_path else Path(self.file_path) if self.file_path else None
        
        if not file_path:
            raise ValueError("No file path specified for saving RAWX backend.")
        
        self.logger.info(f"Saving RAWX data to: {file_path}")
        
        try:
            # Wrap data in network structure for RAWX format
            rawx_data = {
                'network': self.data
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rawx_data, f, indent=2, ensure_ascii=False)
            
            self.file_path = str(file_path)
            self.logger.info(f"Successfully saved RAWX data")
            
        except Exception as e:
            self.logger.error(f"Error saving RAWX file {file_path}: {e}")
            raise
    
    def get_data(self) -> Dict[str, Any]:
        """Get all data from the backend."""
        return self.data
    
    def add_records(self, section_name: str, records: List[List[Any]], 
                   fields: Optional[List[str]] = None) -> None:
        """Add records to a specific section."""
        if section_name not in self.data:
            self.data[section_name] = {
                'fields': fields or [],
                'data': []
            }
        
        if fields:
            self.data[section_name]['fields'] = fields
        
        # Convert records to RAWX format (list of lists)
        if 'data' not in self.data[section_name]:
            self.data[section_name]['data'] = []
        
        self.data[section_name]['data'].extend(records)
        
        # Create backup on first data addition
        if self._original_data_backup is None:
            self._original_data_backup = copy.deepcopy(self.data)
        
        self.logger.debug(f"Added {len(records)} records to section '{section_name}'")
    
    def get_records(self, section: str) -> Dict[str, Any]:
        """Get all records from a section."""
        section_data = self.data.get(section, {})
        if 'data' in section_data:
            return {
                'fields': section_data.get('fields', []),
                'records': section_data['data']
            }
        return {'fields': [], 'records': []}
    
    def get_record(self, section: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific record by ID."""
        section_data = self.data.get(section)
        if not section_data or 'data' not in section_data:
            return None
            
        fields = section_data.get('fields', [])
        records = section_data['data']
        
        # Try to find appropriate ID field
        id_fields = ['id', 'ibus', 'jbus', 'name']
        id_index = None
        for id_field in id_fields:
            id_index = self._find_field_index(fields, [id_field])
            if id_index is not None:
                break
        
        if id_index is None:
            return None
        
        for record in records:
            if len(record) > id_index and str(record[id_index]) == str(record_id):
                return dict(zip(fields, record))
        
        return None
    
    def update_record(self, section: str, record_id: str, data: Dict[str, Any]) -> None:
        """Update a specific record by ID."""
        section_data = self.data.get(section)
        if not section_data or 'data' not in section_data:
            return
            
        fields = section_data.get('fields', [])
        records = section_data['data']
        
        # Find appropriate ID field
        id_fields = ['id', 'ibus', 'jbus', 'name']
        id_index = None
        for id_field in id_fields:
            id_index = self._find_field_index(fields, [id_field])
            if id_index is not None:
                break
        
        if id_index is None:
            return
        
        for i, record in enumerate(records):
            if len(record) > id_index and str(record[id_index]) == str(record_id):
                # Update the record
                for field, value in data.items():
                    if field in fields:
                        field_index = fields.index(field)
                        if field_index < len(record):
                            record[field_index] = value
                break
    
    def delete_record(self, section: str, record_id: str) -> None:
        """Delete a specific record by ID."""
        section_data = self.data.get(section)
        if not section_data or 'data' not in section_data:
            return
            
        fields = section_data.get('fields', [])
        records = section_data['data']
        
        # Find appropriate ID field
        id_fields = ['id', 'ibus', 'jbus', 'name']
        id_index = None
        for id_field in id_fields:
            id_index = self._find_field_index(fields, [id_field])
            if id_index is not None:
                break
        
        if id_index is None:
            return
        
        for i, record in enumerate(records):
            if len(record) > id_index and str(record[id_index]) == str(record_id):
                del records[i]
                break
    
    def _apply_system_state(self, state: SystemState) -> None:
        """Apply the specified system state configuration."""
        if state == SystemState.RESET_TO_NORMAL:
            self._reset_to_normal_state()
        elif state == SystemState.MAINTAIN_CURRENT:
            self._maintain_current_state()
        else:
            self.logger.warning(f"Unknown system state: {state}")
    
    def _reset_to_normal_state(self) -> None:
        """Reset all equipment to normal operating positions."""
        if not self.detect_normal_state_availability():
            self.logger.warning("Normal state data not available - using basic implementation")
            self._reset_to_normal_state_basic()
            return
        
        self.logger.info("Resetting RAWX backend to normal state")
        total_changes = 0
        
        # Handle all equipment types
        equipment_sections = [
            'generator', 'load', 'fixed_shunt', 'switched_shunt', 'acline',
            'transformer', 'system_switching_device', 'substation_switching_device'
        ]
        
        for section_name in equipment_sections:
            if section_name in ['system_switching_device', 'substation_switching_device']:
                changes = self._reset_switching_section_to_normal(section_name)
            else:
                changes = self._reset_section_to_normal(section_name)
            total_changes += changes
        
        self.logger.info(f"Reset to normal state complete. {total_changes} changes made.")
    
    def _reset_section_to_normal(self, section_name: str) -> int:
        """Reset a specific equipment section to normal state."""
        changes = 0
        
        if section_name not in self.data:
            return changes
            
        section = self.data[section_name]
        fields = section.get('fields', [])
        records = section.get('data', [])
        
        if not records or not fields:
            return changes
            
        status_index = self._find_field_index(fields, ['status', 'stat'])
        normal_status_index = self._find_field_index(fields, ['normal_status', 'nstat'])
        
        if status_index is None:
            return changes
        
        for record in records:
            if len(record) > status_index:
                try:
                    if normal_status_index is not None and len(record) > normal_status_index:
                        normal_status = int(record[normal_status_index])
                        current_status = int(record[status_index])
                        if current_status != normal_status:
                            record[status_index] = normal_status
                            changes += 1
                    else:
                        # Default to online if no normal status
                        current_status = int(record[status_index])
                        if current_status != 1:
                            record[status_index] = 1
                            changes += 1
                except (ValueError, IndexError):
                    pass
        
        return changes
    
    def _reset_switching_section_to_normal(self, section_name: str) -> int:
        """Reset switching devices to normal state."""
        changes = 0
        
        if section_name not in self.data:
            return changes
            
        section = self.data[section_name]
        fields = section.get('fields', [])
        records = section.get('data', [])
        
        if not records or not fields:
            return changes
            
        status_index = self._find_field_index(fields, ['status', 'stat'])
        normal_status_index = self._find_field_index(fields, ['normal_status', 'nstat', 'normal_state'])
        
        if status_index is None or normal_status_index is None:
            return changes
        
        for record in records:
            if len(record) > max(status_index, normal_status_index):
                try:
                    normal_status = int(record[normal_status_index])
                    current_status = int(record[status_index])
                    
                    if current_status != normal_status:
                        record[status_index] = normal_status
                        changes += 1
                        
                except (ValueError, IndexError):
                    pass
        
        return changes
    
    def _reset_to_normal_state_basic(self) -> None:
        """Basic normal state reset when normal data is not available."""
        self.logger.info("Using basic normal state reset")
        changes = 0
        
        equipment_sections = ['generator', 'load', 'fixed_shunt', 'switched_shunt', 'acline', 'bus']
        for section_name in equipment_sections:
            if section_name not in self.data:
                continue
                
            section = self.data[section_name]
            fields = section.get('fields', [])
            records = section.get('data', [])
            
            if not records or not fields:
                continue
                
            status_index = self._find_field_index(fields, ['status', 'stat'])
            
            if status_index is None:
                continue
            
            # Set all to online (status = 1)
            for record in records:
                if len(record) > status_index:
                    try:
                        current_status = int(record[status_index])
                        if current_status != 1:
                            record[status_index] = 1
                            changes += 1
                    except (ValueError, IndexError):
                        pass
        
        self.logger.info(f"Basic normal state reset complete. {changes} items set to online.")
    
    def _maintain_current_state(self) -> None:
        """Maintain current equipment positions as stored in the backend."""
        if self._original_data_backup and self._system_state != SystemState.MAINTAIN_CURRENT:
            self.logger.info("Restoring to original current state")
            # Restore from backup
            self.data = copy.deepcopy(self._original_data_backup)
        else:
            self.logger.debug("Already maintaining current state - no changes needed")
    
    def _find_field_index(self, fields: List[str], keywords: List[str]) -> Optional[int]:
        """Find the index of a field that matches any of the given keywords."""
        for keyword in keywords:
            keyword_lower = keyword.lower()
            for i, field in enumerate(fields):
                if keyword_lower in str(field).lower():
                    return i
        return None
    
    def _convert_modeling_approach(self, source: ModelingApproach, target: ModelingApproach) -> None:
        """Convert data from one modeling approach to another."""
        self.logger.info(f"Converting RAWX data from {source.value} to {target.value}")
        # Implementation would depend on specific conversion requirements
        # For now, log the conversion request
        self.logger.warning("Modeling approach conversion not yet implemented for RAWX backend")
    
    # Legacy compatibility methods
    def get_bus(self, bus_id: int) -> Optional[Dict[str, Any]]:
        """Get bus record by ID."""
        bus_data = self.get_records('bus')
        fields = bus_data['fields']
        records = bus_data['records']
        
        # Find bus ID field
        id_index = self._find_field_index(fields, ['ibus', 'bus_id', 'id'])
        if id_index is None:
            return None
        
        for record in records:
            if len(record) > id_index and int(record[id_index]) == bus_id:
                return dict(zip(fields, record))
        
        return None
    
    def get_branch(self, ibus: int, jbus: int, ckt: str = '1') -> Optional[Dict[str, Any]]:
        """Get branch record by from/to bus and circuit ID."""
        # Try different section names
        for section_name in ['acline', 'branch', 'line']:
            branch_data = self.get_records(section_name)
            fields = branch_data['fields']
            records = branch_data['records']
            
            if not records:
                continue
            
            ibus_index = self._find_field_index(fields, ['ibus', 'from_bus'])
            jbus_index = self._find_field_index(fields, ['jbus', 'to_bus'])
            ckt_index = self._find_field_index(fields, ['ckt', 'circuit_id', 'id'])
            
            if None in (ibus_index, jbus_index, ckt_index):
                continue
            
            for record in records:
                if (len(record) > max(ibus_index, jbus_index, ckt_index) and
                    int(record[ibus_index]) == ibus and 
                    int(record[jbus_index]) == jbus and
                    str(record[ckt_index]).strip() == str(ckt).strip()):
                    return dict(zip(fields, record))
        
        return None
    
    def get_load(self, bus_id: int, load_id: str) -> Optional[Dict[str, Any]]:
        """Get load record by bus ID and load ID."""
        load_data = self.get_records('load')
        fields = load_data['fields']
        records = load_data['records']
        
        bus_index = self._find_field_index(fields, ['ibus', 'bus_id'])
        id_index = self._find_field_index(fields, ['id', 'load_id'])
        
        if None in (bus_index, id_index):
            return None
        
        for record in records:
            if (len(record) > max(bus_index, id_index) and
                int(record[bus_index]) == bus_id and 
                str(record[id_index]) == str(load_id)):
                return dict(zip(fields, record))
        
        return None 