#!/usr/bin/env python3
"""
Enhanced Architecture Documentation to PDF Converter

This script tries multiple methods to convert HTML to PDF:
1. <PERSON><PERSON> (best for Mermaid diagrams)
2. html2image + reportlab (fallback)
3. WeasyPrint (if available)
4. pdfkit (if wkhtmltopdf available)
"""

import asyncio
import os
import re
import sys
import time
from pathlib import Path
from typing import Optional

def create_pdf_optimized_html(input_html: str, output_html: str) -> str:
    """Create a PDF-optimized version of the HTML with better diagram scaling."""
    
    with open(input_html, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add PDF-specific CSS for better diagram scaling
    pdf_css = """
    <style>
        /* PDF-specific styling for better diagram layout */
        @media print {
            body {
                max-width: none !important;
                margin: 0 !important;
                padding: 10px !important;
            }
            
            .mermaid {
                max-width: 100% !important;
                max-height: 70vh !important;
                page-break-inside: avoid !important;
                background-color: white !important;
                border: none !important;
                margin: 10px 0 !important;
                padding: 10px !important;
                overflow: visible !important;
                display: block !important;
            }
            
            .mermaid svg {
                max-width: 100% !important;
                max-height: 70vh !important;
                height: auto !important;
                width: auto !important;
            }
            
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid !important;
                margin-top: 20px !important;
                margin-bottom: 10px !important;
            }
            
            pre, blockquote {
                page-break-inside: avoid !important;
                margin: 10px 0 !important;
            }
            
            /* Prevent widows and orphans */
            p {
                orphans: 3;
                widows: 3;
            }
            
            /* Table of contents on separate page */
            .toc {
                page-break-after: always !important;
            }
            
            /* Ensure diagrams don't break across pages */
            .diagram-container {
                page-break-inside: avoid !important;
                margin: 15px 0 !important;
            }
        }
        
        /* Screen-specific improvements */
        @media screen {
            .mermaid {
                max-width: 100%;
                overflow-x: auto;
            }
            
            .mermaid svg {
                max-width: 100%;
                height: auto;
            }
        }
    </style>
    """
    
    # Insert PDF CSS before the closing </head> tag
    content = content.replace('</head>', f'{pdf_css}</head>')
    
    # Wrap mermaid diagrams in containers for better page break control
    content = re.sub(
        r'(<div class="mermaid">.*?</div>)',
        r'<div class="diagram-container">\1</div>',
        content,
        flags=re.DOTALL
    )
    
    # Update mermaid initialization for better PDF rendering
    mermaid_init = """
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis'
                },
                sequence: {
                    useMaxWidth: true
                },
                gantt: {
                    useMaxWidth: true
                },
                themeVariables: {
                    fontSize: '14px',
                    fontFamily: 'arial, sans-serif'
                }
            });
            
            // Wait for all diagrams to render before PDF conversion
            setTimeout(() => {
                const mermaidDivs = document.querySelectorAll('.mermaid');
                mermaidDivs.forEach(div => {
                    const svg = div.querySelector('svg');
                    if (svg) {
                        // Ensure SVG is properly sized
                        svg.style.maxWidth = '100%';
                        svg.style.height = 'auto';
                        svg.removeAttribute('width');
                        svg.removeAttribute('height');
                    }
                });
            }, 2000);
        });
    """
    
    # Replace the mermaid initialization
    content = re.sub(
        r'document\.addEventListener\(\'DOMContentLoaded\'.*?\}\);',
        mermaid_init,
        content,
        flags=re.DOTALL
    )
    
    # Write optimized HTML
    with open(output_html, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return output_html

def convert_with_playwright(html_file: str, pdf_file: str) -> bool:
    """Convert HTML to PDF using Playwright - best for Mermaid diagrams."""
    try:
        from playwright.async_api import async_playwright
        
        # Create PDF-optimized HTML
        optimized_html = html_file.replace('.html', '_pdf_optimized.html')
        create_pdf_optimized_html(html_file, optimized_html)
        
        async def run():
            async with async_playwright() as p:
                # Launch browser
                browser = await p.chromium.launch()
                page = await browser.new_page()
                
                # Set viewport for consistent rendering
                await page.set_viewport_size({"width": 1200, "height": 800})
                
                # Navigate to the optimized HTML file
                file_url = f"file:///{os.path.abspath(optimized_html)}"
                await page.goto(file_url, wait_until='networkidle')
                
                # Wait for Mermaid diagrams to render
                print("Waiting for Mermaid diagrams to render...")
                await page.wait_for_timeout(10000)  # Wait 10 seconds
                
                # Check if mermaid diagrams are rendered
                try:
                    await page.wait_for_selector('svg[id^="mermaid"]', timeout=5000)
                    print("Mermaid diagrams detected and rendered!")
                except:
                    print("No Mermaid SVGs detected, but continuing...")
                
                # Add script to optimize diagram sizing for PDF
                await page.add_script_tag(content="""
                    const diagrams = document.querySelectorAll('.mermaid svg');
                    diagrams.forEach(svg => {
                        // Remove fixed dimensions
                        svg.removeAttribute('width');
                        svg.removeAttribute('height');
                        
                        // Set responsive sizing
                        svg.style.maxWidth = '100%';
                        svg.style.height = 'auto';
                        svg.style.display = 'block';
                        
                        // Get current dimensions
                        const bbox = svg.getBBox();
                        const currentWidth = bbox.width;
                        const currentHeight = bbox.height;
                        
                        // Calculate scale to fit in A4 width (more generous sizing)
                        const maxWidth = 1000;  // Increased from 700
                        const maxHeight = 700;  // Increased from 500
                        
                        // Only scale down if significantly larger than page
                        if (currentWidth > maxWidth || currentHeight > maxHeight) {
                            const scaleX = maxWidth / currentWidth;
                            const scaleY = maxHeight / currentHeight;
                            const scale = Math.min(scaleX, scaleY);
                            
                            // Don't scale down too much - minimum 70% of original
                            const finalScale = Math.max(scale, 0.7);
                            
                            if (finalScale < 1.0) {
                                svg.style.transform = `scale(${finalScale})`;
                                svg.style.transformOrigin = 'top left';
                                svg.style.width = `${currentWidth * finalScale}px`;
                                svg.style.height = `${currentHeight * finalScale}px`;
                            }
                        }
                    });
                """)
                
                # Wait a bit more for scaling to apply
                await page.wait_for_timeout(2000)
                
                # Generate PDF with optimized settings
                await page.pdf(
                    path=pdf_file,
                    format='A4',
                    print_background=True,
                    prefer_css_page_size=False,
                    margin={
                        'top': '0.4in',
                        'right': '0.4in', 
                        'bottom': '0.4in',
                        'left': '0.4in'
                    },
                    scale=0.95  # Less aggressive scaling - changed from 0.8
                )
                
                await browser.close()
                
                # Clean up optimized HTML
                try:
                    os.remove(optimized_html)
                except:
                    pass
                
                return True
        
        # Run the async function
        asyncio.run(run())
        print(f"✅ PDF created successfully using Playwright: {pdf_file}")
        return True
        
    except ImportError:
        print("❌ Playwright not available")
        return False
    except Exception as e:
        print(f"❌ Error with Playwright: {e}")
        return False

def convert_with_html2image_reportlab(html_file: str, pdf_file: str) -> bool:
    """Convert HTML to PDF using html2image + reportlab."""
    try:
        from html2image import Html2Image
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter, A4
        from PIL import Image
        import tempfile
        
        # Create PDF-optimized HTML
        optimized_html = html_file.replace('.html', '_pdf_optimized.html')
        create_pdf_optimized_html(html_file, optimized_html)
        
        # Create temporary directory for images
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize html2image
            hti = Html2Image(output_path=temp_dir, size=(1200, 1600))
            
            # Convert HTML to image
            image_files = hti.screenshot(
                html_file=optimized_html,
                save_as='page.png'
            )
            
            if not image_files:
                return False
            
            image_path = os.path.join(temp_dir, image_files[0])
            
            # Create PDF from image
            c = canvas.Canvas(pdf_file, pagesize=A4)
            
            # Open and get image dimensions
            with Image.open(image_path) as img:
                img_width, img_height = img.size
                
                # Calculate scaling to fit A4
                page_width, page_height = A4
                margin = 30
                
                scale_x = (page_width - 2 * margin) / img_width
                scale_y = (page_height - 2 * margin) / img_height
                scale = min(scale_x, scale_y)
                
                scaled_width = img_width * scale
                scaled_height = img_height * scale
                
                # Center the image
                x = (page_width - scaled_width) / 2
                y = (page_height - scaled_height) / 2
                
                # Draw image on PDF
                c.drawImage(image_path, x, y, scaled_width, scaled_height)
            
            c.save()
            
        # Clean up optimized HTML
        try:
            os.remove(optimized_html)
        except:
            pass
            
        print(f"✅ PDF created using html2image + reportlab: {pdf_file}")
        return True
        
    except ImportError as e:
        print(f"❌ html2image/reportlab not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error with html2image/reportlab: {e}")
        return False

def convert_with_weasyprint(html_file: str, pdf_file: str) -> bool:
    """Convert HTML to PDF using WeasyPrint."""
    try:
        from weasyprint import HTML, CSS
        
        # Create PDF-optimized HTML
        optimized_html = html_file.replace('.html', '_pdf_optimized.html')
        create_pdf_optimized_html(html_file, optimized_html)
        
        # Additional CSS for WeasyPrint
        css_string = """
        @page {
            size: A4;
            margin: 0.5in;
        }
        
        .mermaid {
            max-width: 100% !important;
            page-break-inside: avoid !important;
        }
        
        .mermaid svg {
            max-width: 100% !important;
            height: auto !important;
        }
        """
        
        HTML(filename=optimized_html).write_pdf(
            pdf_file,
            stylesheets=[CSS(string=css_string)]
        )
        
        # Clean up optimized HTML
        try:
            os.remove(optimized_html)
        except:
            pass
            
        print(f"✅ PDF created using WeasyPrint: {pdf_file}")
        return True
        
    except ImportError:
        print("❌ WeasyPrint not available")
        return False
    except Exception as e:
        print(f"❌ Error with WeasyPrint: {e}")
        return False

def convert_with_pdfkit(html_file: str, pdf_file: str) -> bool:
    """Convert HTML to PDF using pdfkit/wkhtmltopdf."""
    try:
        import pdfkit
        
        # Create PDF-optimized HTML
        optimized_html = html_file.replace('.html', '_pdf_optimized.html')
        create_pdf_optimized_html(html_file, optimized_html)
        
        options = {
            'page-size': 'A4',
            'margin-top': '0.5in',
            'margin-right': '0.5in',
            'margin-bottom': '0.5in',
            'margin-left': '0.5in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'javascript-delay': 10000,  # Wait for Mermaid
            'no-stop-slow-scripts': None,
            'disable-smart-shrinking': None,
            'print-media-type': None,
            'background': None,
            'zoom': 0.8  # Scale down slightly
        }
        
        pdfkit.from_file(optimized_html, pdf_file, options=options)
        
        # Clean up optimized HTML
        try:
            os.remove(optimized_html)
        except:
            pass
            
        print(f"✅ PDF created using pdfkit: {pdf_file}")
        return True
        
    except ImportError:
        print("❌ pdfkit not available")
        return False
    except Exception as e:
        print(f"❌ Error with pdfkit: {e}")
        return False

def check_html_file(html_file: str) -> bool:
    """Check if HTML file exists and contains Mermaid content."""
    if not os.path.exists(html_file):
        print(f"❌ HTML file not found: {html_file}")
        return False
    
    # Check for Mermaid content
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    if 'mermaid' in content.lower():
        print(f"✅ HTML file found with Mermaid content: {html_file}")
        return True
    else:
        print(f"⚠️  HTML file found but no Mermaid content detected: {html_file}")
        return True

def main():
    """Main conversion function with multiple fallback methods."""
    # File paths
    html_file = "Architecture_Documentation.html"
    pdf_file = "Architecture_Documentation.pdf"
    
    print("🔄 Enhanced PDF Conversion Tool (Optimized for Diagram Scaling)")
    print("=" * 70)
    
    # Check HTML file
    if not check_html_file(html_file):
        sys.exit(1)
    
    # Try conversion methods in order of preference
    conversion_methods = [
        ("Playwright (Recommended for Mermaid)", convert_with_playwright),
        ("WeasyPrint", convert_with_weasyprint), 
        ("html2image + reportlab", convert_with_html2image_reportlab),
        ("pdfkit/wkhtmltopdf", convert_with_pdfkit)
    ]
    
    print(f"\n🎯 Converting {html_file} to {pdf_file}...")
    print("📐 Optimizations applied:")
    print("  • Diagrams auto-scaled to fit A4 page width")
    print("  • Page breaks prevented within diagrams")
    print("  • PDF-specific CSS styling applied")
    print("  • Responsive SVG sizing enabled")
    print("\nTrying conversion methods in order of preference:\n")
    
    for method_name, method_func in conversion_methods:
        print(f"🔄 Trying {method_name}...")
        
        try:
            if method_func(html_file, pdf_file):
                # Verify PDF was created
                if os.path.exists(pdf_file):
                    file_size = os.path.getsize(pdf_file)
                    print(f"🎉 SUCCESS! PDF created: {pdf_file} ({file_size:,} bytes)")
                    
                    # Show instructions
                    print("\n📖 PDF Conversion Complete!")
                    print(f"📄 Output file: {os.path.abspath(pdf_file)}")
                    print("💡 The PDF should now have properly scaled diagrams that fit within pages.")
                    print("🔍 Each diagram should be contained within a single page when possible.")
                    return
                else:
                    print(f"❌ PDF file not created by {method_name}")
        except Exception as e:
            print(f"❌ Unexpected error with {method_name}: {e}")
        
        print()  # Empty line between attempts
    
    # All methods failed
    print("❌ All PDF conversion methods failed!")
    print("\n🔧 Alternative options:")
    print("1. Open Architecture_Documentation.html in a web browser")
    print("2. Wait for diagrams to render (5-10 seconds)")
    print("3. Press Ctrl+P and save as PDF")
    print("4. In print options:")
    print("   • Enable 'Background graphics'")
    print("   • Set margins to 'Minimum'")
    print("   • Choose 'More settings' → 'Scale' → 'Custom' → 80%")

if __name__ == "__main__":
    main() 