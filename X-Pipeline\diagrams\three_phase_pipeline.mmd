graph TB
    subgraph "Phase 1: Loading & Conversion"
        Input["Input Files<br/>HDB/RAWX/JSON"]
        Backend["Backend Classes<br/>HdbBackend/RawxBackend"]
        Canonical["Canonical Format<br/>Always NODE_BREAKER<br/>Embedded node info"]
    end

    subgraph "Phase 2: Transformation"
        ModelDetection["Model Detection<br/>detect_model_type()"]
        Transformation["ModelTransformations<br/>Node-to-bus mapping<br/>Equipment updates"]
        TransformedData["Transformed Data<br/>Bus numbers updated<br/>Modeling approach applied"]
    end

    subgraph "Phase 3: Export"
        ExportDecision["Export Decision<br/>Hierarchical vs Flat"]
        Writers["Writer Classes<br/>Equipment-specific writers"]
        Output["RAW Format Output<br/>Version-specific format"]
    end

    Input --> Backend
    Backend --> Canonical
    Canonical --> ModelDetection
    ModelDetection --> Transformation
    Transformation --> TransformedData
    TransformedData --> ExportDecision
    ExportDecision --> Writers
    Writers --> Output 