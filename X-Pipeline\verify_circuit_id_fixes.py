#!/usr/bin/env python3
"""
Circuit ID Formatting Verification Script

This script verifies that our pipeline correctly implements the circuit ID formatting
requirements from case_utilities.py:

1. Circuit Breakers (Type == "CB"): Use @ prefix + (Number % 10)
2. Disconnects/Switches (Type != "CB"): Use * prefix + (Number % 10)  
3. Zero Impedance Branches: Always use "ZZ"
4. Transformers: Use (Number % 100) as circuit ID (no prefix)
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import Backend

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s: %(message)s')
logger = logging.getLogger(__name__)

def analyze_circuit_ids_in_file(file_path: str) -> Dict[str, Any]:
    """
    Analyze circuit IDs in a RAW file to verify formatting compliance.
    
    Args:
        file_path: Path to RAW file to analyze
        
    Returns:
        Dict with analysis results
    """
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_path}"}
    
    results = {
        "file": file_path,
        "total_branches": 0,
        "zero_impedance_branches": 0,
        "transformers": 0,
        "circuit_breakers": 0,
        "disconnects": 0,
        "other_branches": 0,
        "circuit_id_issues": [],
        "circuit_id_examples": [],
        "section_analysis": {}
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_section = None
        section_data = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('0 /'):
                continue
                
            # Detect section headers
            if line.startswith('BUS DATA FOLLOWS'):
                current_section = 'bus'
                section_data = []
            elif line.startswith('LOAD DATA FOLLOWS'):
                current_section = 'load'
                section_data = []
            elif line.startswith('FIXED SHUNT DATA FOLLOWS'):
                current_section = 'fixed_shunt'
                section_data = []
            elif line.startswith('GENERATOR DATA FOLLOWS'):
                current_section = 'generator'
                section_data = []
            elif line.startswith('BRANCH DATA FOLLOWS'):
                current_section = 'branch'
                section_data = []
            elif line.startswith('TRANSFORMER DATA FOLLOWS'):
                current_section = 'transformer'
                section_data = []
            elif line.startswith('AREA INTERCHANGE DATA FOLLOWS'):
                current_section = 'area'
                section_data = []
            elif line.startswith('TWO-TERMINAL DC DATA FOLLOWS'):
                current_section = 'dc_line'
                section_data = []
            elif line.startswith('SWITCHED SHUNT DATA FOLLOWS'):
                current_section = 'switched_shunt'
                section_data = []
            elif line.startswith('IMPEDANCE CORRECTION DATA FOLLOWS'):
                current_section = 'impedance_correction'
                section_data = []
            elif line.startswith('MULTI-TERMINAL DC DATA FOLLOWS'):
                current_section = 'multi_terminal_dc'
                section_data = []
            elif line.startswith('MULTI-SECTION LINE DATA FOLLOWS'):
                current_section = 'multi_section_line'
                section_data = []
            elif line.startswith('ZONE DATA FOLLOWS'):
                current_section = 'zone'
                section_data = []
            elif line.startswith('INTER-AREA TRANSFER DATA FOLLOWS'):
                current_section = 'inter_area_transfer'
                section_data = []
            elif line.startswith('OWNER DATA FOLLOWS'):
                current_section = 'owner'
                section_data = []
            elif line.startswith('FACTS DEVICE DATA FOLLOWS'):
                current_section = 'facts'
                section_data = []
            elif line.startswith('SWITCHED SHUNT DATA FOLLOWS'):
                current_section = 'switched_shunt'
                section_data = []
            elif line.startswith('GNE DEVICE DATA FOLLOWS'):
                current_section = 'gne'
                section_data = []
            elif line.startswith('INDUCTION MACHINE DATA FOLLOWS'):
                current_section = 'induction_machine'
                section_data = []
            elif line.startswith('Q RECORDS'):
                current_section = 'end'
                break
            elif current_section and line and not line.startswith('-'):
                # This is a data line
                section_data.append((line_num, line))
        
        # Analyze branch and transformer sections
        if 'branch' in results["section_analysis"]:
            branch_data = results["section_analysis"]['branch']
            for line_num, line in branch_data:
                analyze_branch_line(line, line_num, results)
        
        if 'transformer' in results["section_analysis"]:
            transformer_data = results["section_analysis"]['transformer']
            for line_num, line in transformer_data:
                analyze_transformer_line(line, line_num, results)
        
        # Store section data for analysis
        if current_section and section_data:
            results["section_analysis"][current_section] = section_data
    
    except Exception as e:
        results["error"] = f"Error analyzing file: {e}"
        logger.error(f"Error analyzing {file_path}: {e}")
    
    return results

def analyze_branch_line(line: str, line_num: int, results: Dict[str, Any]) -> None:
    """Analyze a branch line for circuit ID compliance."""
    try:
        # Parse branch line (format: ibus,jbus,ckt,r,x,b,name,rate1,rate2,rate3,status)
        parts = line.split(',')
        if len(parts) < 7:
            return
        
        ibus = int(parts[0].strip())
        jbus = int(parts[1].strip())
        ckt = parts[2].strip().strip("'")  # Remove quotes
        r = float(parts[3].strip())
        x = float(parts[4].strip())
        name = parts[6].strip().strip("'") if len(parts) > 6 else ""
        
        results["total_branches"] += 1
        
        # Check for zero impedance branches
        if abs(r) < 0.001 and abs(x) < 0.001:
            results["zero_impedance_branches"] += 1
            if ckt != "ZZ":
                results["circuit_id_issues"].append({
                    "line": line_num,
                    "type": "zero_impedance_branch",
                    "expected": "ZZ",
                    "actual": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
            else:
                results["circuit_id_examples"].append({
                    "type": "zero_impedance_branch",
                    "ckt": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
        
        # Check for circuit breakers (name contains CB or breaker)
        elif "CB" in name.upper() or "BREAKER" in name.upper():
            results["circuit_breakers"] += 1
            if not ckt.startswith('@'):
                results["circuit_id_issues"].append({
                    "line": line_num,
                    "type": "circuit_breaker",
                    "expected": "starts with @",
                    "actual": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
            else:
                results["circuit_id_examples"].append({
                    "type": "circuit_breaker",
                    "ckt": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
        
        # Check for disconnects (name contains DISC or disconnect)
        elif "DISC" in name.upper() or "DISCONNECT" in name.upper():
            results["disconnects"] += 1
            if not ckt.startswith('*'):
                results["circuit_id_issues"].append({
                    "line": line_num,
                    "type": "disconnect",
                    "expected": "starts with *",
                    "actual": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
            else:
                results["circuit_id_examples"].append({
                    "type": "disconnect",
                    "ckt": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
        
        else:
            results["other_branches"] += 1
            # Regular branches should not have special prefixes
            if ckt.startswith('@') or ckt.startswith('*') or ckt == "ZZ":
                results["circuit_id_issues"].append({
                    "line": line_num,
                    "type": "regular_branch",
                    "expected": "no special prefix",
                    "actual": ckt,
                    "name": name,
                    "ibus": ibus,
                    "jbus": jbus
                })
    
    except Exception as e:
        logger.warning(f"Error parsing branch line {line_num}: {e}")

def analyze_transformer_line(line: str, line_num: int, results: Dict[str, Any]) -> None:
    """Analyze a transformer line for circuit ID compliance."""
    try:
        # Parse transformer line (format: ibus,jbus,kbus,ckt,cw,cz,cm,mag1,mag2,nmet,name,status,...)
        parts = line.split(',')
        if len(parts) < 12:
            return
        
        ibus = int(parts[0].strip())
        jbus = int(parts[1].strip())
        kbus = int(parts[2].strip()) if parts[2].strip() != '' else 0
        ckt = parts[3].strip().strip("'")  # Remove quotes
        name = parts[10].strip().strip("'") if len(parts) > 10 else ""
        
        results["transformers"] += 1
        
        # Transformers should not have special prefixes (@, *, ZZ)
        if ckt.startswith('@') or ckt.startswith('*') or ckt == "ZZ":
            results["circuit_id_issues"].append({
                "line": line_num,
                "type": "transformer",
                "expected": "no special prefix (should be Number % 100)",
                "actual": ckt,
                "name": name,
                "ibus": ibus,
                "jbus": jbus,
                "kbus": kbus
            })
        else:
            results["circuit_id_examples"].append({
                "type": "transformer",
                "ckt": ckt,
                "name": name,
                "ibus": ibus,
                "jbus": jbus,
                "kbus": kbus
            })
    
    except Exception as e:
        logger.warning(f"Error parsing transformer line {line_num}: {e}")

def run_pipeline_and_analyze() -> Dict[str, Any]:
    """Run the pipeline and analyze the output for circuit ID compliance."""
    logger.info("🚀 Running pipeline with circuit ID verification...")
    
    # File paths
    script_dir = Path(__file__).parent
    hdb_path = script_dir / "hdbcontext_original.hdb"
    output_dir = script_dir / "output_demo"
    
    if not hdb_path.exists():
        return {"error": f"HDB file not found: {hdb_path}"}
    
    try:
        # Run pipeline
        backend = Backend()
        backend.load(str(hdb_path))
        
        # Export to RAW format
        output_file = output_dir / "circuit_id_test.raw"
        backend.export_raw(str(output_file))
        
        logger.info(f"✅ Pipeline completed, output: {output_file}")
        
        # Analyze the output
        analysis = analyze_circuit_ids_in_file(str(output_file))
        
        return {
            "pipeline_success": True,
            "output_file": str(output_file),
            "analysis": analysis
        }
    
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return {
            "pipeline_success": False,
            "error": str(e)
        }

def print_analysis_results(results: Dict[str, Any]) -> None:
    """Print formatted analysis results."""
    print("\n" + "="*80)
    print("CIRCUIT ID FORMATTING ANALYSIS RESULTS")
    print("="*80)
    
    if "error" in results:
        print(f"❌ ERROR: {results['error']}")
        return
    
    analysis = results.get("analysis", {})
    
    print(f"📁 File: {analysis.get('file', 'Unknown')}")
    print(f"📊 Total Branches: {analysis.get('total_branches', 0)}")
    print(f"🔗 Zero Impedance Branches: {analysis.get('zero_impedance_branches', 0)}")
    print(f"⚡ Circuit Breakers: {analysis.get('circuit_breakers', 0)}")
    print(f"🔌 Disconnects: {analysis.get('disconnects', 0)}")
    print(f"🔧 Transformers: {analysis.get('transformers', 0)}")
    print(f"📈 Other Branches: {analysis.get('other_branches', 0)}")
    
    # Circuit ID Issues
    issues = analysis.get("circuit_id_issues", [])
    print(f"\n🚨 CIRCUIT ID ISSUES: {len(issues)}")
    print("-" * 50)
    
    if issues:
        for i, issue in enumerate(issues[:10], 1):  # Show first 10 issues
            print(f"{i}. Line {issue['line']}: {issue['type']}")
            print(f"   Expected: {issue['expected']}")
            print(f"   Actual: {issue['actual']}")
            print(f"   Name: {issue['name']}")
            print(f"   Buses: {issue['ibus']} -> {issue['jbus']}")
            print()
        
        if len(issues) > 10:
            print(f"... and {len(issues) - 10} more issues")
    else:
        print("✅ No circuit ID issues found!")
    
    # Circuit ID Examples
    examples = analysis.get("circuit_id_examples", [])
    print(f"\n✅ CORRECT CIRCUIT ID EXAMPLES: {len(examples)}")
    print("-" * 50)
    
    if examples:
        for i, example in enumerate(examples[:5], 1):  # Show first 5 examples
            print(f"{i}. {example['type']}: {example['ckt']}")
            print(f"   Name: {example['name']}")
            print(f"   Buses: {example['ibus']} -> {example['jbus']}")
            print()
        
        if len(examples) > 5:
            print(f"... and {len(examples) - 5} more examples")
    
    # Summary
    print("\n📋 SUMMARY")
    print("-" * 30)
    total_issues = len(issues)
    total_examples = len(examples)
    
    if total_issues == 0:
        print("🎉 PERFECT! All circuit IDs follow case_utilities.py conventions")
    else:
        compliance_rate = (total_examples / (total_examples + total_issues)) * 100
        print(f"📊 Compliance Rate: {compliance_rate:.1f}%")
        print(f"✅ Correct: {total_examples}")
        print(f"❌ Issues: {total_issues}")

def main():
    """Main function to run circuit ID verification."""
    print("🔍 Circuit ID Formatting Verification")
    print("=" * 50)
    print("Verifying compliance with case_utilities.py requirements:")
    print("• Circuit Breakers: @ prefix + (Number % 10)")
    print("• Disconnects: * prefix + (Number % 10)")
    print("• Zero Impedance Branches: ZZ")
    print("• Transformers: (Number % 100) - no prefix")
    print()
    
    # Run pipeline and analyze
    results = run_pipeline_and_analyze()
    
    # Print results
    print_analysis_results(results)
    
    # Save detailed results to file
    output_file = Path(__file__).parent / "circuit_id_analysis_results.txt"
    with open(output_file, 'w') as f:
        f.write("Circuit ID Analysis Results\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Pipeline Success: {results.get('pipeline_success', False)}\n")
        if "error" in results:
            f.write(f"Error: {results['error']}\n")
        else:
            analysis = results.get("analysis", {})
            f.write(f"File: {analysis.get('file', 'Unknown')}\n")
            f.write(f"Total Branches: {analysis.get('total_branches', 0)}\n")
            f.write(f"Zero Impedance Branches: {analysis.get('zero_impedance_branches', 0)}\n")
            f.write(f"Circuit Breakers: {analysis.get('circuit_breakers', 0)}\n")
            f.write(f"Disconnects: {analysis.get('disconnects', 0)}\n")
            f.write(f"Transformers: {analysis.get('transformers', 0)}\n")
            f.write(f"Other Branches: {analysis.get('other_branches', 0)}\n\n")
            
            f.write("Circuit ID Issues:\n")
            for issue in analysis.get("circuit_id_issues", []):
                f.write(f"  Line {issue['line']}: {issue['type']} - Expected: {issue['expected']}, Actual: {issue['actual']}\n")
            
            f.write("\nCircuit ID Examples:\n")
            for example in analysis.get("circuit_id_examples", []):
                f.write(f"  {example['type']}: {example['ckt']} - {example['name']}\n")
    
    print(f"\n📄 Detailed results saved to: {output_file}")

if __name__ == "__main__":
    main() 