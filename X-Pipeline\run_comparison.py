﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive comparison script for HDB/RAWX to RAW pipeline.
Includes device-level field-by-field comparison.
"""

import os
import sys
import re
import json
from collections import defaultdict

def parse_raw_file(file_path):
    """Parse a RAW file and extract all devices by section with their fields, using section names from 'end of ... data, begin {section} data' lines. Special-case buses."""
    print(f"   📖 Parsing RAW file: {os.path.basename(file_path)}")
    
    devices = defaultdict(list)
    current_section = None
    bus_mode = False
    bus_lines = []
    section_line_re = re.compile(r"end of (.+?) data, begin (.+?) data", re.IGNORECASE)
    begin_section_re = re.compile(r"begin (.+?) data", re.IGNORECASE)
    end_of_bus_re = re.compile(r"end of bus data", re.IGNORECASE)
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Skip header lines (find first data line)
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line or line.startswith('//') or line.startswith('/*'):
                i += 1
                continue
            # Look for first numeric data line (bus data usually starts here)
            if re.match(r'^\d+[, ]', line):
                break
            i += 1
        # Now i is at the first data line
        # Start in bus mode unless we see a 'begin bus data' marker
        bus_mode = True
        current_section = 'bus'
        while i < len(lines):
            line = lines[i].strip()
            if not line or line.startswith('//') or line.startswith('/*'):
                i += 1
                continue
            # Section transition: end of ... data, begin {section} data
            m = section_line_re.search(line)
            if m:
                # Save previous section
                if bus_mode and bus_lines:
                    for bus_line in bus_lines:
                        if ',' in bus_line:
                            fields = [field.strip() for field in bus_line.split(',')]
                            cleaned_fields = [f.strip('"\'') for f in fields]
                            devices['bus'].append(cleaned_fields)
                    bus_lines = []
                    bus_mode = False
                prev_section = m.group(1).strip().lower().replace(' ', '_')
                next_section = m.group(2).strip().lower().replace(' ', '_')
                current_section = next_section
                i += 1
                continue
            # End of bus data (if no explicit begin bus data)
            if bus_mode and end_of_bus_re.search(line):
                for bus_line in bus_lines:
                    if ',' in bus_line:
                        fields = [field.strip() for field in bus_line.split(',')]
                        cleaned_fields = [f.strip('"\'') for f in fields]
                        devices['bus'].append(cleaned_fields)
                bus_lines = []
                bus_mode = False
                i += 1
                continue
            # Begin {section} data (rare, but handle)
            m2 = begin_section_re.search(line)
            if m2:
                current_section = m2.group(1).strip().lower().replace(' ', '_')
                i += 1
                continue
            # Data lines
            if bus_mode:
                bus_lines.append(line)
            elif current_section and ',' in line:
                fields = [field.strip() for field in line.split(',')]
                cleaned_fields = [f.strip('"\'') for f in fields]
                devices[current_section].append(cleaned_fields)
            i += 1
        # If file ends while still in bus mode, flush bus lines
        if bus_mode and bus_lines:
            for bus_line in bus_lines:
                if ',' in bus_line:
                    fields = [field.strip() for field in bus_line.split(',')]
                    cleaned_fields = [f.strip('"\'') for f in fields]
                    devices['bus'].append(cleaned_fields)
    except Exception as e:
        print(f"   ❌ Error parsing {file_path}: {e}")
        return {}
    # Report parsing results
    total_devices = sum(len(device_list) for device_list in devices.values())
    print(f"   📊 Parsed {len(devices)} sections, {total_devices:,} total devices")
    for section, device_list in devices.items():
        if device_list:
            print(f"      {section}: {len(device_list):,} devices")
    return devices

def create_device_key(device_fields, section):
    """Create a unique key for a device based on bus numbers and circuit ID."""
    if not device_fields:
        return None
    
    # Define key patterns for different device types
    key_patterns = {
        'bus': lambda f: f[0] if f else None,  # ibus
        'load': lambda f: f[0] if f else None,  # ibus
        'generator': lambda f: f[0] if len(f) > 0 else None,  # ibus
        'ac_line': lambda f: f"{f[0]}.{f[1]}.{f[2]}" if len(f) > 2 else None,  # ibus.jbus.circuit
        'transformer': lambda f: f"{f[0]}.{f[1]}.{f[2]}.{f[3] if len(f) > 3 else '0'}" if len(f) > 2 else None,  # ibus.jbus.kbus.circuit
        'fixed_shunt': lambda f: f[0] if f else None,  # ibus
        'switched_shunt': lambda f: f[0] if f else None,  # ibus
        'area': lambda f: f[0] if f else None,  # area_number
        'zone': lambda f: f[0] if f else None,  # zone_number
        'owner': lambda f: f[0] if f else None,  # owner_number
        'two_terminal_dc_line': lambda f: f"{f[0]}.{f[1]}" if len(f) > 1 else None,  # ibus.jbus
        'facts_device': lambda f: f[0] if f else None,  # ibus
        'voltage_source_converter': lambda f: f[0] if f else None,  # ibus
        'node': lambda f: f[0] if f else None,  # node_number
        'substation': lambda f: f[0] if f else None,  # substation_number
        'switching_device': lambda f: f"{f[0]}.{f[1]}" if len(f) > 1 else None,  # node_from.node_to
        'terminal': lambda f: f"{f[0]}.{f[1]}" if len(f) > 1 else None,  # equipment_id.terminal_number
    }
    
    # Get the key pattern for this section
    key_func = key_patterns.get(section, lambda f: f[0] if f else None)
    
    try:
        key = key_func(device_fields)
        return key
    except (IndexError, ValueError):
        return None

def compare_device_fields(device1, device2, section, key):
    """Compare two devices field by field and return differences."""
    differences = []
    
    # Ensure both devices have same number of fields (pad with empty strings)
    max_fields = max(len(device1), len(device2))
    dev1_padded = device1 + [''] * (max_fields - len(device1))
    dev2_padded = device2 + [''] * (max_fields - len(device2))
    
    for i, (field1, field2) in enumerate(zip(dev1_padded, dev2_padded)):
        if field1 != field2:
            # Try to convert to numbers for better comparison
            try:
                num1 = float(field1) if field1 else 0.0
                num2 = float(field2) if field2 else 0.0
                if abs(num1 - num2) > 1e-6:  # Tolerance for floating point
                    differences.append({
                        'field_index': i,
                        'file1_value': field1,
                        'file2_value': field2,
                        'numeric_diff': num2 - num1
                    })
            except ValueError:
                # String comparison
                differences.append({
                    'field_index': i,
                    'file1_value': field1,
                    'file2_value': field2,
                    'numeric_diff': None
                })
    
    return differences

def comprehensive_device_comparison(file1_path, file2_path, comparison_name):
    """Perform comprehensive device-level field comparison between two RAW files."""
    print(f"\n🔬 COMPREHENSIVE DEVICE COMPARISON: {comparison_name}")
    print(f"   File 1: {os.path.basename(file1_path)}")
    print(f"   File 2: {os.path.basename(file2_path)}")
    
    # Parse both files
    devices1 = parse_raw_file(file1_path)
    devices2 = parse_raw_file(file2_path)
    
    if not devices1 or not devices2:
        print(f"   ❌ Could not parse one or both files")
        return
    
    # Get all sections from both files
    all_sections = set(devices1.keys()) | set(devices2.keys())
    print(f"\n   📊 Comparing {len(all_sections)} sections...")
    
    total_devices_compared = 0
    total_devices_different = 0
    total_field_differences = 0
    section_results = {}
    
    for section in sorted(all_sections):
        print(f"\n   🔍 Section: {section}")
        
        devices_in_1 = devices1.get(section, [])
        devices_in_2 = devices2.get(section, [])
        
        print(f"      File 1: {len(devices_in_1):,} devices")
        print(f"      File 2: {len(devices_in_2):,} devices")
        
        # Create device dictionaries with keys
        device_dict_1 = {}
        device_dict_2 = {}
        
        for device in devices_in_1:
            key = create_device_key(device, section)
            if key:
                device_dict_1[key] = device
        
        for device in devices_in_2:
            key = create_device_key(device, section)
            if key:
                device_dict_2[key] = device
        
        # Compare devices
        common_keys = set(device_dict_1.keys()) & set(device_dict_2.keys())
        only_in_1 = set(device_dict_1.keys()) - set(device_dict_2.keys())
        only_in_2 = set(device_dict_2.keys()) - set(device_dict_1.keys())
        
        print(f"      Common devices: {len(common_keys):,}")
        print(f"      Only in file 1: {len(only_in_1):,}")
        print(f"      Only in file 2: {len(only_in_2):,}")
        
        section_differences = 0
        section_devices_different = 0
        
        # Compare common devices field by field
        for key in sorted(common_keys):
            device1 = device_dict_1[key]
            device2 = device_dict_2[key]
            
            differences = compare_device_fields(device1, device2, section, key)
            if differences:
                section_devices_different += 1
                section_differences += len(differences)
                
                # Show first few differences for this section
                if section_devices_different <= 3:
                    print(f"         Device {key}: {len(differences)} field differences")
                    for diff in differences[:3]:  # Show first 3 field differences
                        print(f"           Field {diff['field_index']}: '{diff['file1_value']}' → '{diff['file2_value']}'")
                    if len(differences) > 3:
                        print(f"           ... and {len(differences) - 3} more differences")
        
        if section_devices_different > 3:
            print(f"         ... and {section_devices_different - 3} more devices with differences")
        
        total_devices_compared += len(common_keys)
        total_devices_different += section_devices_different
        total_field_differences += section_differences
        
        section_results[section] = {
            'common': len(common_keys),
            'only_in_1': len(only_in_1),
            'only_in_2': len(only_in_2),
            'devices_different': section_devices_different,
            'field_differences': section_differences
        }
    
    # Summary report
    print(f"\n   📊 DEVICE COMPARISON SUMMARY")
    print(f"      Total devices compared: {total_devices_compared:,}")
    print(f"      Devices with differences: {total_devices_different:,} ({total_devices_different/max(total_devices_compared,1)*100:.1f}%)")
    print(f"      Total field differences: {total_field_differences:,}")
    
    if total_field_differences > 0:
        print(f"\n   🔍 Sections with most differences:")
        sorted_sections = sorted(section_results.items(), key=lambda x: x[1]['field_differences'], reverse=True)
        for section, results in sorted_sections[:5]:
            if results['field_differences'] > 0:
                print(f"      {section}: {results['field_differences']:,} field differences in {results['devices_different']:,} devices")
    
    return section_results

def main():
    print("Starting Comprehensive Comparison Demo...")
    
    # Import the pipeline
    try:
        import hdb_to_raw_pipeline as pipeline
        print("✅ Pipeline imported successfully")
        
        # Ensure output directory exists
        output_dir = "output_demo"
        os.makedirs(output_dir, exist_ok=True)
        
        # File paths - same directory as script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        rawx_path = os.path.join(script_dir, "savnw_nb.rawx")
        hdb_path = os.path.join(script_dir, "hdbcontext_original.hdb")
        
        # Reference files
        rawx_ref_path = os.path.join(script_dir, "savnw_nb.raw")
        hdb_ref_path = os.path.join(script_dir, "psse_33.raw")
        
        print(f"📂 Input files:")
        print(f"   RAWX: {rawx_path}")
        print(f"   HDB:  {hdb_path}")
        print(f"📁 Reference files:")
        print(f"   RAWX ref: {rawx_ref_path}")
        print(f"   HDB ref:  {hdb_ref_path}")
        
        # Check if input files exist
        print("📂 Checking input files...")
        if not os.path.exists(rawx_path):
            print(f"❌ RAWX file not found: {rawx_path}")
            rawx_available = False
        else:
            print(f"✅ RAWX file found: {rawx_path}")
            rawx_available = True
        
        if not os.path.exists(hdb_path):
            print(f"❌ HDB file not found: {hdb_path}")
            hdb_available = False
        else:
            print(f"✅ HDB file found: {hdb_path}")
            hdb_available = True
        
        # Load backends and export files
        rawx_files = {}
        hdb_files = {}
        
        if rawx_available:
            print(f"\n📂 Loading RAWX file...")
            rawx_backend = pipeline.Backend.load(rawx_path)
            print(f"✅ RAWX backend loaded")
            rawx_results, rawx_files = pipeline.export_backend_formats(rawx_backend, "RAWX", "RAWX_export")
            
        if hdb_available:
            print(f"\n📂 Loading HDB file...")
            hdb_backend = pipeline.Backend.load(hdb_path) 
            print(f"✅ HDB backend loaded")
            hdb_results, hdb_files = pipeline.export_backend_formats(hdb_backend, "HDB", "HDB_export")
        
        # Perform comparisons
        if rawx_files or hdb_files:
            comparison_results = pipeline.perform_comparisons(rawx_files, hdb_files)
        else:
            print(f"\n⚠️  No files exported - skipping comparison analysis")
            comparison_results = {}
        
        # === DEVICE-LEVEL FIELD COMPARISON ===
        print(f"\n" + "="*60)
        print(f"🔬 DEVICE-LEVEL FIELD COMPARISON")
        print(f"="*60)
        
        # Compare RAWX vs HDB outputs (device level)
        if rawx_files and hdb_files:
            print(f"\n📊 RAWX vs HDB Device-Level Comparison:")
            common_formats = set(rawx_files.keys()) & set(hdb_files.keys())
            
            for format_key in sorted(common_formats):
                rawx_file = rawx_files[format_key]
                hdb_file = hdb_files[format_key]
                
                if rawx_file and hdb_file and os.path.exists(rawx_file) and os.path.exists(hdb_file):
                    comprehensive_device_comparison(rawx_file, hdb_file, f"RAWX vs HDB ({format_key})")
                else:
                    print(f"\n   ⚠️  Skipping {format_key}: Files not available")
        
        # Compare against reference files (device level)
        print(f"\n📊 Reference File Device-Level Comparison:")
        
        # HDB outputs vs HDB reference
        hdb_ref_path = os.path.join(script_dir, "psse_33.raw")
        if os.path.exists(hdb_ref_path) and hdb_files:
            print(f"\n🔍 HDB Outputs vs HDB Reference (Device Level):")
            # Compare a representative format against reference
            for format_key in ['v35_bus_branch', 'v33_hybrid_current']:
                if format_key in hdb_files and hdb_files[format_key]:
                    hdb_file = hdb_files[format_key]
                    if os.path.exists(hdb_file):
                        comprehensive_device_comparison(hdb_file, hdb_ref_path, f"HDB {format_key} vs Reference")
                        break  # Just compare one representative format to avoid too much output
        
        # RAWX outputs vs RAWX reference  
        rawx_ref_path = os.path.join(script_dir, "savnw_nb.raw")
        if os.path.exists(rawx_ref_path) and rawx_files:
            print(f"\n🔍 RAWX Outputs vs RAWX Reference (Device Level):")
            # Compare a representative format against reference
            for format_key in ['v35_node_breaker', 'v35_hybrid_current']:
                if format_key in rawx_files and rawx_files[format_key]:
                    rawx_file = rawx_files[format_key]
                    if os.path.exists(rawx_file):
                        comprehensive_device_comparison(rawx_file, rawx_ref_path, f"RAWX {format_key} vs Reference")
                        break  # Just compare one representative format to avoid too much output
        
        print(f"\n🎯 Comprehensive comparison demo completed!")
        print(f"   Output files saved to: {output_dir}")
        print(f"   ✅ File-level comparisons: COMPLETED")
        print(f"   ✅ Device-level field comparisons: COMPLETED")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
