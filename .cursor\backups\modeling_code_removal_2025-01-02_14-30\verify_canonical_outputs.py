#!/usr/bin/env python3
"""
Canonical Output Verification Module
====================================

This module provides comprehensive verification and comparison capabilities
for canonical output files. It handles:
- File hash comparison for bitwise differences
- JSON content comparison for structural differences  
- Detailed field-by-field analysis
- Statistical reporting

Designed to be called from test scripts or used standalone.
"""

import json
import hashlib
import argparse
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional


class CanonicalOutputVerifier:
    """Comprehensive verification of canonical output files."""
    
    def __init__(self, test_dir: str = "test_modeling_consistency"):
        """
        Initialize the verifier.
        
        Args:
            test_dir: Directory containing canonical output files to verify
        """
        self.test_dir = Path(test_dir)
        self.json_files: List[Path] = []
        self.file_hashes: Dict[str, str] = {}
        self.file_sizes: Dict[str, int] = {}
        self.comparison_results: Dict[str, bool] = {}
        
    def find_canonical_files(self) -> List[Path]:
        """Find all canonical JSON files in the test directory."""
        if not self.test_dir.exists():
            raise FileNotFoundError(f"Test directory not found: {self.test_dir}")
        
        # Find all JSON files that match canonical output pattern
        patterns = ["canonical_*.json", "*_canonical_*.json"]
        json_files = []
        
        for pattern in patterns:
            json_files.extend(self.test_dir.glob(pattern))
        
        # Remove duplicates and sort
        self.json_files = sorted(list(set(json_files)))
        
        if len(self.json_files) < 2:
            raise ValueError(f"Found only {len(self.json_files)} JSON files. Need at least 2 to compare.")
        
        return self.json_files
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def calculate_all_hashes(self) -> Dict[str, str]:
        """Calculate hashes for all JSON files."""
        print(f"🔐 Calculating file hashes...")
        
        for file_path in self.json_files:
            file_hash = self.calculate_file_hash(file_path)
            file_size = file_path.stat().st_size
            
            self.file_hashes[file_path.name] = file_hash
            self.file_sizes[file_path.name] = file_size
            
            print(f"   {file_path.name}: {file_hash[:16]}... ({file_size:,} bytes)")
        
        return self.file_hashes
    
    def compare_file_hashes(self) -> bool:
        """Compare file hashes to detect bitwise differences."""
        print(f"\n🔐 FILE HASH COMPARISON:")
        
        unique_hashes = set(self.file_hashes.values())
        hash_differences = len(unique_hashes) > 1
        
        if hash_differences:
            print("   ❌ BITWISE DIFFERENCES DETECTED!")
            for filename, file_hash in self.file_hashes.items():
                size = self.file_sizes.get(filename, 0)
                print(f"      {filename:50} {file_hash[:16]}... ({size:,} bytes)")
            return False
        else:
            print("   ✅ All files have identical hashes")
            if self.file_hashes:
                hash_value = list(self.file_hashes.values())[0]
                print(f"      Common hash: {hash_value[:16]}...")
            return True
    
    def compare_json_content(self, file1: Path, file2: Path) -> bool:
        """Compare two JSON files for exact content match."""
        try:
            with open(file1, 'r', encoding='utf-8') as f1:
                data1 = json.load(f1)
            
            with open(file2, 'r', encoding='utf-8') as f2:
                data2 = json.load(f2)
            
            # Compare structures
            if data1 == data2:
                return True
            else:
                # Find and report differences
                self._report_json_differences(file1.name, file2.name, data1, data2)
                return False
                
        except Exception as e:
            print(f"  ❌ Error comparing {file1.name} vs {file2.name}: {e}")
            return False
    
    def _report_json_differences(self, file1_name: str, file2_name: str, 
                                data1: Dict[str, Any], data2: Dict[str, Any]) -> None:
        """Report detailed differences between two JSON data structures."""
        print(f"  ❌ Content differs: {file1_name} vs {file2_name}")
        
        # Compare top-level sections
        keys1 = set(data1.keys())
        keys2 = set(data2.keys())
        
        if keys1 != keys2:
            only_in_1 = keys1 - keys2
            only_in_2 = keys2 - keys1
            if only_in_1:
                print(f"    Sections only in {file1_name}: {sorted(only_in_1)}")
            if only_in_2:
                print(f"    Sections only in {file2_name}: {sorted(only_in_2)}")
        
        # Compare common sections
        common_sections = keys1.intersection(keys2)
        different_sections = []
        
        for section in sorted(common_sections):
            if data1[section] != data2[section]:
                different_sections.append(section)
        
        if different_sections:
            print(f"    Different sections: {different_sections}")
            
            # Show details for first few different sections
            for section in different_sections[:3]:
                self._compare_section_details(section, data1[section], data2[section])
    
    def _compare_section_details(self, section_name: str, section1: Any, section2: Any) -> None:
        """Compare details of a specific section."""
        print(f"      Section '{section_name}':")
        
        if isinstance(section1, list) and isinstance(section2, list):
            if len(section1) != len(section2):
                print(f"        Different lengths: {len(section1)} vs {len(section2)}")
            else:
                # Compare first few different records
                differences_shown = 0
                for i, (record1, record2) in enumerate(zip(section1, section2)):
                    if record1 != record2 and differences_shown < 2:
                        differences_shown += 1
                        print(f"        Record {i}: differs")
                        if isinstance(record1, dict) and isinstance(record2, dict):
                            diff_fields = [k for k in record1.keys() | record2.keys() 
                                         if record1.get(k) != record2.get(k)]
                            if diff_fields:
                                print(f"          Different fields: {diff_fields[:5]}")
        
        elif isinstance(section1, dict) and isinstance(section2, dict):
            diff_keys = [k for k in section1.keys() | section2.keys() 
                        if section1.get(k) != section2.get(k)]
            if diff_keys:
                print(f"        Different keys: {diff_keys[:5]}")
        
        else:
            print(f"        Type difference: {type(section1).__name__} vs {type(section2).__name__}")
    
    def compare_all_json_content(self) -> bool:
        """Compare JSON content across all files."""
        print(f"\n🔬 JSON CONTENT COMPARISON:")
        
        all_identical = True
        comparison_count = 0
        
        # Compare all pairs
        for i in range(len(self.json_files)):
            for j in range(i + 1, len(self.json_files)):
                file1 = self.json_files[i]
                file2 = self.json_files[j]
                comparison_key = f"{file1.name}_vs_{file2.name}"
                
                print(f"   Comparing {file1.name} vs {file2.name}")
                
                is_identical = self.compare_json_content(file1, file2)
                self.comparison_results[comparison_key] = is_identical
                comparison_count += 1
                
                if is_identical:
                    print(f"   ✅ Content is identical")
                else:
                    all_identical = False
        
        print(f"\n   Performed {comparison_count} pairwise comparisons")
        return all_identical
    
    def analyze_file_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in file names to understand what's being compared."""
        analysis = {
            'backend_types': set(),
            'modeling_approaches': set(),
            'timestamps': set(),
            'file_count': len(self.json_files)
        }
        
        for file_path in self.json_files:
            filename = file_path.name
            
            # Extract patterns from filename: canonical_{backend}_{approach}_{timestamp}.json
            parts = filename.replace('.json', '').split('_')
            
            if len(parts) >= 4 and parts[0] == 'canonical':
                if len(parts) >= 2:
                    analysis['backend_types'].add(parts[1])
                if len(parts) >= 3:
                    analysis['modeling_approaches'].add(parts[2])
                if len(parts) >= 4:
                    analysis['timestamps'].add(parts[3])
        
        return analysis
    
    def generate_verification_report(self, hash_identical: bool, content_identical: bool) -> Path:
        """Generate comprehensive verification report."""
        timestamp = max(self.analyze_file_patterns()['timestamps']) if self.analyze_file_patterns()['timestamps'] else "unknown"
        report_file = self.test_dir / f"verification_report_{timestamp}.txt"
        
        analysis = self.analyze_file_patterns()
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Canonical Output Verification Report\n")
            f.write("=" * 40 + "\n\n")
            
            f.write(f"Test Directory: {self.test_dir}\n")
            f.write(f"Files Analyzed: {analysis['file_count']}\n")
            f.write(f"Backend Types: {sorted(analysis['backend_types'])}\n")
            f.write(f"Modeling Approaches: {sorted(analysis['modeling_approaches'])}\n\n")
            
            f.write("VERIFICATION RESULTS:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Hash Comparison: {'✅ IDENTICAL' if hash_identical else '❌ DIFFERENT'}\n")
            f.write(f"Content Comparison: {'✅ IDENTICAL' if content_identical else '❌ DIFFERENT'}\n\n")
            
            f.write("FILE DETAILS:\n")
            f.write("-" * 12 + "\n")
            for filename in sorted(self.file_hashes.keys()):
                file_hash = self.file_hashes[filename]
                file_size = self.file_sizes[filename]
                f.write(f"{filename:50} {file_hash} ({file_size:,} bytes)\n")
            
            if self.comparison_results:
                f.write(f"\nCOMPARISON RESULTS:\n")
                f.write("-" * 18 + "\n")
                for comparison, result in self.comparison_results.items():
                    status = "✅ IDENTICAL" if result else "❌ DIFFERENT"
                    f.write(f"{comparison:60} {status}\n")
        
        return report_file
    
    def verify_all_files(self) -> bool:
        """Run complete verification of all canonical files."""
        try:
            # Find files
            self.find_canonical_files()
            
            print(f"🔍 Found {len(self.json_files)} canonical output files:")
            for f in self.json_files:
                size = f.stat().st_size
                print(f"   {f.name} ({size:,} bytes)")
            
            # Calculate hashes
            self.calculate_all_hashes()
            
            # Compare hashes
            hash_identical = self.compare_file_hashes()
            
            # Compare JSON content (always run, even if hashes are identical for verification)
            content_identical = self.compare_all_json_content()
            
            # Generate report
            report_file = self.generate_verification_report(hash_identical, content_identical)
            print(f"\n📋 Verification report saved to: {report_file}")
            
            # Final result
            overall_success = hash_identical and content_identical
            
            print(f"\n🎯 VERIFICATION RESULT:")
            if overall_success:
                print("✅ All files are identical (hash and content)")
                print("   No unwanted transformations detected")
            else:
                print("❌ Files have differences")
                if not hash_identical:
                    print("   - Bitwise differences detected in file hashes")
                if not content_identical:
                    print("   - Structural differences detected in JSON content")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ Error during verification: {e}")
            return False


def main():
    """Main function for standalone usage."""
    parser = argparse.ArgumentParser(description='Verify canonical output files for consistency')
    parser.add_argument('--test-dir', '-d', default='test_modeling_consistency',
                       help='Directory containing canonical output files')
    
    args = parser.parse_args()
    
    try:
        verifier = CanonicalOutputVerifier(args.test_dir)
        success = verifier.verify_all_files()
        
        # Exit with appropriate code
        exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        exit(1)


if __name__ == '__main__':
    main() 