# PSS/E RAW Data Format Specifications
# Versions 33, 34, and 35

## Version 33 RAW Data Format

### Section 1: Case Identification Data
**Format**: Three data records
- **Line 1**: IC, SBASE, REV, XFRRAT, NXFRAT, BASFRQ
- **Line 2**: TITLE(1) - Case title line 1
- **Line 3**: TITLE(2) - Case title line 2

**Field Header Format**:
```
@!IC,SBASE,REV,XFRRAT,NXFRAT,BASFRQ
```

**Field Descriptions**:
- IC: Case identification code
- SBASE: System base MVA
- REV: Case revision number
- XFRRAT: Transformer impedance base flag
- NXFRAT: Transformer impedance base flag
- BASFRQ: System base frequency
- TITLE(1-2): Case title lines (up to 60 characters each)

### Section 2: Bus Data
**Field Header Format**:
```
@!I,'NAME',BASKV,IDE,AREA,ZONE,OWNER,VM,VA,NVHI,NVLO,EVHI,EVLO
```

- I: Bus number
- NAME: Bus name (up to 12 characters)
- BASKV: Base voltage (kV)
- IDE: Bus type code
- AREA: Control area number
- ZONE: Zone number
- OWNER: Owner number
- VM, VA: Bus voltage magnitude and angle
- NVHI, NVLO: Normal voltage limits
- EVHI, EVLO: Emergency voltage limits

### Section 3: Load Data
**Field Header Format**:
```
@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT
```

- I: Bus number
- ID: Load identifier
- STATUS: Load status
- AREA, ZONE: Area and zone numbers
- PL, QL: Active and reactive power
- IP, IQ: Current load
- YP, YQ: Admittance load
- OWNER: Owner number
- SCALE: Load scale factor
- INTRPT: Interruptible load flag

### Section 4: Fixed Bus Shunt Data
**Field Header Format**:
```
@!I,'ID',STATUS,GL,BL
```

- I, ID, STATUS, GL, BL
- I: Bus number
- ID: Shunt identifier
- STATUS: Shunt status
- GL, BL: Conductance and susceptance

### Section 5: Generator Data
**Field Header Format**:
```
@!I,'ID',PG,QG,QT,QB,VS,IREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF
```

- I: Bus number
- ID: Generator identifier
- PG, QG: Active and reactive power
- QT, QB: Reactive power limits
- VS: Voltage setpoint
- IREG: Regulated bus number
- MBASE: MVA base
- ZR, ZX: Resistance and reactance
- RT, XT: Transformer tap settings
- GTAP: Transformer off-nominal turns ratio
- STAT: Generator status
- RMPCT: Participation factor
- PT, PB: Active power limits
- O1-F4: Owner and fraction data
- WMOD, WPF: Wind model and power factor

### Section 6: Non-Transformer Branch Data
**Field Header Format**:
```
@!I,J,'CKT',R,X,B,RATEA,RATEB,RATEC,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,O2,F2,O3,F3,O4,F4
```

- I, J: From and to bus numbers
- CKT: Circuit identifier
- R, X, B: Resistance, reactance, and charging
- RATEA, RATEB, RATEC: MVA ratings
- GI, BI, GJ, BJ: Line shunt admittances
- ST: Branch status
- MET: Meter bus number
- LEN: Line length
- O1-F4: Owner and fraction data

### Section 7: Transformer Data
**Format**: 4 lines for 2-winding, 5 lines for 3-winding transformers

**Field Header Format**:
```
@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,VECGRP
@!R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR
@!WINDV1,NOMV1,ANG1,RATA1,RATB1,RATC1,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1
@!WINDV2,NOMV2,ANG2,RATA2,RATB2,RATC2,COD2,CONT2,RMA2,RMI2,VMA2,VMI2,NTP2,TAB2,CR2,CX2,CNXA2
@!WINDV3,NOMV3,ANG3,RATA3,RATB3,RATC3,COD3,CONT3,RMA3,RMI3,VMA3,VMI3,NTP3,TAB3,CR3,CX3,CNXA3
```

**2-Winding Transformer (4 lines)**:
- **Line 1**: I, J, 0, CKT, CW, CZ, CM, MAG1, MAG2, NMETR, NAME, STAT, O1, F1, O2, F2, O3, F3, O4, F4, VECGRP
- **Line 2**: R1-2, X1-2, SBASE1-2
- **Line 3**: WINDV1, NOMV1, ANG1, RATA1, RATB1, RATC1, COD1, CONT1, RMA1, RMI1, VMA1, VMI1, NTP1, TAB1, CR1, CX1, CNXA1
- **Line 4**: WINDV2, NOMV2

**3-Winding Transformer (5 lines)**:
- **Line 1**: I, J, K, CKT, CW, CZ, CM, MAG1, MAG2, NMETR, NAME, STAT, O1, F1, O2, F2, O3, F3, O4, F4, VECGRP
- **Line 2**: R1-2, X1-2, SBASE1-2, R2-3, X2-3, SBASE2-3, R3-1, X3-1, SBASE3-1, VMSTAR, ANSTAR
- **Line 3**: WINDV1, NOMV1, ANG1, RATA1, RATB1, RATC1, COD1, CONT1, RMA1, RMI1, VMA1, VMI1, NTP1, TAB1, CR1, CX1, CNXA1
- **Line 4**: WINDV2, NOMV2, ANG2, RATA2, RATB2, RATC2, COD2, CONT2, RMA2, RMI2, VMA2, VMI2, NTP2, TAB2, CR2, CX2, CNXA2
- **Line 5**: WINDV3, NOMV3, ANG3, RATA3, RATB3, RATC3, COD3, CONT3, RMA3, RMI3, VMA3, VMI3, NTP3, TAB3, CR3, CX3, CNXA3

**Field Descriptions**:
- I, J, K: From, to, and third winding bus numbers
- CKT: Circuit identifier
- CW: Winding I/O code
- CZ: Impedance I/O code
- CM: Magnetizing admittance I/O code
- MAG1, MAG2: Magnetizing conductance and susceptance
- NMETR: Metering flag
- NAME: Transformer name
- STAT: Transformer status
- O1-F4: Owner and fraction data
- VECGRP: Vector group identifier
- R, X, S: Resistance, reactance, and MVA base
- WINDV: Winding voltage
- ANG: Winding angle
- RATE: MVA ratings
- COD: Control mode code
- CONT: Control bus number
- RMA, RMI: Upper and lower limits of (depends on COD) off-nominal turns ratio, voltage, reactive power, or active power
- VMA, VMI: Upper and lower voltage limits (depends on COD) of voltage at controlled bus, reactive power flow, or active power flow
- NTP: Number of tap positions
- TAB: Impedance table number
- CR, CX: Load drop conpensation impedance for voltage controlling transformers
- CNXA: winding connection angle in degrees; used when COD is 5
- VMSTAR, ANSTAR: Voltage and angle at the hidden star point bus

### Section 8: Area Interchange Data
**Field Header Format**:
```
@!I,ISW,PDES,PTOL,'ARNAME'
```

- I, ISW, PDES, PTOL, ARNAME
- I: Area number
- ISW: Area slack bus number
- PDES: Net interchange
- PTOL: Interchange tolerance
- ARNAME: Area name

### Section 9: Two-Terminal DC Transmission Line Data
**Field Header Format**:
```
@!'NAME',MDC,RDC,SETVL,VSCHD,VCMOD,RCOMP,DELTI,METER,DCVMIN,CCCITMX,CCCACC
@!IPR,NBR,ANMXR,ANMNR,RCR,XCR,EBASR,TRR,TAPR,TMXR,TMNR,STPR,ICR,IFR,ITR,IDR,XCAPR
@!IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,IFI,ITI,IDI,XCAPI
```

- I: Bus number
- NAME: VSC name
- MDC: Control mode
- RDC: DC resistance
- SETVL: Current or power demand
- VSCHD: Voltage setpoint
- VCMOD: Mode switch voltage (kV) when inverter voltage falls below this value, the line switches to current control mode
- RCOMP: Compounding resistance (ohms) 
- DELTI: Current margin; fraction by which the order is reduced when ALPHA is at its minimum and the inverter is controlling the line current
- METER: Metering end
- DCVMIN: Minimum voltage
- CCCITMX: Maximum iterations
- CCCACC: Acceleration factor
- IPR: Rectifier converter bus number
- NBR: Rectifier number of tap positions
- ANMXR: Rectifier maximum firing angle (degrees)
- ANMNR: Rectifier minimum firing angle (degrees)
- RCR: Rectifier commutating transformer resistance (ohms)
- XCR: Rectifier commutating transformer reactance (ohms)
- EBASR: Rectifier base voltage (kV)
- TRR: Rectifier transformer turns ratio (secondary/primary)
- TAPR: Rectifier transformer tap setting
- TMXR: Rectifier maximum tap setting
- TMNR: Rectifier minimum tap setting
- STPR: Rectifier tap step
- ICR: Rectifier commutating bus number
- IFR: Rectifier transformer winding 1 bus number
- ITR: Rectifier transformer winding 2 bus number
- IDR: Rectifier transformer winding 3 bus number
- XCAPR: Rectifier load commpensation impedance (ohms)
- IPI: Inverter converter bus number
- NBI: Inverter number of tap positions
- ANMXI: Inverter maximum firing angle (degrees)
- ANMNI: Inverter minimum firing angle (degrees)
- RCI: Inverter commutating transformer resistance (ohms)
- XCI: Inverter commutating transformer reactance (ohms)
- EBASI: Inverter base voltage (kV)
- TRI: Inverter transformer turns ratio (secondary/primary)
- TAPI: Inverter transformer tap setting
- TMXI: Inverter maximum tap setting
- TMNI: Inverter minimum tap setting
- STPI: Inverter tap step
- ICI: Inverter commutating bus number
- IFI: Inverter transformer winding 1 bus number
- ITI: Inverter transformer winding 2 bus number
- IDI: Inverter transformer winding 3 bus number
- XCAPI: Inverter load commpensation impedance (ohms)

### Section 10: VSC DC Transmission Line Data
**Field Header Format**:
```
@!'NAME',MDC,RDC,O1,F1,O2,F2,O3,F3,O4,F4
@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,REMOT,RMPCT
@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,REMOT,RMPCT
```

- I: Bus number
- NAME: VSC name
- MDC: Control mode
- RDC: DC resistance
- O1-F4: Owner and fraction data
- IBUS: Bus number
- TYPE: Converter type
- MODE: Control mode
- DCSET: DC setpoint
- ACSET: AC setpoint
- ALOSS: Active power loss
- BLOSS: Reactive power loss
- MINLOSS: Minimum loss
- SMAX: Maximum power
- IMAX: Maximum current
- PWF: Power factor
- MAXQ: Maximum reactive power
- MINQ: Minimum reactive power
- REMOT: Remote bus number
- RMPCT: Participation factor

### Section 11: Transformer Impedance Correction Tables
**Field Header Format**:
```
@!I,T1,F1,T2,F2,T3,F3,T4,F4,T5,F5,T6,F6,T7,F7,T8,F8,T9,F9,T10,F10,T11,F11
```

- I: Table number
- T1-F11: Off-nominal turns ratio and scaling factor pairs or phase shift angle and scaling factor pairs

### Section 12: Multi-Terminal DC Transmission Line Data
**Field Header Format**:
```
@!'NAME', NCONV, NDCBS, NDCLN, MDC, VCONV, VCMOD, VCONVN
@!IB,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD
@!IDC,IB,AREA,ZONE,'DCNAME',IDC2,RGRND,OWNER
@!IDC,JDC,'DCCKT',MET,RDC,LDC
```

- 'NAME': Multi-terminal DC system name
- NCONV: Number of converters
- NDCBS: Number of DC buses
- NDCLN: Number of DC links
- MDC: Control mode
- VCONV: Voltage setpoint
- VCMOD: Voltage control mode
- VCONVN: Bus number or extended bus name of the ac converter station that controls dc voltage on the positive pole
- IB: Bus number
- N: Number of bridges in series
- ANGMX: Maximum firing angle (degrees)
- ANGMN: Minimum firing angle (degrees)
- RC: Commutating transformer resistance (ohms)
- XC: Commutating transformer reactance (ohms)
- EBAS: Base voltage (kV)
- TR: Transformer turns ratio (secondary/primary)
- TAP: Transformer tap setting
- TPMX: Maximum tap setting
- TPMN: Minimum tap setting
- TSTP: Tap step
- SETVL: Current or power demand
- DCPF: DC Participation factor
- MARG: Current margin
- CNVCOD: Converter code
- IDC: DC bus number
- IB: AC converter bus number
- AREA: Area number
- ZONE: Zone number
- 'DCNAME': DC bus name
- IDC2: Second DC bus number
- RGRND: Ground resistance (ohms)
- OWNER: Owner number
- IDC: DC bus number
- JDC: Second DC bus number
- 'DCCKT': DC circuit name
- MET: Metering end
- RDC: DC resistance (ohms)
- LDC: DC link inductance (microHenries)

### Section 13: Multi-Section Line Group Data
**Field Header Format**:
```
@!I,J,'ID',MET,DUM1,DUM2,DUM3,DUM4,DUM5,DUM6,DUM7,DUM8,DUM9
```

- I, J: From and to bus numbers
- ID: Group identifier
- MET: Metering end
- DUM1-DUM9: Reserved fields

### Section 14: Zone Data
**Field Header Format**:
```
@!I,'ZONAME'
```

- I: Zone number
- ZONAME: Zone name

### Section 15: Interarea Transfer Data
**Field Header Format**:
```
@!ARFROM,ARTO,'TRID',PTRAN
```

- ARFROM: From area number
- ARTO: To area number
- TRID: Transfer identifier
- PTRAN: Transfer amount

### Section 16: Owner Data
**Field Header Format**:
```
@!I,'OWNAME'
```

- I: Owner number
- OWNAME: Owner name

### Section 17: FACTS Device Data
**Field Header Format**:
```
@!'NAME',I,J,MODE,PDES,QDES,VSET,SHMX,TRMX,VTMN,VTMX,VSMX,IMX,LINX,
@!RMPCT,OWNER,SET1,SET2,VSREF,REMOT,'MNAME'
```

- NAME: Device name
- I, J: From and to bus numbers
- MODE: Control mode
- PDES, QDES: Active and reactive power
- VSET: Voltage setpoint
- SHMX: Maximum shunt susceptance
- TRMX: Maximum series reactance
- VTMN, VTMX: Voltage limits
- VSMX: Maximum voltage
- IMX: Maximum current
- LINX: Line index
- RMPCT: Participation factor
- OWNER: Owner number
- SET1, SET2: Setpoint 1 and 2
- VSREF: Voltage reference
- REMOT: Remote bus number
- MNAME: Master device name

### Section 18: Switched Shunt Data
**Format**: Variable number of lines (1 base line + continuation lines as needed)

**Field Header Format**:
```
@!I,MODSW,ADJM,STAT,VSWHI,VSWLO,SWREM,RMPCT,'RMIDNT'
@!BINIT,N1,B1,N2,B2,N3,B3,N4,B4,N5,B5,N6,B6,N7,B7,N8,B8
```

- I: Bus number
- MODSW: Control mode
- ADJM: Adjustment method
- STAT: Shunt status
- VSWHI, VSWLO: Voltage limits
- SWREM: Remote bus number
- RMPCT: Participation factor
- RMIDNT: Remote voltage control ID
- BINIT: Initial susceptance
- N1-B8: Step size and susceptance pairs

### Section 19: GNE Device Data
**Field Header Format**:
```
@!’NAME’,’MODEL’,NTERM, BUS1, ...,BUSNTERM,NREAL,NINTG,NCHAR,
@!STATUS, OWNER,NMETR
@!REAL1, ..., REALmin(10,NREAL)
@!INTG1, ..., INTGmin(10,NINTG)
@!CHAR1, ..., CHARmin(10,NCHAR)
```

- NAME: Device name
- MODEL: Model name
- NTERM: Number of terminals
- BUS1...BUSNTERM: Bus numbers
- NREAL: Number of real data items
- NINTG: Number of integer data items
- NCHAR: Number of character data items
- STATUS: Device status
- OWNER: Owner number
- NMETR: Non-metered end bus number
- REAL1...REALNREAL: Real data items 10 per line with as many lines as needed to supply NREAL data items
- INTG1...INTGNINTG: Bus numbers or extended bus names; 10 per line with as many lines as needed to supply NINTG data items
- CHAR1...CHARNCHAR: Two-character identifiers; 10 per line with as many lines as needed to supply NCHAR data items

### Section 20: Induction Machine Data
**Format**: Variable number of lines (1 base line + continuation lines as needed)

**Field Header Format**:
```
I,ID,STAT,SCODE,DCODE,AREA,ZONE,OWNER,TCODE,BCODE,MBASE,RATEKV,
PCODE,PSET,H,A,B,D,E,RA,XA,XM,R1,X1,R2,X2,X3,E1,SE1,E2,SE2,IA1,IA2,
XAMULT
```

- I: Bus number
- ID: Machine identifier
- STAT: Status
- SCODE: Machine type code
- DCODE: Machine design code
- AREA: Area number
- ZONE: Zone number
- OWNER: Owner number
- TCODE: Torque type code
- BCODE: Base power code
- MBASE: Machine base MVA
- RATEKV: Rated voltage
- PCODE: Power setpoint code
- PSET: Power setpoint
- H: Output of the machine in the real power drawn by the machine
- A, B, D, E: A positive value of electrical power means that the machine is operating as a motor; similarly, a positive value of mechanical power output means that the machine is driving a mechanical load and operating as a motor.
- RA, XA, XM, R1, X1, R2, X2, X3: Electrical parameters
- E1, SE1, E2, SE2: Saturation parameters
- IA1, IA2: Stator current parameters
- XAMULT: Xa multiplier at full leakage reactance saturation

### Section 21: Q Record
**Format**: Three data records
- **Line 1**: Q, 'BLANK', TITLE(1)
- **Line 2**: TITLE(2)
- **Line 3**: TITLE(3)

**Field Header Format**:
```
@!Q,'BLANK','TITLE1'
@!'TITLE2'
@!'TITLE3'
```

**Field Descriptions**:
- Q: Record identifier
- TITLE(1-3): Title lines (up to 60 characters each)

### Section 22: End of Data
**Field Header Format**:
```
@!Q_END
```

- 0 / END OF DATA
- 0: Record identifier
- /: Comment delimiter
- END OF DATA: End of file marker

## Version 34 RAW Data Format

### Section 1: Case Identification Data
**Format**: Three data records
- **Line 1**: IC, SBASE, REV, XFRRAT, NXFRAT, BASFRQ
- **Line 2**: TITLE(1) - Case title line 1
- **Line 3**: TITLE(2) - Case title line 2

**Field Header Format**:
```
@!IC,SBASE,REV,XFRRAT,NXFRAT,BASFRQ
```

**Field Descriptions**:
- IC: Case identification code
- SBASE: System base MVA
- REV: Case revision number
- XFRRAT: Transformer impedance base flag
- NXFRAT: Transformer impedance base flag
- BASFRQ: System base frequency
- TITLE(1-2): Case title lines (up to 60 characters each)

**RAWX Support**: JSON-based parameter set format available

### Section 2: System-Wide Data
**Field Header Format**:
```
@! GENRAL, THRSHZ, PQBRAK, BLOWUP
@! GAUSS, ITMX, ACCP, ACCQ, ACCM, TOL
@! NEWTON, ITMXN, ACCN, TOLN, VCTOLQ, VCTOLV, DVLIM, NDVFCT
@! ADJUST, ADJTHR, ACCTAP, TAPLIM, SWVBND, MXTPSS, MXSWIM
@! TYSL, ITMXTY, ACCTY, TOLTY
@! SOLVER, METHOD, ACTAPS, AREAIN, PHSHFT, DCTAPS, SWSHNT, FLATST, VARLIM, NONDIV
@! RATING, IRATE, 'NAME', 'DESC'
@! RATING2, IRATE2, 'NAME2', 'DESC2'
@! RATING3, IRATE3, 'NAME3', 'DESC3'
@! RATING4, IRATE4, 'NAME4', 'DESC4'
@! RATING5, IRATE5, 'NAME5', 'DESC5'
@! RATING6, IRATE6, 'NAME6', 'DESC6'
@! RATING7, IRATE7, 'NAME7', 'DESC7'
@! RATING8, IRATE8, 'NAME8', 'DESC8'
@! RATING9, IRATE9, 'NAME9', 'DESC9'
@! RATING10, IRATE10, 'NAME10', 'DESC10'
@! RATING11, IRATE11, 'NAME11', 'DESC11'
@! RATING12, IRATE12, 'NAME12', 'DESC12'
```

**Field Descriptions**:
- GENRAL: General parameters
- THRSHZ: Threshold for zero
- PQBRAK: PQ break flag
- BLOWUP: Blowup flag
- GAUSS: Gauss-Seidel parameters
- ITMX: Maximum iterations
- ACCP, ACCQ, ACCM: Acceleration factors
- TOL: Convergence tolerance
- NEWTON: Newton-Raphson parameters
- ITMXN: Maximum iterations
- ACCN: Acceleration factor
- TOLN: Convergence tolerance
- VCTOLQ: Reactive power mismatch convergence tolerance
- VCTOLV: Voltage error convergence tolerance
- DVLIM: Maximum voltage change
- NDVFCT: Non-divergent solution improvement factor
- ADJUST: Automatic adjustment parameters
- ADJTHR: Automatic adjustment threshold
- ACCTAP: Tap movement deceleration factor
- TAPLIM: Maximum tap change per adjustment
- SWVBND: Voltage controlling band mode switched shunts adjustment percentage
- MXTPSS: Maximum number of tap and shunt adjustments
- MXSWIM: Maximum number of induction machine state changes
- TYSL: Tysl parameters
- ITMXTY: Maximum iterations
- ACCTY: Acceleration factor
- TOLTY: Convergence tolerance
- SOLVER: Solver parameters
- METHOD: Solution method
- ACTAPS: AC tap adjustment code
- AREAIN: Area interchange adjustment code
- PHSHFT: Phase shift adjustment code
- DCTAPS: DC tap adjustment code
- SWSHNT: Switched shunt adjustment code
- FLATST: Flat start code
- VARLIM: Reactive power limit application code
- NONDIV: Non-divergent solution code
- RATING: Rating parameters
- IRATE: Rating number
- NAME: Rating name
- DESC: Rating description

### Section 3: Bus Data
**Field Header Format**:
```
@!I,'NAME',BASKV,IDE,AREA,ZONE,OWNER,VM,VA,NVHI,NVLO,EVHI,EVLO
```

- I: Bus number
- NAME: Bus name (up to 12 characters)
- BASKV: Base voltage (kV)
- IDE: Bus type code
- AREA: Control area number
- ZONE: Zone number
- OWNER: Owner number
- VM, VA: Bus voltage magnitude and angle
- NVHI, NVLO: Normal voltage limits
- EVHI, EVLO: Emergency voltage limits

### Section 4: Load Data
**Field Header Format**:
```
@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT,DGENP,DGENQ,DGENM
```

- I: Bus number
- ID: Load identifier
- STATUS: Load status
- AREA, ZONE: Area and zone numbers
- PL, QL: Active and reactive power
- IP, IQ: Current load
- YP, YQ: Admittance load
- OWNER: Owner number
- SCALE: Load scale factor
- INTRPT: Interruptible load flag
- DGENP, DGENQ, DGENM: Distributed Generation fields

### Section 5: Fixed Bus Shunt Data
**Field Header Format**:
```
@!I,'ID',STATUS,GL,BL
```

- I, ID, STATUS, GL, BL
- I: Bus number
- ID: Shunt identifier
- STATUS: Shunt status
- GL, BL: Conductance and susceptance

### Section 6: Generator Data
**Field Header Format**:
```
@!I,'ID',PG,QG,QT,QB,VS,IREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF,NREG
```

- I: Bus number
- ID: Generator identifier
- PG, QG: Active and reactive power
- QT, QB: Reactive power limits
- VS: Voltage setpoint
- IREG: Regulated bus number
- MBASE: MVA base
- ZR, ZX: Resistance and reactance
- RT, XT: Transformer tap settings
- GTAP: Transformer off-nominal turns ratio
- NREG: Number of regulating taps
- PT, PB: Active power limits
- STAT: Generator status
- RMPCT: Participation factor
- O1-F4: Owner and fraction data
- WMOD, WPF: Wind model and power factor

### Section 7: Non-Transformer Branch Data
**Field Header Format**:
```
@!I,J,'CKT',R,X,B,'NAME',RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,O2,F2,O3,F3,O4,F4
```

- I, J: From and to bus numbers
- CKT: Circuit identifier
- R, X, B: Resistance, reactance, and charging
- NAME: Branch name
- RATE1-RATE12: MVA ratings
- GI, BI, GJ, BJ: Line shunt admittances
- ST: Branch status
- MET: Meter bus number
- LEN: Line length
- O1-F4: Owner and fraction data

### Section 8: System Switching Device Data
**Field Header Format**:
```
@!I,J,'CKT',X,RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,STATUS,NSTATUS,METERED,STYPE,'NAME'
```

- I, J: From and to bus numbers
- CKT: Circuit identifier
- X: Reactance
- RATE1-RATE12: MVA ratings
- STATUS: Device status
- NSTATUS: Normal status
- METERED: Metered end bus number
- STYPE: Switching device type
- NAME: Device name

### Section 9: Transformer Data
**Field Header Format**:
```
@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,VECGRP,ZCOD
@!R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR
@!WINDV1,NOMV1,ANG1,RAT11,RAT21,RAT31,RAT41,RAT51,RAT61,RAT71,RAT81,RAT91,RAT101,RAT111,RAT121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1
@!WINDV2,NOMV2,ANG2,RAT12,RAT22,RAT32,RAT42,RAT52,RAT62,RAT72,RAT82,RAT92,RAT102,RAT112,RAT122,COD2,CONT2,RMA2,RMI2,VMA2,VMI2,NTP2,TAB2,CR2,CX2,CNXA2
@!WINDV3,NOMV3,ANG3,RAT13,RAT23,RAT33,RAT43,RAT53,RAT63,RAT73,RAT83,RAT93,RAT103,RAT113,RAT123,COD3,CONT3,RMA3,RMI3,VMA3,VMI3,NTP3,TAB3,CR3,CX3,CNXA3
```

2-winding will only have
@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,VECGRP,ZCOD
@!R1-2,X1-2,SBASE1-2
@!WINDV1,NOMV1,ANG1,RAT11,RAT21,RAT31,RAT41,RAT51,RAT61,RAT71,RAT81,RAT91,RAT101,RAT111,RAT121,COD1,CONT1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1
@!WINDV2,NOMV2

- I, J, K: From, to, and third winding bus numbers
- CKT: Circuit identifier
- CW: Winding I/O code
- CZ: Impedance I/O code
- CM: Magnetizing admittance I/O code
- MAG1, MAG2: Magnetizing conductance and susceptance
- NMETR: Metering flag
- NAME: Transformer name
- STAT: Transformer status
- O1-F4: Owner and fraction data
- VECGRP: Vector group identifier
- ZCOD: Impedance correction method code
- R, X, S: Resistance, reactance, and charging
- VMSTAR, ANSTAR: Voltage and angle at the hidden star point bus
- WINDV: Winding voltage
- ANG: Winding angle
- NOMV: Nominal voltage
- ANGLE: Angle shift
- RATE: MVA ratings
- COD1, COD2: Control mode codes
- CONT: Control bus number
- RMA, RMI: Off-nominal turns ratio limits, voltage limits, or power limits, depending on COD
- VMA, VMI: Voltage at controlled bus, reactive power flow, or active power flow, depending on COD
- NTP: Number of tap positions
- TAB: Impedance table number
- CR, CX: Load drop compensation impedance for voltage controlling transformers
- CNXA: Winding connection angle in degrees; used when COD is 5
- NODE: Node number of bus CONT

### Section 10: Area Interchange Data
**Field Header Format**:
```
@!I,ISW,PDES,PTOL,'ARNAME'
```

- I, ISW, PDES, PTOL, ARNAME
- I: Area number
- ISW: Area slack bus number
- PDES: Net interchange
- PTOL: Interchange tolerance
- ARNAME: Area name


### Section 11: Two-Terminal DC Transmission Line Data
**V. 34 Field Header Format**:
```
@!'NAME',MDC,RDC,SETVL,VSCHD,VCMOD,RCOMP,DELTI,METER,DCVMIN,CCCITMX,CCCACC
@!IPR,NBR,ANMXR,ANMNR,RCR,XCR,EBASR,TRR,TAPR,TMXR,TMNR,STPR,ICR,IFR,ITR,IDR,XCAPR,NDR
@!IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,IFI,ITI,IDI,XCAPI,NDI
```

- NAME: Non-blank alphanumeric identifier (up to 12 characters, in quotes)
- MDC: Control mode (0=blocked, 1=power, 2=current)
- RDC: DC line resistance (ohms)
- SETVL: Current (amps) or power (MW) demand
- VSCHD: Scheduled compounded dc voltage (kV)
- VCMOD: Mode switch dc voltage (kV)
- RCOMP: Compounding resistance (ohms)
- DELTI: Margin (per unit of desired dc power or current)
- METER: Metered end code (R=rectifier, I=inverter)
- DCVMIN: Minimum compounded dc voltage (kV)
- CCCITMX: Iteration limit for capacitor commutated DC line
- CCCACC: Acceleration factor for capacitor commutated DC line
- IPR: Rectifier converter bus number
- NBR: Number of bridges in series (rectifier)
- ANMXR: Nominal maximum rectifier firing angle (degrees)
- ANMNR: Minimum steady-state rectifier firing angle (degrees)
- RCR: Rectifier commutating transformer resistance per bridge (ohms)
- XCR: Rectifier commutating transformer reactance per bridge (ohms)
- EBASR: Rectifier primary base ac voltage (kV)
- TRR: Rectifier transformer ratio
- TAPR: Rectifier tap setting
- TMXR: Maximum rectifier tap setting
- TMNR: Minimum rectifier tap setting
- STPR: Rectifier tap step (must be positive)
- ICR: Rectifier commutating bus number
- IFR: Winding 1 side from bus number (two-winding transformer)
- ITR: Winding 2 side to bus number (two-winding transformer)
- IDR: Circuit identifier for transformer
- XCAPR: Commutating capacitor reactance magnitude per bridge (ohms)
- NDR: Node number of bus ICR
- IPI: Inverter converter bus number
- NBI: Number of bridges in series (inverter)
- ANMXI: Nominal maximum inverter firing angle (degrees)
- ANMNI: Minimum steady-state inverter firing angle (degrees)
- RCI: Inverter commutating transformer resistance per bridge (ohms)
- XCI: Inverter commutating transformer reactance per bridge (ohms)
- EBASI: Inverter primary base ac voltage (kV)
- TRI: Inverter transformer ratio
- TAPI: Inverter tap setting
- TMXI: Maximum inverter tap setting
- TMNI: Minimum inverter tap setting
- STPI: Inverter tap step (must be positive)
- ICI: Inverter commutating bus number
- IFI: Winding 1 side from bus number (two-winding transformer)
- ITI: Winding 2 side to bus number (two-winding transformer)
- IDI: Circuit identifier for transformer
- XCAPI: Commutating capacitor reactance magnitude per bridge (ohms)
- NDI: Node number of bus ICI

### Section 12: VSC DC Transmission Line Data
**Field Header Format**:
```
@!'NAME',MDC,RDC,O1,F1,O2,F2,O3,F3,O4,F4
@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,RMPCT,NREG
@!IBUS,TYPE,MODE,DCSET,ACSET,ALOSS,BLOSS,MINLOSS,SMAX,IMAX,PWF,MAXQ,MINQ,VSREG,RMPCT,NREG
```

- NAME: Non-blank alphanumeric identifier (up to 12 characters, in quotes)
- MDC: Control mode (0=out-of-service, 1=in-service)
- RDC: DC line resistance (ohms)
- O1-F4: Owner number and fraction pairs
- IBUS: Converter bus number
- TYPE: Converter dc control type (0=out-of-service, 1=dc voltage control, 2=MW control)
- MODE: Converter ac control mode (1=ac voltage control, 2=fixed ac power factor)
- DCSET: Converter dc setpoint (kV for TYPE=1, MW for TYPE=2)
- ACSET: Converter ac setpoint (pu voltage for MODE=1, power factor for MODE=2)
- ALOSS: Active power loss coefficient (kW)
- BLOSS: Reactive power loss coefficient (kW/amp)
- MINLOSS: Minimum converter losses (kW)
- SMAX: Converter MVA rating
- IMAX: Converter ac current rating (amps)
- PWF: Power weighting factor (0.0 <= PWF <= 1.0)
- MAXQ: Reactive power upper limit (Mvar)
- MINQ: Reactive power lower limit (Mvar)
- VSREG: Remote regulated bus number for voltage control
- RMPCT: Participation factor percentage
- NREG: Node number of regulated bus

### Section 13: Transformer Impedance Correction Tables
**Field Header Format**:
```
@!I,T1,RE(F1),IM(F1),T2,RE(F2),IM(F2),T3,RE(F3),IM(F3),T4,RE(F4),IM(F4),T5,RE(F5),IM(F5),T6,RE(F6),IM(F6)
```

- I: Impedance correction table number (1 through maximum at current size level)
- Ti: Off-nominal turns ratio (pu) or phase shift angle (degrees)
- RE(Fi): Real part of complex scaling factor for transformer impedance
- IM(Fi): Imaginary part of complex scaling factor for transformer impedance

### Section 14: Multi-Terminal DC Transmission Line Data
**Field Header Format**:
```
@!'NAME',NCONV,NDCBS,NDCLN,MDC,VCONV,VCMOD,VCONVN
@!IB,N,ANGMX,ANGMN,RC,XC,EBAS,TR,TAP,TPMX,TPMN,TSTP,SETVL,DCPF,MARG,CNVCOD
@!IDC,IB,AREA,ZONE,'DCNAME',IDC2,RGRND,OWNER
@!IDC,JDC,'DCCKT',MET,RDC,LDC
```

- NAME: Multi-terminal DC line identifier (up to 12 characters, in quotes)
- NCONV: Number of ac converter station buses
- NDCBS: Number of dc buses
- NDCLN: Number of dc links
- MDC: Control mode (0=blocked, 1=power control, 2=current control)
- VCONV: Bus number of ac converter station that controls dc voltage on positive pole
- VCMOD: Mode switch dc voltage (kV)
- VCONVN: Bus number of ac converter station that controls dc voltage on negative pole

### Section 15: Multi-Section Line Group Data
**Field Header Format**:
```
@!I,J,'ID',MET,DUM1,DUM2,DUM3,DUM4,DUM5,DUM6,DUM7,DUM8,DUM9
```

- I, J: From and to bus numbers
- ID: Group identifier
- MET: Metering end
- DUM1-DUM9: Reserved fields

### Section 16: Zone Data
**Field Header Format**:
```
@!I,'ZONAME'
```

- I: Zone number
- ZONAME: Zone name

### Section 17: Interarea Transfer Data
**Field Header Format**:
```
@!ARFROM,ARTO,'TRID',PTRAN
```

- ARFROM: From area number
- ARTO: To area number
- TRID: Transfer identifier
- PTRAN: Transfer amount

### Section 18: Owner Data
**Field Header Format**:
```
@!I,'OWNAME'
```

- I: Owner number
- OWNAME: Owner name

### Section 19: FACTS Device Data
**Field Header Format**:
```
@!'NAME',I,J,MODE,PDES,QDES,VSET,SHMX,TRMX,VTMN,VTMX,VSMX,IMX,LINX,RMPCT,OWNER,SET1,SET2,VSREF,FCREG,'MNAME',NREG
```

- NAME: Non-blank alphanumeric identifier (up to 12 characters, in quotes)
- I: Sending end bus number
- J: Terminal end bus number (0 for STATCON)
- MODE: Control mode (0=out-of-service to 8=slave IPFC)
- PDES: Desired active power flow arriving at terminal end bus (MW)
- QDES: Desired reactive power flow arriving at terminal end bus (Mvar)
- VSET: Voltage setpoint at sending end bus (pu)
- SHMX: Maximum shunt current at sending end bus (MVA at unity voltage)
- TRMX: Maximum bridge active power transfer (MW)
- VTMN: Minimum voltage at terminal end bus (pu)
- VTMX: Maximum voltage at terminal end bus (pu)
- VSMX: Maximum series voltage (pu)
- IMX: Maximum series current (MVA at unity voltage, 0=no limit)
- LINX: Reactance of dummy series element (pu)
- RMPCT: Percent of total Mvar contributed by shunt element
- OWNER: Owner number
- SET1, SET2: Control setpoints (depends on MODE)
- VSREF: Series voltage reference code (0=sending end voltage, 1=series current)
- FCREG: Bus number for voltage regulation by shunt element
- MNAME: Name of IPFC master device (for slave devices)
- NREG: Node number of regulated bus

### Section 20: Switched Shunt Data
**Field Header Format**:
```
@!I,MODSW,ADJM,STATUS,VSWHI,VSWLO,SWREG,RMPCT,'RMIDNT',BINIT,N1,B1,N2,B2,N3,B3,N4,B4,N5,B5,N6,B6,N7,B7,N8,B8,NREG
```

- I: Bus number
- MODSW: Control mode (0=locked, 1=discrete voltage, 2=continuous voltage, 3=discrete reactive power, 4=VSC reactive power, 5=admittance setting, 6=FACTS reactive power)
- ADJM: Adjustment method (0=input order, 1=highest/lowest total admittance)
- STATUS: Initial status (1=in-service, 0=out-of-service)
- VSWHI: Controlled voltage upper limit (pu) or reactive power range upper limit
- VSWLO: Controlled voltage lower limit (pu) or reactive power range lower limit
- SWREG: Remote controlled bus number
- RMPCT: Participation factor percentage
- RMIDNT: Remote identifier for controlled device
- BINIT: Initial susceptance (Mvar at 1.0 pu voltage)
- N1-B8: Step size and susceptance pairs
- NREG: Node number of regulated bus

### Section 21: GNE Device Data
**Field Header Format**:
```
@!'NAME','MODEL',NTERM,BUS1,BUS2,NREAL,NINTG,NCHAR,STATUS,OWNER,NMETR
@!REAL1,REAL2,REAL3,REAL4,REAL5,REAL6,REAL7,REAL8,REAL9,REAL10
@!INTG1,INTG2,INTG3,INTG4,INTG5,INTG6,INTG7,INTG8,INTG9,INTG10
@!CHAR1,CHAR2,CHAR3,CHAR4,CHAR5,CHAR6,CHAR7,CHAR8,CHAR9,CHAR10
```

- NAME: Non-blank alphanumeric identifier (up to 12 characters, in quotes)
- MODEL: Name of BOSL model (root name of .mac or .xmac file)
- NTERM: Number of buses (1 or 2 for variable admittance, 1 for variable power/current)
- BUS1, BUS2: Bus numbers to which device is connected
- NREAL: Number of floating point data items required by model
- NINTG: Number of buses required in calculating inputs for model
- NCHAR: Number of two-character identifiers required by model
- STATUS: Device status (1=in-service, 0=out-of-service)
- OWNER: Owner number (1-9999)
- NMETR: Non-metered end bus number (for NTERM > 1)
- REAL1-REAL10: Floating point data items (10 per line, as many lines as needed)
- INTG1-INTG10: Bus numbers required by model (10 per line, as many lines as needed)
- CHAR1-CHAR10: Two-character identifiers (10 per line, as many lines as needed)

### Section 22: Induction Machine Data
**Field Header Format**:
```
@!I,'ID',STATUS,SCODE,DCODE,AREA,ZONE,OWNER,TCODE,BCODE,MBASE,RATEKV,PCODE,PSET,H,A,B,D,E,RA,XA,XM,R1,X1,R2,X2,X3,E1,SE1,E2,SE2,IA1,IA2,XAMULT
```

- I: Bus number
- ID: Machine identifier (1-2 uppercase alphanumeric characters)
- STATUS: Machine status (1=in-service, 0=out-of-service)
- SCODE: Machine standard code (1=NEMA, 2=IEC)
- DCODE: Machine design code (0=Custom, 1=NEMA A, 2=NEMA B/IEC N, 3=NEMA C/IEC H, 4=NEMA D, 5=NEMA E)
- AREA: Area number (1-9999)
- ZONE: Zone number (1-9999)
- OWNER: Owner number (1-9999)
- TCODE: Mechanical load torque variation type (1=simple power law, 2=WECC model)
- BCODE: Machine base power code (1=mechanical MW output, 2=electrical MVA input)
- MBASE: Machine base power (MW or MVA according to BCODE)
- RATEKV: Machine rated voltage (kV line-to-line, 0=use bus base voltage)
- PCODE: Scheduled power code (1=mechanical MW output, 2=electrical MW input)
- PSET: Scheduled active power at 1.0 pu terminal voltage (MW)
- H: Machine inertia (pu on MBASE base)
- A, B, D, E: Torque variation constants (D for simple power law, all for WECC model)
- RA: Armature resistance (pu on MBASE and RATEKV base)
- XA: Armature leakage reactance (pu on MBASE and RATEKV base)
- XM: Unsaturated magnetizing reactance (pu on MBASE and RATEKV base)
- R1: First rotor winding resistance (pu on MBASE and RATEKV base)
- X1: First rotor winding reactance (pu on MBASE and RATEKV base)
- R2: Second rotor winding resistance (pu on MBASE and RATEKV base)
- X2: Second rotor winding reactance (pu on MBASE and RATEKV base)
- X3: Third rotor reactance (pu on MBASE and RATEKV base)
- E1: First saturation curve voltage point (pu on RATEKV base)
- SE1: Saturation factor at E1
- E2: Second saturation curve voltage point (pu on RATEKV base)
- SE2: Saturation factor at E2
- IA1, IA2: Stator currents specifying saturation of stator leakage reactance (pu)
- XAMULT: Multiplier for saturated value (0.0-1.0)

### Section 23: Q Record
**Format**: Three data records
- **Line 1**: Q, 'BLANK', TITLE(1)
- **Line 2**: TITLE(2)
- **Line 3**: TITLE(3)

**Field Header Format**:
```
@!Q,'BLANK','TITLE1'
@!'TITLE2'
@!'TITLE3'
```

**Field Descriptions**:
- Q: Record identifier
- TITLE(1-3): Title lines (up to 60 characters each)

### Section 24: End of Data
**Field Header Format**:
```
@!Q_END
```

- 0 / END OF DATA
- 0: Record identifier
- /: Comment delimiter
- END OF DATA: End of file marker

## Version 35 RAW Data Format

### Section 1: Case Identification Data
**Format**: Three data records
- **Line 1**: IC, SBASE, REV, XFRRAT, NXFRAT, BASFRQ
- **Line 2**: TITLE(1) - Case title line 1
- **Line 3**: TITLE(2) - Case title line 2

**Field Header Format**:
```
@!IC,SBASE,REV,XFRRAT,NXFRAT,BASFRQ
```

**Field Descriptions**:
- IC: Case identification code
- SBASE: System base MVA
- REV: Case revision number
- XFRRAT: Transformer impedance base flag
- NXFRAT: Transformer impedance base flag
- BASFRQ: System base frequency
- TITLE(1-2): Case title lines (up to 60 characters each)

**RAWX Support**: JSON-based parameter set format available

### Section 2: System-Wide Data
**Field Header Format**:
```
@! GENRAL, THRSHZ, PQBRAK, BLOWUP
@! GAUSS, ITMX, ACCP, ACCQ, ACCM, TOL
@! NEWTON, ITMXN, ACCN, TOLN, VCTOLQ, VCTOLV, DVLIM, NDVFCT
@! ADJUST, ADJTHR, ACCTAP, TAPLIM, SWVBND, MXTPSS, MXSWIM
@! TYSL, ITMXTY, ACCTY, TOLTY
@! SOLVER, METHOD, ACTAPS, AREAIN, PHSHFT, DCTAPS, SWSHNT, FLATST, VARLIM, NONDIV
@! RATING, IRATE, 'NAME', 'DESC'
@! RATING2, IRATE2, 'NAME2', 'DESC2'
@! RATING3, IRATE3, 'NAME3', 'DESC3'
@! RATING4, IRATE4, 'NAME4', 'DESC4'
@! RATING5, IRATE5, 'NAME5', 'DESC5'
@! RATING6, IRATE6, 'NAME6', 'DESC6'
@! RATING7, IRATE7, 'NAME7', 'DESC7'
@! RATING8, IRATE8, 'NAME8', 'DESC8'
@! RATING9, IRATE9, 'NAME9', 'DESC9'
@! RATING10, IRATE10, 'NAME10', 'DESC10'
@! RATING11, IRATE11, 'NAME11', 'DESC11'
@! RATING12, IRATE12, 'NAME12', 'DESC12'

```

**Field Descriptions**:
- GENRAL: General parameters
- THRSHZ: Thresholds and switches
- PQBRAK: PQ load flow breaker
- BLOWUP: Blowup parameters
- GAUSS: Gauss-Seidel parameters
- ITMX: Maximum iterations
- ACCP: Real component acceleration factor
- ACCQ: Reactive component acceleration factor
- ACCM: Special MSLV acceleration factor
- TOL: Convergence tolerance
- NEWTON: Newton-Raphson parameters
- ITMXN: Maximum iterations
- ACCN: Acceleration factor
- TOLN: Convergence tolerance
- VCTOLQ: Reactive power mismatch convergence tolerance
- VCTOLV: Voltage error convergence tolerance
- DVLIM: Maximum voltage change
- NDVFCT: Non-divergent solution improvement factor
- ADJUST: Automatic adjustment parameters
- ADJTHR: Automatic adjustment threshold
- ACCTAP: Tap movement deceleration factor
- TAPLIM: Maximum tap change per adjustment
- SWVBND: Voltage controlling band mode switched shunts adjustment percentage
- MXTPSS: Maximum number of tap and shunt adjustments
- MXSWIM: Maximum number of induction machine state changes
- TYSL: Tysl parameters
- ITMXTY: Maximum iterations
- ACCTY: Acceleration factor
- TOLTY: Convergence tolerance
- SOLVER: Solver parameters
- METHOD: Solution method
- ACTAPS: AC tap adjustment code
- AREAIN: Area interchange adjustment code
- PHSHFT: Phase shift adjustment code
- DCTAPS: DC tap adjustment code
- SWSHNT: Switched shunt adjustment code
- FLATST: Flat start code
- VARLIM: Reactive power limit application code
- NONDIV: Non-divergent solution code
- RATING: Rating parameters
- IRATE: Rating number
- NAME: Rating name
- DESC: Rating description

### Section 2: Bus Data
**Field Header Format**:
```
@!I,'NAME',BASKV,IDE,AREA,ZONE,OWNER,VM,VA,NVHI,NVLO,EVHI,EVLO
```

- I: Bus number (1-999997)
- NAME: Bus name (up to 12 characters, in quotes if contains blanks/special chars)
- BASKV: Base voltage (kV)
- IDE: Bus type code (1=load, 2=generator, 3=swing, 4=disconnected)
- AREA: Control area number (1-9999)
- ZONE: Zone number (1-9999)  
- OWNER: Owner number (1-9999)
- VM: Bus voltage magnitude (pu)
- VA: Bus voltage phase angle (degrees)
- NVHI: Normal voltage magnitude high limit (pu)
- NVLO: Normal voltage magnitude low limit (pu)
- EVHI: Emergency voltage magnitude high limit (pu)
- EVLO: Emergency voltage magnitude low limit (pu)

### Section 3: Load Data
**Field Header Format**:
```
@!I,'ID',STATUS,AREA,ZONE,PL,QL,IP,IQ,YP,YQ,OWNER,SCALE,INTRPT,DGENP,DGENQ,DGENM,LOADTYPE
```

- I, ID, STATUS, AREA, ZONE, PL, QL, IP, IQ, YP, YQ, OWNER, SCALE, INTRPT, DGENP, DGENQ, DGENM, LOADTYPE
- I: Bus number
- ID: Load identifier
- STATUS: Load status
- AREA, ZONE: Area and zone numbers
- PL, QL: Active and reactive power
- IP, IQ: Current load
- YP, YQ: Admittance load
- OWNER: Owner number
- SCALE: Load scale factor
- INTRPT: Interruptible load flag
- DGENP, DGENQ, DGENM: Distributed Generation fields
- LOADTYPE: Load type descriptor

### Section 4: Fixed Bus Shunt Data
**Field Header Format**:
```
@!I,'ID',STATUS,GL,BL
```

- I, ID, STATUS, GL, BL
- I: Bus number
- ID: Shunt identifier
- STATUS: Shunt status
- GL, BL: Conductance and susceptance

### Section 5: Generator Data
**Field Header Format**:
```
@!I,'ID',PG,QG,QT,QB,VS,IREG,NREG,MBASE,ZR,ZX,RT,XT,GTAP,STAT,RMPCT,PT,PB,BASLOD,O1,F1,O2,F2,O3,F3,O4,F4,WMOD,WPF
```

- I: Bus number
- ID: Generator identifier (1-2 uppercase alphanumeric characters)
- PG: Generator active power output (MW)
- QG: Generator reactive power output (Mvar)
- QT: Maximum generator reactive power output (Mvar)
- QB: Minimum generator reactive power output (Mvar)
- VS: Regulated voltage setpoint (pu)
- IREG: Regulated bus number (0=self-regulation)
- NREG: Node number of regulated bus
- MBASE: Total MVA base of units (MVA)
- ZR, ZX: Complex machine impedance (pu on MBASE base)
- RT, XT: Step-up transformer impedance (pu on MBASE base)
- GTAP: Step-up transformer off-nominal turns ratio (pu)
- STAT: Machine status (1=in-service, 0=out-of-service)
- RMPCT: Percent Mvar contribution for voltage control
- PT: Maximum generator active power output (MW)
- PB: Minimum generator active power output (MW)
- BASLOD: Base loaded flag (0=normal, 1=down only, 2=neither, 3=up only)
- O1-O4: Owner numbers (1-9999)
- F1-F4: Ownership fractions
- WMOD: Machine control mode (0=conventional, 1-4=renewable/infeed types)
- WPF: Power factor for reactive power limits calculation

### Section 6: Non-Transformer Branch Data
**Field Header Format**:
```
@!I,J,'CKT',R,X,B,'NAME',RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,GI,BI,GJ,BJ,ST,MET,LEN,O1,F1,O2,F2,O3,F3,O4,F4
```

- I: Branch from bus number
- J: Branch to bus number
- CKT: Circuit identifier (1-2 uppercase alphanumeric characters)
- R: Branch resistance (pu)
- X: Branch reactance (pu, non-zero required)
- B: Total branch charging susceptance (pu)
- NAME: Branch name (up to 40 characters, must be unique)
- RATE1-RATE12: Line ratings (MVA or current as MVA)
- GI, BI: Complex admittance of line shunt at bus I end (pu)
- GJ, BJ: Complex admittance of line shunt at bus J end (pu)
- ST: Branch status (1=in-service, 0=out-of-service)
- MET: Metered end flag (≤1=bus I, ≥2=bus J)
- LEN: Line length (user-selected units)
- O1-O4: Owner numbers (1-9999)
- F1-F4: Ownership fractions

### Section 7: System Switching Device Data
**Field Header Format**:
```
@!I,J,'CKT',X,RATE1,RATE2,RATE3,RATE4,RATE5,RATE6,RATE7,RATE8,RATE9,RATE10,RATE11,RATE12,STATUS,NSTATUS,METERED,STYPE,'NAME'
```

- I: From bus number
- J: To bus number
- CKT: Two-character uppercase alphanumeric switching device identifier
- X: Branch reactance (pu, must be less than ZTHRES)
- RATE1-RATE12: Switching device ratings (MVA or current as MVA)
- STATUS: 1 for close, 0 for open
- NSTATUS: Normal service status (1=normally open, 0=normally close)
- METERED: Metered end
- STYPE: Switching device type (1=Generic connector, 2=Circuit breaker, 3=Disconnect switch)
- NAME: System switching device name

### Section 8: Transformer Data
**Format**: 4 records for 2-winding, 5 records for 3-winding transformers

**Field Header Format**:
```
@!I,J,K,'CKT',CW,CZ,CM,MAG1,MAG2,NMETR,'NAME',STAT,O1,F1,O2,F2,O3,F3,O4,F4,'VECGRP',ZCOD
@!R1-2,X1-2,SBASE1-2,R2-3,X2-3,SBASE2-3,R3-1,X3-1,SBASE3-1,VMSTAR,ANSTAR
@!WINDV1,NOMV1,ANG1,RATE11,RATE12,RATE13,RATE14,RATE15,RATE16,RATE17,RATE18,RATE19,RATE110,RATE111,RATE112,COD1,CONT1,NODE1,RMA1,RMI1,VMA1,VMI1,NTP1,TAB1,CR1,CX1,CNXA1
@!WINDV2,NOMV2,ANG2,RATE21,RATE22,RATE23,RATE24,RATE25,RATE26,RATE27,RATE28,RATE29,RATE210,RATE211,RATE212,COD2,CONT2,NODE2,RMA2,RMI2,VMA2,VMI2,NTP2,TAB2,CR2,CX2,CNXA2
@!WINDV3,NOMV3,ANG3,RATE31,RATE32,RATE33,RATE34,RATE35,RATE36,RATE37,RATE38,RATE39,RATE310,RATE311,RATE312,COD3,CONT3,NODE3,RMA3,RMI3,VMA3,VMI3,NTP3,TAB3,CR3,CX3,CNXA3
```

**Record 1**: I, J, K, CKT, CW, CZ, CM, MAG1, MAG2, NMETR, NAME, STAT, O1, F1, O2, F2, O3, F3, O4, F4, VECGRP, ZCOD
- I: Winding 1 bus number
- J: Winding 2 bus number  
- K: Winding 3 bus number (0 for 2-winding)
- CKT: Circuit identifier
- CW: Winding data I/O code
- CZ: Impedance data I/O code
- CM: Magnetizing admittance I/O code
- MAG1: Magnetizing conductance (pu on system base)
- MAG2: Magnetizing susceptance (pu on system base)
- NMETR: Non-metered end code
- NAME: Transformer name
- STAT: Transformer status
- O1-O4: Owner numbers
- F1-F4: Ownership fractions
- VECGRP: Vector group
- ZCOD: Impedance correction method code (3-winding only)

**Record 2**: R1-2, X1-2, SBASE1-2, R2-3, X2-3, SBASE2-3, R3-1, X3-1, SBASE3-1, VMSTAR, ANSTAR
- R1-2, X1-2: Winding 1-2 resistance and reactance (pu)
- SBASE1-2: Winding 1-2 base MVA
- R2-3, X2-3, SBASE2-3: Winding 2-3 impedance (3-winding only)
- R3-1, X3-1, SBASE3-1: Winding 3-1 impedance (3-winding only)
- VMSTAR: Winding 1 voltage magnitude star point (pu)
- ANSTAR: Winding 1 voltage angle star point (degrees)

**Records 3-5**: Winding control data
- WINDVn: Winding voltage ratio
- NOMVn: Nominal voltage (kV)
- ANGn: Winding angle (degrees)
- RATEn1-RATEn12: Winding ratings 1-12
- CODn: Control mode
- CONTn: Controlled bus number
- NODEn: Controlled node number
- RMAn, RMIn: Tap ratio limits
- VMAn, VMIn: Voltage limits
- NTPn: Number of tap positions
- TABn: Impedance correction table
- CRn, CXn: Load drop compensation impedance
- CNXAn: Connection reactance

### Section 9: Area Interchange Data
**Field Header Format**:
```
@!I,ISW,PDES,PTOL,'ARNAME'
```

- I: Area number (1-9999)
- ISW: Area slack bus number (must be Type 2 generator bus in area)
- PDES: Desired net interchange leaving area (MW)
- PTOL: Interchange tolerance bandwidth (MW)
- ARNAME: Area name (up to 12 characters, in quotes if contains blanks)

### Section 10: Two-Terminal DC Transmission Line Data
**Format**: 3 data records per DC line

**Field Header Format**:
```
@!'NAME',MDC,RDC,SETVL,VSCHD,VCMOD,RCOMP,DELTI,METER,DCVMIN,CCCITMX,CCCACC
@!IPR,NBR,ANMXR,ANMNR,RCR,XCR,EBASR,TRR,TAPR,TMXR,TMNR,STPR,ICR,NDR,IFR,ITR,IDR,XCAPR
@!IPI,NBI,ANMXI,ANMNI,RCI,XCI,EBASI,TRI,TAPI,TMXI,TMNI,STPI,ICI,NDI,IFI,ITI,IDI,XCAPI
```

**Record 1**: NAME, MDC, RDC, SETVL, VSCHD, VCMOD, RCOMP, DELTI, METER, DCVMIN, CCCITMX, CCCACC
- NAME: DC line identifier (up to 12 characters, in quotes)
- MDC: Control mode (0=blocked, 1=power, 2=current)
- RDC: DC line resistance (ohms)
- SETVL: Current (amps) or power (MW) demand
- VSCHD: Scheduled compounded dc voltage (kV)
- VCMOD: Mode switch dc voltage (kV)
- RCOMP: Compounding resistance (ohms)
- DELTI: Margin (per unit of desired dc power/current)
- METER: Metered end code (R=rectifier, I=inverter)
- DCVMIN: Minimum compounded dc voltage (kV)
- CCCITMX: Iteration limit for capacitor commutated solution
- CCCACC: Acceleration factor for capacitor commutated solution

**Record 2**: Rectifier data - IPR, NBR, ANMXR, ANMNR, RCR, XCR, EBASR, TRR, TAPR, TMXR, TMNR, STPR, ICR, NDR, IFR, ITR, IDR, XCAPR
- IPR: Rectifier converter bus number
- NBR: Number of bridges in series (rectifier)
- ANMXR: Nominal maximum rectifier firing angle (degrees)
- ANMNR: Minimum steady-state rectifier firing angle (degrees)
- RCR: Rectifier commutating transformer resistance per bridge (ohms)
- XCR: Rectifier commutating transformer reactance per bridge (ohms)
- EBASR: Rectifier primary base ac voltage (kV)
- TRR: Rectifier transformer ratio (secondary/primary)
- TAPR: Rectifier transformer tap setting
- TMXR: Maximum rectifier tap setting
- TMNR: Minimum rectifier tap setting
- STPR: Rectifier tap step (must be positive)
- ICR: Rectifier commutating bus number
- NDR: Node number of bus ICR
- IFR: Winding 1 side from bus number (two-winding transformer)
- ITR: Winding 2 side to bus number (two-winding transformer)
- IDR: Circuit identifier for transformer
- XCAPR: Commutating capacitor reactance magnitude per bridge (ohms)

**Record 3**: Inverter data - IPI, NBI, ANMXI, ANMNI, RCI, XCI, EBASI, TRI, TAPI, TMXI, TMNI, STPI, ICI, NDI, IFI, ITI, IDI, XCAPI
- IPI: Inverter converter bus number
- NBI: Number of bridges in series (inverter)
- ANMXI: Nominal maximum inverter firing angle (degrees)
- ANMNI: Minimum steady-state inverter firing angle (degrees)
- RCI: Inverter commutating transformer resistance per bridge (ohms)
- XCI: Inverter commutating transformer reactance per bridge (ohms)
- EBASI: Inverter primary base ac voltage (kV)
- TRI: Inverter transformer ratio (secondary/primary)
- TAPI: Inverter transformer tap setting
- TMXI: Maximum inverter tap setting
- TMNI: Minimum inverter tap setting
- STPI: Inverter tap step (must be positive)
- ICI: Inverter commutating bus number
- NDI: Node number of bus ICI
- IFI: Winding 1 side from bus number (two-winding transformer)
- ITI: Winding 2 side to bus number (two-winding transformer)
- IDI: Circuit identifier for transformer
- XCAPI: Commutating capacitor reactance magnitude per bridge (ohms)



### Section 11: VSC DC Transmission Line Data
**Format**: 3 data records per VSC DC line

**Field Header Format**:
```
@!'NAME',MDC,RDC,O1,F1,O2,F2,O3,F3,O4,F4
@!IBUS1,TYPE1,MODE1,DCSET1,ACSET1,ALOSS1,BLOSS1,MINLOSS1,SMAX1,IMAX1,PWF1,MAXQ1,MINQ1,VSREG1,RMPCT1,NREG1
@!IBUS2,TYPE2,MODE2,DCSET2,ACSET2,ALOSS2,BLOSS2,MINLOSS2,SMAX2,IMAX2,PWF2,MAXQ2,MINQ2,VSREG2,RMPCT2,NREG2
```

**Record 1**: NAME, MDC, RDC, O1, F1, O2, F2, O3, F3, O4, F4
- NAME: VSC DC line name (up to 12 characters, in quotes)
- MDC: Control mode for VSC DC line
- RDC: DC line resistance (ohms)
- O1-O4: Owner numbers (1-9999)
- F1-F4: Ownership fractions

**Records 2-3**: Converter data for each end
- IBUSn: Converter AC bus number
- TYPEn: Converter type
- MODEn: Control mode
- DCSETn: DC voltage or power setpoint
- ACSETn: AC voltage setpoint
- ALOSSn, BLOSSn: Converter loss coefficients
- MINLOSSn: Minimum converter loss
- SMAXn: Maximum apparent power
- IMAXn: Maximum current
- PWFn: Power weighting factor
- MAXQn, MINQn: Reactive power limits
- VSREGn: Voltage regulation bus
- RMPCTn: Participation factor
- NREGn: Regulated node number

### Section 12: Transformer Impedance Correction Tables
**Field Header Format**:
```
@!I,T1,F1,T2,F2,T3,F3,T4,F4,T5,F5,T6,F6,T7,F7,T8,F8,T9,F9,T10,F10,T11,F11
```

- I, T1, F1, T2, F2, T3, F3, T4, F4, T5, F5, T6, F6, T7, F7, T8, F8, T9, F9, T10, F10, T11, F11
- I: Table number
- T1-F11: Temperature and fraction pairs

### Section 12: Multi-Terminal DC Transmission Line Data
**Field Header Format**:
```
@!I,NCONV,NDCBS,MDC,VCONV,VCMOD,VCONVN
```

- I, NCONV, NDCBS, MDC, VCONV, VCMOD, VCONVN
- I: Bus number
- NCONV: Number of converters
- NDCBS: Number of DC buses
- MDC: Control mode
- VCONV: Voltage setpoint
- VCMOD: Voltage control mode
- VCONVN: Nominal voltage

### Section 13: Multi-Section Line Group Data
**Field Header Format**:
```
@!I,J,'ID',DUM1,DUM2,DUM3,DUM4,DUM5,DUM6,DUM7,DUM8,DUM9
```

- I, J, ID, DUM1, DUM2, DUM3, DUM4, DUM5, DUM6, DUM7, DUM8, DUM9
- I, J: From and to bus numbers
- ID: Group identifier
- DUM1-DUM9: Reserved fields

### Section 14: Zone Data
**Field Header Format**:
```
@!I,'ZONAME'
```

- I, ZONAME
- I: Zone number
- ZONAME: Zone name

### Section 15: Interarea Transfer Data
**Field Header Format**:
```
@!ARFROM,ARTO,'TRID',PTRAN
```

- ARFROM, ARTO, TRID, PTRAN
- ARFROM: From area number
- ARTO: To area number
- TRID: Transfer identifier
- PTRAN: Transfer amount

### Section 16: Owner Data
**Field Header Format**:
```
@!I,'OWNAME'
```

- I, OWNAME
- I: Owner number
- OWNAME: Owner name

### Section 17: FACTS Device Data
**Field Header Format**:
```
@!'NAME',I,J,MODE,PDES,QDES,VSET,SHMX,TRMX,VTMN,VTMX,VSMX,IMX,LINX,RMPCT,OWNER,SET1,SET2,VSREF,FCREG,NREG,'MNAME'
```

- NAME: FACTS device identifier (up to 12 characters, in quotes)
- I: Sending end bus number
- J: Terminal end bus number (0 for STATCON)
- MODE: Control mode (0=out-of-service, 1=series+shunt operating, 2=series bypassed, 3=constant impedance, 4=constant voltage, 5=IPFC master P&Q, 6=IPFC slave P&Q, 7=IPFC master voltage, 8=IPFC slave voltage)
- PDES: Desired active power flow (MW)
- QDES: Desired reactive power flow (Mvar)
- VSET: Voltage setpoint at sending end (pu)
- SHMX: Maximum shunt current (MVA at unity voltage)
- TRMX: Maximum bridge active power transfer (MW)
- VTMN, VTMX: Terminal end voltage limits (pu)
- VSMX: Maximum series voltage (pu)
- IMX: Maximum series current (MVA at unity voltage, 0=no limit)
- LINX: Reactance of dummy series element (pu)
- RMPCT: Percent Mvar contribution for voltage control
- OWNER: Owner number (1-9999)
- SET1, SET2: Control setpoints (depends on MODE)
- VSREF: Series voltage reference code (0=sending end voltage, 1=series current)
- FCREG: Bus for voltage regulation by shunt element
- NREG: Node number of regulated bus
- MNAME: Master FACTS device name for IPFC slave devices

### Section 18: Switched Shunt Data
**Field Header Format**:
```
@!I,'ID',MODSW,ADJM,STAT,VSWHI,VSWLO,SWREG,NREG,RMPCT,'RMIDNT',BINIT,S1,N1,B1,S2,N2,B2,S3,N3,B3,S4,N4,B4,S5,N5,B5,S6,N6,B6,S7,N7,B7,S8,N8,B8
```

- I: Bus number
- ID: Switched shunt identifier (1-2 uppercase alphanumeric characters)
- MODSW: Control mode (0=locked, 1=discrete voltage control, 2=continuous voltage control, 3=reactive power control, 4=VSC converter control, 5=switched shunt control, 6=FACTS shunt control)
- ADJM: Adjustment method (0=input order, 1=optimal order)
- STAT: Initial status (1=in-service, 0=out-of-service)
- VSWHI, VSWLO: Controlled voltage or reactive power limits (pu)
- SWREG: Controlled bus number
- NREG: Controlled node number
- RMPCT: Percent Mvar contribution for voltage control
- RMIDNT: Remote identifier (VSC name, switched shunt ID, or FACTS name)
- BINIT: Initial switched shunt admittance (Mvar at unity voltage)
- S1-S8: Initial status for blocks 1-8 (1=in-service, 0=out-of-service)
- N1-N8: Number of steps for blocks 1-8
- B1-B8: Admittance increment for blocks 1-8 (Mvar at unity voltage)

### Section 19: GNE Device Data
**Format**: Variable number of records per device (1 main + continuation records as needed)

**Field Header Format**:
```
@!'NAME','MODEL',NTERM,BUS1,BUS2,NREAL,NINTG,NCHAR,STATUS,OWNER,NMET
@!REAL1,REAL2,REAL3,REAL4,REAL5,REAL6,REAL7,REAL8,REAL9,REAL10
@!INTG1,INTG2,INTG3,INTG4,INTG5,INTG6,INTG7,INTG8,INTG9,INTG10
@!CHAR1,CHAR2,CHAR3,CHAR4,CHAR5,CHAR6,CHAR7,CHAR8,CHAR9,CHAR10
```

**Record 1**: NAME, MODEL, NTERM, BUS1, BUS2, NREAL, NINTG, NCHAR, STATUS, OWNER, NMET
- NAME: GNE device identifier (up to 12 characters, in quotes)
- MODEL: BOSL model name (root name of .mac or .xmac file)
- NTERM: Number of connected buses (1 or 2 for variable admittance, 1 for variable power/current)
- BUS1, BUS2: Bus numbers to which device is connected
- NREAL: Number of floating point data items required by model
- NINTG: Number of integer bus number items required by model
- NCHAR: Number of two-character identifier items required by model
- STATUS: Device status (1=in-service, 0=out-of-service)
- OWNER: Owner number (1-9999)
- NMET: Non-metered end bus number (for NTERM > 1)

**Record 2** (if NREAL > 0): REAL1-REAL10 (floating point data, 10 per line)

**Record 3** (if NINTG > 0): INTG1-INTG10 (integer bus numbers, 10 per line)

**Record 4** (if NCHAR > 0): CHAR1-CHAR10 (two-character identifiers, 10 per line)

### Section 20: Induction Machine Data
**Field Header Format**:
```
@!I,'ID',STATUS,SCODE,DCODE,AREA,ZONE,OWNER,TCODE,BCODE,MBASE,RATEKV,PCODE,PSET,H,A,B,D,E,RA,XA,XM,R1,X1,R2,X2,X3,E1,SE1,E2,SE2,IA1,IA2,XAMULT
```

- I: Bus number
- ID: Machine identifier (1-2 uppercase alphanumeric characters)
- STATUS: Machine status (1=in-service, 0=out-of-service)
- SCODE: Machine standard code (1=NEMA, 2=IEC)
- DCODE: Machine design code (0=Custom, 1=NEMA Design A, 2=NEMA Design B/IEC Design N, 3=NEMA Design C/IEC Design H, 4=NEMA Design D, 5=NEMA Design E)
- AREA: Area number (1-9999)
- ZONE: Zone number (1-9999)
- OWNER: Owner number (1-9999)
- TCODE: Mechanical load torque variation type (1=simple power law, 2=WECC model)
- BCODE: Machine base power code (1=mechanical power MW, 2=apparent electrical power MVA)
- MBASE: Machine base power (MW or MVA according to BCODE)
- RATEKV: Machine rated voltage (kV line-to-line, 0=use bus base voltage)
- PCODE: Scheduled power code
- PSET: Scheduled power setpoint
- H: Inertia constant (MW·s/MVA)
- A, B, D, E: Torque coefficients for mechanical load model
- RA: Stator resistance (pu)
- XA: Stator leakage reactance (pu)
- XM: Magnetizing reactance (pu)
- R1: Rotor resistance referred to stator (pu)
- X1: Rotor leakage reactance referred to stator (pu)
- R2, X2: Additional rotor circuit parameters (pu)
- X3: Additional reactance parameter (pu)
- E1, SE1: First saturation point voltage and factor
- E2, SE2: Second saturation point voltage and factor
- IA1, IA2: Acceleration factors
- XAMULT: Reactance multiplier

### Section 21: Substation Data
**Format**: Substation data blocks (variable number of lines per substation)

**Field Header Format**:
```
@!IS,'NAME',LATI,LONG,SRG
```

**Substation Data Record**: IS, NAME, LATI, LONG, SRG

**Field Descriptions**:
- IS: Substation number (1 through 99999)
- NAME: Substation name (up to 40 characters)
- LATI: Substation latitude in degrees (-90.0 to 90.0)
- LONG: Substation longitude in degrees (-180.0 to 180.0)
- SRG: Substation grounding DC resistance in ohms

**Note**: Each substation data record is followed by substation node data, switching device data, and terminal data for that substation.

### Section 22: Substation Node Data
**Format**: Multiple lines (one per node in the substation)

**Field Header Format**:
```
@!IS,IN,'NAME',IBUS,STAT,VM,VA
```

**Node Data Record**: IS, IN, NAME, IBUS, STAT, VM, VA

**Field Descriptions**:
- IS: Substation number
- IN: Node number (1 through 999)
- NAME: Node name (up to 40 characters)
- IBUS: Electrical bus number in bus-branch model
- STAT: Node status (0=out-of-service, 1=in-service)
- VM: Node voltage magnitude (pu)
- VA: Node voltage phase angle (degrees)

### Section 23: Substation Switching Device Data
**Format**: Multiple lines (one per switching device in the substation)

**Field Header Format**:
```
@!IS,IN,JN,'SWDID','NAME',TYPE,STAT,NSTAT,XPU,RATE1,RATE2,RATE3
```

**Switching Device Record**: IS, IN, JN, SWDID, NAME, TYPE, STAT, NSTAT, XPU, RATE1, RATE2, RATE3

**Field Descriptions**:
- IS: Substation number
- IN: From node number (must be in station IS)
- JN: To node number (must be in station IS)
- SWDID: Two-character switching device identifier
- NAME: Switching device name (up to 40 characters)
- TYPE: Switching device type (1=Generic connector, 2=Circuit breaker, 3=Disconnect switch)
- STAT: Device status (0=out-of-service, 1=in-service)
- NSTAT: Normal status (0=out-of-service, 1=in-service)
- XPU: Switching device reactance (pu)
- RATE1, RATE2, RATE3: MVA ratings

### Section 24: Substation Terminal Data
**Format**: Multiple lines (one per terminal connection in the substation)

**Field Header Format**:
```
@!IS,IN,'TYPE','EQID',IBUS,JBUS,KBUS
```

**Terminal Data Record**: IS, IN, TYPE, EQID, IBUS, JBUS, KBUS

**Field Descriptions**:
- IS: Substation number
- IN: Node number connected to IBUS terminal of device
- TYPE: Equipment type code (L=load, F=fixed shunt, M=machine, B/2=branch, 3=three-winding, S=switched shunt, I=ind mach, D=dc2term, V=vscdc, A=facts)
- EQID: Equipment ID or NAME of terminal device
- IBUS: First bus number of terminal device
- JBUS: Second bus number of terminal device (if applicable)
- KBUS: Third bus number of terminal device (if applicable)

### Section 25: Q Record
**Format**: Three data records
- **Line 1**: Q, 'BLANK', TITLE(1)
- **Line 2**: TITLE(2)
- **Line 3**: TITLE(3)

**Field Header Format**:
```
@!Q,'BLANK','TITLE1'
@!'TITLE2'
@!'TITLE3'
```

**Field Descriptions**:
- Q: Record identifier
- TITLE(1-3): Title lines (up to 60 characters each)

### Section 26: End of Data
**Field Header Format**:
```
@!Q_END
```

- 0 / END OF DATA
- 0: Record identifier
- /: Comment delimiter
- END OF DATA: End of file marker

## Version 35 Extensions

### RAWX Format (Version 35 Only)
Version 35 introduces the Extensible Power Flow Data File (RAWX) format, which provides:

**JSON-Based Structure**:
- Parameter sets for case-wide data
- Data tables for equipment records
- Custom field definitions
- Partial dataset support
- CSV import/export capabilities

**Key Features**:
- JSON data types for structured data
- Field list customization
- Custom table definitions
- Importing/exporting from CSV files
- Support for partial datasets (read and change operations)

**Additional Parameters in Version 35**:
- **GENERAL Record**: Added MAXISOLLVLS, CAMAXREPTSLN, CHKDUPCNTLBL parameters
- **Enhanced Substation Data**: More comprehensive substation modeling capabilities
- **RAWX Field Keys**: JSON-based field mapping for all data types

**RAWX Parameter Set Example**:
```json
{
    "caseid": {
        "fields": ["ic", "sbase", "rev", "xfrrat", "nxfrat", "basfrq", "title1", "title2"],
        "data": [0, 100.00, 35, 0, 1, 60.00, "", ""]
    }
}
```

**RAWX Data Table Example**:
```json
{
    "bus": {
        "fields": ["i", "name", "baskv", "ide", "area", "zone", "owner", "vm", "va"],
        "data": [
            [1, "BUS1", 138.0, 3, 1, 1, 1, 1.0, 0.0],
            [2, "BUS2", 138.0, 1, 1, 1, 1, 1.0, 0.0]
        ]
    }
}
```

## Version Comparison Summary

| Feature | Version 33 | Version 34 | Version 35 |
|---------|------------|------------|------------|
| Case ID Format | 3 data records | 3 data records | 8 data items on 3 lines |
| RAWX Support | ❌ | ❌ | ✅ |
| Additional GENERAL params | ❌ | ❌ | ✅ |
| Enhanced Substation Data | Basic | Basic | Enhanced |
| JSON Export/Import | ❌ | ❌ | ✅ |
| CSV Import/Export | ❌ | ❌ | ✅ |
| Custom Field Definitions | ❌ | ❌ | ✅ |
| Total Sections | 22 | 22 | 26 |

## New Fields Added by Version

### Version 34 Additions

**Load Data (Section 3)**:
- `DGENP`: Distributed Generation active power component (MW)
- `DGENQ`: Distributed Generation reactive power component (MVAR)
- `DGENM`: Distributed Generation operation mode (0=OFF, 1=ON)

**Transformer Data (Section 7)**:
- `VECGRP`: Vector group identifier (alphanumeric, up to 12 characters)
- `ZCOD`: Impedance correction method code (0=default, 1=bus-to-bus impedance)

### Version 35 Additions

**Load Data (Section 3)**:
- `LOADTYPE`: Load type descriptor (alphanumeric, up to 12 characters)

**Generator Data (Section 5)**:
- `BASLOD`: Baseload flag (0=not baseload, 1=baseload)

**Non-Transformer Branch Data (Section 6)**:
- `NAME`: Branch name identifier (alphanumeric)
- `LEN`: Line length (float)

**System-Wide Data (GENERAL Record)**:
- `MAXISOLLVLS`: Maximum levels to move outward when isolating elements by breaker/switch actions
- `CAMAXREPTSLN`: Maximum number of times to try and solve a contingency/remedial action
- `CHKDUPCNTLBL`: Check for duplicate contingency labels when creating DFAX file and running contingency analysis

**Substation Data (New Sections 21-24 in Version 35)**:
- **Section 21: Substation Data (sub)**: IS, NAME, LATI, LONG, SRG
- **Section 22: Substation Node Data (subnode)**: IS, IN, NAME, IBUS, STAT, VM, VA
- **Section 23: Substation Switching Device Data (subswd)**: IS, IN, JN, SWDID, NAME, TYPE, STAT, NSTAT, XPU, RATE1, RATE2, RATE3
- **Section 24: Substation Terminal Data (subterm)**: IS, IN, TYPE, EQID, IBUS, JBUS, KBUS

**RAWX Format Support**:
- JSON-based parameter sets for all data types
- Custom field definitions and data tables
- CSV import/export capabilities
- Partial dataset support for read and change operations

### Field Summary by Device Type

| Device Type | Version 33 | Version 34 Additions | Version 35 Additions |
|-------------|------------|---------------------|---------------------|
| **Load** | 14 fields | +3 (DGENP, DGENQ, DGENM) | +1 (LOADTYPE) |
| **Generator** | 22 fields | 0 | +1 (BASLOD) |
| **Non-Transformer Branch** | 18 fields | 0 | +2 (NAME, LEN) |
| **Transformer** | 28 fields | +2 (VECGRP, ZCOD) | 0 |
| **System-Wide (GENERAL)** | 3 fields | 0 | +3 (MAXISOLLVLS, CAMAXREPTSLN, CHKDUPCNTLBL) |
| **Substation Data** | ❌ | Basic support | +4 new sections with comprehensive modeling |

**Total New Fields**:
- **Version 34**: 5 new fields across 2 device types
- **Version 35**: 7 new fields across 4 device types + 4 new substation data sections (Sections 21-24)
- **RAWX Format**: Complete JSON-based alternative format with all fields 