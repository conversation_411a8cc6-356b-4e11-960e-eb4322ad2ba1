official_to_human = {'tol': 'tol', 'options': 'options', 'dfxfile': 'dfxfile', 'accfile': 'accfile', 'thrfile': 'thrfile', 'option': 'option', 'nfiles': 'nfiles', 'acfiles': 'acfiles', 'values': 'values', 'status': 'status', 'intval': 'intval', 'realval': 'realval', 'rfile': 'rfile', 'labels': 'labels', 'islct': 'islct', 'filarg': 'filarg', 'ival': 'ival', 'sid': 'sid', 'all': 'all', 'opt': 'opt', 'ianew': 'ianew', 'rval': 'rval', 'dotest': 'dotest', 'tolval': 'tolval', 'busrng': 'busrng', 'oldnam': 'oldnam', 'tfile': 'tfile', 'out': 'out', 'ofile': 'ofile', 'buslo': 'buslo', 'bushi': 'bushi', 'sfile': 'sfile', 'thrsh': 'thrsh', 'confile': 'confile', 'apiopt': 'apiopt', 'cfile': 'cfile', 'v': 'v', 'loadin': 'loadin', 'tblname': 'tblname', 'brnch': 'brnch', 'ckt': 'ckt', 'inlfile': 'inlfile', 'subfile': 'subfile', 'monfile': 'monfile', 'bus': 'bus', 'idvfil': 'idvfil', 'dval': 'dval', 'tblnam': 'tblnam', 'filetype': 'filetype', 'filename': 'filename', 'tblid': 'tblid', 'name': 'name', 'bskv': 'bskv', 'gfile': 'gfile', 'namarg': 'namarg', 'vrev': 'vrev', 'pathzip': 'pathzip', 'isvfile': 'isvfile', 'inout': 'inout', 'motopt': 'motopt', 'genopt': 'genopt', 'event': 'event', 'atcfile': 'atcfile', 'ecdfile': 'ecdfile', 'ifile': 'ifile', 'iocode': 'iocode', 'ibus': 'ibus', 'jbus': 'jbus', 'device': 'device', 'label': 'label', 'aozopt': 'aozopt', 'frmbus': 'frmbus', 'tobus': 'tobus', 'fraction': 'fraction', 'newnum': 'newnum', 'newnam': 'newnam', 'newkv': 'newkv', 'irat': 'irat', 'rload': 'rload', 'fmax': 'fmax', 'jbus1': 'jbus1', 'jbus2': 'jbus2', 'newtobus2': 'newtobus2', 'newckt': 'newckt', 'newtobus': 'newtobus', 'id': 'id', 'newbus': 'newbus', 'newid': 'newid', 'inpfile': 'inpfile', 'trnfile': 'trnfile', 'outfile': 'outfile', 'basemva': 'basemva', 'titl1': 'titl1', 'titl2': 'titl2', 'basefreq': 'basefreq', 'buses': 'buses', 'ties': 'ties', 'ionew': 'ionew', 'pathname': 'pathname', 'resfile': 'resfile', 'accfiles': 'accfiles', 'delete': 'delete', 'ssfile': 'ssfile', 'mnfile': 'mnfile', 'linflg': 'linflg', 'rating': 'rating', 'trnflg': 'trnflg', 'brkflg': 'brkflg', 'numnam': 'numnam', 'vernum': 'vernum', 'file': 'file', 'prbfile': 'prbfile', 'stsfile': 'stsfile', 'ftype': 'ftype', 'realar': 'realar', 'scalval': 'scalval', 'flwopt': 'flwopt', 'brnckt': 'brnckt', 'mode': 'mode', 'direction': 'direction', 'prtfile': 'prtfile', 'carg': 'carg', 'init': 'init', 'vlo': 'vlo', 'vhi': 'vhi', 'gzoptn': 'gzoptn', 'iznew': 'iznew', 'iarea': 'iarea', 'arname': 'arname', 'intgar': 'intgar', 'ratings': 'ratings', 'namear': 'namear', 'inode': 'inode', 'line1': 'line1', 'line2': 'line2', 'realari': 'realari', 'mname': 'mname', 'fd': 'fd', 'i': 'i', 'points': 'points', 'cplxar': 'cplxar', 'lodtyp': 'lodtyp', 'lnglns': 'lnglns', 'pgenar': 'pgenar', 'qmaxar': 'qmaxar', 'qminar': 'qminar', 'dc': 'dc', 'dcnew': 'dcnew', 'idnew': 'idnew', 'frmarea': 'frmarea', 'toarea': 'toarea', 'fdnew': 'fdnew', 'namnew': 'namnew', 'idc': 'idc', 'dcname': 'dcname', 'intgari': 'intgari', 'jdc': 'jdc', 'dcckt': 'dcckt', 'iowner': 'iowner', 'owname': 'owname', 'kbus': 'kbus', 'ibus1': 'ibus1', 'ckt1': 'ckt1', 'ibus2': 'ibus2', 'ckt2': 'ckt2', 'irate': 'irate', 'descr': 'descr', 'rmidnt': 'rmidnt', 'dscrp': 'dscrp', 'applyf': 'applyf', 'arfrom': 'arfrom', 'arto': 'arto', 'trid': 'trid', 'meter': 'meter', 'cnvflg': 'cnvflg', 'ic': 'ic', 'izone': 'izone', 'zoname': 'zoname', 'iarg': 'iarg', 'namearg': 'namearg', 'cktarg': 'cktarg', 'istarg': 'istarg', 'station': 'station', 'subtyp': 'subtyp', 'intopt': 'intopt', 'node': 'node', 'nodes': 'nodes', 'dsnode': 'dsnode', 'newnode': 'newnode', 'newstation': 'newstation', 'jnode': 'jnode', 'cktnew': 'cktnew', 'knode': 'knode', 'rbrxar': 'rbrxar', 'iary': 'iary', 'rary': 'rary', 'opfile': 'opfile', 'rcfile': 'rcfile', 'osid': 'osid', 'eibus': 'eibus', 'ejbus': 'ejbus', 'cktid': 'cktid', 'useval': 'useval', 'tbl': 'tbl', 'shntid': 'shntid', 'enod': 'enod', 'bflwid': 'bflwid', 'autoadd': 'autoadd', 'labl': 'labl', 'nprs': 'nprs', 'xy': 'xy', 'cost': 'cost', 'lincst': 'lincst', 'quacst': 'quacst', 'expcst': 'expcst', 'expon': 'expon', 'genid': 'genid', 'apdtbl': 'apdtbl', 'dsptch': 'dsptch', 'rcstat': 'rcstat', 'ifid': 'ifid', 'ekbus': 'ekbus', 'iqid': 'iqid', 'itbl': 'itbl', 'coeff': 'coeff', 'flwid': 'flwid', 'swshid': 'swshid', 'enode': 'enode', 'loadid': 'loadid', 'rsvid': 'rsvid', 'inarg': 'inarg', 'busary': 'busary', 'vltary': 'vltary', 'cptary': 'cptary', 'relfile': 'relfile', 'fcdfile': 'fcdfile', 'scfile': 'scfile', 'lvlbak': 'lvlbak', 'flttim': 'flttim', 'bfile': 'bfile', 'ffile': 'ffile', 'brktim': 'brktim', 'iecfile': 'iecfile', 'thresh': 'thresh', 'units': 'units', 'warg': 'warg', 'lbus': 'lbus', 'typarg': 'typarg', 'harmrsltfile': 'harmrsltfile', 'charar': 'charar', 'npts': 'npts', 'har': 'har', 'iar': 'iar', 'anar': 'anar', 'hptold': 'hptold', 'hpt': 'hpt', 'ipt': 'ipt', 'anpt': 'anpt', 'sendbus': 'sendbus', 'termbus': 'termbus', 'rar': 'rar', 'lar': 'lar', 'car': 'car', 'rpt': 'rpt', 'lpt': 'lpt', 'cpt': 'cpt', 'var': 'var', 'vpt': 'vpt', 'intgoptns': 'intgoptns', 'realoptns': 'realoptns', 'efldfile': 'efldfile', 'kfactors': 'kfactors', 'ftyp': 'ftyp', 'fnam': 'fnam', 'gicearg': 'gicearg', 'gicnarg': 'gicnarg', 'xmerlbl': 'xmerlbl', 'gictfarg': 'gictfarg', 'ievt': 'ievt', 'desc': 'desc', 'layers': 'layers', 'rho': 'rho', 'thickness': 'thickness', 'subnum': 'subnum', 'earthmdl': 'earthmdl', 'vecgrp': 'vecgrp', 'libraryname': 'libraryname', 'number': 'number', 'newval': 'newval', 'ntype': 'ntype', 'atype': 'atype', 'first': 'first', 'last': 'last', 'programname': 'programname', 'csvfilename': 'csvfilename', 'startindx': 'startindx', 'dyrefile': 'dyrefile', 'conecfile': 'conecfile', 'conetfile': 'conetfile', 'compilfil': 'compilfil', 'tpause': 'tpause', 'nprt': 'nprt', 'nplt': 'nplt', 'crtplt': 'crtplt', 'vdelta': 'vdelta', 'pinitial': 'pinitial', 'pdelta': 'pdelta', 'ident': 'ident', 'frelvthresh': 'frelvthresh', 'angle': 'angle', 'mbase': 'mbase', 'trip': 'trip', 'power': 'power', 'speed': 'speed', 'lmwthresh': 'lmwthresh', 'lpqthresh': 'lpqthresh', 'lvtthresh': 'lvtthresh', 'exclxfmr': 'exclxfmr', 'switch': 'switch', 'ibusex': 'ibusex', 'vdpchk': 'vdpchk', 'vdpthr': 'vdpthr', 'vdpdur': 'vdpdur', 'chnarg': 'chnarg', 'adrarg': 'adrarg', 'idnarg': 'idnarg', 'crtchan': 'crtchan', 'channel': 'channel', 'cmin': 'cmin', 'cmax': 'cmax', 'j': 'j', 'chdata': 'chdata', 'rdata': 'rdata', 'mtype': 'mtype', 'idata': 'idata', 'ltype': 'ltype', 'rs': 'rs', 'nicn': 'nicn', 'ncon': 'ncon', 'model': 'model', 'vsdcnm': 'vsdcnm', 'mins': 'mins', 'isgndx': 'isgndx', 'basekv': 'basekv', 'fault': 'fault', 'num': 'num', 'areas': 'areas', 'busnum': 'busnum', 'owners': 'owners', 'zones': 'zones', 'string': 'string', 'flag': 'flag', 'owner': 'owner', 'entry': 'entry', 'fcttyp': 'fcttyp', 'brns': 'brns', 'sel': 'sel', 'selopt1': 'selopt1', 'selopt2': 'selopt2', 'rpttype': 'rpttype', 'monels': 'monels', 'dflag': 'dflag', 'sys1': 'sys1', 'sys2': 'sys2', 'transfer': 'transfer', 'subsfr': 'subsfr', 'subsin': 'subsin', 'trans': 'trans', 'contno': 'contno', 'menunm': 'menunm', 'parmid': 'parmid', 'newvalue': 'newvalue', 'frtype': 'frtype', 'vlow': 'vlow', 'vhigh': 'vhigh', 'nummonel': 'nummonel', 'listml': 'listml', 'violmeth': 'violmeth', 'subs': 'subs', 'numtrans': 'numtrans', 'listtr': 'listtr', 'cnfile': 'cnfile', 'cviol': 'cviol', 'cevents': 'cevents', 'cisland': 'cisland', 'cdispatch': 'cdispatch', 'marg': 'marg', 'exitc': 'exitc', 'maxrep': 'maxrep', 'mwlev': 'mwlev', 'contnum': 'contnum', 'frobj': 'frobj', 'toobj': 'toobj', 'numfrom': 'numfrom', 'listfr': 'listfr', 'numto': 'numto', 'listto': 'listto', 'listop': 'listop', 'trpow': 'trpow', 'maxpowtr': 'maxpowtr', 'sysfrom': 'sysfrom', 'systo': 'systo', 'reptyp': 'reptyp', 'trstep': 'trstep', 'csfile': 'csfile', 'exfile': 'exfile', 'sbfile': 'sbfile', 'fname': 'fname', 'wrfile': 'wrfile', 'busno': 'busno', 'areano': 'areano', 'zoneno': 'zoneno', 'sevco': 'sevco', 'numconts': 'numconts', 'contlist': 'contlist', 'numbra': 'numbra', 'listbra': 'listbra', 'numfgates': 'numfgates', 'listfgates': 'listfgates', 'numinter': 'numinter', 'listinter': 'listinter', 'numbus': 'numbus', 'listbus': 'listbus', 'actoln': 'actoln', 'maxtr': 'maxtr', 'azindex': 'azindex', 'azindexfrom': 'azindexfrom', 'azindexto': 'azindexto'}
