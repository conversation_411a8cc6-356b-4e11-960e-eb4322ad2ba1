Application Program Interface (API) 
PSS®E 35.2.0

December 2020

Siemens Industry, Inc.
Siemens Power Technologies International
400 State Street
Schenectady, NY 12301-1058 USA
+1 518-395-5000
www.siemens.com/power-technologies

Copyright © 1990, 2020 Siemens Power Technologies International
Information in this manual and any software described herein is confidential and subject to change without notice and does not repre-
sent a commitment on the part of Siemens Industry, Inc., Siemens Power Technologies International. The software described in this
manual is furnished under a license agreement or nondisclosure agreement and may be used or copied only in accordance with the
terms of the agreement. No part of this manual may be reproduced or transmitted in any form or by any means, electronic or me-
chanical, including photocopying, for any purpose other than the purchaser’s personal use, without the express written permission of
Siemens Industry, Inc., Siemens Power Technologies International. PSS®E high-performance transmission planning software is a reg-
istered trademark of Siemens Industry, Inc., Siemens Power Technologies International in the United States and other countries. The
Windows 7® and Windows 10® operating systems, the Visual C++® development system, Microsoft Office Excel® and Microsoft Visual
Studio® are registered trademarks of Microsoft Corporation in the United States and other countries. The Python programming language
is a trademark of the Python Software Foundation. Other names may be trademarks of their respective owners.

All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
ii

Table of Contents

Preface  ...........................................................................................................................................  lii
Power  Flow  ......................................................................................................................................   1
Power Flow Operation ..............................................................................................................  2
ACCC  ...............................................................................................................................   2
ACCC_2  ............................................................................................................................  4
ACCC_MULTIPLE_MERGE .................................................................................................... 6
ACCC_MULTIPLE_RUN_REPORT ...........................................................................................  8
ACCC_MULTIPLE_RUN_REPORT_2 ...................................................................................... 11
ACCC_PARALLEL ..............................................................................................................  15
ACCC_PARALLEL_2 ..........................................................................................................  18
ACCC_SINGLE_RUN_REPORT ............................................................................................. 21
ACCC_SINGLE_RUN_REPORT_2 .........................................................................................  24
ACCC_SINGLE_RUN_REPORT_3 .........................................................................................  27
ACCC_SINGLE_RUN_REPORT_4 .........................................................................................  30
ACCC_SINGLE_RUN_REPORT_5 .........................................................................................  33
ACCC_TRIP_COR ..............................................................................................................  37
ACCC_TRIP_COR_2 ..........................................................................................................  41
ACCC_TRIP_COR_3 ..........................................................................................................  45
ACCC_WITH_COR ............................................................................................................  50
ACCC_WITH_COR_2 .........................................................................................................  54
ACCC_WITH_COR_3 .........................................................................................................  58
ACCC_WITH_DSP .............................................................................................................  62
ACCC_WITH_DSP_2 .........................................................................................................  65
ACCC_WITH_DSP_3 .........................................................................................................  68
ACCC_WITH_TRIP ............................................................................................................  71
ACCC_WITH_TRIP_2 .........................................................................................................  74
ACCC_WITH_TRIP_PARALLEL .............................................................................................  77
ACCOR  ...........................................................................................................................   80
ACCOR_2  ........................................................................................................................  83
ACCOR_3  ........................................................................................................................  86
ALERT_OUTPUT ............................................................................................................... 89
ALLOW_PSSUSERPF .........................................................................................................  91
ALPH  ..............................................................................................................................  92
APPEND_ACCC ................................................................................................................  93
APPLY_VAR_LIMITS ..........................................................................................................  95
AREA  ..............................................................................................................................  96
AREA_2  ..........................................................................................................................   97
AREA_ZONE  ....................................................................................................................  98
ARNM  ............................................................................................................................   99
ARNM_2  .......................................................................................................................  101
BASE_FREQUENCY ......................................................................................................... 103
BGEN  ...........................................................................................................................   104
BRCH  ............................................................................................................................  105
BRCH_2  ........................................................................................................................   107
BSNM  ...........................................................................................................................  109
BUSN  ...........................................................................................................................   112
BUS_INPUT  ...................................................................................................................   113
BUS_OUTPUT ................................................................................................................  114

All material contained in this documentation is proprietary to Siemens Industry Inc., Siemens Power Technologies International
iii

BUS_SIZE_LEVEL ...........................................................................................................  115
CASE  ............................................................................................................................  116
CA_ITERATIONS ............................................................................................................. 117
CHECKVOLTAGELIMITS ...................................................................................................  118
CHECK_POWERFLOW_DATA ............................................................................................  119
CHKCNTDUPLICON ........................................................................................................  121
CHKCNTDUPLIDFX .........................................................................................................  122
CLOSE_POWERFLOW ...................................................................................................... 123
CLOSE_REPORT .............................................................................................................  124
CMPR  ...........................................................................................................................   125
CNTB  ............................................................................................................................  127
CONG  ...........................................................................................................................  129
CONL  ...........................................................................................................................   130
CONNECTIVITY_CHECK ..................................................................................................  132
CONTROL_AREA_INTERCHANGE .........................................................................