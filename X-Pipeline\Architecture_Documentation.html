
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDB to RAW Pipeline Architecture Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 2.2em;
        }
        
        h2 {
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.4em;
        }
        
        h4 {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
            color: #333;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            padding-left: 20px;
            margin-left: 0;
            font-style: italic;
            color: #7f8c8d;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        ul, ol {
            padding-left: 25px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .mermaid {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 8px;
        }
        
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        strong {
            color: #2c3e50;
        }
        
        em {
            color: #7f8c8d;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
            
            h1, h2, h3 {
                page-break-after: avoid;
            }
            
            pre, blockquote {
                page-break-inside: avoid;
            }
            
            .mermaid {
                page-break-inside: avoid;
                background-color: white !important;
            }
        }
    </style>
    
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                },
                sequence: {
                    useMaxWidth: true
                },
                gantt: {
                    useMaxWidth: true
                }
            });
        });
    </script>
    
</head>
<body>
    <h1 id="hdb-to-raw-pipeline-architecture-documentation"><a class="toclink" href="#hdb-to-raw-pipeline-architecture-documentation">HDB to RAW Pipeline Architecture Documentation</a></h1>
<h2 id="overview"><a class="toclink" href="#overview">Overview</a></h2>
<p>This document provides a comprehensive technical overview of the HDB to RAW pipeline architecture, designed for developers who need to understand the complete data flow, transformations, and implementation details.</p>
<p><strong>Note</strong>: The architecture diagrams in this document are sourced from individual Mermaid files in the <code>diagrams/</code> directory. See <code>diagrams/README.md</code> for information on how to edit and use these diagrams independently.</p>
<h2 id="architecture-components"><a class="toclink" href="#architecture-components">Architecture Components</a></h2>
<h3 id="1-backend-classes"><a class="toclink" href="#1-backend-classes">1. Backend Classes</a></h3>
<p>The pipeline uses a clean backend architecture with the following key classes:</p>
<h4 id="basebackend-abstract-base-class"><a class="toclink" href="#basebackend-abstract-base-class">BaseBackend (Abstract Base Class)</a></h4>
<ul>
<li><strong>Location</strong>: Lines 141-340 in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Provides consistent interface for all database backends</li>
<li><strong>Key Features</strong>:</li>
<li>Quality checking integration (<code>DataQualityChecker</code>)</li>
<li>Modeling approach management (<code>ModelingApproach</code> enum)</li>
<li>System state management (<code>SystemState</code> enum)</li>
<li>Automatic issue fixing capabilities</li>
</ul>
<h4 id="hdbbackend"><a class="toclink" href="#hdbbackend">HdbBackend</a></h4>
<ul>
<li><strong>Location</strong>: Lines 5874-6558 in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Handles HDB (Hierarchical Database) format parsing and conversion</li>
<li><strong>Key Features</strong>:</li>
<li>Parses HDB JSON format containing substation topology</li>
<li>Always produces NODE_BREAKER canonical format (richest representation)</li>
<li>Embeds node information in equipment records during conversion</li>
<li>Uses performance tracking for converter optimization</li>
</ul>
<h4 id="rawxbackend"><a class="toclink" href="#rawxbackend">RawxBackend</a></h4>
<ul>
<li><strong>Location</strong>: Lines 6559-7341 in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Handles RAWX format parsing and conversion</li>
<li><strong>Key Features</strong>:</li>
<li>Parses PSS/E RAW format files</li>
<li>Detects modeling approach from file structure</li>
<li>Converts to canonical format while preserving original modeling</li>
</ul>
<h3 id="2-canonical-data-system"><a class="toclink" href="#2-canonical-data-system">2. Canonical Data System</a></h3>
<h4 id="canonicaldatainterface"><a class="toclink" href="#canonicaldatainterface">CanonicalDataInterface</a></h4>
<ul>
<li><strong>Location</strong>: <code>canonical_data_interface.py</code> lines 97-305</li>
<li><strong>Purpose</strong>: Provides immutable, type-safe access to power system data</li>
<li><strong>Key Classes</strong>:</li>
<li><code>CanonicalRecord</code>: Immutable record with frozen data</li>
<li><code>CanonicalSection</code>: Container for records with metadata</li>
<li><code>ReadOnlyCanonicalDataInterface</code>: Implementation with field mapping</li>
</ul>
<h4 id="data-flow-through-canonical-system"><a class="toclink" href="#data-flow-through-canonical-system">Data Flow Through Canonical System</a></h4>
<ol>
<li><strong>Backend → Canonical</strong>: Raw data converted to dictionaries with human-readable field names</li>
<li><strong>Canonical → Transformations</strong>: Immutable access prevents accidental modifications</li>
<li><strong>Transformations → Export</strong>: Clean interface for writers</li>
</ol>
<h3 id="3-model-transformations"><a class="toclink" href="#3-model-transformations">3. Model Transformations</a></h3>
<h4 id="modeltransformations-class"><a class="toclink" href="#modeltransformations-class">ModelTransformations Class</a></h4>
<ul>
<li><strong>Location</strong>: Lines 7342-9309 in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Handles conversions between modeling approaches</li>
<li><strong>Key Methods</strong>:</li>
<li><code>convert_to_hybrid_modeling()</code>: NODE_BREAKER → HYBRID conversion</li>
<li><code>detect_model_type()</code>: Automatic modeling approach detection</li>
<li><code>_create_node_mapping_from_equipment()</code>: Creates node-to-bus mappings</li>
</ul>
<h4 id="node-to-bus-conversion-process"><a class="toclink" href="#node-to-bus-conversion-process">Node-to-Bus Conversion Process</a></h4>
<p>The most complex part of the architecture is the node-to-bus conversion for hybrid modeling:</p>
<h5 id="1-node-mapping-creation"><a class="toclink" href="#1-node-mapping-creation">1. Node Mapping Creation</a></h5>
<pre><code class="language-python">node_to_new_bus = {}   # Maps (substation_id, node_id) → new_bus_number
</code></pre>
<h5 id="2-equipment-processing"><a class="toclink" href="#2-equipment-processing">2. Equipment Processing</a></h5>
<ul>
<li>Equipment records contain embedded node information:</li>
<li><code>substation_id</code>: Which substation the equipment belongs to</li>
<li><code>node_id</code>: Which node within the substation</li>
<li><code>from_substation_id</code>, <code>from_node_id</code>: For lines/transformers</li>
<li><code>to_substation_id</code>, <code>to_node_id</code>: For lines/transformers</li>
</ul>
<h5 id="3-bus-number-assignment"><a class="toclink" href="#3-bus-number-assignment">3. Bus Number Assignment</a></h5>
<pre><code class="language-python"># Create mapping from node coordinates to bus numbers
substation_node_key = (substation_id, node_id)
node_to_new_bus[substation_node_key] = new_bus_num
</code></pre>
<h5 id="4-equipment-update-process"><a class="toclink" href="#4-equipment-update-process">4. Equipment Update Process</a></h5>
<ul>
<li><strong>ALL equipment sections</strong> get their bus numbers updated</li>
<li>Writers call <code>record.get('bus_number', 0)</code> and receive transformed values</li>
<li>Writers are completely unaware of the transformation process</li>
</ul>
<h3 id="4-writer-system"><a class="toclink" href="#4-writer-system">4. Writer System</a></h3>
<h4 id="rawsectionwriter-abstract-base-class"><a class="toclink" href="#rawsectionwriter-abstract-base-class">RawSectionWriter (Abstract Base Class)</a></h4>
<ul>
<li><strong>Location</strong>: Lines 9310-9442 in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Base class for all RAW format writers</li>
<li><strong>Key Methods</strong>:</li>
<li><code>write_section_canonical()</code>: Main writing method using canonical interface</li>
<li><code>write_section()</code>: Legacy method for backward compatibility</li>
</ul>
<h4 id="equipment-writers"><a class="toclink" href="#equipment-writers">Equipment Writers</a></h4>
<p>All writers inherit from <code>RawSectionWriter</code> and follow the same pattern:</p>
<ul>
<li><strong>BusWriter</strong> (Lines 9491-9532): Writes bus data</li>
<li><strong>LoadWriter</strong> (Lines 9533-9596): Writes load data</li>
<li><strong>GeneratorWriter</strong> (Lines 9597-9710): Writes generator data</li>
<li><strong>AcLineWriter</strong> (Lines 9711-9822): Writes AC line data</li>
<li><strong>TransformerWriter</strong> (Lines 9823-10093): Writes transformer data</li>
<li><strong>FixedShuntWriter</strong> (Lines 10094-10141): Writes fixed shunt data</li>
</ul>
<h4 id="hierarchical-writers"><a class="toclink" href="#hierarchical-writers">Hierarchical Writers</a></h4>
<ul>
<li><strong>SubstationWriter</strong> (Lines 10229-10393): Writes hierarchical substation data</li>
<li><strong>NodeWriter</strong> (Lines 10394-10422): Writes node data</li>
<li><strong>SwitchingDeviceWriter</strong> (Lines 10423-10467): Writes switching device data</li>
<li><strong>TerminalWriter</strong> (Lines 10468-10496): Writes terminal data</li>
</ul>
<h3 id="5-export-system"><a class="toclink" href="#5-export-system">5. Export System</a></h3>
<h4 id="export_to_raw_format-function"><a class="toclink" href="#export_to_raw_format-function">export_to_raw_format Function</a></h4>
<ul>
<li><strong>Location</strong>: Lines 11742+ in <code>hdb_to_raw_pipeline.py</code></li>
<li><strong>Purpose</strong>: Orchestrates the complete export process</li>
<li><strong>Key Parameters</strong>:</li>
<li><code>data</code>: Canonical data dictionary</li>
<li><code>output_path</code>: Output file path</li>
<li><code>version</code>: PSS/E version (33, 34, 35)</li>
<li><code>modeling</code>: Modeling approach ("bus_branch", "node_breaker", "hybrid")</li>
</ul>
<h4 id="export-logic-flow"><a class="toclink" href="#export-logic-flow">Export Logic Flow</a></h4>
<ol>
<li><strong>Modeling Check</strong>: Determines output format based on modeling parameter</li>
<li><strong>Hierarchical vs Flat</strong>: </li>
<li>If <code>modeling == "node_breaker"</code> AND substation data exists → Hierarchical format</li>
<li>Otherwise → Flat format</li>
<li><strong>Section Writing</strong>: Uses appropriate writers for each section</li>
<li><strong>Termination</strong>: Ends with 'Q' after all sections</li>
</ol>
<h2 id="data-flow-architecture"><a class="toclink" href="#data-flow-architecture">Data Flow Architecture</a></h2>
<div class="mermaid">
graph TB
    subgraph &quot;Input Layer&quot;
        HDB[HDB Files]
        RAWX[RAWX Files]
        JSON[JSON Files]
    end

    subgraph &quot;Backend Layer&quot;
        HdbBackend[&quot;HdbBackend&lt;br/&gt;- Parses HDB JSON&lt;br/&gt;- Embeds node info in equipment&lt;br/&gt;- Always produces NODE_BREAKER canonical&quot;]
        RawxBackend[&quot;RawxBackend&lt;br/&gt;- Parses PSS/E RAW format&lt;br/&gt;- Detects modeling approach&lt;br/&gt;- Preserves original modeling&quot;]
    end

    subgraph &quot;Canonical Data Layer&quot;
        CanonicalData[&quot;Canonical Data&lt;br/&gt;- Immutable dictionaries&lt;br/&gt;- Human-readable field names&lt;br/&gt;- Embedded node information&quot;]
        CanonicalInterface[&quot;CanonicalDataInterface&lt;br/&gt;- Type-safe access&lt;br/&gt;- Prevents direct backend access&lt;br/&gt;- Field mapping integration&quot;]
    end

    subgraph &quot;Transformation Layer&quot;
        ModelTransformations[&quot;ModelTransformations&lt;br/&gt;- Node-to-bus mapping&lt;br/&gt;- Equipment bus number updates&lt;br/&gt;- Modeling approach conversion&quot;]
        NodeMapping[&quot;Node Mapping Tables&lt;br/&gt;node_to_new_bus:&lt;br/&gt;(substation_id, node_id) → bus_number&quot;]
    end

    subgraph &quot;Export Layer&quot;
        Writers[&quot;RawSectionWriter Classes&lt;br/&gt;- BusWriter, LoadWriter, etc.&lt;br/&gt;- Hierarchical writers&lt;br/&gt;- Canonical interface only&quot;]
        ExportFunction[&quot;export_to_raw_format&lt;br/&gt;- Orchestrates export process&lt;br/&gt;- Handles modeling approach&lt;br/&gt;- Hierarchical vs flat logic&quot;]
    end

    subgraph &quot;Output Layer&quot;
        BusBranch[&quot;Bus-Branch RAW Files&lt;br/&gt;- Traditional PSS/E format&lt;br/&gt;- Flat bus structure&quot;]
        NodeBreaker[&quot;Node-Breaker RAW Files&lt;br/&gt;- Hierarchical substation format&lt;br/&gt;- Full topology detail&quot;]
        Hybrid[&quot;Hybrid RAW Files&lt;br/&gt;- Mixed representation&lt;br/&gt;- Substation data + simplified buses&quot;]
    end

    HDB --&gt; HdbBackend
    RAWX --&gt; RawxBackend
    JSON --&gt; HdbBackend

    HdbBackend --&gt; CanonicalData
    RawxBackend --&gt; CanonicalData

    CanonicalData --&gt; CanonicalInterface
    CanonicalInterface --&gt; ModelTransformations

    ModelTransformations --&gt; NodeMapping
    NodeMapping --&gt; Writers

    Writers --&gt; ExportFunction
    ExportFunction --&gt; BusBranch
    ExportFunction --&gt; NodeBreaker
    ExportFunction --&gt; Hybrid
</div>
<h2 id="equipment-data-transformation"><a class="toclink" href="#equipment-data-transformation">Equipment Data Transformation</a></h2>
<div class="mermaid">
graph LR
    subgraph &quot;Original Equipment Record&quot;
        OriginalBus[&quot;bus_number: 0&lt;br/&gt;substation_id: 'SUB1'&lt;br/&gt;node_id: 'N1'&quot;]
    end

    subgraph &quot;Node Mapping Process&quot;
        NodeKey[&quot;Node Key&lt;br/&gt;('SUB1', 'N1')&quot;]
        BusAssignment[&quot;Bus Assignment&lt;br/&gt;new_bus_number: 1001&quot;]
        MappingTable[&quot;node_to_new_bus&lt;br/&gt;('SUB1', 'N1') → 1001&quot;]
    end

    subgraph &quot;Transformed Equipment Record&quot;
        TransformedBus[&quot;bus_number: 1001&lt;br/&gt;substation_id: 'SUB1'&lt;br/&gt;node_id: 'N1'&quot;]
    end

    subgraph &quot;Writer Access&quot;
        WriterCall[&quot;record.get('bus_number', 0)&lt;br/&gt;returns: 1001&quot;]
        WriterOutput[&quot;RAW Format Output&lt;br/&gt;1001, ...&quot;]
    end

    OriginalBus --&gt; NodeKey
    NodeKey --&gt; BusAssignment
    BusAssignment --&gt; MappingTable
    MappingTable --&gt; TransformedBus
    TransformedBus --&gt; WriterCall
    WriterCall --&gt; WriterOutput
</div>
<h2 id="modeling-approach-comparison"><a class="toclink" href="#modeling-approach-comparison">Modeling Approach Comparison</a></h2>
<div class="mermaid">
graph TB
    subgraph &quot;Bus-Branch Modeling&quot;
        BusBranchData[&quot;Equipment Data&lt;br/&gt;- Direct bus references&lt;br/&gt;- No substation topology&lt;br/&gt;- Traditional PSS/E format&quot;]
        BusBranchOutput[&quot;RAW Output&lt;br/&gt;- Flat bus structure&lt;br/&gt;- Standard equipment sections&lt;br/&gt;- Ends with 'Q'&quot;]
    end

    subgraph &quot;Node-Breaker Modeling&quot;
        NodeBreakerData[&quot;Equipment Data&lt;br/&gt;- Substation topology&lt;br/&gt;- Node connections&lt;br/&gt;- Switching devices&quot;]
        NodeBreakerOutput[&quot;RAW Output&lt;br/&gt;- Hierarchical substation blocks&lt;br/&gt;- Node/switching device sections&lt;br/&gt;- Ends with 'Q'&quot;]
    end

    subgraph &quot;Hybrid Modeling&quot;
        HybridData[&quot;Equipment Data&lt;br/&gt;- Node info embedded&lt;br/&gt;- Mapped to bus numbers&lt;br/&gt;- Simplified topology&quot;]
        HybridOutput[&quot;RAW Output&lt;br/&gt;- Simplified bus structure&lt;br/&gt;- Standard equipment sections&lt;br/&gt;- Optional substation data&quot;]
    end

    BusBranchData --&gt; BusBranchOutput
    NodeBreakerData --&gt; NodeBreakerOutput
    HybridData --&gt; HybridOutput
</div>
<h2 id="three-phase-pipeline-architecture"><a class="toclink" href="#three-phase-pipeline-architecture">Three-Phase Pipeline Architecture</a></h2>
<div class="mermaid">
graph TB
    subgraph &quot;Phase 1: Loading &amp; Conversion&quot;
        Input[&quot;Input Files&lt;br/&gt;HDB/RAWX/JSON&quot;]
        Backend[&quot;Backend Classes&lt;br/&gt;HdbBackend/RawxBackend&quot;]
        Canonical[&quot;Canonical Format&lt;br/&gt;Always NODE_BREAKER&lt;br/&gt;Embedded node info&quot;]
    end

    subgraph &quot;Phase 2: Transformation&quot;
        ModelDetection[&quot;Model Detection&lt;br/&gt;detect_model_type()&quot;]
        Transformation[&quot;ModelTransformations&lt;br/&gt;Node-to-bus mapping&lt;br/&gt;Equipment updates&quot;]
        TransformedData[&quot;Transformed Data&lt;br/&gt;Bus numbers updated&lt;br/&gt;Modeling approach applied&quot;]
    end

    subgraph &quot;Phase 3: Export&quot;
        ExportDecision[&quot;Export Decision&lt;br/&gt;Hierarchical vs Flat&quot;]
        Writers[&quot;Writer Classes&lt;br/&gt;Equipment-specific writers&quot;]
        Output[&quot;RAW Format Output&lt;br/&gt;Version-specific format&quot;]
    end

    Input --&gt; Backend
    Backend --&gt; Canonical
    Canonical --&gt; ModelDetection
    ModelDetection --&gt; Transformation
    Transformation --&gt; TransformedData
    TransformedData --&gt; ExportDecision
    ExportDecision --&gt; Writers
    Writers --&gt; Output
</div>
<h2 id="key-architectural-principles"><a class="toclink" href="#key-architectural-principles">Key Architectural Principles</a></h2>
<h3 id="1-clean-separation-of-concerns"><a class="toclink" href="#1-clean-separation-of-concerns">1. Clean Separation of Concerns</a></h3>
<ul>
<li><strong>Backends</strong>: Handle format parsing only</li>
<li><strong>Transformations</strong>: Handle modeling conversions only</li>
<li><strong>Writers</strong>: Handle output formatting only</li>
</ul>
<h3 id="2-immutable-canonical-data"><a class="toclink" href="#2-immutable-canonical-data">2. Immutable Canonical Data</a></h3>
<ul>
<li>All data access through <code>CanonicalDataInterface</code></li>
<li>Records are frozen after creation</li>
<li>Prevents accidental modifications</li>
</ul>
<h3 id="3-embedded-node-information"><a class="toclink" href="#3-embedded-node-information">3. Embedded Node Information</a></h3>
<ul>
<li>Equipment records contain <code>substation_id</code> and <code>node_id</code></li>
<li>Enables transparent node-to-bus conversion</li>
<li>Writers remain unaware of transformations</li>
</ul>
<h3 id="4-universal-transformation-logic"><a class="toclink" href="#4-universal-transformation-logic">4. Universal Transformation Logic</a></h3>
<ul>
<li><code>ModelTransformations</code> handles all conversions centrally</li>
<li>Equipment writers never handle mapping logic</li>
<li>Consistent transformation across all equipment types</li>
</ul>
<h2 id="bi-directional-functions"><a class="toclink" href="#bi-directional-functions">Bi-Directional Functions</a></h2>
<h3 id="functions-that-can-be-called-both-ways"><a class="toclink" href="#functions-that-can-be-called-both-ways">Functions That Can Be Called Both Ways</a></h3>
<ol>
<li><strong>Backend Methods</strong>:
   ```python
   # Can be called to convert TO canonical
   canonical_data = backend.to_canonical()</li>
</ol>
<p># Can be called to convert FROM canonical (planned)
   raw_data = backend.from_canonical(canonical_data)
   ```</p>
<ol>
<li>
<p><strong>ModelTransformations Methods</strong>:
   <code>python
   # Can convert between any modeling approaches
   hybrid_result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
   node_breaker_result = ModelTransformations.convert_to_node_breaker(canonical_data)</code></p>
</li>
<li>
<p><strong>Writer Methods</strong>:
   ```python
   # Can write from canonical format
   writer.write_section_canonical(canonical_section)</p>
</li>
</ol>
<p># Can write from raw format (legacy)
   writer.write_section(raw_section)
   ```</p>
<h3 id="functions-that-are-unidirectional"><a class="toclink" href="#functions-that-are-unidirectional">Functions That Are Unidirectional</a></h3>
<ol>
<li><strong>File Parsing</strong>: Input files → Backend data (cannot reverse)</li>
<li><strong>Export Functions</strong>: Canonical data → RAW file (cannot reverse)</li>
<li><strong>Node Mapping</strong>: Nodes → Bus numbers (reverse requires additional logic)</li>
</ol>
<h2 id="node-to-bus-conversion-details"><a class="toclink" href="#node-to-bus-conversion-details">Node-to-Bus Conversion Details</a></h2>
<h3 id="1-mapping-table-structure"><a class="toclink" href="#1-mapping-table-structure">1. Mapping Table Structure</a></h3>
<pre><code class="language-python">node_to_new_bus = {
    ('SUBSTATION_A', 'NODE_1'): 1001,
    ('SUBSTATION_A', 'NODE_2'): 1002,
    ('SUBSTATION_B', 'NODE_1'): 2001,
    ('SUBSTATION_B', 'NODE_2'): 2002,
    # ...
}
</code></pre>
<h3 id="2-equipment-processing-algorithm"><a class="toclink" href="#2-equipment-processing-algorithm">2. Equipment Processing Algorithm</a></h3>
<pre><code class="language-python">def update_equipment_bus_numbers(equipment_records, node_to_new_bus):
    for record in equipment_records:
        # Extract embedded node information
        substation_id = record.get('substation_id')
        node_id = record.get('node_id')

        # Create node key
        node_key = (substation_id, node_id)

        # Look up new bus number
        new_bus = node_to_new_bus.get(node_key)

        if new_bus:
            # Update bus number in record
            record['bus_number'] = new_bus
</code></pre>
<h3 id="3-multi-terminal-equipment-lines-transformers"><a class="toclink" href="#3-multi-terminal-equipment-lines-transformers">3. Multi-Terminal Equipment (Lines, Transformers)</a></h3>
<pre><code class="language-python"># Lines and transformers have FROM and TO connections
from_node_key = (record.get('from_substation_id'), record.get('from_node_id'))
to_node_key = (record.get('to_substation_id'), record.get('to_node_id'))

record['from_bus'] = node_to_new_bus.get(from_node_key, 0)
record['to_bus'] = node_to_new_bus.get(to_node_key, 0)
</code></pre>
<h2 id="performance-considerations"><a class="toclink" href="#performance-considerations">Performance Considerations</a></h2>
<h3 id="1-converter-performance-tracking"><a class="toclink" href="#1-converter-performance-tracking">1. Converter Performance Tracking</a></h3>
<ul>
<li>Each converter is tracked individually</li>
<li>Performance metrics saved to <code>performance_tracking.txt</code></li>
<li>Helps identify optimization opportunities</li>
</ul>
<h3 id="2-memory-management"><a class="toclink" href="#2-memory-management">2. Memory Management</a></h3>
<ul>
<li>Immutable records prevent memory leaks</li>
<li>Canonical data is frozen after creation</li>
<li>Large datasets handled efficiently</li>
</ul>
<h3 id="3-optimization-strategies"><a class="toclink" href="#3-optimization-strategies">3. Optimization Strategies</a></h3>
<ul>
<li>Bus conflict resolution optimized</li>
<li>Node mapping creation optimized</li>
<li>Writer operations parallelizable</li>
</ul>
<h2 id="error-handling-and-quality-assurance"><a class="toclink" href="#error-handling-and-quality-assurance">Error Handling and Quality Assurance</a></h2>
<h3 id="1-data-quality-checker"><a class="toclink" href="#1-data-quality-checker">1. Data Quality Checker</a></h3>
<ul>
<li>Integrated into all backends</li>
<li>Automatic issue detection and fixing</li>
<li>Configurable validation rules</li>
</ul>
<h3 id="2-validation-at-each-stage"><a class="toclink" href="#2-validation-at-each-stage">2. Validation at Each Stage</a></h3>
<ul>
<li><strong>Backend</strong>: Format validation</li>
<li><strong>Canonical</strong>: Data consistency checks</li>
<li><strong>Transformation</strong>: Mapping validation</li>
<li><strong>Export</strong>: Output format validation</li>
</ul>
<h3 id="3-comprehensive-logging"><a class="toclink" href="#3-comprehensive-logging">3. Comprehensive Logging</a></h3>
<ul>
<li>Debug information for troubleshooting</li>
<li>Performance tracking</li>
<li>Error reporting with context</li>
</ul>
<h2 id="integration-points"><a class="toclink" href="#integration-points">Integration Points</a></h2>
<h3 id="1-adding-new-backends"><a class="toclink" href="#1-adding-new-backends">1. Adding New Backends</a></h3>
<ol>
<li>Inherit from <code>BaseBackend</code></li>
<li>Implement <code>to_canonical()</code> method</li>
<li>Add to backend factory</li>
</ol>
<h3 id="2-adding-new-equipment-types"><a class="toclink" href="#2-adding-new-equipment-types">2. Adding New Equipment Types</a></h3>
<ol>
<li>Create converter class</li>
<li>Add to converter map in backends</li>
<li>Create writer class</li>
<li>Add to writer registry</li>
</ol>
<h3 id="3-adding-new-modeling-approaches"><a class="toclink" href="#3-adding-new-modeling-approaches">3. Adding New Modeling Approaches</a></h3>
<ol>
<li>Add to <code>ModelingApproach</code> enum</li>
<li>Implement transformation methods</li>
<li>Update export logic</li>
<li>Add writer support</li>
</ol>
<h2 id="configuration-and-customization"><a class="toclink" href="#configuration-and-customization">Configuration and Customization</a></h2>
<h3 id="1-backend-configuration"><a class="toclink" href="#1-backend-configuration">1. Backend Configuration</a></h3>
<pre><code class="language-python">backend = HdbBackend(
    enable_quality_check=True,
    auto_fix=True,
    system_state=SystemState.MAINTAIN_CURRENT
)
</code></pre>
<h3 id="2-export-configuration"><a class="toclink" href="#2-export-configuration">2. Export Configuration</a></h3>
<pre><code class="language-python">export_to_raw_format(
    data=canonical_data,
    output_path=&quot;output.raw&quot;,
    version=35,
    modeling=&quot;hybrid&quot;
)
</code></pre>
<h3 id="3-transformation-configuration"><a class="toclink" href="#3-transformation-configuration">3. Transformation Configuration</a></h3>
<pre><code class="language-python">result = ModelTransformations.convert_to_hybrid_modeling(
    canonical_data=data
)
</code></pre>
<h2 id="summary"><a class="toclink" href="#summary">Summary</a></h2>
<p>This architecture provides a robust, extensible system for power system data conversion with clear separation of concerns, comprehensive error handling, and support for multiple modeling approaches. The key innovation is the embedded node information system that enables transparent node-to-bus conversion without requiring writers to understand the transformation process.</p>
<p>The pipeline is designed for both batch processing and interactive use, with comprehensive logging and performance tracking to ensure reliability and maintainability. </p>
</body>
</html>
