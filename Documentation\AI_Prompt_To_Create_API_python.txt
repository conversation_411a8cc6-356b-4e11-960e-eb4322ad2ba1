# AI Prompt for PSSPY Power Flow Data API Python Wrappers
You are tasked with generating Pythonic, developer-friendly API wrappers for a legacy or complex API (such as PSSPY), making it accessible, robust, and easy to use for modern Python developers. Follow these instructions and conventions exactly:

## CONTEXT & PROGRESS
- This project generates Pythonic, developer-friendly wrappers for the PSSPY Power Flow Data API.
- The wrappers are split into canonical (official) and human-readable functions for each API call.
- The last implemented function was `nxtbrn_2` in chapter 10 (API3.md line 8036).
- The previous batch included: `nxtbrn_2`.
- Continue sequentially through the Single Element Data API section in `API3.md`.

## RULES & REQUIREMENTS
- **Architecture:**
  - Code must be modular, extensible, and decoupled (SOLID, DRY, high cohesion, low coupling).
  - Use interfaces, factories, registries, or plugin loaders for abstraction and flexible composition.
  - Favor composition over inheritance; avoid hardcoded dependencies.
  - Components must be swappable without import path or format rigidity.
  - Support mocking and dependency injection by default.
- **Function Patterns:**
  - Each API function has a canonical (official) and a human-readable version.
  - Canonical functions accept both array (e.g., intgar, realar) and named arguments for each element.
  - Merge arrays and named arguments, with named arguments overriding array values; blanks/defaults filled in.
  - Canonical functions call the human-readable function with only named arguments (not arrays).
  - Docstrings for canonical functions enumerate all array elements, defaults, and show canonical example call with variable names (not fake data).
  - Human-readable functions have robust docstrings, usage examples, and call the canonical function bi-directionally.
  - All error codes/messages are in constant dictionaries, referenced in docstrings.
  - Full official documentation for each function is included as comments above the function.
  - All functions are registered in a mapping dictionary at the top of the file.
- **Testing & Logging:**
  - Every function must have robust, readable tests covering normal, edge, and failure cases.
  - All test results and debug output must be written to structured log files in a dedicated log directory.
  - Never rely on print statements or manual terminal checks.
- **Documentation & Naming:**
  - Every class, method, and complex block must have clear docstrings or inline comments.
  - Use descriptive, unambiguous names for all identifiers.
  - No commented-out code in the codebase.
  - Usage examples for all public interfaces and modules.
- **Security & Input Handling:**
  - No hardcoded secrets/tokens/passwords; use environment variables or secret managers.
  - Validate all inputs and sanitize all outputs.
  - Use parameterized queries to prevent injection attacks.
  - Log errors securely without exposing sensitive data.
- **Performance:**
  - Design for scalability, use async/generators/pagination/streaming where appropriate.
  - Avoid blocking the main thread with long-running operations.
- **Formatting, Linting & Typing:**
  - Enforce formatting with standard tools (e.g., black, flake8).
  - All functions must have clear type annotations (avoid Any unless necessary).
  - Pre-commit hooks for linters.
- **Auto-Documentation:**
  - All public interfaces must support auto-generated documentation (e.g., Sphinx).
  - Define expected usage and extension pattern for each module/class.
- **Version Control:**
  - All commits must be atomic, test-passing, and clearly documented.
  - No broken, partial, or mixed-purpose commits.
- **Markdown:**
  - Follow strict formatting rules for Markdown files (see user rules).
- **Windows Shell:**
  - Use Windows-compatible syntax for all shell commands/scripts/paths.

## HOW TO CONTINUE
- Resume at the next four unimplemented Single Elemented Data API functions in `API3.md` (next: `facts_chng_2`).
- For each function:
  - Extract all parameter details, error codes, and official documentation from `API3.md`.
  - Implement both canonical and human-readable functions as described above.
  - Update the mapping dictionary and error code dictionaries.
  - Ensure all docstrings, comments, and examples follow the established pattern.
- Continue automatically through all logical next steps without pausing for permission, unless a user decision is strictly required.
- After each batch, prepare to continue with the next set of functions until the Power Flow Data API section is complete.## HOW TO CONTINUE
- Resume at the next unimplemented Single Element Data API function in `API3.md` (next: `facts_chng_2`).
- For each function:
  - Extract all parameter details, error codes, and official documentation from `API3.md`.
  - Implement both canonical and human-readable functions as described above.
  - Update the mapping dictionary and error code dictionaries.
  - Ensure all docstrings, comments, and examples follow the established pattern.
- Continue automatically through all logical next steps without pausing for permission, unless a user decision is strictly required.
- After each batch, prepare to continue with the next set of functions until the Power Flow Data API section is complete.  
## LESSONS LEARNED
- Do not invent or remove real API functions; enumerate and implement only those found in the official documentation.
- Canonical example calls must use variable names, not fake data.
- Do not store or pass around merged arrays internally; always unpack to named arguments for the human-readable function.
- Always update this file with any new requirements or lessons learned before stopping work. 

1. **Function Generation**
   - For each API function, generate two Python functions:
     - The official API function, matching the original signature as closely as possible.
     - A human-readable, Pythonic version with clear parameter names, type annotations, and default values where possible.
   - Both versions must be ready to use (not commented out). The user should be able to comment out either version as desired.
   - Implement bi-directional calls: the official function should call the human-readable version, and vice versa.

2. **Type Annotations & Parameters**
   - Add type annotations for all parameters and return types. Avoid using `Any` unless absolutely necessary.
   - For array parameters (e.g., `intgar`, `realar`, `charar`), list and name the components in the call (e.g., `intgar = [IDE: Optional[int] = 1, AREA: Optional[int] = ?, ...]`).
   - If documentation is unclear about defaults or parameter meanings, search the workspace documentation or the web for clarification.

3. **Error Codes**
   - For every API call, create a constant dictionary of error codes and their messages (e.g., `ERROR_CODES_FUNCTION = {...}`).

4. **Documentation**
   - Each function must have a robust docstring, including the full API call for reference.
   - At the end of each API call block, paste the exact wording from the official API documentation as a comment for reference.

5. **Architecture & Best Practices**
   - Code must be modular, extensible, and follow SOLID, DRY, and other best practices.
   - Use interfaces, factories, registries, or plugin loaders to abstract dependencies and support flexible composition.
   - Favor composition over inheritance. Avoid hardcoded dependencies.
   - Components must be swappable without modifying import paths or requiring exact format compatibility.
   - Support mocking and dependency injection by default.
   - Include robust error handling and logging (write all logs/results to structured log files, not just the terminal).
   - All new code must be accompanied by robust, readable tests.

6. **Automation**
   - Proceed automatically through all logical next steps without waiting for user approval, unless a user decision is strictly required.
   - After any terminal command (tests, linters, etc.), automatically check the log output and fix any detected errors or warnings before continuing.
   - Always verify that the code is present in the file after writing, before proceeding to the next batch of commands.

7. **Formatting & Naming**
   - Use descriptive, unambiguous names for all identifiers. Avoid generic names like `temp`, `misc`, or `data`.
   - Document every class, method, and complex block with clear docstrings or inline comments.
   - Follow standard formatting and linting tools (e.g., `black`, `flake8`).
   - All functions must have clear type annotations, including return types.

8. **Official Documentation**
   - Always include the exact official documentation block for each API function as a comment at the end of the function definition.

9. **Mapping**
   - Maintain a mapping dictionary at the top of the file linking official API names to their human-readable versions and error code dicts.

10. **General**
    - If you encounter missing or unclear documentation, search the workspace and/or the web for clarification.
    - Never leave commented-out code in the codebase.
    - Ensure all code is atomic, test-passing, and clearly documented.

This process should be repeated for each API function, in batches if necessary, until the entire API is covered. Always verify your work and proceed automatically to the next logical step. 
