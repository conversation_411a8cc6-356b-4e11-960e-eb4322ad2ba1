#!/usr/bin/env python3
"""
Comprehensive Backend Test Suite
================================

This script runs modeling consistency tests on all available backends
and provides a comprehensive summary of results.

Tests performed:
- HDB Backend with hdbcontext_original.hdb
- RAWX Backend with savnw_nb.rawx
- Consistency verification for each backend
- Cross-backend comparison analysis
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Tuple


class BackendTestSuite:
    """Comprehensive test suite for all backend types."""
    
    def __init__(self, test_dir: str = "test_modeling_consistency"):
        """Initialize the test suite."""
        self.test_dir = Path(test_dir)
        self.script_dir = Path(__file__).parent
        self.parent_dir = self.script_dir.parent
        
        # Test configurations
        self.test_configs = [
            {
                'backend_type': 'hdb',
                'input_file': '../hdbcontext_original.hdb',
                'description': 'HDB Backend - Large hierarchical database file'
            },
            {
                'backend_type': 'rawx',
                'input_file': '../savnw_nb.rawx',
                'description': 'RAWX Backend - Node-breaker RAWX file'
            }
        ]
        
        # Results storage
        self.test_results: Dict[str, Dict[str, Any]] = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print(f"🧪 Backend Test Suite")
        print(f"   Test directory: {self.test_dir}")
        print(f"   Test timestamp: {self.timestamp}")
    
    def run_single_backend_test(self, config: Dict[str, str]) -> Dict[str, Any]:
        """Run consistency test for a single backend."""
        backend_type = config['backend_type']
        input_file = config['input_file']
        description = config['description']
        
        print(f"\n🔬 Testing {backend_type.upper()} Backend")
        print(f"   {description}")
        print(f"   Input file: {input_file}")
        
        # Construct command
        cmd = [
            sys.executable,
            'test_backend_modeling_consistency.py',
            input_file,
            '--backend-type', backend_type,
            '--output-dir', str(self.test_dir)
        ]
        
        try:
            # Run the test
            result = subprocess.run(
                cmd,
                cwd=str(self.script_dir),
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            success = result.returncode == 0
            
            print(f"   Result: {'✅ PASSED' if success else '❌ FAILED'}")
            
            if not success:
                print(f"   Error output:")
                error_lines = result.stderr.split('\n')
                for line in error_lines[-10:]:  # Show last 10 lines of error
                    if line.strip():
                        print(f"     {line}")
            
            return {
                'backend_type': backend_type,
                'success': success,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'input_file': input_file,
                'description': description
            }
            
        except subprocess.TimeoutExpired:
            print(f"   ❌ TIMEOUT - Test exceeded 5 minutes")
            return {
                'backend_type': backend_type,
                'success': False,
                'return_code': -1,
                'stdout': '',
                'stderr': 'Test timeout after 5 minutes',
                'input_file': input_file,
                'description': description
            }
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
            return {
                'backend_type': backend_type,
                'success': False,
                'return_code': -2,
                'stdout': '',
                'stderr': str(e),
                'input_file': input_file,
                'description': description
            }
    
    def analyze_test_outputs(self) -> Dict[str, Any]:
        """Analyze test outputs and extract key metrics."""
        analysis = {
            'total_backends_tested': len(self.test_results),
            'successful_backends': 0,
            'failed_backends': 0,
            'backend_summaries': {},
            'output_files_found': [],
            'verification_reports': []
        }
        
        # Count successes and failures
        for backend_type, result in self.test_results.items():
            if result['success']:
                analysis['successful_backends'] += 1
            else:
                analysis['failed_backends'] += 1
        
        # Find output files
        if self.test_dir.exists():
            json_files = list(self.test_dir.glob("canonical_*.json"))
            report_files = list(self.test_dir.glob("*_report_*.txt"))
            
            analysis['output_files_found'] = [f.name for f in json_files]
            analysis['verification_reports'] = [f.name for f in report_files]
        
        # Extract key metrics from each backend
        for backend_type, result in self.test_results.items():
            summary = {
                'success': result['success'],
                'modeling_approaches_tested': 0,
                'total_records': 0,
                'file_sizes': [],
                'consistency_verified': False
            }
            
            if result['success']:
                stdout = result['stdout']
                
                # Extract modeling approaches
                if 'Testing 3 modeling approaches' in stdout:
                    summary['modeling_approaches_tested'] = 3
                
                # Extract record counts (look for "Total records:")
                import re
                record_matches = re.findall(r'Total records: ([\d,]+)', stdout)
                if record_matches:
                    # Parse the last record count (should be consistent across approaches)
                    try:
                        total_records = int(record_matches[-1].replace(',', ''))
                        summary['total_records'] = total_records
                    except ValueError:
                        pass
                
                # Extract file sizes
                size_matches = re.findall(r'File size: ([\d,]+) bytes', stdout)
                summary['file_sizes'] = [int(size.replace(',', '')) for size in size_matches]
                
                # Check for verification success
                if 'All files are identical (hash and content)' in stdout:
                    summary['consistency_verified'] = True
            
            analysis['backend_summaries'][backend_type] = summary
        
        return analysis
    
    def generate_comprehensive_report(self, analysis: Dict[str, Any]) -> Path:
        """Generate comprehensive test report."""
        report_file = self.test_dir / f"comprehensive_test_report_{self.timestamp}.txt"
        
        self.test_dir.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Comprehensive Backend Test Suite Report\n")
            f.write("=" * 45 + "\n\n")
            
            f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Test Directory: {self.test_dir}\n")
            f.write(f"Total Backends Tested: {analysis['total_backends_tested']}\n\n")
            
            f.write("OVERALL RESULTS:\n")
            f.write("-" * 16 + "\n")
            f.write(f"Successful Backends: {analysis['successful_backends']}\n")
            f.write(f"Failed Backends: {analysis['failed_backends']}\n")
            f.write(f"Success Rate: {100 * analysis['successful_backends'] / max(1, analysis['total_backends_tested']):.1f}%\n\n")
            
            f.write("BACKEND DETAILS:\n")
            f.write("-" * 16 + "\n")
            for backend_type, result in self.test_results.items():
                f.write(f"\n{backend_type.upper()} Backend:\n")
                f.write(f"  Status: {'✅ PASSED' if result['success'] else '❌ FAILED'}\n")
                f.write(f"  Input File: {result['input_file']}\n")
                f.write(f"  Description: {result['description']}\n")
                
                if result['success']:
                    summary = analysis['backend_summaries'][backend_type]
                    f.write(f"  Modeling Approaches Tested: {summary['modeling_approaches_tested']}\n")
                    f.write(f"  Total Records: {summary['total_records']:,}\n")
                    f.write(f"  Consistency Verified: {'✅ YES' if summary['consistency_verified'] else '❌ NO'}\n")
                    if summary['file_sizes']:
                        avg_size = sum(summary['file_sizes']) / len(summary['file_sizes'])
                        f.write(f"  Average Output Size: {avg_size:,.0f} bytes\n")
                else:
                    f.write(f"  Error: {result['stderr'][:200]}...\n")
            
            f.write(f"\nOUTPUT FILES GENERATED:\n")
            f.write("-" * 23 + "\n")
            for filename in analysis['output_files_found']:
                f.write(f"  {filename}\n")
            
            f.write(f"\nVERIFICATION REPORTS:\n")
            f.write("-" * 20 + "\n")
            for filename in analysis['verification_reports']:
                f.write(f"  {filename}\n")
            
            f.write(f"\nTEST CONFIGURATIONS:\n")
            f.write("-" * 19 + "\n")
            for config in self.test_configs:
                f.write(f"  {config['backend_type'].upper()}: {config['input_file']}\n")
                f.write(f"    {config['description']}\n")
        
        return report_file
    
    def run_all_tests(self) -> bool:
        """Run all backend tests and generate comprehensive report."""
        print(f"\n🚀 STARTING COMPREHENSIVE BACKEND TEST SUITE")
        print(f"=" * 60)
        
        # Clean up test directory
        if self.test_dir.exists():
            import shutil
            shutil.rmtree(self.test_dir)
        
        self.test_dir.mkdir(exist_ok=True)
        
        # Run tests for each backend
        for config in self.test_configs:
            result = self.run_single_backend_test(config)
            self.test_results[config['backend_type']] = result
        
        # Analyze results
        analysis = self.analyze_test_outputs()
        
        # Generate comprehensive report
        report_file = self.generate_comprehensive_report(analysis)
        
        # Print summary
        print(f"\n🎯 FINAL SUMMARY:")
        print(f"=" * 20)
        print(f"Backends tested: {analysis['total_backends_tested']}")
        print(f"Successful: {analysis['successful_backends']}")
        print(f"Failed: {analysis['failed_backends']}")
        print(f"Success rate: {100 * analysis['successful_backends'] / max(1, analysis['total_backends_tested']):.1f}%")
        
        # Show backend-specific results
        for backend_type, summary in analysis['backend_summaries'].items():
            status = "✅ CONSISTENT" if summary['consistency_verified'] else "❌ INCONSISTENT" 
            print(f"{backend_type.upper()} Backend: {status}")
            if summary['total_records'] > 0:
                print(f"  Records processed: {summary['total_records']:,}")
        
        print(f"\n📋 Comprehensive report saved to: {report_file}")
        
        # Return overall success
        return analysis['failed_backends'] == 0
    
    def cleanup_test_files(self):
        """Clean up test files after completion."""
        if self.test_dir.exists():
            # Keep only the comprehensive report
            report_files = list(self.test_dir.glob("comprehensive_test_report_*.txt"))
            
            if report_files:
                # Remove all files except the comprehensive report
                for file_path in self.test_dir.iterdir():
                    if file_path.is_file() and file_path not in report_files:
                        file_path.unlink()
                
                print(f"🧹 Cleaned up test files, kept comprehensive report")


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Run comprehensive backend test suite')
    parser.add_argument('--output-dir', '-o', default='test_modeling_consistency',
                       help='Output directory for test results')
    parser.add_argument('--keep-files', '-k', action='store_true',
                       help='Keep all test output files (default: cleanup after test)')
    
    args = parser.parse_args()
    
    try:
        # Run the test suite
        suite = BackendTestSuite(args.output_dir)
        success = suite.run_all_tests()
        
        # Clean up files unless requested to keep them
        if not args.keep_files:
            suite.cleanup_test_files()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ Error running test suite: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main() 