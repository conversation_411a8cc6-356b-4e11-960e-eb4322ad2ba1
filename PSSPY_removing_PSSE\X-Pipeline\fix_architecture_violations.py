#!/usr/bin/env python3
"""
Fix Architecture Violations
===========================

This script fixes the critical architectural violations identified:
1. Remove all _get_mapped_record() calls (violates Master Objective #5)
2. Ensure direct dictionary access using canonical field names
3. Fix bitwise comparison issues with reference files
"""

import re
import os

def fix_mapped_record_violations():
    """Remove _get_mapped_record calls and replace with direct dictionary access."""
    
    pipeline_file = "hdb_to_raw_pipeline.py"
    
    if not os.path.exists(pipeline_file):
        print(f"❌ Pipeline file not found: {pipeline_file}")
        return False
    
    print("🔧 Fixing _get_mapped_record violations...")
    
    # Read the file
    with open(pipeline_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create backup
    backup_file = f"{pipeline_file}.backup_arch_fix"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"💾 Backup created: {backup_file}")
    
    # Count violations before fix
    violations_before = len(re.findall(r'_get_mapped_record', content))
    print(f"📊 Found {violations_before} _get_mapped_record violations")
    
    # Fix 1: Remove the _get_mapped_record method definition
    method_pattern = r'def _get_mapped_record\(self, section_type: str, record: list, fields: list\) -> Dict\[str, Any\]:.*?(?=def|\Z)'
    content = re.sub(method_pattern, '', content, flags=re.DOTALL)
    
    # Fix 2: Replace all _get_mapped_record calls with direct access
    # Pattern: mapped_record = self._get_mapped_record('section', record, fields)
    # Replace with: mapped_record = record (since record is already a dictionary)
    call_pattern = r'mapped_record = self\._get_mapped_record\([^)]+\)'
    content = re.sub(call_pattern, 'mapped_record = record  # Direct dictionary access - architectural compliance', content)
    
    # Verify fixes
    violations_after = len(re.findall(r'_get_mapped_record', content))
    
    # Write fixed file
    with open(pipeline_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Fixed {violations_before - violations_after} violations")
    print(f"📊 Remaining violations: {violations_after}")
    
    return violations_after == 0

def verify_dictionary_architecture():
    """Verify the pipeline uses proper dictionary architecture."""
    
    pipeline_file = "hdb_to_raw_pipeline.py"
    
    with open(pipeline_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n🔍 Verifying Dictionary Architecture...")
    
    # Check for dictionary access patterns
    dict_access_count = len(re.findall(r'record\.get\(', content))
    list_access_count = len(re.findall(r'record\[\d+\]', content))
    
    print(f"📊 Dictionary access patterns: {dict_access_count}")
    print(f"📊 List access patterns: {list_access_count}")
    
    if dict_access_count > list_access_count:
        print("✅ Dictionary architecture is dominant")
        return True
    else:
        print("⚠️  List access patterns still present")
        return False

def test_pipeline_syntax():
    """Test that the pipeline has valid syntax after fixes."""
    
    pipeline_file = "hdb_to_raw_pipeline.py"
    
    print("\n🧪 Testing Pipeline Syntax...")
    
    import subprocess
    import sys
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', pipeline_file
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Pipeline syntax is valid")
            return True
        else:
            print("❌ Pipeline syntax error:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Syntax test failed: {e}")
        return False

def analyze_export_differences():
    """Analyze why exports don't match reference files."""
    
    print("\n📊 Analyzing Export Differences...")
    
    # Look for reference files
    reference_files = []
    output_files = []
    
    for filename in os.listdir('.'):
        if filename.endswith('.raw'):
            if any(ref in filename.lower() for ref in ['reference', 'psse_33', 'savnw_nb']):
                reference_files.append(filename)
            elif 'export' in filename.lower():
                output_files.append(filename)
    
    print(f"📁 Reference files found: {reference_files}")
    print(f"📁 Export files found: {output_files}")
    
    if not reference_files:
        print("⚠️  No clear reference files found")
        print("📋 Expected reference files:")
        print("   - savnw_nb.raw (for RAWX comparison)")
        print("   - psse_33.raw (for HDB comparison)")
        return False
    
    # Analyze file sizes
    for ref_file in reference_files:
        if os.path.exists(ref_file):
            size = os.path.getsize(ref_file)
            with open(ref_file, 'r') as f:
                lines = len(f.readlines())
            print(f"📊 {ref_file}: {size:,} bytes, {lines:,} lines")
    
    return True

def create_reference_comparison_test():
    """Create a test to compare exports with reference files."""
    
    test_script = """#!/usr/bin/env python3
'''
Reference File Comparison Test
=============================

This script runs the pipeline and compares output with reference files
to identify exact differences for bitwise matching.
'''

import os
import subprocess
import sys

def run_pipeline_and_compare():
    '''Run pipeline and compare with reference files.'''
    
    print("🧪 Running Pipeline and Comparing with Reference Files")
    print("=" * 60)
    
    # Test files
    test_files = ['savnw_nb.rawx', 'sample_nb.rawx']
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"⚠️  Test file not found: {test_file}")
            continue
        
        print(f"\\n📁 Testing: {test_file}")
        
        # Run pipeline
        try:
            result = subprocess.run([
                sys.executable, 'hdb_to_raw_pipeline.py', test_file
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ Pipeline completed successfully")
                
                # Look for output files
                output_dir = 'output_demo'
                if os.path.exists(output_dir):
                    output_files = [f for f in os.listdir(output_dir) if f.endswith('.raw')]
                    print(f"📄 Output files: {output_files}")
                    
                    # Compare with reference if available
                    for output_file in output_files:
                        output_path = os.path.join(output_dir, output_file)
                        compare_with_reference(output_path)
                else:
                    print("⚠️  Output directory not found")
            else:
                print("❌ Pipeline failed:")
                print(result.stderr[:500])
                
        except Exception as e:
            print(f"❌ Test failed: {e}")

def compare_with_reference(output_file):
    '''Compare output file with reference.'''
    
    print(f"🔍 Analyzing: {os.path.basename(output_file)}")
    
    if not os.path.exists(output_file):
        print("❌ Output file not found")
        return
    
    # Get file stats
    size = os.path.getsize(output_file)
    with open(output_file, 'r') as f:
        lines = f.readlines()
    
    print(f"📊 Size: {size:,} bytes, Lines: {len(lines):,}")
    
    # Show first few lines for format analysis
    print("📋 First 5 lines:")
    for i, line in enumerate(lines[:5]):
        print(f"  {i+1}: {line.strip()}")
    
    # Look for potential reference file
    base_name = os.path.basename(output_file)
    potential_refs = ['savnw_nb.raw', 'psse_33.raw']
    
    for ref_file in potential_refs:
        if os.path.exists(ref_file):
            print(f"🔍 Comparing with reference: {ref_file}")
            compare_files_detailed(output_file, ref_file)
            break

def compare_files_detailed(file1, file2):
    '''Detailed file comparison.'''
    
    with open(file1, 'r') as f1, open(file2, 'r') as f2:
        lines1 = f1.readlines()
        lines2 = f2.readlines()
    
    print(f"📊 Lines: {len(lines1)} vs {len(lines2)}")
    
    # Compare line by line (first 10 differences)
    differences = []
    for i, (line1, line2) in enumerate(zip(lines1, lines2)):
        if line1.strip() != line2.strip():
            differences.append((i+1, line1.strip(), line2.strip()))
            if len(differences) >= 10:
                break
    
    if differences:
        print(f"❌ Found {len(differences)} differences (showing first 10):")
        for line_num, l1, l2 in differences:
            print(f"  Line {line_num}:")
            print(f"    Output: {l1[:100]}")
            print(f"    Reference: {l2[:100]}")
    else:
        print("✅ Files are identical!")

if __name__ == "__main__":
    run_pipeline_and_compare()
"""
    
    with open("reference_comparison_test.py", "w") as f:
        f.write(test_script)
    
    print("📝 Created reference_comparison_test.py")

def main():
    """Main execution function."""
    
    print("🚀 Fixing Architectural Violations")
    print("=" * 50)
    
    # Step 1: Fix _get_mapped_record violations
    print("🔧 Step 1: Fixing _get_mapped_record violations")
    if fix_mapped_record_violations():
        print("✅ _get_mapped_record violations fixed")
    else:
        print("❌ Failed to fix _get_mapped_record violations")
        return False
    
    # Step 2: Verify dictionary architecture
    print("\\n🔍 Step 2: Verifying dictionary architecture")
    if verify_dictionary_architecture():
        print("✅ Dictionary architecture verified")
    else:
        print("⚠️  Dictionary architecture needs improvement")
    
    # Step 3: Test syntax
    print("\\n🧪 Step 3: Testing pipeline syntax")
    if test_pipeline_syntax():
        print("✅ Pipeline syntax is valid")
    else:
        print("❌ Pipeline has syntax errors")
        return False
    
    # Step 4: Analyze export differences
    print("\\n📊 Step 4: Analyzing export differences")
    analyze_export_differences()
    
    # Step 5: Create comparison test
    print("\\n📝 Step 5: Creating reference comparison test")
    create_reference_comparison_test()
    
    print("\\n" + "=" * 50)
    print("🏁 ARCHITECTURAL FIXES COMPLETE")
    print("=" * 50)
    print("✅ Architecture violations fixed")
    print("✅ Pipeline syntax validated")
    print("📋 Next: Run reference_comparison_test.py to identify export differences")
    print("🎯 Goal: Achieve bitwise identical exports with reference files")
    
    return True

if __name__ == "__main__":
    main() 