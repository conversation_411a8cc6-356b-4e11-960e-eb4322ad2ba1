"""
Analyze converter performance and identify optimization opportunities.
This script examines the performance tracking data to find slow converters.
"""

import os
from typing import Dict, List, Any
from performance_tracker import get_performance_tracker

def analyze_converter_performance():
    """Analyze converter performance and identify optimization opportunities."""
    print("🔍 Analyzing Converter Performance...")
    
    tracker = get_performance_tracker()
    
    # Check if performance file exists
    if not os.path.exists(tracker.performance_file):
        print(f"❌ Performance tracking file not found: {tracker.performance_file}")
        print("   Run the pipeline first to generate performance data.")
        return
    
    # Get historical performance data
    historical_data = tracker.get_historical_performance()
    
    if not historical_data:
        print("❌ No performance data found.")
        print("   Run the pipeline first to generate performance data.")
        return
    
    print(f"📊 Found {len(historical_data)} performance records")
    
    # Group by converter
    converter_stats = {}
    for record in historical_data:
        conv_name = record['converter_name']
        if conv_name not in converter_stats:
            converter_stats[conv_name] = {
                'executions': [],
                'total_time': 0,
                'total_records': 0,
                'avg_time': 0,
                'avg_rate': 0,
                'max_time': 0,
                'min_time': float('inf')
            }
        
        stats = converter_stats[conv_name]
        stats['executions'].append(record)
        stats['total_time'] += record['execution_time']
        stats['total_records'] += record['record_count']
        stats['max_time'] = max(stats['max_time'], record['execution_time'])
        stats['min_time'] = min(stats['min_time'], record['execution_time'])
    
    # Calculate averages
    for conv_name, stats in converter_stats.items():
        executions = stats['executions']
        if executions:
            stats['avg_time'] = stats['total_time'] / len(executions)
            stats['avg_rate'] = sum(e['records_per_second'] for e in executions) / len(executions)
            stats['min_time'] = stats['min_time'] if stats['min_time'] != float('inf') else 0
    
    # Sort by average time (slowest first)
    sorted_converters = sorted(converter_stats.items(), key=lambda x: x[1]['avg_time'], reverse=True)
    
    print(f"\n📈 CONVERTER PERFORMANCE ANALYSIS")
    print("=" * 80)
    print(f"{'Converter':<25} {'Avg Time':<10} {'Max Time':<10} {'Avg Rate':<12} {'Total Records':<12} {'Runs':<5}")
    print("-" * 80)
    
    slow_converters = []
    fast_converters = []
    
    for conv_name, stats in sorted_converters:
        avg_time = stats['avg_time']
        max_time = stats['max_time']
        avg_rate = stats['avg_rate']
        total_records = stats['total_records']
        runs = len(stats['executions'])
        
        print(f"{conv_name:<25} {avg_time:<10.3f} {max_time:<10.3f} {avg_rate:<12.1f} {total_records:<12} {runs:<5}")
        
        # Classify converters
        if avg_time > 1.0 or (avg_rate < 100 and avg_rate > 0):
            slow_converters.append((conv_name, stats))
        else:
            fast_converters.append((conv_name, stats))
    
    # Performance recommendations
    print(f"\n🚀 OPTIMIZATION RECOMMENDATIONS")
    print("=" * 80)
    
    if slow_converters:
        print(f"⚠️  SLOW CONVERTERS NEEDING OPTIMIZATION ({len(slow_converters)}):")
        for conv_name, stats in slow_converters:
            avg_time = stats['avg_time']
            avg_rate = stats['avg_rate']
            max_records = max(e['record_count'] for e in stats['executions'])
            
            print(f"\n🐌 {conv_name}:")
            print(f"   Average time: {avg_time:.3f}s")
            print(f"   Processing rate: {avg_rate:.1f} records/sec")
            print(f"   Max records processed: {max_records}")
            
            # Specific recommendations
            if avg_time > 5.0:
                print(f"   🔥 CRITICAL: Very slow execution - needs major optimization")
            elif avg_time > 1.0:
                print(f"   ⚠️  WARNING: Slow execution - consider optimization")
            
            if avg_rate < 50 and max_records > 100:
                print(f"   💡 SUGGESTION: Low processing rate suggests nested loops")
            
            if max_records > 1000 and avg_time > 2.0:
                print(f"   💡 SUGGESTION: Large dataset + slow processing → implement lookup tables")
            
            print(f"   📝 RECOMMENDED OPTIMIZATIONS:")
            print(f"      - Replace nested loops with lookup tables")
            print(f"      - Add caching for expensive operations")
            print(f"      - Pre-build data structures in __init__")
            print(f"      - Use direct dictionary lookups instead of iteration")
    else:
        print("✅ No slow converters found - all converters are performing well!")
    
    if fast_converters:
        print(f"\n✅ FAST CONVERTERS ({len(fast_converters)}):")
        for conv_name, stats in fast_converters[:5]:  # Show top 5
            print(f"   {conv_name}: {stats['avg_time']:.3f}s avg, {stats['avg_rate']:.1f} records/sec")
    
    # Get optimization recommendations from tracker
    recommendations = tracker.get_optimization_recommendations()
    if recommendations:
        print(f"\n🎯 ADDITIONAL RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"   - {rec}")
    
    print(f"\n📊 SUMMARY:")
    print(f"   Total converters analyzed: {len(converter_stats)}")
    print(f"   Slow converters: {len(slow_converters)}")
    print(f"   Fast converters: {len(fast_converters)}")
    print(f"   Total performance records: {len(historical_data)}")

def main():
    """Run the converter performance analysis."""
    analyze_converter_performance()

if __name__ == "__main__":
    main() 