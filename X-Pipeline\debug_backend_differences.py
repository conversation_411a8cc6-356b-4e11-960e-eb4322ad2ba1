#!/usr/bin/env python3
"""
Debug script to understand differences between HDB and RAWX backend canonical outputs
and their behavior with ModelTransformations.
"""

import json
import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from hdb_to_raw_pipeline import (
    Backend, HdbBackend, RawxBackend, ModelTransformations,
    TransformationResult
)

def debug_backend_canonical_format():
    """Compare canonical formats produced by HDB vs RAWX backends."""
    
    print("🔍 Debugging Backend Canonical Format Differences")
    print("=" * 60)
    
    # File paths - adjust these as needed
    hdb_file = "hdbcontext_original.hdb"  # Adjust path as needed
    rawx_file = "sample_nb.rawx"          # Adjust path as needed
    
    results = {}
    
    # Test HDB Backend
    if os.path.exists(hdb_file):
        print(f"\n📂 Testing HDB Backend: {hdb_file}")
        try:
            hdb_backend = HdbBackend(file_path=hdb_file)
            hdb_canonical = hdb_backend.to_canonical()
            
            print(f"✅ HDB canonical sections: {list(hdb_canonical.keys())}")
            
            # Check model type detection
            hdb_model_type = ModelTransformations.detect_model_type(hdb_canonical)
            print(f"🔍 HDB detected model type: {hdb_model_type}")
            
            # Try hybrid conversion
            if hdb_model_type == 'node_breaker':
                print(f"🔄 Testing HDB hybrid conversion...")
                hdb_hybrid_result = ModelTransformations.convert_to_hybrid_modeling(hdb_canonical)
                print(f"✅ HDB hybrid conversion: {hdb_hybrid_result.success}")
                if not hdb_hybrid_result.success:
                    print(f"❌ HDB hybrid errors: {hdb_hybrid_result.errors}")
                else:
                    hybrid_sections = list(hdb_hybrid_result.canonical_data.keys()) if hdb_hybrid_result.canonical_data else []
                    print(f"📊 HDB hybrid sections: {hybrid_sections}")
            
            results['hdb'] = {
                'sections': list(hdb_canonical.keys()),
                'model_type': hdb_model_type,
                'hybrid_success': hdb_hybrid_result.success if hdb_model_type == 'node_breaker' else 'N/A'
            }
            
        except Exception as e:
            print(f"❌ HDB backend error: {e}")
            results['hdb'] = {'error': str(e)}
    else:
        print(f"⚠️  HDB file not found: {hdb_file}")
        results['hdb'] = {'error': 'File not found'}
    
    # Test RAWX Backend
    if os.path.exists(rawx_file):
        print(f"\n📂 Testing RAWX Backend: {rawx_file}")
        try:
            rawx_backend = RawxBackend(file_path=rawx_file)
            rawx_canonical = rawx_backend.to_canonical()
            
            print(f"✅ RAWX canonical sections: {list(rawx_canonical.keys())}")
            
            # Check model type detection
            rawx_model_type = ModelTransformations.detect_model_type(rawx_canonical)
            print(f"🔍 RAWX detected model type: {rawx_model_type}")
            
            # Try hybrid conversion
            if rawx_model_type == 'node_breaker':
                print(f"🔄 Testing RAWX hybrid conversion...")
                rawx_hybrid_result = ModelTransformations.convert_to_hybrid_modeling(rawx_canonical)
                print(f"✅ RAWX hybrid conversion: {rawx_hybrid_result.success}")
                if not rawx_hybrid_result.success:
                    print(f"❌ RAWX hybrid errors: {rawx_hybrid_result.errors}")
                else:
                    hybrid_sections = list(rawx_hybrid_result.canonical_data.keys()) if rawx_hybrid_result.canonical_data else []
                    print(f"📊 RAWX hybrid sections: {hybrid_sections}")
            
            results['rawx'] = {
                'sections': list(rawx_canonical.keys()),
                'model_type': rawx_model_type,
                'hybrid_success': rawx_hybrid_result.success if rawx_model_type == 'node_breaker' else 'N/A'
            }
            
        except Exception as e:
            print(f"❌ RAWX backend error: {e}")
            results['rawx'] = {'error': str(e)}
    else:
        print(f"⚠️  RAWX file not found: {rawx_file}")
        results['rawx'] = {'error': 'File not found'}
    
    # Comparison Analysis
    print(f"\n" + "=" * 60)
    print(f"📊 COMPARISON ANALYSIS")
    print(f"=" * 60)
    
    if 'hdb' in results and 'rawx' in results:
        hdb_data = results['hdb']
        rawx_data = results['rawx']
        
        if 'error' not in hdb_data and 'error' not in rawx_data:
            print(f"\n🔍 Section Comparison:")
            hdb_sections = set(hdb_data['sections'])
            rawx_sections = set(rawx_data['sections'])
            
            common_sections = hdb_sections & rawx_sections
            hdb_only = hdb_sections - rawx_sections
            rawx_only = rawx_sections - hdb_sections
            
            print(f"   Common sections: {sorted(common_sections)}")
            print(f"   HDB only: {sorted(hdb_only)}")
            print(f"   RAWX only: {sorted(rawx_only)}")
            
            print(f"\n🔍 Model Type Comparison:")
            print(f"   HDB model type: {hdb_data['model_type']}")
            print(f"   RAWX model type: {rawx_data['model_type']}")
            print(f"   Types match: {hdb_data['model_type'] == rawx_data['model_type']}")
            
            print(f"\n🔍 Hybrid Conversion Comparison:")
            print(f"   HDB hybrid success: {hdb_data['hybrid_success']}")
            print(f"   RAWX hybrid success: {rawx_data['hybrid_success']}")
            
            if hdb_data['hybrid_success'] != rawx_data['hybrid_success']:
                print(f"❌ ISSUE FOUND: Different hybrid conversion results!")
                print(f"   This indicates the backends are producing incompatible canonical formats")
            else:
                print(f"✅ Both backends have same hybrid conversion result")
    
    # Save detailed results
    results_file = "debug_backend_comparison.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    return results

def inspect_canonical_data_structure():
    """Inspect the structure of canonical data from both backends."""
    
    print(f"\n🔍 Inspecting Canonical Data Structure")
    print("=" * 50)
    
    # Check available files
    hdb_file = "hdbcontext_original.hdb"
    rawx_file = "sample_nb.rawx"
    
    for backend_name, file_path in [("HDB", hdb_file), ("RAWX", rawx_file)]:
        if os.path.exists(file_path):
            print(f"\n📂 {backend_name} Backend Structure:")
            try:
                if backend_name == "HDB":
                    backend = HdbBackend(file_path=file_path)
                else:
                    backend = RawxBackend(file_path=file_path)
                
                canonical = backend.to_canonical()
                
                # Show structure of key sections
                key_sections = ['bus', 'node', 'substation', 'subterm', 'ac_line']
                for section in key_sections:
                    if section in canonical:
                        section_data = canonical[section]
                        if isinstance(section_data, dict):
                            record_count = len(section_data.get('data', []))
                            fields = section_data.get('fields', [])
                            print(f"   {section}: {record_count} records, fields: {fields[:5]}{'...' if len(fields) > 5 else ''}")
                        else:
                            print(f"   {section}: {type(section_data)} (not standard format)")
                    else:
                        print(f"   {section}: Not present")
                        
            except Exception as e:
                print(f"❌ Error inspecting {backend_name}: {e}")

def main():
    """Main diagnostic function."""
    
    print("🚀 Backend Diagnostic Tool")
    print("Comparing HDB vs RAWX canonical format generation and ModelTransformations")
    
    # Run diagnostics
    results = debug_backend_canonical_format()
    inspect_canonical_data_structure()
    
    print(f"\n🎯 Diagnostic Complete!")
    print(f"Check the output above and debug_backend_comparison.json for detailed analysis")

if __name__ == "__main__":
    main() 