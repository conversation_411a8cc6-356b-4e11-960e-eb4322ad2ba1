"""
HDB (Hierarchical Database) Backend implementation.

This backend handles HDB format data with support for multiple modeling approaches
and automatic conversion between them.
"""
import json
import logging
import os
import copy
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from ..base_backend import BaseBackend, ModelingApproach, SystemState
# Use clean separation: field mapping + business logic
from ..field_mapping_only import PureFieldMapper
from .hdb_business_logic import HdbBusinessLogic

class HdbBackend(BaseBackend):
    """
    Backend for handling HDB (Hierarchical Database) format data.
    
    Supports multiple modeling approaches and conversion between them:
    - Bus-Branch: Traditional PSS/E format
    - Node-Breaker: Full substation model with nodes and switching devices
    - Hybrid Bus-Breaker: Full substation using buses/branches with zero impedance
    
    Also supports system state management for operational vs normal configurations.
    """
    
    def __init__(self, enable_quality_check: bool = True, auto_fix: bool = True,
                 modeling_approach: Optional[ModelingApproach] = None,
                 system_state: SystemState = SystemState.MAINTAIN_CURRENT,
                 file_path: Optional[str] = None):
        """
        Initialize HDB backend.
        
        Args:
            enable_quality_check: Enable data quality validation
            auto_fix: Enable automatic fixing of data quality issues 
            modeling_approach: Power system modeling approach (auto-detected if None)
            system_state: System operational state configuration
            file_path: Path to HDB file to load
        """
        super().__init__(enable_quality_check, auto_fix, modeling_approach, system_state)
        self.data: Dict[str, Any] = {}
        self.file_path: Optional[str] = file_path
        
        # Conversion state tracking
        self._conversion_map: Dict[str, Dict[str, Any]] = {}
        
        # System state backup for restoration
        self._original_data_backup: Optional[Dict[str, Any]] = None
        
        # Initialize clean separation components
        self.field_mapper = PureFieldMapper()
        self.business_logic = HdbBusinessLogic()
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if file_path:
            self.load(file_path)
    
    def to_canonical(self, modeling_approach: ModelingApproach = None) -> Dict[str, Any]:
        """
        Convert HDB data to canonical format for specific modeling approach.
        
        This method orchestrates the conversion using:
        1. HDB converters for equipment conversion
        2. Business logic for HDB-specific rules
        3. Field mapping for data transformation
        4. Bus 0 handling for invalid bus references
        
        Args:
            modeling_approach: Target modeling approach (None to auto-detect)
            
        Returns:
            Canonical format data dictionary
        """
        if modeling_approach is None:
            modeling_approach = self.modeling_approach or ModelingApproach.BUS_BRANCH
            
        self.logger.info(f"Converting HDB to canonical format for {modeling_approach.value} modeling")
        
        # Import HDB converters here to avoid circular imports
        from RawEditor.database.hdb_converters import (
            BusConverter, LoadConverter, GeneratorConverter, 
            LineConverter, TransformerConverter, SwitchingDeviceConverter,
            AreaConverter, OwnerConverter, SubstationConverter,
            ZoneConverter, FixedShuntConverter, SwitchedShuntConverter,
            CircuitBreakerConverter, NodeConverter, BranchConverter,
            ZeroImpedanceBranchConverter
        )
        
        canonical_data = {}
        
        # Convert each equipment type using specialized converters
        # Always use original data backup to ensure fresh conversion each time
        source_data = self._original_data_backup or self.data
        
        # Create converter map based on target modeling approach
        converter_map = self._get_converter_map_for_approach(source_data, modeling_approach)
        
        for equipment_type, converter in converter_map.items():
            if self._has_hdb_data_for_equipment(equipment_type):
                try:
                    result = converter.convert()
                    
                    if result and 'data' in result and result['data']:
                        canonical_data[equipment_type] = result
                        self.logger.info(f"Converted {len(result['data'])} {equipment_type} records")
                    else:
                        self.logger.debug(f"No {equipment_type} data to convert")
                        
                except Exception as e:
                    self.logger.error(f"Error converting {equipment_type}: {e}")
                    continue
        
        # Modeling-specific sections are now handled by converter map
        # No additional processing needed
            
        # Apply system state transformations if needed
        if self._system_state != SystemState.MAINTAIN_CURRENT:
            self._apply_system_state_to_canonical(canonical_data)
        
        return canonical_data
    
    def _get_converter_map_for_approach(self, source_data: Dict[str, Any], 
                                       modeling_approach: ModelingApproach) -> Dict[str, Any]:
        """
        Get converter map based on target modeling approach.
        
        Args:
            source_data: HDB source data
            modeling_approach: Target modeling approach
            
        Returns:
            Dictionary mapping equipment types to converters
        """
        # Import HDB converters here to avoid circular imports
        from RawEditor.database.hdb_converters import (
            BusConverter, LoadConverter, GeneratorConverter, 
            LineConverter, TransformerConverter, SwitchingDeviceConverter,
            AreaConverter, OwnerConverter, SubstationConverter,
            ZoneConverter, FixedShuntConverter, SwitchedShuntConverter,
            CircuitBreakerConverter, NodeConverter, BranchConverter,
            ZeroImpedanceBranchConverter
        )
        
        # Common equipment for all modeling approaches
        common_converters = {
            'load': LoadConverter(source_data), 
            'generator': GeneratorConverter(source_data),
            'ac_line': LineConverter(source_data),
            'transformer': TransformerConverter(source_data),
            'area': AreaConverter(source_data),
            'owner': OwnerConverter(source_data),
            'zone': ZoneConverter(source_data),
            'fixed_shunt': FixedShuntConverter(source_data),
            'switched_shunt': SwitchedShuntConverter(source_data),
            'branch': BranchConverter(source_data),
            'zero_impedance_branch': ZeroImpedanceBranchConverter(source_data)
        }
        
        if modeling_approach == ModelingApproach.NODE_BREAKER:
            # Node-breaker: Buses + full substation topology (most detailed)
            self.logger.info("Creating node-breaker converter map (buses + full substation topology)")
            return {
                **common_converters,
                'bus': BusConverter(source_data),
                'substation': SubstationConverter(source_data),
                'node': NodeConverter(source_data),  # Standard canonical name
                'switching_device': SwitchingDeviceConverter(source_data),
                'circuit_breaker': CircuitBreakerConverter(source_data),
            }
            
        elif modeling_approach == ModelingApproach.BUS_BRANCH:
            # Bus-branch: Buses only (node-breaker without substations)
            self.logger.info("Creating bus-branch converter map (buses only, no substation topology)")
            return {
                **common_converters,
                'bus': BusConverter(source_data),
                # Note: NO substation/node converters - simplified buses only
            }
            
        elif modeling_approach == ModelingApproach.HYBRID_BUS_BREAKER:
            # Hybrid: Create same sections as node-breaker, let Universal Backend convert to hybrid format
            # Universal Backend will convert nodes→buses and switches→branches automatically
            self.logger.info("Creating hybrid converter map (same as node-breaker, Universal Backend will convert)")
            return {
                **common_converters,
                'bus': BusConverter(source_data),
                'node': NodeConverter(source_data),  # Standard canonical name - will be converted to buses
                'switching_device': SwitchingDeviceConverter(source_data),  # Will be converted to branches
                # Note: Universal Backend will handle the hybrid conversion automatically
            }
            
        else:
            # Default to bus-branch
            self.logger.warning(f"Unknown modeling approach {modeling_approach}, defaulting to bus-branch")
            return {
                **common_converters,
                'bus': BusConverter(source_data),
            }
    
    def _has_hdb_data_for_equipment(self, equipment_type: str) -> bool:
        """Check if HDB data contains records for the specified equipment type."""
        equipment_map = {
            'bus': ['bus'],  # Real buses from HDB 'bus' section
            'load': ['load'],
            'generator': ['unit'],
            'ac_line': ['line_segment'],
            'transformer': ['transformer'],
            'switching_device': ['switching_device', 'breaker'],
            'area': ['area'],
            'owner': ['company'],
            'substation': ['substation', 'station'],  # Substations from HDB 'substation' section
            'zone': ['division'],
            'fixed_shunt': ['shunt'],
            'switched_shunt': ['static_var_system'],
            'circuit_breaker': ['circuit_breaker'],
            'node': ['node'],  # Standard canonical name - nodes from HDB 'node' section
            'branch': ['line'],
            'zero_impedance_branch': ['zero_impedance_branches']
        }
        
        # Use original data backup to check for equipment data
        source_data = self._original_data_backup or self.data
        hdb_sections = equipment_map.get(equipment_type, [])
        return any(section in source_data and source_data[section] for section in hdb_sections)
    
    def _add_substation_sections(self, canonical_data: Dict[str, Any], modeling_approach: ModelingApproach) -> None:
        """Add substation, node, and switching device sections for advanced modeling."""
        if 'substation' not in self.data:
            self.logger.warning("No substation data available for node-breaker modeling")
            return
            
        # Process substation data
        if modeling_approach == ModelingApproach.NODE_BREAKER:
            # For node-breaker: include all substation topology
            canonical_data['substation'] = self._convert_hdb_substations()
            canonical_data['node'] = self._convert_hdb_nodes() 
            canonical_data['switching_device'] = self._convert_hdb_switching_devices()
            
        elif modeling_approach == ModelingApproach.HYBRID_BUS_BREAKER:
            # For hybrid: node numbers become bus numbers
            canonical_data['substation'] = self._convert_hdb_substations()
            # In hybrid mode, nodes are represented as buses with node-based numbering
            self._apply_hybrid_bus_numbering(canonical_data)
    
    def _convert_hdb_substations(self) -> Dict[str, Any]:
        """Convert HDB substation data to canonical format."""
        # Implementation would convert substation records
        # For now, return empty structure
        return {'fields': ['number', 'name'], 'data': []}
    
    def _convert_hdb_nodes(self) -> Dict[str, Any]:
        """Convert HDB node data to canonical format.""" 
        # Implementation would convert node records
        # For now, return empty structure
        return {'fields': ['number', 'station', 'name'], 'data': []}
    
    def _convert_hdb_switching_devices(self) -> Dict[str, Any]:
        """Convert HDB switching device data to canonical format."""
        # Implementation would convert switching device records
        # For now, return empty structure
        return {'fields': ['ibus', 'jbus', 'ckt', 'type'], 'data': []}
    
    def _apply_hybrid_bus_numbering(self, canonical_data: Dict[str, Any]) -> None:
        """Apply hybrid bus numbering where node numbers become bus numbers."""
        # This would implement the hybrid-specific bus numbering logic
        # where node numbers are used as bus numbers instead of the HDB Bus Number field
        self.logger.info("Applied hybrid bus numbering scheme")
    
    def _apply_system_state_to_canonical(self, canonical_data: Dict[str, Any]) -> None:
        """Apply system state transformations to canonical data."""
        if self._system_state == SystemState.NORMAL:
            self._reset_canonical_to_normal_state(canonical_data)
        # Other system states would be handled here
    
    def _reset_canonical_to_normal_state(self, canonical_data: Dict[str, Any]) -> None:
        """Reset canonical data to normal operating state."""
        # Implementation would reset equipment status to normal
        self.logger.info("Reset canonical data to normal state")
    
    def load(self, file_path: Union[str, Path]) -> None:
        """Load HDB data from file."""
        file_path = Path(file_path)
        self.logger.info(f"Loading HDB file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Try to parse as JSON
            try:
                loaded_data = json.loads(content)
            except json.JSONDecodeError as e:
                self.logger.error(f"Invalid JSON in file {file_path}: {e}")
                raise
                
            if not isinstance(loaded_data, dict):
                raise ValueError(f"Expected dictionary in HDB file, got {type(loaded_data)}")
                
            # Validate HDB structure
            if loaded_data:
                self.data = loaded_data
                
            self.file_path = str(file_path)
            
            # Create backup for system state restoration
            self._original_data_backup = copy.deepcopy(self.data)
            
            # Detect normal state availability
            self.detect_normal_state_availability()
            
            self.logger.info(f"Successfully loaded HDB data with {len(self.data)} sections")
        except Exception as e:
            self.logger.error(f"Error loading HDB file {file_path}: {e}")
            raise
    
    def save(self, file_path: Union[str, Path]) -> None:
        """Save HDB data to file.
        
        Args:
            file_path: Path to save HDB data to
        """
        file_path = Path(file_path)
        self.logger.info(f"Saving HDB data to {file_path}")
        
        try:
            # Apply quality check before saving if enabled
            data_to_save = self.check_data_quality() if self.enable_quality_check else self.data
            
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2)
            self.logger.info("Successfully saved HDB data")
        except Exception as e:
            self.logger.error(f"Error saving HDB file: {e}")
            raise
    
    def get_data(self) -> Dict[str, Any]:
        """Get all data stored in the backend."""
        return self.data
    
    def add_records(self, section: str, records: List[List[Any]], fields: List[str]) -> None:
        """Add records to a section.
        
        Args:
            section: Section name
            records: List of record data
            fields: List of field names
        """
        # Use quality check if enabled
        if self.enable_quality_check:
            self.add_records_with_quality_check(section, records, fields)
            return
            
        # Call the actual record addition logic
        self._add_records_internal(section, records, fields)
    
    def _add_records_internal(self, section: str, records: List[List[Any]], fields: List[str]) -> None:
        """Internal method to add records without quality checking.
        
        Args:
            section: Section name
            records: List of record data
            fields: List of field names
        """
        if section not in self.data:
            self.data[section] = {'records': {}}
            
        if fields:
            self.data[section]['fields'] = fields
            
        # Convert records to dictionary format
        for record in records:
            if len(record) != len(fields):
                self.logger.warning(f"Record length {len(record)} doesn't match fields length {len(fields)}")
                continue
                
            record_dict = dict(zip(fields, record))
            record_id = self._generate_record_id(section, record_dict)
            self.data[section]['records'][record_id] = record_dict
    
    def get_records(self, section: str) -> Dict[str, Dict[str, Any]]:
        """Get all records from a section.
        
        Args:
            section: Section name
            
        Returns:
            Dictionary of records
        """
        return self.data.get(section, {}).get('records', {})
    
    def get_record(self, section: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific record.
        
        Args:
            section: Section name
            record_id: Record identifier
            
        Returns:
            Record data or None if not found
        """
        return self.data.get(section, {}).get('records', {}).get(record_id)
    
    def update_record(self, section: str, record_id: str, data: Dict[str, Any]) -> None:
        """Update a specific record.
        
        Args:
            section: Section name
            record_id: Record identifier
            data: New record data
        """
        if section not in self.data:
            self.data[section] = {'records': {}}
            
        self.data[section]['records'][record_id] = data
    
    def delete_record(self, section: str, record_id: str) -> None:
        """Delete a specific record.
        
        Args:
            section: Section name
            record_id: Record identifier
        """
        if section in self.data and 'records' in self.data[section]:
            self.data[section]['records'].pop(record_id, None)
    
    def _generate_record_id(self, section: str, record: Dict[str, Any]) -> str:
        """Generate a unique record ID based on section type and record data.
        
        Args:
            section: Section type (e.g., 'node', 'bus', 'line_segment')
            record: Record data dictionary
            
        Returns:
            Unique record ID string
        """
        if section == 'node':
            return str(record.get('Number', ''))
        elif section == 'bus':
            return str(record.get('Bus Number', ''))
        elif section == 'line_segment':
            return f"{record.get('From Node', '')}_{record.get('To Node', '')}_{record.get('Circuit', '1')}"
        elif section == 'transformer':
            return f"{record.get('From Node', '')}_{record.get('To Node', '')}_{record.get('Circuit', '1')}"
        elif section == 'load':
            return f"{record.get('Bus', '')}_{record.get('Id', '1')}"
        elif section == 'unit':
            return f"{record.get('Bus', '')}_{record.get('Id', '1')}"
        else:
            return str(len(self.data.get(section, {}).get('records', {})) + 1)
    
    def _convert_modeling_approach(self, source: ModelingApproach, target: ModelingApproach) -> None:
        """
        Convert data from one modeling approach to another.
        
        Args:
            source: Source modeling approach
            target: Target modeling approach
        """
        self.logger.info(f"Converting HDB data from {source.value} to {target.value}")
        # Implementation would depend on specific conversion requirements
        # For now, log the conversion request
        self.logger.warning("Modeling approach conversion not yet implemented for HDB backend")
    
    def _find_field_index(self, fields: List[str], keywords: List[str]) -> Optional[int]:
        """Find the index of a field in the fields list that contains any of the keywords."""
        for keyword in keywords:
            if keyword in fields:
                return fields.index(keyword)
        return None
    
    def _apply_system_state(self, state: SystemState) -> None:
        """
        Apply the specified system state configuration.
        
        Args:
            state: System state to apply
        """
        if state == SystemState.RESET_TO_NORMAL:
            self._reset_to_normal_state()
        elif state == SystemState.MAINTAIN_CURRENT:
            self._maintain_current_state()
        else:
            self.logger.warning(f"Unknown system state: {state}")
    
    def _reset_to_normal_state(self) -> None:
        """
        Reset all equipment to normal operating positions.
        
        Sets:
        - Normally closed breakers to closed (status = 1)
        - Normally open breakers to open (status = 0)
        - All terminal equipment to online (status = 1)
        """
        if not self._normal_state_available:
            self.logger.warning("Normal state data not available - creating stub implementation")
            self._reset_to_normal_state_stub()
            return
        
        self.logger.info("Resetting system to normal state")
        changes_made = 0
        
        # Reset switching devices
        changes_made += self._reset_switching_devices_to_normal()
        
        # Reset terminal equipment
        changes_made += self._reset_terminal_equipment_to_online()
        
        # Reset branches (for circuit breakers modeled as branches)
        changes_made += self._reset_branches_to_normal()
        
        self.logger.info(f"Reset to normal state complete. {changes_made} changes made.")
    
    def _reset_switching_devices_to_normal(self) -> int:
        """Reset switching devices to their normal positions."""
        changes = 0
        
        switching_sections = ['substation_switching_device', 'system_switching_device']
        for section_name in switching_sections:
            if section_name not in self.data:
                continue
                
            section = self.data[section_name]
            if 'fields' not in section or 'records' not in section:
                continue
                
            fields = section['fields']
            
            # Find relevant field indices
            status_index = self._find_field_index(fields, ['status', 'stat'])
            normal_status_index = self._find_field_index(fields, ['normal_status', 'nstat', 'normal_state'])
            
            if status_index is None or normal_status_index is None:
                self.logger.warning(f"Missing status fields in {section_name}")
                continue
            
            # Update each record
            for record in section['records']:
                if len(record) > max(status_index, normal_status_index):
                    try:
                        normal_status = int(record[normal_status_index])
                        current_status = int(record[status_index])
                        
                        if current_status != normal_status:
                            record[status_index] = normal_status
                            changes += 1
                            
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"Error processing switching device record: {e}")
        
        return changes
    
    def _reset_terminal_equipment_to_online(self) -> int:
        """Reset all terminal equipment (generators, loads) to online status."""
        changes = 0
        
        equipment_sections = ['generator', 'load', 'fixed_shunt', 'switched_shunt']
        for section_name in equipment_sections:
            if section_name not in self.data:
                continue
                
            section = self.data[section_name]
            if 'fields' not in section or 'records' not in section:
                continue
                
            fields = section['fields']
            status_index = self._find_field_index(fields, ['status', 'stat'])
            
            if status_index is None:
                continue
            
            # Set all equipment to online (status = 1)
            for record in section['records']:
                if len(record) > status_index:
                    try:
                        current_status = int(record[status_index])
                        if current_status != 1:
                            record[status_index] = 1
                            changes += 1
                    except (ValueError, IndexError):
                        pass
        
        return changes
    
    def _reset_branches_to_normal(self) -> int:
        """Reset branches (lines with breakers) to normal status."""
        changes = 0
        
        if 'acline' not in self.data:
            return changes
            
        section = self.data['acline']
        if 'fields' not in section or 'records' not in section:
            return changes
            
        fields = section['fields']
        status_index = self._find_field_index(fields, ['status', 'stat'])
        normal_status_index = self._find_field_index(fields, ['normal_status', 'nstat'])
        
        if status_index is None:
            return changes
        
        # If no normal status field, assume all lines should be online
        if normal_status_index is None:
            for record in section['records']:
                if len(record) > status_index:
                    try:
                        current_status = int(record[status_index])
                        if current_status != 1:
                            record[status_index] = 1
                            changes += 1
                    except (ValueError, IndexError):
                        pass
        else:
            # Use normal status field
            for record in section['records']:
                if len(record) > max(status_index, normal_status_index):
                    try:
                        normal_status = int(record[normal_status_index])
                        current_status = int(record[status_index])
                        
                        if current_status != normal_status:
                            record[status_index] = normal_status
                            changes += 1
                            
                    except (ValueError, IndexError):
                        pass
        
        return changes
    
    def _maintain_current_state(self) -> None:
        """
        Maintain current equipment positions as stored in the backend.
        
        This restores from backup if changes were made.
        """
        if self._original_data_backup and self._system_state != SystemState.MAINTAIN_CURRENT:
            self.logger.info("Restoring to original current state")
            # Restore from backup
            self.data = copy.deepcopy(self._original_data_backup)
        else:
            self.logger.debug("Already maintaining current state - no changes needed")
    
    def _reset_to_normal_state_stub(self) -> None:
        """
        Stub implementation for normal state reset when normal data is not available.
        
        This provides a basic implementation that can be enhanced when normal state
        data becomes available in the backend.
        """
        self.logger.info("Using stub implementation for normal state reset")
        changes = 0
        
        # Basic approach: Set all equipment to online status
        equipment_sections = ['generator', 'load', 'fixed_shunt', 'switched_shunt', 'acline']
        
        for section_name in equipment_sections:
            if section_name not in self.data:
                continue
                
            section = self.data[section_name]
            if 'fields' not in section or 'records' not in section:
                continue
                
            fields = section['fields']
            status_index = self._find_field_index(fields, ['status', 'stat'])
            
            if status_index is None:
                continue
            
            # Set all to online (status = 1)
            for record in section['records']:
                if len(record) > status_index:
                    try:
                        current_status = int(record[status_index])
                        if current_status != 1:
                            record[status_index] = 1
                            changes += 1
                    except (ValueError, IndexError):
                        pass
        
        self.logger.info(f"Stub normal state reset complete. {changes} items set to online.")
        self.logger.warning("This is a basic implementation. Add normal status fields to data for proper normal state support.") 