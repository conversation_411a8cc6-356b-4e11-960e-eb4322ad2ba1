# PSS/E Dependency Analyzer

## Overview

This tool analyzes Python codebases to identify PSS/E dependencies, including `psspy` imports, function calls, `psse35` imports, and PSS/E initialization patterns. It's designed to help assess the scope of PSS/E integration in Python projects.

## Files Included

- **`psspy_analyzer.py`** - Main Python script with comprehensive analysis capabilities
- **`run_psspy_analyzer.bat`** - Windows batch file for easy execution
- **`run_psspy_analyzer.sh`** - Linux/Mac shell script for easy execution
- **`README_PSSPY_Analyzer.md`** - This documentation file

## Features

### What It Analyzes

1. **PSSPY Function Calls**: Detects all `psspy.function_name()` calls
2. **Import Patterns**: Finds `import psspy`, `import psse35`, and `from psse35/psspy` statements
3. **PSS/E Initialization**: Identifies PSS/E setup code like `psseinit`, path modifications, etc.
4. **Function Usage Statistics**: Shows which PSS/E functions are used most frequently
5. **File-by-File Breakdown**: Detailed analysis of each file's PSS/E dependency level

### Report Contents

- **Summary Statistics**: Total files, calls, imports, unique functions
- **Top Functions**: Most frequently used PSS/E functions across the codebase
- **File Rankings**: Files ranked by PSS/E usage intensity
- **Detailed Analysis**: In-depth breakdown of high-usage files
- **Full File Paths**: Complete paths to all files with PSS/E dependencies

## Usage Options

### Option 1: Direct Python Execution

```bash
# Analyze current directory
python psspy_analyzer.py

# Analyze specific directory
python psspy_analyzer.py /path/to/your/project

# Save report to file
python psspy_analyzer.py /path/to/project --output analysis_report.txt

# Show only function usage statistics
python psspy_analyzer.py /path/to/project --functions-only
```

### Option 2: Windows Batch File

1. Double-click `run_psspy_analyzer.bat`
2. Enter the directory path when prompted (or press Enter for current directory)
3. Optionally specify an output file to save the report

### Option 3: Linux/Mac Shell Script

1. Make executable: `chmod +x run_psspy_analyzer.sh`
2. Run: `./run_psspy_analyzer.sh`
3. Enter the directory path when prompted
4. Optionally specify an output file to save the report

## Command Line Arguments

- **`directory`** (optional): Directory to scan. If not provided, you'll be prompted
- **`--output`, `-o`**: Save report to specified file
- **`--functions-only`**: Show only function usage statistics (no detailed file analysis)

## Example Output

```
================================================================================
📊 PSS/E DEPENDENCY ANALYSIS REPORT
================================================================================

📈 SUMMARY STATISTICS:
   Directory scanned: C:\Projects\MyPSSEProject
   Files with PSS/E usage: 30
   Total psspy calls: 984
   Files with 'import psspy': 27
   Files with 'import psse35': 7
   Files with PSS/E initialization: 10
   Unique psspy functions used: 171

🔥 TOP 10 MOST USED PSSPY FUNCTIONS:
   Function                  Count    Files
   ------------------------- -------- --------
   getdefaultchar            20       20
   getdefaultreal            20       20
   getdefaultint             16       16
   psseinit                  5        5
   iterat                    4        4

📋 FILES RANKED BY PSSPY USAGE:
   Calls  Funcs  Imports         Full Path
   ------ ------ --------------- --------------------------------------------------
   243    <USER>     <GROUP>,psse35,init controller\psse\psspy_api.py
   108    11     psspy,psse35    controller\psse\obsolete\bus_nb.py
   83     12     psspy,psse35    controller\psse\obsolete\machine_nb.py
```

## Understanding the Report

### Import Status Flags

- **`psspy`**: File contains `import psspy` or `from psspy import ...`
- **`psse35`**: File contains `import psse35` or `from psse35 import ...`
- **`init`**: File contains PSS/E initialization code
- **`none`**: File has PSS/E function calls but no direct imports (uses inherited/injected psspy)

### File Rankings

Files are ranked by:
1. **Calls**: Number of `psspy.` function calls
2. **Funcs**: Number of unique PSS/E functions used
3. **Imports**: Type of PSS/E imports present

### Detailed Analysis Criteria

The detailed analysis section shows files that meet any of these criteria:
- More than 10 psspy function calls
- Contains `psse35` imports
- Contains PSS/E initialization patterns

## Use Cases

### 1. **PSS/E Migration Assessment**
Understand the scope of PSS/E dependencies before planning a migration to another power system analysis tool.

### 2. **Code Refactoring**
Identify which files are most heavily dependent on PSS/E for targeted refactoring efforts.

### 3. **Dependency Management**
Catalog all PSS/E function usage to understand which PSS/E features your codebase relies on.

### 4. **Remote Analysis**
Run on remote machines or servers to analyze codebases without needing PSS/E installed.

### 5. **Documentation**
Generate comprehensive documentation of PSS/E usage for team knowledge transfer.

## Requirements

- **Python 3.6+** (uses f-strings and other modern Python features)
- **Standard Library Only** (no external dependencies required)
- **Cross-Platform** (works on Windows, Linux, Mac)

## Error Handling

The tool gracefully handles:
- Missing files or directories
- Permission issues
- Encoding problems in source files
- Non-Python files (ignores them)
- Large codebases (efficient memory usage)

## Sample Analysis Commands

```bash
# Quick analysis of current project
python psspy_analyzer.py

# Deep analysis with report saved
python psspy_analyzer.py ./my_project --output psse_analysis_$(date +%Y%m%d).txt

# Function-only analysis for API mapping
python psspy_analyzer.py ./src --functions-only

# Analyze multiple directories (run separately)
python psspy_analyzer.py ./controller
python psspy_analyzer.py ./modelling  
python psspy_analyzer.py ./study
```

## Tips for Large Codebases

1. **Use Output Files**: Save reports to files for easier review and sharing
2. **Focus on High-Usage Files**: Start refactoring with files that have >50 psspy calls
3. **Function Mapping**: Use `--functions-only` to create PSS/E API replacement mappings
4. **Incremental Analysis**: Analyze subdirectories separately for focused reviews

## Troubleshooting

### Python Not Found
- Ensure Python is installed and in your system PATH
- Try `python3` instead of `python` on Linux/Mac

### Permission Denied
- Run with administrator/sudo privileges if analyzing system directories
- Check file permissions on the target directory

### Large Output
- Use `--output` to save to file instead of console
- Pipe output: `python psspy_analyzer.py ./project | less`

This tool provides comprehensive insight into PSS/E dependencies and is essential for any project involving PSS/E integration, migration, or refactoring efforts. 