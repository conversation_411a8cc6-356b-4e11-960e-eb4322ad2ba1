#!/usr/bin/env python3
"""
Test script to verify the node key fix for generator bus lookup.
This tests that we're using node numbers consistently between the generator lookup table
and the bus nodes list.
"""

def safe_str(value):
    """Safe string conversion."""
    return str(value) if value is not None else ''

def test_node_key_consistency():
    """Test that node numbers are used consistently."""
    
    # Mock HDB data structure - realistic example
    hdb_context = {
        'unit': {
            'unit1': {
                'Node': '15',  # This is the node number from unit record
                'Station': 'ROGERSRD',
                'MW Output': 100.0,
                'MVAR Output': 50.0
            },
            'unit2': {
                'Node': '20',  # This is the node number from unit record
                'Station': 'SUBSTATION_B',
                'MW Output': 200.0,
                'MVAR Output': 75.0
            }
        },
        'node': {
            'node_key_abc': {  # This is the dictionary key (arbitrary)
                'Number': '15',  # This is the actual node number
                'Station': 'ROGERSRD',
                'Bus Number': 1001,
                'Base KV': 138.0
            },
            'node_key_def': {  # This is the dictionary key (arbitrary)
                'Number': '20',  # This is the actual node number
                'Station': 'SUBSTATION_B',
                'Bus Number': 2001,
                'Base KV': 230.0
            },
            'node_key_ghi': {  # This is the dictionary key (arbitrary)
                'Number': '25',  # This is the actual node number
                'Station': 'ROGERSRD', 
                'Bus Number': 1002,
                'Base KV': 138.0
            }
        }
    }
    
    # Build the generator lookup table (like BusConverter does)
    def build_generator_node_lookup():
        generator_nodes = set()
        hdb_units = hdb_context.get('unit', {})
        
        if isinstance(hdb_units, dict):
            for unit_record in hdb_units.values():
                if isinstance(unit_record, dict):
                    station = safe_str(unit_record.get('Station', ''))
                    node_key = safe_str(unit_record.get('Node', ''))  # This gets the node number
                    if station and node_key:
                        generator_nodes.add((station, node_key))
        return generator_nodes
    
    generator_nodes = build_generator_node_lookup()
    print(f"Generator lookup table: {generator_nodes}")
    
    # Simulate how bus nodes are stored (using node numbers from node records)
    def simulate_bus_creation():
        buses_data = {}
        hdb_nodes = hdb_context.get('node', {})
        
        # Simulate the node iteration like in BusConverter
        if isinstance(hdb_nodes, dict):
            node_items = list(hdb_nodes.items())
        else:
            node_items = [(str(i), record) for i, record in enumerate(hdb_nodes)]
        
        for node_key, node_record in node_items:
            station = safe_str(node_record.get('Station', ''))
            bus_number = 1001  # Simplified for test
            node_number = safe_str(node_record.get('Number', node_key))
            
            if bus_number not in buses_data:
                buses_data[bus_number] = {
                    'nodes': [node_number],  # Store node number, not node_key
                    'substation_name': station
                }
            else:
                if node_number not in buses_data[bus_number]['nodes']:
                    buses_data[bus_number]['nodes'].append(node_number)
        
        return buses_data
    
    buses_data = simulate_bus_creation()
    print(f"Bus data: {buses_data}")
    
    # Test the lookup function
    def determine_bus_type_from_nodes(voltage_pu, bus_number, nodes_list, station):
        """Test version of the lookup function."""
        if voltage_pu < 0.89:
            return 4  # Isolated/out of service
        
        # Check if any node on this bus has generators
        for node_number in nodes_list:
            if (station, str(node_number)) in generator_nodes:
                return 2  # Generator bus
                
        return 1  # Default load bus
    
    # Test cases
    test_cases = [
        # (voltage_pu, bus_number, nodes_list, station, expected_result, description)
        (1.0, 1001, ['15'], 'ROGERSRD', 2, 'Generator bus - node 15 has generator'),
        (1.0, 2001, ['20'], 'SUBSTATION_B', 2, 'Generator bus - node 20 has generator'),
        (1.0, 1002, ['25'], 'ROGERSRD', 1, 'Load bus - node 25 has no generator'),
        (1.0, 1003, ['15', '25'], 'ROGERSRD', 2, 'Generator bus - node 15 has generator, 25 does not'),
        (1.0, 1004, ['25', '26'], 'ROGERSRD', 1, 'Load bus - neither node has generator'),
        (0.8, 1001, ['15'], 'ROGERSRD', 4, 'Isolated bus - low voltage'),
    ]
    
    print("\n🧪 Testing Node Key Consistency Fix")
    print("=" * 50)
    
    all_passed = True
    for voltage_pu, bus_number, nodes_list, station, expected, description in test_cases:
        result = determine_bus_type_from_nodes(voltage_pu, bus_number, nodes_list, station)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Input: voltage={voltage_pu}, bus={bus_number}, nodes={nodes_list}, station='{station}'")
        print(f"    Expected: {expected}, Got: {result}")
        
        # Debug info for failures
        if result != expected:
            print(f"    Debug: Checking nodes {nodes_list} in station '{station}'")
            for node_number in nodes_list:
                lookup_key = (station, str(node_number))
                in_table = lookup_key in generator_nodes
                print(f"      Node {node_number}: lookup_key={lookup_key}, in_generator_table={in_table}")
        print()
        
        if result != expected:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests passed! The node key consistency fix is working correctly.")
    else:
        print("❌ Some tests failed. The fix may need adjustment.")
    
    return all_passed

if __name__ == "__main__":
    test_node_key_consistency()
