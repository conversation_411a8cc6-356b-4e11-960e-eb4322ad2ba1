graph LR
    subgraph "Original Equipment Record"
        OriginalBus["bus_number: 0<br/>substation_id: 'SUB1'<br/>node_id: 'N1'"]
    end

    subgraph "Node Mapping Process"
        NodeKey["Node Key<br/>('SUB1', 'N1')"]
        BusAssignment["Bus Assignment<br/>new_bus_number: 1001"]
        MappingTable["node_to_new_bus<br/>('SUB1', 'N1') → 1001"]
    end

    subgraph "Transformed Equipment Record"
        TransformedBus["bus_number: 1001<br/>substation_id: 'SUB1'<br/>node_id: 'N1'"]
    end

    subgraph "Writer Access"
        WriterCall["record.get('bus_number', 0)<br/>returns: 1001"]
        WriterOutput["RAW Format Output<br/>1001, ..."]
    end

    OriginalBus --> NodeKey
    NodeKey --> BusAssignment
    BusAssignment --> MappingTable
    MappingTable --> TransformedBus
    TransformedBus --> WriterCall
    WriterCall --> WriterOutput 