#!/usr/bin/env python3
"""
Test script to verify the generator bus lookup fix.
This tests the logic we implemented in the _determine_bus_type_direct method.
"""

def safe_str(value):
    """Safe string conversion."""
    return str(value) if value is not None else ''

def safe_int(value):
    """Safe integer conversion."""
    try:
        return int(value) if value is not None else 0
    except (ValueError, TypeError):
        return 0

def test_generator_bus_lookup():
    """Test the generator bus lookup logic we implemented."""
    
    # Mock HDB data structure
    hdb_context = {
        'unit': {
            'unit1': {
                'Node': '15',
                'Station': 'ROGERSRD',
                'MW Output': 100.0,
                'MVAR Output': 50.0
            },
            'unit2': {
                'Node': '20',
                'Station': 'SUBSTATION_B',
                'MW Output': 200.0,
                'MVAR Output': 75.0
            }
        },
        'node': {
            'node1': {
                'Number': '15',
                'Station': 'ROGERSRD',
                'Bus Number': 1001,
                'Base KV': 138.0
            },
            'node2': {
                'Number': '20', 
                'Station': 'SUBSTATION_B',
                'Bus Number': 2001,
                'Base KV': 230.0
            },
            'node3': {
                'Number': '25',
                'Station': 'ROGERSRD', 
                'Bus Number': 1002,
                'Base KV': 138.0
            }
        }
    }
    
    def determine_bus_type_direct(voltage_pu, bus_number, node_key, station):
        """
        Simplified version of the _determine_bus_type_direct method we fixed.
        """
        # Check for isolated bus first (highest priority)
        if voltage_pu < 0.89:
            return 4  # Isolated/out of service
        
        # Check if this bus has generators (direct lookup using node_key)
        hdb_units = hdb_context.get('unit', {})
        hdb_nodes = hdb_context.get('node', {})
        
        if isinstance(hdb_units, dict):
            for unit_record in hdb_units.values():
                # Get the node_key from the generator unit record
                unit_node_key = safe_str(unit_record.get('Node', ''))
                unit_station = safe_str(unit_record.get('Station', ''))
                
                # Check if this unit matches our current node and station
                if unit_node_key == node_key and unit_station == station:
                    return 2  # Generator bus
                
                # Alternative approach: lookup bus_number from node_key
                if isinstance(hdb_nodes, dict):
                    for node_record in hdb_nodes.values():
                        if isinstance(node_record, dict):
                            # Check if this node matches the unit's node
                            if (safe_str(node_record.get('Number', '')) == unit_node_key and
                                safe_str(node_record.get('Station', '')) == unit_station):
                                # Get the bus number from the node record
                                unit_bus_number = safe_int(node_record.get('Bus Number', 0))
                                if unit_bus_number == bus_number:
                                    return 2  # Generator bus
        return 1  # Default load bus
    
    # Test cases
    test_cases = [
        # (voltage_pu, bus_number, node_key, station, expected_result, description)
        (1.0, 1001, '15', 'ROGERSRD', 2, 'Generator bus - direct node match'),
        (1.0, 2001, '20', 'SUBSTATION_B', 2, 'Generator bus - bus number lookup'),
        (1.0, 1002, '25', 'ROGERSRD', 1, 'Load bus - no generator'),
        (0.8, 1001, '15', 'ROGERSRD', 4, 'Isolated bus - low voltage'),
        (1.0, 9999, '99', 'UNKNOWN', 1, 'Load bus - unknown node/station'),
    ]
    
    print("🧪 Testing Generator Bus Lookup Fix")
    print("=" * 50)
    
    all_passed = True
    for voltage_pu, bus_number, node_key, station, expected, description in test_cases:
        result = determine_bus_type_direct(voltage_pu, bus_number, node_key, station)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} {description}")
        print(f"    Input: voltage={voltage_pu}, bus={bus_number}, node='{node_key}', station='{station}'")
        print(f"    Expected: {expected}, Got: {result}")
        print()
        
        if result != expected:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests passed! The generator bus lookup fix is working correctly.")
    else:
        print("❌ Some tests failed. The fix may need adjustment.")
    
    return all_passed

if __name__ == "__main__":
    test_generator_bus_lookup()
