# Architecture Diagrams

This directory contains Mermaid diagrams that illustrate the HDB to RAW pipeline architecture.

## Diagram Files

### 1. Overall Data Flow (`overall_data_flow.mmd`)
Shows the complete data flow from input files through all processing layers to output files.

**Key Components:**
- Input Layer: HDB, RAWX, JSON files
- Backend Layer: HdbBackend, RawxBackend classes
- Canonical Data Layer: Immutable data structures
- Transformation Layer: Node-to-bus mapping
- Export Layer: Writer classes and export orchestration
- Output Layer: Different RAW file formats

### 2. Equipment Transformation (`equipment_transformation.mmd`)
Illustrates how equipment records are transformed during node-to-bus conversion.

**Key Process:**
- Original equipment with embedded node info
- Node key creation and bus assignment
- Equipment record transformation
- Writer access pattern

### 3. Modeling Approach Comparison (`modeling_comparison.mmd`)
Compares the three supported modeling approaches and their output formats.

**Approaches:**
- Bus-Branch: Traditional PSS/E format
- Node-Breaker: Hierarchical substation format
- Hybrid: Mixed representation

### 4. Three-Phase Pipeline (`three_phase_pipeline.mmd`)
Shows the three main phases of the pipeline processing.

**Phases:**
- Phase 1: Loading & Conversion
- Phase 2: Transformation
- Phase 3: Export

## How to Use These Diagrams

### 1. Mermaid Live Editor
Copy the content of any `.mmd` file and paste it into the [Mermaid Live Editor](https://mermaid.live/)

### 2. Mermaid CLI
If you have the Mermaid CLI installed:
```bash
mmdc -i overall_data_flow.mmd -o overall_data_flow.png
```

### 3. VS Code Extension
Install the "Mermaid Preview" extension in VS Code and open the `.mmd` files.

### 4. GitHub/GitLab
These platforms natively support Mermaid diagrams in markdown files.

## Including in Documentation

To include these diagrams in markdown documentation:

```markdown
```mermaid
graph TB
    [paste diagram content here]
```
```

## Diagram Updates

When updating the architecture:
1. Update the corresponding `.mmd` file
2. Regenerate any exported images if needed
3. Update the main Architecture_Documentation.md file
4. Regenerate the PDF documentation

## File Formats

- `.mmd` - Mermaid diagram source files
- Can be exported to PNG, SVG, or PDF using Mermaid tools
- Directly usable in many documentation platforms 