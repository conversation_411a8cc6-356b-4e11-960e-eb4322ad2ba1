#!/usr/bin/env python3
"""
Universal Backend - The Simple Interface You Actually Want

This provides the painfully simple interface that makes common operations trivial:
    db = Backend.load("file.json")  # Auto-detects format
    db.save("output.xlsx")          # Auto-converts
    db.export_raw("output.raw")     # Exports to PSS/E RAW

No configuration, no boilerplate, no dozens of import statements.
Just load, convert, export. Done.
"""

import os
import json
from pathlib import Path
from typing import Union, Optional, Dict, Any
import logging

# Hybrid modeling configuration constants
HYBRID_BUS_BASE = 900000  # Starting bus number for hybrid node conversion
SWITCH_STATUS_MODES = ["current", "normal"]  # Available switch status modes

# Import all backends
from .backends.hdb_backend import HdbBackend
from .backends.canonical_backend import CanonicalBackend  
from .backends.excel_backend import ExcelBackend
from .backends.json_backend import JsonBackend
from .backends.sqlite_backend import SQLiteBackend
from .backends.rawx_backend import RawxBackend

# Import converters for RAW export
from RawEditor.export.export_raw import export_to_raw_format


class Backend:
    """
    Universal Backend - The interface that makes everything painfully simple.
    
    Auto-detects file formats, handles all conversions internally,
    provides simple load/save/export methods.
    """
    
    def __init__(self, backend_instance=None):
        """Initialize with a specific backend instance, or empty for later loading."""
        self._backend = backend_instance
        self._data = backend_instance.get_data() if backend_instance else {}
        self.logger = logging.getLogger("UniversalBackend")
        
        # Set up console logging
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(levelname)s - %(name)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    @classmethod
    def load(cls, file_path: Union[str, Path], file_type: Optional[str] = None) -> 'Backend':
        """
        Load any supported file format with auto-detection.
        
        Args:
            file_path: Path to file to load
            file_type: Optional explicit format ('hdb', 'json', 'excel', 'sqlite', 'rawx')
                      If None, auto-detects from file extension and content
        
        Returns:
            Backend instance loaded with data
            
        Examples:
            db = Backend.load("hdbcontext_export.json")    # Auto-detects HDB
            db = Backend.load("data.xlsx")                 # Auto-detects Excel
            db = Backend.load("network.rawx")              # Auto-detects RAWX
        """
        file_path = Path(file_path)
        
        print(f"🔍 Loading file: {file_path}")
        
        if not file_path.exists():
            print(f"❌ ERROR: File not found: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")
        
        print(f"✅ File exists, size: {file_path.stat().st_size} bytes")
        
        # Auto-detect format if not specified
        if file_type is None:
            file_type = cls._detect_format(file_path)
        
        print(f"🔍 Detected format: {file_type}")
        
        # Load with appropriate backend
        try:
            if file_type == 'hdb':
                print("📂 Loading with HDB backend...")
                backend = HdbBackend(file_path=str(file_path))
                print(f"✅ HDB backend loaded successfully")
                
                # Store raw HDB data for later conversion with specific modeling approach
                hdb_data = backend.get_data()
                print(f"   Raw HDB sections: {list(hdb_data.keys())}")
                
            elif file_type == 'json':
                print("📂 Loading with JSON backend...")
                backend = JsonBackend(json_path=str(file_path))
                print(f"✅ JSON backend loaded successfully")
            elif file_type == 'excel':
                print("📂 Loading with Excel backend...")
                backend = ExcelBackend(excel_path=str(file_path))
                print(f"✅ Excel backend loaded successfully")
            elif file_type == 'sqlite':
                print("📂 Loading with SQLite backend...")
                backend = SQLiteBackend(db_path=str(file_path))
                print(f"✅ SQLite backend loaded successfully")
            elif file_type == 'rawx':
                print("📂 Loading with RAWX backend...")
                backend = RawxBackend(file_path=str(file_path))
                print(f"✅ RAWX backend loaded successfully")
            else:
                print(f"❌ ERROR: Unsupported file type: {file_type}")
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # Check if data was loaded
            data = backend.get_data() if hasattr(backend, 'get_data') else getattr(backend, 'data', {})
            print(f"📊 Final data loaded: {len(data)} sections")
            if data:
                print(f"   Sections: {list(data.keys())}")
                total_records = sum(len(section.get('data', [])) if isinstance(section, dict) else 0 for section in data.values())
                print(f"   Total records: {total_records}")
            else:
                print("⚠️  WARNING: No data found in loaded file")
            
        except Exception as e:
            print(f"❌ ERROR loading file: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        return cls(backend)
    
    @classmethod
    def _detect_format(cls, file_path: Path) -> str:
        """Auto-detect file format from extension and content."""
        suffix = file_path.suffix.lower()
        
        print(f"🔍 Detecting format for: {file_path}")
        print(f"   File extension: '{suffix}'")
        
        # Extension-based detection
        if suffix == '.xlsx' or suffix == '.xls':
            print("   → Detected as Excel")
            return 'excel'
        elif suffix == '.db' or suffix == '.sqlite':
            print("   → Detected as SQLite")
            return 'sqlite'
        elif suffix == '.rawx':
            print("   → Detected as RAWX")
            return 'rawx'
        elif suffix == '.json':
            print("   → JSON file, checking content...")
            # Need to distinguish between HDB JSON and regular JSON
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   → JSON loaded, top-level keys: {list(data.keys())}")
                    
                # Check for HDB-specific indicators
                if 'station' in data or 'unit' in data or 'node' in data:
                    print("   → Detected as HDB JSON (contains station/unit/node)")
                    return 'hdb'
                else:
                    print("   → Detected as Generic JSON")
                    return 'json'
            except Exception as e:
                print(f"   → Error reading JSON, defaulting to generic JSON: {e}")
                return 'json'
        elif suffix == '.hdb':
            print("   → Detected as HDB (by extension)")
            return 'hdb'
        else:
            print(f"   → Unknown extension '{suffix}', defaulting to JSON")
            return 'json'
    
    def save(self, file_path: Union[str, Path], file_type: Optional[str] = None) -> 'Backend':
        """
        Save to any supported format with auto-detection.
        
        Args:
            file_path: Path to save to
            file_type: Optional explicit format, auto-detected from extension if None
            
        Returns:
            New Backend instance for the saved format (for chaining)
            
        Examples:
            db.save("output.xlsx")              # Auto-detects Excel format
            db.save("backup.json")              # Auto-detects JSON format
            db.save("data.db", "sqlite")        # Explicit SQLite format
        """
        file_path = Path(file_path)
        
        print(f"💾 Saving to: {file_path}")
        
        if file_type is None:
            file_type = self._detect_format_for_save(file_path)
        
        print(f"🔍 Save format: {file_type}")
        
        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        print(f"📁 Directory created/verified: {file_path.parent}")
        
        # Check if we have data to save
        if not self._data:
            print("⚠️  WARNING: No data to save!")
            return self
        
        print(f"📊 Data to save: {len(self._data)} sections")
        for section, section_data in self._data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                record_count = len(section_data['data'])
                print(f"   {section}: {record_count} records")
        
        # Save with appropriate backend
        try:
            if file_type == 'json':
                print("💾 Saving with JSON backend...")
                backend = JsonBackend()
                backend.data = self._data
                backend.save(file_path)
                print(f"✅ JSON save completed")
            elif file_type == 'excel':
                print("💾 Saving with Excel backend...")
                backend = ExcelBackend()
                backend.data = self._data
                backend.save(file_path)
                print(f"✅ Excel save completed")
            elif file_type == 'sqlite':
                print("💾 Saving with SQLite backend...")
                backend = SQLiteBackend()
                backend.data = self._data
                backend.save(file_path)
                print(f"✅ SQLite save completed")
            elif file_type == 'rawx':
                print("💾 Saving with RAWX backend...")
                backend = RawxBackend()
                backend.data = self._data
                backend.save(file_path)
                print(f"✅ RAWX save completed")
            else:
                print(f"❌ ERROR: Unsupported save format: {file_type}")
                raise ValueError(f"Unsupported save format: {file_type}")
            
            # Verify file was created
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"✅ File saved successfully: {file_path} ({size} bytes)")
            else:
                print(f"❌ WARNING: Save completed but file not found at {file_path}")
            
        except Exception as e:
            print(f"❌ ERROR saving file: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        self.logger.info(f"Saved data to {file_path} as {file_type}")
        return Backend(backend)
    
    def _detect_format_for_save(self, file_path: Path) -> str:
        """Auto-detect save format from file extension."""
        suffix = file_path.suffix.lower()
        
        if suffix == '.xlsx' or suffix == '.xls':
            return 'excel'
        elif suffix == '.db' or suffix == '.sqlite':
            return 'sqlite'
        elif suffix == '.rawx':
            return 'rawx'
        elif suffix == '.json':
            return 'json'
        else:
            raise ValueError(f"Cannot auto-detect format for extension: {suffix}")
    
    def export_raw(self, output_path: Union[str, Path], 
                   version: str = "35", 
                   modeling: str = "bus_branch",
                   switch_status: str = "current") -> str:
        """
        Export data to PSS/E RAW format.
        
        Args:
            output_path: Path for output RAW file
            version: PSS/E version ("33", "34", "35")
            modeling: Modeling approach ("bus_branch", "node_breaker", "hybrid")
            switch_status: Switch status mode ("current", "normal")
                         - "current": Export switches in their existing status
                         - "normal": Reset switches to their normal status
            
        Returns:
            Path to exported RAW file
            
        Examples:
            db.export_raw("system.raw")                             # Default V35 bus-branch, current status
            db.export_raw("system.raw", version="33")               # V33 format
            db.export_raw("system.raw", modeling="hybrid")          # Hybrid approach
            db.export_raw("system.raw", switch_status="normal")     # Reset to normal status
        """
        output_path = Path(output_path)
        
        print(f"📤 Exporting to PSS/E RAW format: {output_path}")
        print(f"   Version: {version}")
        print(f"   Modeling: {modeling}")
        print(f"   Switch status: {switch_status}")
        
        # Validate switch status mode
        if switch_status not in SWITCH_STATUS_MODES:
            print(f"❌ ERROR: Unsupported switch status mode: {switch_status}")
            raise ValueError(f"Unsupported switch status mode: {switch_status}. Supported: {SWITCH_STATUS_MODES}")
        
        # Validate version
        if version not in ["33", "34", "35"]:
            print(f"❌ ERROR: Unsupported PSS/E version: {version}")
            raise ValueError(f"Unsupported PSS/E version: {version}. Supported: 33, 34, 35")
        
        # Use modeling converter strategy to get data in the correct approach
        from .modeling_converter import create_converter
        from .base_backend import ModelingApproach
        
        # Create converter for this backend
        converter = create_converter(self._backend)
        
        # Map string to enum
        modeling_map = {
            'bus_branch': ModelingApproach.BUS_BRANCH,
            'node_breaker': ModelingApproach.NODE_BREAKER,
            'hybrid': ModelingApproach.HYBRID_BUS_BREAKER
        }
        
        target_approach = modeling_map.get(modeling, ModelingApproach.BUS_BRANCH)
        
        # Get raw backend data and convert to target modeling approach
        raw_data = self._backend.get_data()
        source_approach = converter.detect_approach(raw_data)
        
        print(f"🔄 Converting from {source_approach.value} to {target_approach.value} modeling approach...")
        data = converter.convert_data(raw_data, source_approach, target_approach)
        
        if not data:
            print("❌ ERROR: No data to export")
            raise ValueError("No data to export")

        print(f"📊 Data sections: {list(data.keys())}")
        
        # Count records for each section
        total_records = 0
        for section, section_data in data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                record_count = len(section_data['data'])
                total_records += record_count
                print(f"   {section}: {record_count} records")
        
        print(f"📊 Total records to export: {total_records}")
        
        # Apply additional hybrid-specific processing if needed  
        if modeling == "hybrid":
            print(f"🔄 Applying hybrid-specific processing...")
            # For hybrid, ensure we don't add node-breaker sections if we already have clean bus data
            if 'substation_node' in data or 'switching_device' in data:
                data = self._convert_to_hybrid_modeling(data, switch_status)
            else:
                print(f"   ✅ Hybrid data already in correct bus-only format")
            print(f"✅ Hybrid processing completed")
        
        # Validate and fix ownership fractions before export
        print(f"🔧 Validating and fixing ownership fractions...")
        validated_data = self._validate_ownership_fractions(data)
        
        # Validate required sections for the specified version
        required_sections = self._get_required_sections(version, modeling)
        missing_sections = [section for section in required_sections if section not in validated_data]
        
        if missing_sections:
            print(f"⚠️  WARNING: Missing required sections for V{version}: {missing_sections}")
            print("   These sections will be skipped or use default values")
        
        # Create output directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Export using the new modular export system
            from RawEditor.export.export_raw import export_to_raw_format
            
            exported_path = export_to_raw_format(
                data=validated_data,
                output_path=output_path,
                version=version,
                modeling_approach=modeling
            )
            
            print(f"✅ Successfully exported {total_records} records to {exported_path}")
            
            # Validate the exported file
            self._validate_exported_raw(exported_path, version)
            
            return str(exported_path)
            
        except Exception as e:
            print(f"❌ ERROR exporting RAW file: {e}")
            raise
    
    def _get_required_sections(self, version: str, modeling: str) -> list:
        """Get required sections for the specified version and modeling approach."""
        # Base sections for all versions
        base_sections = ['caseid', 'bus', 'load', 'fixed_shunt', 'generator', 'ac_line', 'transformer', 'area', 'zone', 'owner']
        
        if version == "33":
            return base_sections
        elif version == "34":
            # V34 adds some additional sections
            v34_sections = base_sections + ['dc_line_2t', 'vsc_dc', 'impedance_correction', 'multi_terminal_dc', 'multi_section_line', 'inter_area_transfer', 'facts_device', 'switched_shunt', 'gne_device', 'induction_machine']
            return v34_sections
        elif version == "35":
            # V35 adds node-breaker modeling sections
            if modeling == "node_breaker":
                v35_sections = base_sections + ['substation', 'node', 'switching_device', 'terminal', 'dc_line_2t', 'vsc_dc', 'impedance_correction', 'multi_terminal_dc', 'multi_section_line', 'inter_area_transfer', 'facts_device', 'switched_shunt', 'gne_device', 'induction_machine']
            else:  # bus_branch or hybrid
                v35_sections = base_sections + ['dc_line_2t', 'vsc_dc', 'impedance_correction', 'multi_terminal_dc', 'multi_section_line', 'inter_area_transfer', 'facts_device', 'switched_shunt', 'gne_device', 'induction_machine']
            return v35_sections
        
        return base_sections
    
    def _validate_ownership_fractions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and fix ownership fractions to ensure they sum to exactly 1.0 (100%).
        
        PSS/E requires ownership fractions to sum to exactly 1.0, or it rejects the generator
        with "Machine ownership table is full" errors.
        
        Args:
            data: Raw data dictionary with equipment sections
            
        Returns:
            Validated data with corrected ownership fractions
        """
        import copy
        validated_data = copy.deepcopy(data)
        
        # Equipment types that have ownership data (o1-o4, f1-f4)
        ownership_equipment = ['generator', 'acline', 'ac_line', 'transformer']
        
        for equipment_type in ownership_equipment:
            if equipment_type not in validated_data:
                continue
                
            section_data = validated_data[equipment_type]
            if not isinstance(section_data, dict) or 'data' not in section_data or 'fields' not in section_data:
                continue
                
            fields = section_data['fields']
            records = section_data['data']
            
            # Find ownership field indices
            ownership_fields = {}
            for i, field in enumerate(fields):
                if field in ['o1', 'o2', 'o3', 'o4', 'f1', 'f2', 'f3', 'f4']:
                    ownership_fields[field] = i
            
            # Skip if no ownership fields found
            if not ownership_fields:
                print(f"   {equipment_type}: No ownership fields found")
                continue
                
            fixed_count = 0
            total_count = len(records)
            
            for record_idx, record in enumerate(records):
                # Extract current fractions
                fractions = {}
                owners = {}
                for field in ['f1', 'f2', 'f3', 'f4']:
                    if field in ownership_fields:
                        fractions[field] = record[ownership_fields[field]] or 0.0
                        
                for field in ['o1', 'o2', 'o3', 'o4']:
                    if field in ownership_fields:
                        owners[field] = record[ownership_fields[field]] or 0
                
                # Calculate current sum
                current_sum = sum(fractions.values())
                
                # Check if sum needs adjustment (allow small tolerance for floating point)
                if abs(current_sum - 1.0) > 1e-6:  # More than 0.0001% off
                    # Special case: if sum is zero (all fractions are 0), set f1=1.0
                    if current_sum == 0.0:
                        if 'f1' in ownership_fields:
                            record[ownership_fields['f1']] = 1.0
                            # Ensure owner 1 is set
                            if 'o1' in ownership_fields and record[ownership_fields['o1']] == 0:
                                record[ownership_fields['o1']] = 1
                            fixed_count += 1
                            if equipment_type in ['generator', 'acline']:  # Show details for key equipment
                                print(f"   {equipment_type} record {record_idx+1}: zero fractions → 100% owner 1")
                        continue
                    
                    # Find the last non-zero fraction to adjust
                    last_fraction_field = None
                    for field in ['f4', 'f3', 'f2', 'f1']:  # Check in reverse order
                        if field in fractions and fractions[field] > 0:
                            last_fraction_field = field
                            break
                    
                    if last_fraction_field:
                        # Adjust the last fraction to make sum exactly 1.0
                        adjustment = 1.0 - current_sum
                        old_value = fractions[last_fraction_field]
                        new_value = old_value + adjustment
                        
                        # Ensure the adjusted value is not negative
                        if new_value >= 0:
                            record[ownership_fields[last_fraction_field]] = new_value
                            fixed_count += 1
                            
                            if equipment_type == 'generator':  # Only show details for generators to avoid spam
                                print(f"   {equipment_type} record {record_idx+1}: sum {current_sum:.6f} → 1.0 (adjusted {last_fraction_field}: {old_value:.6f} → {new_value:.6f})")
                        else:
                            print(f"   ⚠️ {equipment_type} record {record_idx+1}: Cannot fix ownership (would make {last_fraction_field} negative)")
                    else:
                        # No valid fraction to adjust, set f1=1.0 and others=0
                        if 'f1' in ownership_fields:
                            record[ownership_fields['f1']] = 1.0
                            # Ensure owner 1 is set
                            if 'o1' in ownership_fields and record[ownership_fields['o1']] == 0:
                                record[ownership_fields['o1']] = 1
                            for field in ['f2', 'f3', 'f4']:
                                if field in ownership_fields:
                                    record[ownership_fields[field]] = 0.0
                            fixed_count += 1
                            if equipment_type in ['generator', 'acline']:  # Show details for key equipment
                                print(f"   {equipment_type} record {record_idx+1}: Reset to 100% owner 1")
            
            if fixed_count > 0:
                print(f"   {equipment_type}: Fixed {fixed_count}/{total_count} ownership records")
            else:
                print(f"   {equipment_type}: All {total_count} ownership records valid")
        
        return validated_data
    
    def _validate_exported_raw(self, file_path: Path, version: str):
        """Validate the exported RAW file."""
        print(f"🔍 Validating exported RAW file: {file_path}")
        
        if not file_path.exists():
            print("❌ ERROR: Exported file does not exist")
            return
        
        file_size = file_path.stat().st_size
        print(f"📊 File size: {file_size} bytes")
        
        if file_size == 0:
            print("❌ ERROR: Exported file is empty")
            return
        
        # Read first few lines to validate format
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📊 Total lines: {len(lines)}")
            
            # Check for required elements
            has_case_header = any('@!IC' in line and 'SBASE' in line and 'REV' in line for line in lines[:10])
            has_bus_header = any('@!' in line and ('I,' in line or "I,'NAME" in line) and 'BASKV' in line for line in lines[:20])
            has_end_data = any('END OF DATA' in line for line in lines[-5:])
            
            print(f"   Case header: {'✅' if has_case_header else '❌'}")
            print(f"   Bus header: {'✅' if has_bus_header else '❌'}")
            print(f"   End of data: {'✅' if has_end_data else '❌'}")
            
            # Count sections
            section_headers = [line for line in lines if line.strip().startswith('@!')]
            print(f"   Section headers found: {len(section_headers)}")
            
            # Show first few section headers
            for i, header in enumerate(section_headers[:5]):
                print(f"     {i+1}. {header.strip()}")
            
            if len(section_headers) > 5:
                print(f"     ... and {len(section_headers) - 5} more")
            
            print("✅ RAW file validation completed")
            
        except Exception as e:
            print(f"⚠️  WARNING: Could not validate exported file: {e}")
    
    def get_data(self) -> Dict[str, Any]:
        """Get the raw data for advanced operations."""
        return self._data
    
    def get_summary(self) -> Dict[str, int]:
        """Get a summary of equipment counts."""
        summary = {}
        for section, section_data in self._data.items():
            if isinstance(section_data, dict) and 'records' in section_data:
                summary[section] = len(section_data['records'])
            elif isinstance(section_data, dict) and 'data' in section_data:
                summary[section] = len(section_data['data'])
            elif isinstance(section_data, list):
                summary[section] = len(section_data)
        return summary
    
    def _convert_to_hybrid_modeling(self, data: Dict[str, Any], switch_status: str) -> Dict[str, Any]:
        """
        Convert node-breaker data to hybrid modeling approach.
        
        Hybrid approach:
        - Nodes become buses with unique bus numbers
        - Switching devices become zero-impedance branches  
        - Terminals connect equipment directly to new buses
        - Substations are removed (bus-branch compatible)
        
        Args:
            data: Canonical data with node-breaker sections
            switch_status: "current" or "normal" switch status mode
            
        Returns:
            Modified data with hybrid bus-branch sections
        """
        import copy
        hybrid_data = copy.deepcopy(data)
        
        # Check if we have node-breaker data to convert - use standard canonical names
        node_section = hybrid_data.get('subnode', hybrid_data.get('node', {}))
        switching_section = hybrid_data.get('subswd', hybrid_data.get('switching_device', {}))
        terminal_section = hybrid_data.get('subterm', hybrid_data.get('terminal', {}))
        substation_section = hybrid_data.get('sub', hybrid_data.get('substation', {}))
        
        if not any([node_section.get('data'), switching_section.get('data'), terminal_section.get('data')]):
            print("   ⚪ No node-breaker data found - skipping hybrid conversion")
            return hybrid_data
        
        # Step 1: Convert nodes to buses
        if node_section.get('data'):
            print(f"   🔄 Converting {len(node_section['data'])} nodes to buses...")
            hybrid_data = self._convert_nodes_to_hybrid_buses(hybrid_data)
        
        # Step 2: Convert switching devices to zero-impedance branches
        if switching_section.get('data'):
            print(f"   🔄 Converting {len(switching_section['data'])} switching devices to branches...")
            hybrid_data = self._convert_switches_to_hybrid_branches(hybrid_data, switch_status)
        
        # Step 3: Update terminal connections to use new bus numbers
        if terminal_section.get('data'):
            print(f"   🔄 Updating {len(terminal_section['data'])} terminal connections...")
            hybrid_data = self._update_terminal_connections(hybrid_data)
        
        # Step 4: Remove node-breaker specific sections (both RAWX and standard canonical names)
        sections_to_remove = ['substation', 'node', 'switching_device', 'terminal', 'sub', 'subnode', 'subswd', 'subterm']
        for section in sections_to_remove:
            if section in hybrid_data:
                del hybrid_data[section]
                print(f"   🗑️  Removed {section} section")
        
        return hybrid_data
    
    def _convert_nodes_to_hybrid_buses(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert substation nodes to unique buses for hybrid modeling."""
        # Try RAWX section name first, then canonical
        node_section = data.get('subnode', data.get('node', {}))
        if not node_section.get('data'):
            return data
        
        # Check if nodes already have unique bus numbers
        nodes_have_unique_buses = self._check_node_bus_uniqueness(node_section['data'], node_section['fields'])
        
        if nodes_have_unique_buses:
            print("   ✅ Nodes already have unique bus numbers - using existing bus IDs")
            # Use existing bus numbers from nodes
            self._use_existing_node_buses(data)
        else:
            print("   🔢 Nodes don't have unique bus numbers - generating new hybrid bus numbers")
            # Generate new unique bus numbers
            self._generate_hybrid_bus_numbers(data)
        
        return data
    
    def _check_node_bus_uniqueness(self, node_data: list, node_fields: list) -> bool:
        """Check if nodes already have unique bus numbers that can be used directly."""
        # Find bus field in node data
        bus_field_index = None
        for i, field in enumerate(node_fields):
            if field.lower() in ['bus', 'ibus', 'bus_id', 'bus_number']:
                bus_field_index = i
                break
        
        if bus_field_index is None:
            return False
        
        # Check if all bus numbers are unique and valid
        bus_numbers = []
        for node in node_data:
            if len(node) > bus_field_index:
                bus_num = node[bus_field_index]
                if bus_num is None or bus_num <= 0:
                    return False
                if bus_num in bus_numbers:
                    return False  # Duplicate found
                bus_numbers.append(bus_num)
        
        return True  # All bus numbers are unique and valid
    
    def _use_existing_node_buses(self, data: Dict[str, Any]) -> None:
        """Use existing bus numbers from nodes (for HDB-like data where nodes already have unique bus IDs)."""
        # Try RAWX section name first, then standard canonical
        node_section = data.get('subnode', data.get('node', {}))
        if not node_section.get('data'):
            return
        
        # Create bus section if it doesn't exist
        if 'bus' not in data:
            data['bus'] = {'fields': [], 'data': []}
        
        # Standard bus fields
        bus_fields = ['ibus', 'name', 'basekv', 'ide', 'area', 'zone', 'owner', 'va', 'vm', 'nvhi', 'nvlo', 'evhi', 'evlo']
        data['bus']['fields'] = bus_fields
        
        node_fields = node_section['fields']
        
        # Use existing bus numbers from nodes
        for node in node_section['data']:
            # Extract node information - nodes already have unique bus numbers
            bus_id = self._get_field_value(node, node_fields, ['ibus', 'bus', 'bus_id'], 1)
            node_name = self._get_field_value(node, node_fields, ['name', 'node_name'], f"BUS_{bus_id}")
            basekv = self._get_field_value(node, node_fields, ['basekv', 'base_kv'], 138.0)
            
            # Create bus record using existing bus number
            bus_record = [
                bus_id,             # ibus (use existing)
                node_name[:12],     # name (truncated to 12 chars)
                basekv,             # basekv
                1,                  # ide (PQ bus)
                1,                  # area
                1,                  # zone  
                1,                  # owner
                0.0,                # va (voltage angle)
                1.0,                # vm (voltage magnitude)
                1.1,                # nvhi (normal voltage high)
                0.9,                # nvlo (normal voltage low)
                1.1,                # evhi (emergency voltage high)
                0.9                 # evlo (emergency voltage low)
            ]
            
            data['bus']['data'].append(bus_record)
    
    def _generate_hybrid_bus_numbers(self, data: Dict[str, Any]) -> None:
        """Generate new unique bus numbers for nodes (for RAWX-like data)."""
        # Try RAWX section name first, then canonical
        node_section = data.get('subnode', data.get('node', {}))
        if not node_section.get('data'):
            return
        
        # Create bus section if it doesn't exist
        if 'bus' not in data:
            data['bus'] = {'fields': [], 'data': []}
        
        # Standard bus fields
        bus_fields = ['ibus', 'name', 'basekv', 'ide', 'area', 'zone', 'owner', 'va', 'vm', 'nvhi', 'nvlo', 'evhi', 'evlo']
        data['bus']['fields'] = bus_fields
        
        node_fields = node_section['fields']
        
        # Generate new bus numbers and create bus records
        for node in node_section['data']:
            # Extract node information - use RAWX field names first, then fallbacks
            sub_id = self._get_field_value(node, node_fields, ['isub', 'substation', 'sub_id', 'substation_id'], 1)
            node_id = self._get_field_value(node, node_fields, ['inode', 'node', 'node_id'], 1)
            node_name = self._get_field_value(node, node_fields, ['name', 'node_name'], f"HYB_BUS_{sub_id}_{node_id}")
            
            # Generate unique hybrid bus number
            new_bus_number = HYBRID_BUS_BASE + (sub_id * 100) + node_id
            
            # Create bus record
            bus_record = [
                new_bus_number,     # ibus
                node_name[:12],     # name (truncated to 12 chars)
                138.0,              # basekv (default)
                1,                  # ide (PQ bus)
                1,                  # area
                1,                  # zone  
                1,                  # owner
                0.0,                # va (voltage angle)
                1.0,                # vm (voltage magnitude)
                1.1,                # nvhi (normal voltage high)
                0.9,                # nvlo (normal voltage low)
                1.1,                # evhi (emergency voltage high)
                0.9                 # evlo (emergency voltage low)
            ]
            
            data['bus']['data'].append(bus_record)
    
    def _convert_switches_to_hybrid_branches(self, data: Dict[str, Any], switch_status: str) -> Dict[str, Any]:
        """Convert switching devices to zero-impedance branches."""
        # Try RAWX section name first, then canonical
        switching_section = data.get('subswd', data.get('switching_device', {}))
        if not switching_section.get('data'):
            return data
        
        # Get or create acline section
        if 'acline' not in data:
            data['acline'] = {'fields': [], 'data': []}
        
        # Use existing field structure if available, otherwise create standard one
        if not data['acline']['fields']:
            # Standard branch fields for new section
            branch_fields = ['ibus', 'jbus', 'ckt', 'r', 'x', 'b', 'ratea', 'rateb', 'ratec', 'ratio', 'angle', 'gi', 'bi', 'gj', 'bj', 'st', 'met', 'len', 'o1', 'f1', 'o2', 'f2', 'o3', 'f3', 'o4', 'f4']
            data['acline']['fields'] = branch_fields
        
        # Use existing field structure for consistency
        branch_fields = data['acline']['fields']
        switching_fields = switching_section['fields']
        
        # Convert each switching device to a zero-impedance branch
        for switch in switching_section['data']:
            # Get switch information - use RAWX field names first, then fallbacks
            sub_id = self._get_field_value(switch, switching_fields, ['isub', 'substation', 'sub_id'], 1)
            from_node = self._get_field_value(switch, switching_fields, ['inode', 'from_node', 'ni'], 1)
            to_node = self._get_field_value(switch, switching_fields, ['jnode', 'to_node', 'nj'], 2)
            switch_id = self._get_field_value(switch, switching_fields, ['swdid', 'id', 'ckt'], '1')
            
            # Determine switch status
            if switch_status == "current":
                # Use current status - RAWX uses 'stat' field
                status = self._get_field_value(switch, switching_fields, ['stat', 'status'], 1)
            else:  # switch_status == "normal"
                # Use normal status - RAWX uses 'nstat' field
                normal_status = self._get_field_value(switch, switching_fields, ['nstat', 'normal_status'], 1)
                status = normal_status
            
            # Extract ownership data from original switch (switching devices typically don't have ownership)
            # Use default ownership pattern: owner 1 with 100% ownership (matching typical AC line pattern)
            o1 = self._get_field_value(switch, switching_fields, ['o1', 'owner1'], 1)
            f1 = self._get_field_value(switch, switching_fields, ['f1', 'fraction1'], 1.0)
            o2 = self._get_field_value(switch, switching_fields, ['o2', 'owner2'], 0)
            f2 = self._get_field_value(switch, switching_fields, ['f2', 'fraction2'], 0.0)
            o3 = self._get_field_value(switch, switching_fields, ['o3', 'owner3'], 0)
            f3 = self._get_field_value(switch, switching_fields, ['f3', 'fraction3'], 0.0)
            o4 = self._get_field_value(switch, switching_fields, ['o4', 'owner4'], 0)
            f4 = self._get_field_value(switch, switching_fields, ['f4', 'fraction4'], 0.0)
            
            # Generate bus numbers for nodes
            from_bus = HYBRID_BUS_BASE + (sub_id * 100) + from_node
            to_bus = HYBRID_BUS_BASE + (sub_id * 100) + to_node
            
            # Create zero-impedance branch record matching existing field structure
            branch_record = [None] * len(branch_fields)  # Initialize with correct length
            
            # Map values to correct field positions
            field_map = {field: i for i, field in enumerate(branch_fields)}
            
            # Basic branch data
            if 'ibus' in field_map: branch_record[field_map['ibus']] = from_bus
            if 'jbus' in field_map: branch_record[field_map['jbus']] = to_bus
            if 'ckt' in field_map: branch_record[field_map['ckt']] = switch_id
            if 'r' in field_map: branch_record[field_map['r']] = 0.0
            if 'x' in field_map: branch_record[field_map['x']] = 1e-6
            if 'b' in field_map: branch_record[field_map['b']] = 0.0
            
            # Set ratings (use different field names for different formats)
            for rate_field in ['ratea', 'rate1']: 
                if rate_field in field_map: branch_record[field_map[rate_field]] = 9999.0
            for rate_field in ['rateb', 'rate2']: 
                if rate_field in field_map: branch_record[field_map[rate_field]] = 9999.0
            for rate_field in ['ratec', 'rate3']: 
                if rate_field in field_map: branch_record[field_map[rate_field]] = 9999.0
                
            # Additional ratings for V35 format
            for i in range(4, 13):
                rate_field = f'rate{i}'
                if rate_field in field_map: branch_record[field_map[rate_field]] = 0.0
            
            # Other electrical parameters
            if 'ratio' in field_map: branch_record[field_map['ratio']] = 0.0
            if 'angle' in field_map: branch_record[field_map['angle']] = 0.0
            if 'gi' in field_map: branch_record[field_map['gi']] = 0.0
            if 'bi' in field_map: branch_record[field_map['bi']] = 0.0
            if 'gj' in field_map: branch_record[field_map['gj']] = 0.0
            if 'bj' in field_map: branch_record[field_map['bj']] = 0.0
            if 'st' in field_map: branch_record[field_map['st']] = status
            if 'stat' in field_map: branch_record[field_map['stat']] = status  # Alternative field name
            if 'met' in field_map: branch_record[field_map['met']] = 1
            if 'len' in field_map: branch_record[field_map['len']] = 0.0
            if 'length' in field_map: branch_record[field_map['length']] = 0.0  # Alternative field name
            
            # Set name if field exists
            if 'name' in field_map: branch_record[field_map['name']] = f"HYBRID_SWITCH_{from_bus}_{to_bus}"
            
            # Ownership data
            if 'o1' in field_map: branch_record[field_map['o1']] = o1
            if 'f1' in field_map: branch_record[field_map['f1']] = f1
            if 'o2' in field_map: branch_record[field_map['o2']] = o2
            if 'f2' in field_map: branch_record[field_map['f2']] = f2
            if 'o3' in field_map: branch_record[field_map['o3']] = o3
            if 'f3' in field_map: branch_record[field_map['f3']] = f3
            if 'o4' in field_map: branch_record[field_map['o4']] = o4
            if 'f4' in field_map: branch_record[field_map['f4']] = f4
            
            data['acline']['data'].append(branch_record)
        
        return data
    
    def _update_terminal_connections(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update terminal connections to use new hybrid bus numbers."""
        # Try RAWX section name first, then canonical
        terminal_section = data.get('subterm', data.get('terminal', {}))
        if not terminal_section.get('data'):
            print("   ⚪ No terminal data to update")
            return data
        
        # For now, we'll implement a basic version that logs what terminals we have
        # The full implementation would update equipment connections (loads, generators, transformers)
        # to reference the new hybrid bus numbers instead of node references
        print(f"   ⚪ Terminal connection updating not yet implemented - skipping {len(terminal_section['data'])} terminals")
        
        # Future implementation would:
        # 1. Map terminal equipment IDs to their new hybrid bus numbers
        # 2. Update load, generator, transformer sections to use new bus numbers
        # 3. Handle multi-terminal equipment (transformers) correctly
        
        return data
    
    def _get_field_value(self, record: list, fields: list, field_names: list, default_value):
        """Get field value from record using multiple possible field names."""
        for field_name in field_names:
            if field_name in fields:
                field_index = fields.index(field_name)
                if field_index < len(record) and record[field_index] is not None:
                    return record[field_index]
        return default_value

    def __repr__(self):
        summary = self.get_summary()
        total_items = sum(summary.values())
        return f"Backend(loaded={bool(self._data)}, items={total_items}, sections={list(summary.keys())})"


# Convenience functions for even simpler usage
def load(file_path: Union[str, Path]) -> Backend:
    """Convenience function: load any file format."""
    return Backend.load(file_path)


def convert(input_path: Union[str, Path], output_path: Union[str, Path]) -> Backend:
    """
    Convenience function: convert any format to any other format.
    
    Examples:
        convert("data.json", "data.xlsx")      # JSON → Excel
        convert("hdb.json", "output.db")       # HDB → SQLite  
    """
    return Backend.load(input_path).save(output_path)


def to_raw(input_path: Union[str, Path], output_path: Union[str, Path], 
           version: str = "33", modeling: str = "bus_branch") -> str:
    """
    Convenience function: convert any format directly to PSS/E RAW.
    
    Examples:
        to_raw("hdbcontext_export.json", "system.raw")
        to_raw("data.xlsx", "network.raw", "34", "hybrid")
    """
    return Backend.load(input_path).export_raw(output_path, version, modeling) 