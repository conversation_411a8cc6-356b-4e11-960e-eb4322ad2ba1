@echo off
REM Generate API documentation from docstrings
python generate_api_docs.py

REM Check if mkdocs is installed
where mkdocs >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo MkDocs not found. Installing with pip...
    pip install mkdocs
)

REM Start mkdocs serve in the background
start "MkDocs Server" cmd /k "mkdocs serve"

REM Wait a moment for the server to start
ping 127.0.0.1 -n 3 >nul

REM Open the homepage in the default browser
start http://127.0.0.1:8000/ 