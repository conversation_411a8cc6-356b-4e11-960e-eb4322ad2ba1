graph TB
    subgraph "Input Layer"
        HDB[HDB Files]
        RAWX[RAWX Files]
        JSON[JSON Files]
    end

    subgraph "Backend Layer"
        HdbBackend["HdbBackend<br/>- Parses HDB JSON<br/>- Embeds node info in equipment<br/>- Always produces NODE_BREAKER canonical"]
        RawxBackend["RawxBackend<br/>- Parses PSS/E RAW format<br/>- Detects modeling approach<br/>- Preserves original modeling"]
    end

    subgraph "Canonical Data Layer"
        CanonicalData["Canonical Data<br/>- Immutable dictionaries<br/>- Human-readable field names<br/>- Embedded node information"]
        CanonicalInterface["CanonicalDataInterface<br/>- Type-safe access<br/>- Prevents direct backend access<br/>- Field mapping integration"]
    end

    subgraph "Transformation Layer"
        ModelTransformations["ModelTransformations<br/>- Node-to-bus mapping<br/>- Equipment bus number updates<br/>- Modeling approach conversion"]
        NodeMapping["Node Mapping Tables<br/>node_to_new_bus:<br/>(substation_id, node_id) → bus_number"]
    end

    subgraph "Export Layer"
        Writers["RawSectionWriter Classes<br/>- BusWriter, LoadWriter, etc.<br/>- Hierarchical writers<br/>- Canonical interface only"]
        ExportFunction["export_to_raw_format<br/>- Orchestrates export process<br/>- Handles modeling approach<br/>- Hierarchical vs flat logic"]
    end

    subgraph "Output Layer"
        BusBranch["Bus-Branch RAW Files<br/>- Traditional PSS/E format<br/>- Flat bus structure"]
        NodeBreaker["Node-Breaker RAW Files<br/>- Hierarchical substation format<br/>- Full topology detail"]
        Hybrid["Hybrid RAW Files<br/>- Mixed representation<br/>- Substation data + simplified buses"]
    end

    HDB --> HdbBackend
    RAWX --> RawxBackend
    JSON --> HdbBackend

    HdbBackend --> CanonicalData
    RawxBackend --> CanonicalData

    CanonicalData --> CanonicalInterface
    CanonicalInterface --> ModelTransformations

    ModelTransformations --> NodeMapping
    NodeMapping --> Writers

    Writers --> ExportFunction
    ExportFunction --> BusBranch
    ExportFunction --> NodeBreaker
    ExportFunction --> Hybrid 