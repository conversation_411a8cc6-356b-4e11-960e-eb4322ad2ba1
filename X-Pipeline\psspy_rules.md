# PSSPY Pipeline Architectural Rules

**Version:** 1.0  
**Date:** January 6, 2025  
**Purpose:** Prevent AI edits from violating canonical data flow architecture  

## 🚨 CRITICAL ARCHITECTURAL RULES

### Rule 1: Canonical Data Flow Enforcement

**MANDATORY**: All export methods MUST access data ONLY through the canonical data interface. Direct backend data access is FORBIDDEN.

```python
# ❌ FORBIDDEN - Direct backend access
def export_to_rawx(self, output_file):
    buses = self.backend_data.buses  # VIOLATION
    generators = self.backend_data.generators  # VIOLATION

# ✅ REQUIRED - Canonical data access
def export_to_rawx(self, canonical_data: CanonicalDataInterface, output_file):
    buses = canonical_data.get_buses()  # CORRECT
    generators = canonical_data.get_generators()  # CORRECT
```

### Rule 2: Backend Data Encapsulation

**MANDATORY**: All backend data structures MUST be private and accessed only through controlled interfaces.

```python
# ❌ FORBIDDEN - Public backend data
class Pipeline:
    def __init__(self):
        self.backend_data = {}  # VIOLATION - public access

# ✅ REQUIRED - Private backend data
class Pipeline:
    def __init__(self):
        self._backend_data = {}  # CORRECT - private
        self._data_manager = BackendDataManager()
    
    def get_canonical_data(self) -> CanonicalDataInterface:
        return self._data_manager.get_canonical_interface()
```

### Rule 3: Export Method Signatures

**MANDATORY**: All export methods MUST accept canonical data as their first parameter.

```python
# ❌ FORBIDDEN - No canonical data parameter
def export_raw(self, output_file):
    pass

# ✅ REQUIRED - Canonical data parameter
def export_raw(self, canonical_data: CanonicalDataInterface, output_file: str):
    pass
```

### Rule 4: Data Validation Layer

**MANDATORY**: All data MUST pass through the canonical validation layer before export.

```python
# ❌ FORBIDDEN - No validation
def process_data(self, raw_data):
    return raw_data  # VIOLATION - no validation

# ✅ REQUIRED - Validation through canonical interface
def process_data(self, raw_data):
    canonical_data = self._validate_and_convert(raw_data)
    return canonical_data
```

## 🔒 ENFORCEMENT MECHANISMS

### Rule 5: Name Mangling for Backend Data

**MANDATORY**: All backend data attributes MUST use double underscore prefix to prevent accidental access.

```python
# ❌ FORBIDDEN - Single underscore
class BackendManager:
    def __init__(self):
        self._data = {}  # VIOLATION - accessible

# ✅ REQUIRED - Double underscore
class BackendManager:
    def __init__(self):
        self.__data = {}  # CORRECT - name mangled
```

### Rule 6: Interface Segregation

**MANDATORY**: Separate interfaces for data editing and data export.

```python
# ❌ FORBIDDEN - Single interface
class DataInterface:
    def edit_data(self): pass
    def export_data(self): pass  # VIOLATION - mixed concerns

# ✅ REQUIRED - Separate interfaces
class DataEditInterface:
    def edit_data(self): pass

class DataExportInterface:
    def export_data(self): pass
```

### Rule 7: Immutable Export Data

**MANDATORY**: Canonical data provided to export methods MUST be immutable.

```python
# ❌ FORBIDDEN - Mutable export data
def export_data(self, data):
    data['modified'] = True  # VIOLATION - mutation

# ✅ REQUIRED - Immutable data
def export_data(self, canonical_data: CanonicalDataInterface):
    # canonical_data is immutable - no modification possible
    pass
```

## 🛡️ FAIL-SAFE PATTERNS

### Rule 8: Capability-Based Access Control

**MANDATORY**: Use capability objects to control data access.

```python
# ❌ FORBIDDEN - Direct access
class ExportMethod:
    def __init__(self, pipeline):
        self.pipeline = pipeline  # VIOLATION - full access

# ✅ REQUIRED - Capability-based access
class ExportMethod:
    def __init__(self, canonical_data_capability):
        self.canonical_data = canonical_data_capability  # CORRECT - limited access
```

### Rule 9: Context Managers for Safe Editing

**MANDATORY**: Use context managers for any backend data editing operations.

```python
# ❌ FORBIDDEN - Direct editing
def modify_data(self):
    self.backend_data['key'] = 'value'  # VIOLATION

# ✅ REQUIRED - Context manager
def modify_data(self):
    with self._edit_context() as editor:
        editor.set_value('key', 'value')  # CORRECT
```

### Rule 10: Type-Safe Factory Patterns

**MANDATORY**: Use factory patterns to create properly configured objects.

```python
# ❌ FORBIDDEN - Direct instantiation
def create_export(self):
    return ExportMethod(self)  # VIOLATION - direct access

# ✅ REQUIRED - Factory pattern
def create_export(self):
    return ExportMethodFactory.create_with_canonical_access(self.get_canonical_interface())
```

## 📋 IMPLEMENTATION CHECKLIST

### Before Making Any Changes:

1. **Architectural Review**: Does this change maintain canonical data flow?
2. **Interface Check**: Are all data access patterns through canonical interface?
3. **Validation Layer**: Is data validation maintained?
4. **Encapsulation**: Are backend data structures properly encapsulated?
5. **Test Coverage**: Do tests verify architectural compliance?

### During Implementation:

1. **No Direct Backend Access**: Verify no export method accesses backend data directly
2. **Canonical Parameters**: Ensure all export methods accept canonical data
3. **Validation**: Confirm data passes through validation layer
4. **Immutability**: Verify export data is immutable
5. **Interface Separation**: Maintain separate edit and export interfaces

### After Implementation:

1. **Architectural Tests**: Run compliance tests
2. **Performance Check**: Verify no significant performance degradation
3. **Documentation**: Update architecture documentation
4. **Code Review**: Review for architectural violations
5. **Integration Test**: Test with existing systems

## 🚫 FORBIDDEN PATTERNS

### Never Do These:

1. **Direct Backend Access in Exports**:
   ```python
   # NEVER DO THIS
   def export_method(self):
       return self.backend_data  # VIOLATION
   ```

2. **Public Backend Data**:
   ```python
   # NEVER DO THIS
   class Pipeline:
       backend_data = {}  # VIOLATION
   ```

3. **Mixed Interface Concerns**:
   ```python
   # NEVER DO THIS
   class DataInterface:
       def edit_and_export(self): pass  # VIOLATION
   ```

4. **Mutable Export Data**:
   ```python
   # NEVER DO THIS
   def export(self, data):
       data['modified'] = True  # VIOLATION
   ```

5. **Bypass Validation**:
   ```python
   # NEVER DO THIS
   def process(self, raw_data):
       return raw_data  # VIOLATION - no validation
   ```

## ✅ REQUIRED PATTERNS

### Always Do These:

1. **Canonical Data Access**:
   ```python
   # ALWAYS DO THIS
   def export_method(self, canonical_data: CanonicalDataInterface):
       buses = canonical_data.get_buses()
       generators = canonical_data.get_generators()
   ```

2. **Private Backend Data**:
   ```python
   # ALWAYS DO THIS
   class Pipeline:
       def __init__(self):
           self.__backend_data = {}  # Private
   ```

3. **Separate Interfaces**:
   ```python
   # ALWAYS DO THIS
   class EditInterface:
       def edit_data(self): pass
   
   class ExportInterface:
       def export_data(self): pass
   ```

4. **Immutable Export Data**:
   ```python
   # ALWAYS DO THIS
   def export(self, canonical_data: CanonicalDataInterface):
       # canonical_data is immutable
       pass
   ```

5. **Validation Layer**:
   ```python
   # ALWAYS DO THIS
   def process(self, raw_data):
       return self._validate_and_convert(raw_data)
   ```

## 🔍 COMPLIANCE VERIFICATION

### Automated Checks:

1. **Static Analysis**: Use tools to detect direct backend access
2. **Type Checking**: Enforce canonical data interface types
3. **Import Analysis**: Prevent unauthorized imports
4. **Method Signature Validation**: Ensure canonical parameters
5. **Architectural Tests**: Automated compliance verification

### Manual Reviews:

1. **Code Review**: Check for architectural violations
2. **Interface Review**: Verify proper interface usage
3. **Data Flow Review**: Trace data through canonical interface
4. **Performance Review**: Ensure no performance regressions
5. **Documentation Review**: Verify architecture documentation

## 📚 REFERENCE DOCUMENTATION

### Key Files:

- `architectural_plan_pipeline_canonical_flow_2025-01-06.md` - Detailed implementation plan
- `canonical_data_interface.py` - Canonical data interface definition
- `backend_data_manager.py` - Backend data management
- `test_architecture_compliance.py` - Compliance verification tests

### Architecture Principles:

1. **Separation of Concerns**: Backend data management separate from export logic
2. **Data Validation**: All data validated through canonical interface
3. **Immutability**: Export data is immutable
4. **Interface Segregation**: Separate edit and export interfaces
5. **Encapsulation**: Backend data properly encapsulated

---

**These rules MUST be followed by ALL AI assistants and developers working on this codebase. Violations will result in architectural non-compliance and potential system failures.** 