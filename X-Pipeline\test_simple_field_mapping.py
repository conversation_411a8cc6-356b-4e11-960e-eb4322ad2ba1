#!/usr/bin/env python3
"""
Simple test to verify field mapping functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hdb_to_raw_pipeline import PureFieldMapper

def test_simple_field_mapping():
    """Test basic field mapping functionality."""
    print("Testing simple field mapping...")
    
    field_mapper = PureFieldMapper()
    
    # Test substation mapping with correct field names
    print("\nTesting substation mapping:")
    substation_record = ['1', 'SUBSTATION1', '40.0', '-74.0', '0.0']  # [isub, name, lati, long, srg]
    substation_fields = ['isub', 'name', 'lati', 'long', 'srg']  # Field names for the record
    mapped_record = field_mapper.map_record('substation', substation_record, substation_fields)
    print(f"Original: {substation_record}")
    print(f"Fields: {substation_fields}")
    print(f"Mapped: {mapped_record}")
    print(f"substation_number: {mapped_record.get('substation_number')}")
    print(f"substation_name: {mapped_record.get('substation_name')}")
    
    # Test node mapping with correct field names
    print("\nTesting node mapping:")
    node_record = ['1', '1', 'NODE1', '101', '1', '1.0', '0.0']  # [isub, inode, name, ibus, stat, vm, va]
    node_fields = ['isub', 'inode', 'name', 'ibus', 'stat', 'vm', 'va']  # Field names for the record
    mapped_record = field_mapper.map_record('node', node_record, node_fields)
    print(f"Original: {node_record}")
    print(f"Fields: {node_fields}")
    print(f"Mapped: {mapped_record}")
    print(f"substation_number: {mapped_record.get('substation_number')}")
    print(f"node_number: {mapped_record.get('node_number')}")
    print(f"bus_number: {mapped_record.get('bus_number')}")
    
    # Test switching device mapping with correct field names
    print("\nTesting switching device mapping:")
    swd_record = ['1', '1', '2', 'SW1', 'SWITCH1', '1', '1']  # [isub, inode, jnode, swdid, name, type, stat]
    swd_fields = ['isub', 'inode', 'jnode', 'swdid', 'name', 'type', 'stat']  # Field names for the record
    mapped_record = field_mapper.map_record('switching_device', swd_record, swd_fields)
    print(f"Original: {swd_record}")
    print(f"Fields: {swd_fields}")
    print(f"Mapped: {mapped_record}")
    print(f"substation_number: {mapped_record.get('substation_number')}")
    print(f"from_node: {mapped_record.get('from_node')}")
    print(f"to_node: {mapped_record.get('to_node')}")
    print(f"device_id: {mapped_record.get('device_id')}")
    
    print("\n✅ Simple field mapping test completed!")

if __name__ == "__main__":
    test_simple_field_mapping() 