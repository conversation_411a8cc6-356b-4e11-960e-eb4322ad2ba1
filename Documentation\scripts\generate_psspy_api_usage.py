import os
import re
from collections import defaultdict
import csv

# === Output mode: 'print', 'file', or 'both' ===
OUTPUT_MODE = 'both'  # Change to 'print', 'file', or 'both' as needed

API_DOC_PATH = os.path.join('Documentation', 'docs', 'API.md')
WORKSPACE_ROOT = '.'
CSV_OUTPUT_PATH = os.path.join(os.path.dirname(__file__), 'All_Active_PSSPY_Commands.csv')

# Build a mapping from psspy command (UPPERCASE) to (section number, line number)
psspy_section_map = {}
# Match lines like '2.1.1. COMMAND' or '2.1.1. **COMMAND**'
section_pattern = re.compile(r'^(\d+(?:\.\d+)+)\.\s+(?:\*\*)?([A-Z0-9_]+)(?:\*\*)?')
# Match lines like '**19.7. GETDEFAULTCHAR**'
bold_section_pattern = re.compile(r'^\*\*(\d+(?:\.\d+)*)\.\s+([A-Z0-9_]+)\*\*')

with open(API_DOC_PATH, encoding='utf-8') as f:
    for lineno, line in enumerate(f, 1):
        line_stripped = line.strip()
        match = section_pattern.match(line_stripped)
        if match:
            section_num = match.group(1)
            cmd = match.group(2)
            psspy_section_map[cmd] = (section_num, lineno)
        else:
            match_bold = bold_section_pattern.match(line_stripped)
            if match_bold:
                section_num = match_bold.group(1)
                cmd = match_bold.group(2)
                psspy_section_map[cmd] = (section_num, lineno)

# Debug: print the parsed mapping
print(f'Parsed {len(psspy_section_map)} commands from API.md (section-numbered)')
for cmd, (section, lineno) in list(psspy_section_map.items())[:20]:
    print(f'  {cmd}: {section} (line {lineno})')
if len(psspy_section_map) > 20:
    print('  ...')

# Recursively scan for psspy.* calls, group by file
psspy_cmd_pattern = re.compile(r'psspy\.([a-zA-Z0-9_]+)')
file_calls = {}
for root, _, files in os.walk(WORKSPACE_ROOT):
    for fname in files:
        if fname.endswith('.py'):
            fpath = os.path.join(root, fname)
            try:
                with open(fpath, encoding='utf-8') as f:
                    for i, line in enumerate(f, 1):
                        for match in psspy_cmd_pattern.finditer(line):
                            cmd = match.group(1)
                            section_info = psspy_section_map.get(cmd.upper(), None)
                            if section_info:
                                section, api_lineno = section_info
                                section_str = f'{section} (line {api_lineno})'
                            else:
                                section_str = 'UNKNOWN'
                            file_calls.setdefault(fpath, []).append((i, cmd, section_str))
            except Exception as e:
                file_calls.setdefault(fpath, []).append((None, f'# Could not read: {e}', 'ERROR'))

# Prepare CSV output if needed
csv_rows = []

for fpath, calls in sorted(file_calls.items()):
    if OUTPUT_MODE in ('print', 'both'):
        print(f'{fpath}:')
    # Deduplicate by command, but count and track first line
    cmd_map = defaultdict(lambda: {'count': 0, 'first_line': None, 'section': None})
    for i, cmd, section in calls:
        if i is not None:
            entry = cmd_map[cmd]
            entry['count'] += 1
            if entry['first_line'] is None or i < entry['first_line']:
                entry['first_line'] = i
                entry['section'] = section
        else:
            if OUTPUT_MODE in ('print', 'both'):
                print(f'    {cmd}')
    for cmd, info in sorted(cmd_map.items()):
        padded_cmd = cmd if len(cmd) > 30 else cmd.ljust(30)
        if len(cmd) > 30:
            padded_cmd += ' '
        count_str = str(info['count']).rjust(2)
        first_line_str = str(info['first_line']).rjust(4)
        # Parse section and line from info['section']
        section = info['section']
        if section and section != 'UNKNOWN' and '(' in section and 'line' in section:
            # e.g., '19.7 (line 138377)'
            try:
                sec, line = section.split(' (line ')
                line = line.rstrip(')')
                section_str = sec.ljust(9)
                line_str = line.rjust(6)
            except Exception:
                section_str = section.ljust(9)
                line_str = ''.rjust(6)
                sec = section
                line = ''
        else:
            section_str = (section if section else '').ljust(9)
            line_str = ''.rjust(6)
            sec = section if section else ''
            line = ''
        if OUTPUT_MODE in ('print', 'both'):
            print(f'    {padded_cmd}# count: {count_str} first line: {first_line_str} API.md section: {section_str} line: {line_str}')
        if OUTPUT_MODE in ('file', 'both'):
            csv_rows.append({
                'command': cmd,
                'section': sec,
                'line': line,
                'count': info['count'],
                'first_line': info['first_line'],
                'filename': fpath
            })

if OUTPUT_MODE in ('file', 'both'):
    with open(CSV_OUTPUT_PATH, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=['command', 'section', 'line', 'count', 'first_line', 'filename'])
        writer.writeheader()
        for row in csv_rows:
            writer.writerow(row)
    print(f'Wrote CSV output to {CSV_OUTPUT_PATH}') 