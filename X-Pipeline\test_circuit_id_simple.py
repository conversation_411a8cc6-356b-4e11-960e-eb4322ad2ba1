#!/usr/bin/env python3
"""
Simple Circuit ID Test

Check circuit ID formatting in output files.
"""

import os
from pathlib import Path

def check_file(file_path):
    """Check a single file for circuit ID patterns."""
    print(f"\n📁 Checking: {file_path}")
    print("-" * 50)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for branch data section
        if 'BEGIN BRANCH DATA' in content:
            print("✅ Found branch data section")
            
            # Extract branch lines
            lines = content.split('\n')
            branch_section = False
            branch_count = 0
            zero_impedance_count = 0
            
            for line in lines:
                if 'BEGIN BRANCH DATA' in line:
                    branch_section = True
                    continue
                elif 'END OF BRANCH DATA' in line:
                    branch_section = False
                    continue
                elif branch_section and line.strip() and not line.startswith('@') and not line.startswith('0 /'):
                    # This is a branch line
                    branch_count += 1
                    parts = line.split(',')
                    if len(parts) >= 7:
                        ckt = parts[2].strip().strip("'")
                        r = float(parts[3].strip()) if parts[3].strip() else 0
                        x = float(parts[4].strip()) if parts[4].strip() else 0
                        name = parts[6].strip().strip("'") if len(parts) > 6 else ""
                        
                        # Check for zero impedance branches
                        if abs(r) < 0.001 and abs(x) < 0.001:
                            zero_impedance_count += 1
                            if ckt == "ZZ":
                                print(f"✅ Zero impedance branch with correct 'ZZ' circuit ID: {name}")
                            else:
                                print(f"❌ Zero impedance branch with incorrect circuit ID '{ckt}' (should be 'ZZ'): {name}")
                        else:
                            # Regular branch
                            if ckt.startswith('@'):
                                print(f"✅ Circuit breaker with '@' prefix: {ckt} - {name}")
                            elif ckt.startswith('*'):
                                print(f"✅ Disconnect with '*' prefix: {ckt} - {name}")
                            elif ckt == "ZZ":
                                print(f"❌ Regular branch incorrectly using 'ZZ': {ckt} - {name}")
                            else:
                                print(f"✅ Regular branch with normal circuit ID: {ckt} - {name}")
            
            print(f"\n📊 Summary:")
            print(f"   Total branches: {branch_count}")
            print(f"   Zero impedance branches: {zero_impedance_count}")
            
        else:
            print("❌ No branch data section found")
        
        # Look for transformer data section
        if 'BEGIN TRANSFORMER DATA' in content:
            print("✅ Found transformer data section")
            
            # Extract transformer lines
            lines = content.split('\n')
            transformer_section = False
            transformer_count = 0
            
            for line in lines:
                if 'BEGIN TRANSFORMER DATA' in line:
                    transformer_section = True
                    continue
                elif 'END OF TRANSFORMER DATA' in line:
                    transformer_section = False
                    continue
                elif transformer_section and line.strip() and not line.startswith('@') and not line.startswith('0 /'):
                    # This is a transformer line
                    transformer_count += 1
                    parts = line.split(',')
                    if len(parts) >= 12:
                        ckt = parts[3].strip().strip("'")
                        name = parts[10].strip().strip("'") if len(parts) > 10 else ""
                        
                        # Transformers should not have special prefixes
                        if ckt.startswith('@') or ckt.startswith('*') or ckt == "ZZ":
                            print(f"❌ Transformer with incorrect circuit ID '{ckt}' (should be Number % 100): {name}")
                        else:
                            print(f"✅ Transformer with correct circuit ID: {ckt} - {name}")
            
            print(f"   Total transformers: {transformer_count}")
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    """Main function."""
    print("🔍 Simple Circuit ID Formatting Check")
    print("=" * 50)
    
    # Check specific files
    output_dir = Path(__file__).parent / "output_demo"
    
    test_files = [
        "SAVNW_NB_v35_hybrid_current.raw",
        "HDB_export_v35_hybrid_current.raw"
    ]
    
    for filename in test_files:
        file_path = output_dir / filename
        if file_path.exists():
            check_file(str(file_path))
        else:
            print(f"❌ File not found: {filename}")

if __name__ == "__main__":
    main() 