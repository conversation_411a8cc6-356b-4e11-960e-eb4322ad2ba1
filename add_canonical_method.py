# Add canonical method to SystemDataWriter
with open('hdb_to_raw_pipeline.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Find the SystemDataWriter class and add the canonical method
old_text = '''        return records_written


class RawExportRegistry:'''

new_text = '''        return records_written

    def write_section_canonical(self, canonical_interface, output_file) -> int:
        """Write system-wide data using canonical interface (ARCHITECTURAL COMPLIANCE)."""
        # V33 does not support system-wide data - skip writing
        if self.version == RawVersion.V33:
            return 0
            
        records_written = 0
        
        # Use get_section method to access system data sections
        # Add default rating entries if none exist
        rating_entries = [
            ('RatingName1', 'Description1 '),
            ('RatingName2', 'Description2 '),
            ('RatingName3', 'Description3 '),
            ('RatingName4', 'Description4 '),
            ('RatingName5', 'Description5 '),
            ('RatingName6', 'Description6 '),
            ('RatingName7', 'Description7 '),
            ('RatingName8', 'Description8 '),
            ('RatingName9', 'Description9 '),
            ('RatingName10', 'Description10 '),
            ('RatingName11', 'Description11 '),
            ('RatingName12', 'Description12 ')
        ]
        
        for i, (code, description) in enumerate(rating_entries, 1):
            output_file.write(f"RATING,{i:>2}, \"{code}\", \"{description}\"\\n")
            records_written += 1
        
        return records_written


class RawExportRegistry:'''

# Replace the text
new_content = content.replace(old_text, new_text)

# Write the updated content back
with open('hdb_to_raw_pipeline.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print('Added canonical method to SystemDataWriter') 