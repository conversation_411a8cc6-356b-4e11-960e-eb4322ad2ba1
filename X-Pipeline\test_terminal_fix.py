#!/usr/bin/env python3

from hdb_to_raw_pipeline import RawxBackend, ModelTransformations

print("Testing terminal section after architectural fix...")

backend = RawxBackend()
backend.load('savnw_nb.rawx')
canonical = backend.to_canonical()

print(f"Total canonical sections: {len(canonical)}")

print("\nTerminal section check:")
if 'subterm' in canonical:
    subterm = canonical['subterm']
    print(f"  subterm: {len(subterm.get('data', []))} records")
    print(f"  fields: {subterm.get('fields', [])}")
else:
    print("  subterm: NOT FOUND")

if 'terminal' in canonical:
    terminal = canonical['terminal'] 
    print(f"  terminal: {len(terminal.get('data', []))} records")
    print(f"  fields: {terminal.get('fields', [])}")
else:
    print("  terminal: NOT FOUND")

print("\nTesting hybrid conversion...")
result = ModelTransformations.convert_to_hybrid_modeling(canonical)
print(f"Success: {result.success}")
if result.errors:
    print("Errors:")
    for error in result.errors:
        print(f"  - {error}")
if result.warnings:
    print("Warnings:")
    for warning in result.warnings:
        print(f"  - {warning}") 