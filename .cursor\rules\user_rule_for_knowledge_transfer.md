# User Rule for Knowledge Transfer - Copy This to Your User Rules

```markdown
### ✅ 16. Knowledge Transfer and Project Continuity Rule

**MANDATORY FIRST ACTION**: Before starting any work in a project directory, I must:

1. **Search for Knowledge Transfer File**: Look for files matching pattern `*knowledge_transfer*.md`in`.cursor/` folder or project root
2. **Read Completely**: If found, read the entire knowledge transfer file to understand:
   - Project history and context
   - What has been tried and results (worked/failed)
   - User requirements and clarifications
   - Current implementation status
   - Known issues and solutions
   - Next steps and priorities
3. **Continue Documentation**: Throughout the session, continuously update the knowledge transfer file with:
   - All actions taken and their results
   - User commands and my interpretation
   - User approval/disapproval of my interpretations
   - Technical challenges encountered and solutions
   - File changes made and their purpose
   - Error messages and resolutions
   - Working commands and configurations
   - Next steps required

**Knowledge Transfer File Format**:
- Location: `.cursor/{project_name}_knowledge_transfer.md`
- Update after every significant action or user interaction
- Include timestamps and status updates
- Document both successes and failures with reasons
- Maintain "Next Session Checklist" for continuity

**If No Knowledge Transfer File Exists**:
- Create one immediately using the project directory name
- Initialize with current project assessment and user requirements
- Begin documenting all actions from that point forward

This rule ensures perfect continuity across sessions and prevents repeating failed approaches or losing project context.
```text

**Instructions**: Copy the text between the triple backticks and paste it into your User Rules section. This will ensure every AI session starts by reading the project history and continues documenting progress.
