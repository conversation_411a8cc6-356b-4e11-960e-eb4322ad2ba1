#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from hdb_to_raw_pipeline import RawxBackend, configure_logging
import json

# Configure logging
configure_logging()

print("🔍 Loading RAWX file and checking canonical sections...")

file_path = "savnw_nb.rawx"

# Test 1: Constructor-based loading (the problematic path)
print("\n=== TEST 1: Constructor-based loading (current failing method) ===")
try:
    rawx_backend = RawxBackend(file_path=file_path)
    print(f"✅ Constructor completed successfully")
    print(f"📊 Backend data sections after constructor: {len(rawx_backend.data)}")
    
    if rawx_backend.data:
        print("🗂️ Backend data sections:")
        for section_name, section_data in rawx_backend.data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                print(f"  ✅ {section_name}: {len(section_data['data'])} records")
            elif isinstance(section_data, list):
                print(f"  ✅ {section_name}: {len(section_data)} records (list)")
            else:
                print(f"  ⚠️ {section_name}: {type(section_data)}")
    else:
        print("❌ No data in backend after constructor!")
        
    # Test canonical conversion
    canonical_data = rawx_backend.to_canonical()
    print(f"📊 CANONICAL data sections: {len(canonical_data)}")
    
except Exception as e:
    print(f"❌ Constructor-based loading failed: {e}")
    import traceback
    traceback.print_exc()

# Test 2: Manual loading (the working path)  
print("\n=== TEST 2: Manual loading (working method) ===")
try:
    manual_backend = RawxBackend()
    print(f"✅ Empty backend created")
    print(f"📊 Backend data sections before load: {len(manual_backend.data)}")
    
    manual_backend.load(file_path)
    print(f"✅ Manual load completed")
    print(f"📊 Backend data sections after manual load: {len(manual_backend.data)}")
    
    if manual_backend.data:
        print("🗂️ Backend data sections:")
        for section_name, section_data in manual_backend.data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                print(f"  ✅ {section_name}: {len(section_data['data'])} records")
            elif isinstance(section_data, list):
                print(f"  ✅ {section_name}: {len(section_data)} records (list)")
            else:
                print(f"  ⚠️ {section_name}: {type(section_data)}")
    
    # Test canonical conversion
    canonical_data = manual_backend.to_canonical()
    print(f"📊 CANONICAL data sections: {len(canonical_data)}")
    
    if canonical_data:
        print("🗂️ Canonical sections:")
        for section_name, section_data in canonical_data.items():
            if isinstance(section_data, dict) and 'data' in section_data:
                print(f"  ✅ {section_name}: {len(section_data['data'])} records")
            elif isinstance(section_data, list):
                print(f"  ✅ {section_name}: {len(section_data)} records (list)")
            else:
                print(f"  ⚠️ {section_name}: {type(section_data)}")

except Exception as e:
    print(f"❌ Manual loading failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Direct JSON comparison
print("\n=== TEST 3: Direct JSON file check ===")
try:
    with open(file_path, 'r') as f:
        rawx_json = json.load(f)
    
    if 'network' in rawx_json:
        network_data = rawx_json['network']
        print(f"✅ JSON file has 'network' key with {len(network_data)} sections")
        
        # Check for key sections
        key_sections = ['sub', 'subnode', 'subswd', 'subterm']
        for section in key_sections:
            if section in network_data:
                section_data = network_data[section]
                if isinstance(section_data, dict) and 'data' in section_data:
                    print(f"  ✅ {section}: {len(section_data['data'])} records")
                elif isinstance(section_data, list):
                    print(f"  ✅ {section}: {len(section_data)} records (list)")
            else:
                print(f"  ❌ {section}: not found")
    else:
        print(f"❌ JSON file does not have 'network' key. Keys: {list(rawx_json.keys())}")
        
except Exception as e:
    print(f"❌ Direct JSON loading failed: {e}")

print("\n🎯 Comparison complete!") 