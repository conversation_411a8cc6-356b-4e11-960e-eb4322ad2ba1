digraph AgileEcosystem {
	rankdir=LR size="8,5"
	node [color="#e0e0e0" fontname=Arial shape=box style=filled]
	AgileDevice [label="Agile Device Classes
(Bus, Load, Area, etc.)" fillcolor="#b3cde0"]
	APIManager [label="api_manager
(Unified API)" fillcolor="#fbb4ae"]
	BackendAdapter [label="Backend Adapter" fillcolor="#ccebc5"]
	PSSPY [label="psspy_api
(PSS/E Adapter)" fillcolor="#decbe4"]
	DB [label="database_backend
(DB Adapter)" fillcolor="#fed9a6"]
	Config [label="backend_config.py
+
Environment Variables" fillcolor="#ffffcc"]
	AgileDevice -> APIManager [label=uses color="#377eb8" penwidth=2]
	APIManager -> BackendAdapter [label=delegates color="#e41a1c" penwidth=2]
	BackendAdapter -> PSSPY [label="PSS/E" color="#984ea3" style=dashed]
	BackendAdapter -> DB [label=Database color="#ff7f00" style=dashed]
	Config -> APIManager [label="selects backend" color="#4daf4a" penwidth=2]
	NewBackend [label="New Backend
(Adapter)" fillcolor="#f2f2f2" style=dashed]
	BackendAdapter -> NewBackend [label="(extensible)" color="#999999" style=dotted]
}
