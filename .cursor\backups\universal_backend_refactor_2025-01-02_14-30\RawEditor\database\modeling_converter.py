"""
Modeling Approach Converter Interface and Implementations

Clean strategy pattern for handling modeling approach conversions
without coupling Universal Backend to specific backend implementations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from .base_backend import ModelingApproach
import logging


class ModelingConverter(ABC):
    """
    Abstract base class for modeling approach converters.
    
    Each backend can provide its own implementation to handle
    conversions between modeling approaches (bus-branch, node-breaker, hybrid).
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def convert_data(self, source_data: Dict[str, Any], 
                    source_approach: ModelingApproach,
                    target_approach: ModelingApproach) -> Dict[str, Any]:
        """
        Convert data from one modeling approach to another.
        
        Args:
            source_data: Raw backend data to convert
            source_approach: Source modeling approach
            target_approach: Target modeling approach
            
        Returns:
            Converted data in target modeling approach
        """
        pass
    
    @abstractmethod
    def detect_approach(self, data: Dict[str, Any]) -> ModelingApproach:
        """
        Detect the modeling approach of the given data.
        
        Args:
            data: Raw backend data
            
        Returns:
            Detected modeling approach
        """
        pass


class PassThroughConverter(ModelingConverter):
    """
    Pass-through converter for backends that don't need modeling conversions.
    Used by RAWX, JSON, Excel, SQLite backends.
    """
    
    def convert_data(self, source_data: Dict[str, Any], 
                    source_approach: ModelingApproach,
                    target_approach: ModelingApproach) -> Dict[str, Any]:
        """Pass through data without conversion."""
        if source_approach != target_approach:
            self.logger.warning(f"Modeling approach conversion not implemented: {source_approach.value} → {target_approach.value}")
        return source_data
    
    def detect_approach(self, data: Dict[str, Any]) -> ModelingApproach:
        """Default to bus-branch approach."""
        return ModelingApproach.BUS_BRANCH


class HdbModelingConverter(ModelingConverter):
    """
    HDB-specific modeling converter.
    
    Handles conversion from HDB data to different modeling approaches
    using the canonical format system.
    """
    
    def __init__(self, hdb_backend):
        super().__init__()
        self.hdb_backend = hdb_backend
    
    def convert_data(self, source_data: Dict[str, Any], 
                    source_approach: ModelingApproach,
                    target_approach: ModelingApproach) -> Dict[str, Any]:
        """
        Convert HDB data to the target modeling approach.
        
        Args:
            source_data: HDB raw data 
            source_approach: Ignored (HDB is always source)
            target_approach: Target modeling approach
            
        Returns:
            Canonical data in target modeling approach
        """
        self.logger.info(f"Converting HDB data to {target_approach.value} modeling approach")
        
        # Use the HDB backend's to_canonical method with the target approach
        try:
            canonical_data = self.hdb_backend.to_canonical(target_approach)
            
            # Log conversion results
            total_records = 0
            for section, section_data in canonical_data.items():
                if isinstance(section_data, dict) and 'data' in section_data:
                    record_count = len(section_data['data'])
                    total_records += record_count
                    self.logger.debug(f"   {section}: {record_count} records")
            
            self.logger.info(f"HDB conversion completed: {total_records} total records")
            return canonical_data
            
        except Exception as e:
            self.logger.error(f"Error converting HDB data: {e}")
            raise
    
    def detect_approach(self, data: Dict[str, Any]) -> ModelingApproach:
        """
        Detect HDB modeling approach based on data structure.
        
        HDB data is natively in node-breaker format (contains nodes, substations, switching devices).
        This needs to be converted to bus-branch or hybrid as requested.
        """
        # HDB data is natively node-breaker format
        # Contains nodes, substations, switching devices by default
        return ModelingApproach.NODE_BREAKER


def create_converter(backend) -> ModelingConverter:
    """
    Factory function to create appropriate converter for a backend.
    
    Args:
        backend: Backend instance
        
    Returns:
        Appropriate ModelingConverter implementation
    """
    backend_type = type(backend).__name__
    
    if 'Hdb' in backend_type:
        return HdbModelingConverter(backend)
    else:
        # RAWX, JSON, Excel, SQLite use pass-through
        return PassThroughConverter() 