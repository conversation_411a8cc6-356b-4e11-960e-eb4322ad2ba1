#!/usr/bin/env python3
"""
Quick test to verify circuit_name field is populated in transformer records.
"""

import sys
sys.path.append('.')
from hdb_to_raw_pipeline import HdbBackend

def test_circuit_name_field():
    """Test that circuit_name field is populated with transformer Id values."""
    
    # Load HDB data
    backend = HdbBackend()
    backend.load('hdbcontext_original.hdb')
    
    # Convert to canonical format
    canonical_data = backend.to_canonical()
    
    # Check transformer data
    transformer_data = canonical_data.get('transformer', {})
    if not transformer_data or 'data' not in transformer_data:
        print("❌ No transformer data found")
        return False
    
    fields = transformer_data['fields']
    records = transformer_data['data']
    
    # Find field indices
    circuit_name_idx = None
    circuit_id_idx = None
    transformer_name_idx = None
    
    for i, field in enumerate(fields):
        if field == 'circuit_name':
            circuit_name_idx = i
        elif field == 'circuit_id':
            circuit_id_idx = i
        elif field == 'transformer_name':
            transformer_name_idx = i
    
    print(f"📊 Found {len(records)} transformer records")
    print(f"📊 Fields: {len(fields)} total")
    print(f"📊 circuit_name field at index: {circuit_name_idx}")
    print(f"📊 circuit_id field at index: {circuit_id_idx}")
    print(f"📊 transformer_name field at index: {transformer_name_idx}")
    
    if circuit_name_idx is None:
        print("❌ circuit_name field not found in transformer fields!")
        print(f"Available fields: {fields}")
        return False
    
    # Check first few records
    print("\n🔍 Sample transformer records:")
    populated_count = 0
    
    for i, record in enumerate(records[:5]):
        if circuit_name_idx < len(record):
            circuit_name = record[circuit_name_idx]
            circuit_id = record[circuit_id_idx] if circuit_id_idx is not None and circuit_id_idx < len(record) else 'N/A'
            transformer_name = record[transformer_name_idx] if transformer_name_idx is not None and transformer_name_idx < len(record) else 'N/A'
            
            print(f"  Record {i+1}:")
            print(f"    circuit_name: '{circuit_name}'")
            print(f"    circuit_id: '{circuit_id}'")
            print(f"    transformer_name: '{transformer_name}'")
            
            if circuit_name and circuit_name.strip():
                populated_count += 1
        else:
            print(f"  Record {i+1}: circuit_name field missing (record too short)")
    
    print(f"\n✅ {populated_count} out of 5 sample records have populated circuit_name field")
    
    # Check overall population rate
    total_populated = 0
    for record in records:
        if circuit_name_idx < len(record) and record[circuit_name_idx] and str(record[circuit_name_idx]).strip():
            total_populated += 1
    
    population_rate = (total_populated / len(records)) * 100 if records else 0
    print(f"📊 Overall circuit_name population rate: {total_populated}/{len(records)} ({population_rate:.1f}%)")
    
    return circuit_name_idx is not None and total_populated > 0

if __name__ == "__main__":
    success = test_circuit_name_field()
    if success:
        print("\n✅ circuit_name field test PASSED!")
    else:
        print("\n❌ circuit_name field test FAILED!")
        sys.exit(1)
