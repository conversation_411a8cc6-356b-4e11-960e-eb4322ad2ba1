#!/usr/bin/env python3
"""Simple debug script to test transformer configuration values."""

import sys
sys.path.append('.')
from hdb_to_raw_pipeline import HdbBackend

def test_transformer_config():
    """Test transformer configuration values."""
    print("🔍 Testing transformer configuration...")
    
    # Create HDB backend
    backend = HdbBackend()
    
    # Load data
    backend.load_data("sample_nb.rawx")
    
    # Get canonical data
    canonical_data = backend.get_canonical_data()
    
    # Get transformer data
    transformer_data = canonical_data.get('transformer', {})
    
    print(f"📊 Found {len(transformer_data)} transformers in canonical data")
    
    # Check first transformer
    for i, (key, xfm) in enumerate(list(transformer_data.items())[:3]):
        print(f"\n🔧 Transformer {i+1}: {key}")
        print(f"   winding_config: {xfm.get('winding_config', 'NOT_FOUND')}")
        print(f"   impedance_config: {xfm.get('impedance_config', 'NOT_FOUND')}")
        print(f"   magnetizing_config: {xfm.get('magnetizing_config', 'NOT_FOUND')}")
        print(f"   metered_winding: {xfm.get('metered_winding', 'NOT_FOUND')}")
        print(f"   name: {xfm.get('name', 'NOT_FOUND')}")
        print(f"   ibus: {xfm.get('ibus', 'NOT_FOUND')}")
        print(f"   jbus: {xfm.get('jbus', 'NOT_FOUND')}")

if __name__ == "__main__":
    test_transformer_config() 