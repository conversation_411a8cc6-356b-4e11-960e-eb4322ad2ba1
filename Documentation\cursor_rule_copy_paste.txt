### ✅ Markdown Formatting Rule (MD Compliance)

When editing or creating .md (Markdown) files:

**MD009 - No trailing spaces:**
- Remove all trailing whitespace at the end of lines
- Do not leave spaces or tabs after the final character on any line
- Exception: Two spaces at end of line for explicit line break (rare usage)

**MD012 - No multiple consecutive blank lines:**
- Use only single blank lines to separate sections
- Never have more than one blank line in a row
- Remove any multiple consecutive blank lines

**MD022 - Headings surrounded by blank lines:**
- Always insert a blank line before each heading (e.g., before #, ##, ###, etc.)
- Always insert a blank line after each heading
- Exception: No blank line needed before the first heading in a file

**MD031 & MD032 - Code blocks and lists surrounded by blank lines:**
- Always surround fenced code blocks with a blank line above and below
- Always surround lists (both ordered and unordered) with a blank line above and below
- This includes both ``` code blocks and list items starting with - or 1.

**MD036 - Use proper headings instead of emphasis:**
- Use # ## ### headings instead of **bold text** for section titles
- Use **bold** and *italic* only for emphasis within paragraphs, not as heading substitutes
- Structure documents with proper heading hierarchy

**MD038 - No spaces inside code spans:**
- Remove spaces inside inline code spans: `code` not ` code ` or `code `
- Inline code should be tight against the backticks
- Use `code` not `  code  ` or ` code` or `code `

**MD040 - Fenced code blocks must specify language:**
- Always specify the language for syntax highlighting immediately after the opening triple backticks
- Use ```python, ```bash, ```json, ```yaml, ```javascript, etc.
- Never leave fenced code blocks blank (```language not just ```)
- If no specific language, use ```text or ```plaintext

**File ending:**
- Always ensure there is exactly one newline at the end of the file
- Do not leave trailing spaces or multiple blank lines at the end
- The file must end with a clean newline character 