#!/usr/bin/env python3
"""
Debug script to examine switching device data structure and field mappings.
"""

import logging
from pathlib import Path
import sys

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from hdb_to_raw_pipeline import HdbBackend

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_switching_device_data():
    """Debug switching device data structure."""
    
    print("DEBUG: Switching Device Data Structure")
    print("="*60)
    
    # Create HDB backend
    hdb_backend = HdbBackend()
    
    # Get canonical data from HDB backend
    print("Loading HDB canonical data...")
    canonical_data = hdb_backend.to_canonical()
    
    # Check switching device data
    if 'switching_device' in canonical_data and canonical_data['switching_device']:
        swd_section = canonical_data['switching_device']
        print(f"FOUND: switching device section with {len(swd_section.get('data', []))} records")
        
        # Show field structure
        fields = swd_section.get('fields', [])
        print(f"FIELDS: Field structure ({len(fields)} fields):")
        for i, field in enumerate(fields):
            print(f"   {i}: {field}")
        
        # Show first few records
        data = swd_section.get('data', [])
        if data:
            print(f"\nDATA: First 5 switching device records:")
            for i, record in enumerate(data[:5]):
                print(f"   Record {i+1}: {record}")
                print(f"      Length: {len(record)}")
                
                # Show field mappings
                if len(record) >= len(fields):
                    print(f"      Field mappings:")
                    for j, (field, value) in enumerate(zip(fields, record)):
                        print(f"         {field}: {value} ({type(value).__name__})")
    else:
        print("WARNING: No switching device data found")
        print("Available sections:")
        for section_name in sorted(canonical_data.keys()):
            if canonical_data[section_name] and canonical_data[section_name].get('data'):
                count = len(canonical_data[section_name]['data'])
                print(f"   {section_name}: {count} records")
    
    # Check substation data for naming
    if 'substation' in canonical_data and canonical_data['substation']:
        sub_section = canonical_data['substation']
        print(f"\nSUBSTATIONS: Substation data for naming reference:")
        print(f"   Fields: {sub_section.get('fields', [])}")
        
        # Show first few substations
        data = sub_section.get('data', [])
        if data:
            print(f"   First 3 substations:")
            for i, record in enumerate(data[:3]):
                print(f"      {i+1}: {record}")
    
    # Check node data for naming
    if 'node' in canonical_data and canonical_data['node']:
        node_section = canonical_data['node']
        print(f"\nNODES: Node data for naming reference:")
        print(f"   Fields: {node_section.get('fields', [])}")
        
        # Show first few nodes
        data = node_section.get('data', [])
        if data:
            print(f"   First 3 nodes:")
            for i, record in enumerate(data[:3]):
                print(f"      {i+1}: {record}")
    
    print("\n" + "="*60)
    print("DEBUG: Complete")

if __name__ == "__main__":
    debug_switching_device_data() 