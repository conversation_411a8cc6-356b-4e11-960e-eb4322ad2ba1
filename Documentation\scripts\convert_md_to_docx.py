import pypandoc
import logging
import os

logging.basicConfig(filename='convert_md_to_docx.log', level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

def convert(md_file, docx_file):
    try:
        output = pypandoc.convert_file(md_file, 'docx', outputfile=docx_file)
        logging.info(f"Converted {md_file} to {docx_file} successfully.")
    except OSError as e:
        if 'No pandoc was found' in str(e):
            logging.warning(f"Pandoc not found. Downloading pandoc...")
            pypandoc.download_pandoc()
            try:
                output = pypandoc.convert_file(md_file, 'docx', outputfile=docx_file)
                logging.info(f"Converted {md_file} to {docx_file} successfully after downloading pandoc.")
            except Exception as e2:
                logging.error(f"Failed to convert {md_file} to {docx_file} after downloading pandoc: {e2}")
        else:
            logging.error(f"Failed to convert {md_file} to {docx_file}: {e}")
    except Exception as e:
        logging.error(f"Failed to convert {md_file} to {docx_file}: {e}")

if __name__ == "__main__":
    files = [
        ("EMS_Export_Primer.md", "EMS_Export_Primer.docx"),
        ("Building_Seed_Case_Primer.md", "Building_Seed_Case_Primer.docx"),
        ("Topology_Package__Primer.md", "Topology_Package__Primer.docx"),
        ("README.md", "README.docx"),
    ]
    for md, docx in files:
        convert(md, docx) 