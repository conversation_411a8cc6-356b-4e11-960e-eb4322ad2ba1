#!/usr/bin/env python3
"""
Test the full fixed shunt pipeline after field mapping fix.
"""

def test_fixed_shunt_pipeline():
    """Test the complete fixed shunt pipeline."""
    
    print('=== TESTING FULL FIXED SHUNT PIPELINE ===')
    print()
    
    try:
        from hdb_to_raw_pipeline import HdbBackend
        from canonical_data_interface import CanonicalDataInterface
        
        # Test HDB backend pipeline
        print('1. Testing HDB Pipeline...')
        hdb_backend = HdbBackend()
        hdb_backend.load('hdbcontext_original.hdb')
        hdb_canonical = hdb_backend.to_canonical()
        
        # Check fixed shunt data
        fixed_shunt_data = hdb_canonical.get('fixed_shunt', {})
        data_count = len(fixed_shunt_data.get('data', []))
        print(f'   HDB fixed shunt records: {data_count}')
        
        if data_count > 0:
            # Show sample data
            sample_data = fixed_shunt_data['data'][0]
            fields = fixed_shunt_data['fields']
            print(f'   Fields: {fields}')
            print(f'   Sample record: {sample_data}')
        
        # Export to RAW and check
        print('\n2. Testing RAW Export...')
        from hdb_to_raw_pipeline import export_to_raw_format
        export_to_raw_format(hdb_canonical, 'test_fixed_shunt.raw', version="35")
        print('   HDB RAW export completed')
        
        # Check the RAW file
        with open('test_fixed_shunt.raw', 'r') as f:
            content = f.read()
            
        if 'BEGIN FIXED SHUNT DATA' in content and 'END OF FIXED SHUNT DATA' in content:
            start = content.find('BEGIN FIXED SHUNT DATA')
            end = content.find('END OF FIXED SHUNT DATA')
            section = content[start:end]
            lines = section.split('\n')
            data_lines = [line for line in lines if line.strip() and not line.strip().startswith('@') and not line.strip().startswith('0 /')]
            print(f'   Fixed shunt data lines in RAW: {len(data_lines)}')
            if data_lines:
                print(f'   First record: {data_lines[0][:100]}...')
                print('   ✅ Fixed shunt data successfully exported to RAW!')
            else:
                print('   ❌ No fixed shunt data found in RAW export!')
        else:
            print('   ❌ Fixed shunt section not found in RAW export!')
        
        print('\n=== PIPELINE TEST COMPLETE ===')
        
    except Exception as e:
        print(f'Pipeline test failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_shunt_pipeline() 