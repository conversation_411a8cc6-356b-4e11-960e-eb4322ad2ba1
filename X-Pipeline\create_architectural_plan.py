#!/usr/bin/env python3
"""
Comprehensive Analysis and Architectural Plan Generator
Compares HDB export output against PSS/E reference and creates systematic fix plan.
"""

import logging
from pathlib import Path
import sys
from datetime import datetime
import re
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set

# Configure logging to file
log_file = f"architectural_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RAWFileSection:
    """Represents a section in a RAW file."""
    def __init__(self, name: str, fields: List[str], records: List[List]):
        self.name = name
        self.fields = fields
        self.records = records
        
    def __len__(self):
        return len(self.records)

class RAWFileAnalyzer:
    """Analyzes and compares RAW files."""
    
    def __init__(self):
        self.sections = {}
        
    def parse_raw_file(self, file_path: str) -> Dict[str, RAWFileSection]:
        """Parse a RAW file into sections."""
        sections = {}
        current_section = None
        current_records = []
        
        logger.info(f"Parsing RAW file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                    
                # Check for section headers or data
                if line.startswith('0 /'):  # Section end marker
                    if current_section:
                        sections[current_section] = RAWFileSection(
                            current_section, [], current_records
                        )
                        current_records = []
                        current_section = None
                        
                elif '//' in line:  # Comment or section start
                    continue
                    
                elif current_section is None:
                    # Try to identify section type from data patterns
                    if self._is_bus_record(line):
                        current_section = 'bus'
                        current_records = [self._parse_record(line)]
                    elif self._is_load_record(line):
                        current_section = 'load'
                        current_records = [self._parse_record(line)]
                    elif self._is_generator_record(line):
                        current_section = 'generator'
                        current_records = [self._parse_record(line)]
                    elif self._is_branch_record(line):
                        current_section = 'branch'
                        current_records = [self._parse_record(line)]
                    elif self._is_transformer_record(line):
                        current_section = 'transformer'
                        current_records = [self._parse_record(line)]
                        
                else:
                    # Add to current section
                    current_records.append(self._parse_record(line))
                    
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            
        logger.info(f"Parsed {len(sections)} sections from {file_path}")
        return sections
    
    def _parse_record(self, line: str) -> List:
        """Parse a single record line."""
        # Handle quoted strings and comma separation
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == "'" and not in_quotes:
                in_quotes = True
                current_part += char
            elif char == "'" and in_quotes:
                in_quotes = False
                current_part += char
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char
                
        if current_part.strip():
            parts.append(current_part.strip())
            
        return parts
    
    def _is_bus_record(self, line: str) -> bool:
        """Check if line looks like a bus record."""
        parts = line.split(',')
        if len(parts) < 8:
            return False
        try:
            int(parts[0])  # Bus number
            float(parts[2])  # Base voltage
            return True
        except:
            return False
    
    def _is_load_record(self, line: str) -> bool:
        """Check if line looks like a load record."""
        parts = line.split(',')
        if len(parts) < 6:
            return False
        try:
            int(parts[0])  # Bus number
            # Load ID in quotes
            return "'" in parts[1]
        except:
            return False
    
    def _is_generator_record(self, line: str) -> bool:
        """Check if line looks like a generator record."""
        parts = line.split(',')
        if len(parts) < 8:
            return False
        try:
            int(parts[0])  # Bus number
            # Generator ID in quotes
            return "'" in parts[1]
        except:
            return False
    
    def _is_branch_record(self, line: str) -> bool:
        """Check if line looks like a branch record."""
        parts = line.split(',')
        if len(parts) < 10:
            return False
        try:
            int(parts[0])  # From bus
            int(parts[1])  # To bus
            # Circuit ID in quotes
            return "'" in parts[2]
        except:
            return False
    
    def _is_transformer_record(self, line: str) -> bool:
        """Check if line looks like a transformer record."""
        parts = line.split(',')
        if len(parts) < 12:
            return False
        try:
            int(parts[0])  # From bus
            int(parts[1])  # To bus
            int(parts[2])  # Third bus (0 for 2-winding)
            return True
        except:
            return False

def compare_files(hdb_file: str, ref_file: str) -> Dict:
    """Compare HDB output against reference file."""
    
    logger.info("="*80)
    logger.info("COMPREHENSIVE RAW FILE COMPARISON ANALYSIS")
    logger.info("="*80)
    
    analyzer = RAWFileAnalyzer()
    
    # Parse both files
    logger.info(f"Parsing HDB output: {hdb_file}")
    hdb_sections = analyzer.parse_raw_file(hdb_file)
    
    logger.info(f"Parsing reference file: {ref_file}")
    ref_sections = analyzer.parse_raw_file(ref_file)
    
    # Compare sections
    comparison = {
        'section_counts': {},
        'missing_sections': [],
        'extra_sections': [],
        'field_mismatches': {},
        'record_mismatches': {},
        'critical_issues': [],
        'switching_device_analysis': {}
    }
    
    # Section count comparison
    logger.info("\nSECTION COUNT COMPARISON:")
    for section_name in set(list(hdb_sections.keys()) + list(ref_sections.keys())):
        hdb_count = len(hdb_sections.get(section_name, []))
        ref_count = len(ref_sections.get(section_name, []))
        
        comparison['section_counts'][section_name] = {
            'hdb': hdb_count,
            'ref': ref_count,
            'diff': hdb_count - ref_count
        }
        
        logger.info(f"  {section_name}: HDB={hdb_count}, REF={ref_count}, DIFF={hdb_count - ref_count}")
        
        if section_name in ref_sections and section_name not in hdb_sections:
            comparison['missing_sections'].append(section_name)
        elif section_name in hdb_sections and section_name not in ref_sections:
            comparison['extra_sections'].append(section_name)
    
    # Analyze switching device to ZBR conversion
    if 'branch' in hdb_sections:
        zbr_analysis = analyze_zbr_branches(hdb_sections['branch'])
        comparison['switching_device_analysis'] = zbr_analysis
        logger.info(f"\nZBR ANALYSIS: Found {zbr_analysis['zbr_count']} potential ZBR branches")
        
    return comparison

def analyze_zbr_branches(branch_section: RAWFileSection) -> Dict:
    """Analyze zero-impedance branches that should come from switching devices."""
    
    zbr_count = 0
    circuit_id_issues = []
    naming_issues = []
    
    for record in branch_section.records:
        if len(record) >= 6:
            try:
                # Check for zero or very small impedance
                r = float(record[3]) if record[3] else 0.0
                x = float(record[4]) if record[4] else 0.0
                
                if abs(r) <= 0.0001 and abs(x) <= 0.0001:
                    zbr_count += 1
                    
                    # Check circuit ID issues
                    ckt_id = record[2].strip("'\"")
                    if ckt_id == '0.' or ckt_id == '0':
                        circuit_id_issues.append(record)
                    
                    # Check naming issues
                    if len(record) > 6:
                        name = record[6].strip("'\"") if record[6] else ""
                        if name.startswith('DUP') or name == '':
                            naming_issues.append(record)
                            
            except (ValueError, IndexError):
                continue
                
    return {
        'zbr_count': zbr_count,
        'circuit_id_issues': len(circuit_id_issues),
        'naming_issues': len(naming_issues),
        'examples': {
            'circuit_id_examples': circuit_id_issues[:3],
            'naming_examples': naming_issues[:3]
        }
    }

def create_architectural_plan(comparison: Dict) -> str:
    """Create comprehensive architectural plan based on analysis."""
    
    plan_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plan_content = f"""
# ARCHITECTURAL PLAN: HDB to RAW Export Issues Resolution

**Version:** 1.0  
**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Analysis Timestamp:** {plan_timestamp}  
**Purpose:** Systematic resolution of HDB export field and record mismatches  

## 🚨 EXECUTIVE SUMMARY

This architectural plan addresses critical issues discovered in the HDB to RAW export pipeline,
focusing on switching device conversion, field mapping consistency, and architectural compliance.

### Critical Issues Identified:

1. **Switching Device to ZBR Conversion Problems**
   - Circuit ID field mapping failures (showing '0.' instead of device IDs)
   - Poor branch naming (showing 'DUP1000' instead of meaningful names)
   - Field detection logic using wrong field names

2. **Architectural Violations**
   - HDB and RAWX backends should produce identical results through canonical format
   - ModelTransformations must be backend-agnostic
   - Field mapping inconsistencies violate DRY principles

3. **Data Integrity Issues**
   - Missing or malformed switching device records
   - Inconsistent field ordering between backends
   - Type conversion errors in numeric fields

## 📊 DETAILED ANALYSIS RESULTS

### Section Count Analysis:
"""

    # Add section counts
    for section, counts in comparison['section_counts'].items():
        plan_content += f"\n- **{section}**: HDB={counts['hdb']}, REF={counts['ref']}, DIFF={counts['diff']}"

    if comparison['missing_sections']:
        plan_content += f"\n\n### Missing Sections in HDB Output:\n"
        for section in comparison['missing_sections']:
            plan_content += f"- {section}\n"

    if comparison['extra_sections']:
        plan_content += f"\n### Extra Sections in HDB Output:\n"
        for section in comparison['extra_sections']:
            plan_content += f"- {section}\n"

    # Add ZBR analysis
    zbr = comparison['switching_device_analysis']
    plan_content += f"""

### Switching Device to ZBR Analysis:
- **Total ZBR branches found**: {zbr['zbr_count']}
- **Circuit ID issues**: {zbr['circuit_id_issues']} branches with malformed circuit IDs
- **Naming issues**: {zbr['naming_issues']} branches with poor naming

#### Example Circuit ID Issues:
"""
    for example in zbr['examples']['circuit_id_examples']:
        plan_content += f"- {example[:7]}  # Shows '0.' circuit ID problem\n"

    plan_content += """

## 🏗️ ARCHITECTURAL SOLUTION FRAMEWORK

### Phase 1: Field Mapping Standardization (HIGH PRIORITY)

#### Problem:
- Switching device field detection uses outdated field names
- HDB canonical format uses 'swdid' but detection looks for 'ckt'
- Field mapping is inconsistent between backends

#### Solution:
```python
# File: hdb_to_raw_pipeline.py, Line ~7904
# CURRENT (BROKEN):
elif field.lower() in ['ckt', 'cktid', 'circuit_id', 'circuit', 'id']:
    ckt_idx = i

# FIXED:
elif field.lower() in ['ckt', 'cktid', 'circuit_id', 'circuit', 'id', 'swdid']:
    ckt_idx = i
```

#### Implementation Steps:
1. **Update field detection logic** in `_convert_node_breaker_to_hybrid()` method
2. **Standardize field names** across all converters
3. **Create canonical field mapping registry** to ensure consistency
4. **Add field validation** to catch mapping errors early

### Phase 2: ZBR Branch Naming Enhancement (MEDIUM PRIORITY)

#### Problem:
- ZBR branches show generic names like 'DUP1000'
- Should use meaningful names from switching device data
- No fallback naming convention for missing names

#### Solution:
```python
# Enhanced naming logic
def create_zbr_branch_name(switching_device_record, substation_lookup):
    if device_name and device_name != 'DUP1000':
        return clean_device_name(device_name)
    else:
        sub_name = get_clean_substation_name(substation_id)
        return f"{sub_name}_{from_node}_{to_node}"
```

#### Implementation Steps:
1. **Enhance switching device name extraction**
2. **Implement substation name lookup for fallback naming**
3. **Add name truncation logic** for PSS/E 40-character limit
4. **Create consistent naming convention** across all ZBR branches

### Phase 3: Circuit ID Type Safety (HIGH PRIORITY)

#### Problem:
- Circuit IDs appear as '0.' (float) instead of proper string IDs
- Type conversion issues in field mapping
- Breaks PSS/E import compatibility

#### Solution:
```python
# Ensure proper string handling
ckt_id = str(swd_record[ckt_idx]).strip() if ckt_idx >= 0 else '1'
# Never allow numeric conversion that adds decimal points
if ckt_id.endswith('.0'):
    ckt_id = ckt_id[:-2]
```

#### Implementation Steps:
1. **Fix circuit ID extraction** to preserve string format
2. **Add type validation** for all field mappings
3. **Implement string sanitization** for PSS/E compatibility
4. **Add unit tests** for circuit ID handling

### Phase 4: Backend Output Standardization (CRITICAL)

#### Problem:
- HDB and RAWX backends produce different ZBR results
- Violates architectural principle that backends should be interchangeable
- ModelTransformations should be backend-agnostic

#### Solution:
Ensure both backends produce identical canonical format:

```python
# Verification check in ModelTransformations
def validate_backend_consistency(canonical_data):
    assert 'switching_device' in canonical_data
    assert canonical_data['switching_device']['fields'] == STANDARD_SWITCHING_FIELDS
    assert all(isinstance(record[CIRCUIT_ID_IDX], str) for record in data)
```

#### Implementation Steps:
1. **Standardize canonical field names** across all backends
2. **Add backend output validation** to catch inconsistencies
3. **Create test suite** comparing backend outputs
4. **Implement canonical format verification** in CI/CD

## 🔧 IMPLEMENTATION PLAN

### Sprint 1: Critical Field Mapping Fixes (Week 1)
- [ ] Fix switching device field detection (swdid recognition)
- [ ] Implement circuit ID type safety
- [ ] Add field mapping validation
- [ ] Create unit tests for field detection

### Sprint 2: ZBR Branch Enhancement (Week 2)  
- [ ] Implement meaningful branch naming
- [ ] Add substation name lookup
- [ ] Create naming fallback logic
- [ ] Test naming consistency

### Sprint 3: Backend Standardization (Week 3)
- [ ] Standardize canonical field formats
- [ ] Add backend output validation
- [ ] Create comparison test suite
- [ ] Implement consistency checks

### Sprint 4: Integration & Testing (Week 4)
- [ ] Integration testing with full pipeline
- [ ] Performance impact assessment  
- [ ] Documentation updates
- [ ] User acceptance testing

## 🧪 TESTING STRATEGY

### Unit Tests:
- Field detection logic for all switching device field variants
- Circuit ID type preservation under various input formats
- Branch naming logic with edge cases
- Substation name cleanup and truncation

### Integration Tests:
- End-to-end pipeline with HDB and RAWX backends
- Comparison tests ensuring identical output
- RAW file format validation
- PSS/E import compatibility verification

### Regression Tests:
- Existing functionality preservation
- Performance benchmarks
- Memory usage validation
- Error handling verification

## 📏 SUCCESS CRITERIA

1. **✅ Field Mapping Accuracy**: 100% correct field detection for switching devices
2. **✅ Branch Naming Quality**: No more 'DUP1000' or generic names in output
3. **✅ Circuit ID Integrity**: All circuit IDs preserve original string format
4. **✅ Backend Consistency**: HDB and RAWX produce identical canonical results
5. **✅ Performance**: No more than 5% performance degradation
6. **✅ Compatibility**: Generated RAW files import successfully into PSS/E

## 🚨 RISK MITIGATION

### High Risk: Breaking Existing Functionality
- **Mitigation**: Comprehensive regression test suite
- **Contingency**: Feature flags for new vs old behavior
- **Monitoring**: Automated comparison against known good outputs

### Medium Risk: Performance Impact
- **Mitigation**: Performance profiling during development
- **Contingency**: Optimization of critical paths
- **Monitoring**: Continuous performance benchmarks

### Low Risk: Field Name Variations
- **Mitigation**: Extensive field name mapping registry
- **Contingency**: Configurable field mappings
- **Monitoring**: Field detection logging and alerts

## 📋 MAINTENANCE PLAN

### Ongoing Monitoring:
- Automated comparison tests in CI/CD pipeline
- Field mapping validation in production
- Performance metrics tracking
- Error rate monitoring for ZBR conversion

### Documentation Updates:
- Field mapping registry documentation
- Switching device conversion guide
- Troubleshooting guide for common issues
- Architecture decision records (ADRs)

### Code Quality:
- Regular code reviews for field mapping changes
- Static analysis for type safety
- Dependency updates and security patches
- Performance optimization reviews

---

**This architectural plan ensures systematic resolution of all identified issues while maintaining the hard-earned architectural principles and following all established development rules.**
"""

    return plan_content

def main():
    """Main analysis and plan generation function."""
    
    # File paths
    hdb_file = "output_demo/HDB_export_v33_hybrid_current.raw"
    ref_file = "psse_33.raw"
    
    # Verify files exist
    if not Path(hdb_file).exists():
        logger.error(f"HDB file not found: {hdb_file}")
        return
        
    if not Path(ref_file).exists():
        logger.error(f"Reference file not found: {ref_file}")
        return
    
    # Perform comprehensive analysis
    logger.info("Starting comprehensive analysis...")
    comparison = compare_files(hdb_file, ref_file)
    
    # Generate architectural plan
    logger.info("Generating architectural plan...")
    plan_content = create_architectural_plan(comparison)
    
    # Save plan to file
    plan_filename = f"architectural_plan_switching_device_fixes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(plan_filename, 'w', encoding='utf-8') as f:
        f.write(plan_content)
    
    logger.info(f"Architectural plan saved to: {plan_filename}")
    logger.info(f"Analysis log saved to: {log_file}")
    
    # Print summary
    logger.info("\n" + "="*80)
    logger.info("ANALYSIS COMPLETE")
    logger.info("="*80)
    logger.info(f"Files analyzed: {hdb_file} vs {ref_file}")
    logger.info(f"Architectural plan: {plan_filename}")
    logger.info(f"Analysis log: {log_file}")
    
    zbr = comparison['switching_device_analysis']
    logger.info(f"ZBR Issues Found: {zbr['circuit_id_issues']} circuit ID issues, {zbr['naming_issues']} naming issues")
    
    if comparison['missing_sections']:
        logger.info(f"Missing Sections: {', '.join(comparison['missing_sections'])}")
    
    logger.info("Review the architectural plan for systematic resolution steps.")

if __name__ == "__main__":
    main() 