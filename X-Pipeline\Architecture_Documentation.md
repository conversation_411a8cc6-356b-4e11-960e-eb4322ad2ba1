# HDB to RAW Pipeline Architecture Documentation

## Overview

This document provides a comprehensive technical overview of the HDB to RAW pipeline architecture, designed for developers who need to understand the complete data flow, transformations, and implementation details.

**Note**: The architecture diagrams in this document are sourced from individual Mermaid files in the `diagrams/` directory. See `diagrams/README.md` for information on how to edit and use these diagrams independently.

## Architecture Components

### 1. Backend Classes

The pipeline uses a clean backend architecture with the following key classes:

#### BaseBackend (Abstract Base Class)
- **Location**: Lines 141-340 in `hdb_to_raw_pipeline.py`
- **Purpose**: Provides consistent interface for all database backends
- **Key Features**:
  - Quality checking integration (`DataQualityChecker`)
  - Modeling approach management (`ModelingApproach` enum)
  - System state management (`SystemState` enum)
  - Automatic issue fixing capabilities

#### HdbBackend
- **Location**: Lines 5874-6558 in `hdb_to_raw_pipeline.py`
- **Purpose**: Handles HDB (Hierarchical Database) format parsing and conversion
- **Key Features**:
  - Parses HDB JSON format containing substation topology
  - Always produces NODE_BREAKER canonical format (richest representation)
  - Embeds node information in equipment records during conversion
  - Uses performance tracking for converter optimization

#### RawxBackend
- **Location**: Lines 6559-7341 in `hdb_to_raw_pipeline.py`
- **Purpose**: Handles RAWX format parsing and conversion
- **Key Features**:
  - Parses PSS/E RAW format files
  - Detects modeling approach from file structure
  - Converts to canonical format while preserving original modeling

### 2. Canonical Data System

#### CanonicalDataInterface
- **Location**: `canonical_data_interface.py` lines 97-305
- **Purpose**: Provides immutable, type-safe access to power system data
- **Key Classes**:
  - `CanonicalRecord`: Immutable record with frozen data
  - `CanonicalSection`: Container for records with metadata
  - `ReadOnlyCanonicalDataInterface`: Implementation with field mapping

#### Data Flow Through Canonical System
1. **Backend → Canonical**: Raw data converted to dictionaries with human-readable field names
2. **Canonical → Transformations**: Immutable access prevents accidental modifications
3. **Transformations → Export**: Clean interface for writers

### 3. Model Transformations

#### ModelTransformations Class
- **Location**: Lines 7342-9309 in `hdb_to_raw_pipeline.py`
- **Purpose**: Handles conversions between modeling approaches
- **Key Methods**:
  - `convert_to_hybrid_modeling()`: NODE_BREAKER → HYBRID conversion
  - `detect_model_type()`: Automatic modeling approach detection
  - `_create_node_mapping_from_equipment()`: Creates node-to-bus mappings

#### Node-to-Bus Conversion Process

The most complex part of the architecture is the node-to-bus conversion for hybrid modeling:

##### 1. Node Mapping Creation
```python
node_to_new_bus = {}   # Maps (substation_id, node_id) → new_bus_number
```

##### 2. Equipment Processing
- Equipment records contain embedded node information:
  - `substation_id`: Which substation the equipment belongs to
  - `node_id`: Which node within the substation
  - `from_substation_id`, `from_node_id`: For lines/transformers
  - `to_substation_id`, `to_node_id`: For lines/transformers

##### 3. Bus Number Assignment
```python
# Create mapping from node coordinates to bus numbers
substation_node_key = (substation_id, node_id)
node_to_new_bus[substation_node_key] = new_bus_num
```

##### 4. Equipment Update Process
- **ALL equipment sections** get their bus numbers updated
- Writers call `record.get('bus_number', 0)` and receive transformed values
- Writers are completely unaware of the transformation process

### 4. Writer System

#### RawSectionWriter (Abstract Base Class)
- **Location**: Lines 9310-9442 in `hdb_to_raw_pipeline.py`
- **Purpose**: Base class for all RAW format writers
- **Key Methods**:
  - `write_section_canonical()`: Main writing method using canonical interface
  - `write_section()`: Legacy method for backward compatibility

#### Equipment Writers
All writers inherit from `RawSectionWriter` and follow the same pattern:

- **BusWriter** (Lines 9491-9532): Writes bus data
- **LoadWriter** (Lines 9533-9596): Writes load data
- **GeneratorWriter** (Lines 9597-9710): Writes generator data
- **AcLineWriter** (Lines 9711-9822): Writes AC line data
- **TransformerWriter** (Lines 9823-10093): Writes transformer data
- **FixedShuntWriter** (Lines 10094-10141): Writes fixed shunt data

#### Hierarchical Writers
- **SubstationWriter** (Lines 10229-10393): Writes hierarchical substation data
- **NodeWriter** (Lines 10394-10422): Writes node data
- **SwitchingDeviceWriter** (Lines 10423-10467): Writes switching device data
- **TerminalWriter** (Lines 10468-10496): Writes terminal data

### 5. Export System

#### export_to_raw_format Function
- **Location**: Lines 11742+ in `hdb_to_raw_pipeline.py`
- **Purpose**: Orchestrates the complete export process
- **Key Parameters**:
  - `data`: Canonical data dictionary
  - `output_path`: Output file path
  - `version`: PSS/E version (33, 34, 35)
  - `modeling`: Modeling approach ("bus_branch", "node_breaker", "hybrid")

#### Export Logic Flow
1. **Modeling Check**: Determines output format based on modeling parameter
2. **Hierarchical vs Flat**: 
   - If `modeling == "node_breaker"` AND substation data exists → Hierarchical format
   - Otherwise → Flat format
3. **Section Writing**: Uses appropriate writers for each section
4. **Termination**: Ends with 'Q' after all sections

## Data Flow Architecture

```mermaid
graph TB
    subgraph "Input Layer"
        HDB[HDB Files]
        RAWX[RAWX Files]
        JSON[JSON Files]
    end

    subgraph "Backend Layer"
        HdbBackend["HdbBackend<br/>- Parses HDB JSON<br/>- Embeds node info in equipment<br/>- Always produces NODE_BREAKER canonical"]
        RawxBackend["RawxBackend<br/>- Parses PSS/E RAW format<br/>- Detects modeling approach<br/>- Preserves original modeling"]
    end

    subgraph "Canonical Data Layer"
        CanonicalData["Canonical Data<br/>- Immutable dictionaries<br/>- Human-readable field names<br/>- Embedded node information"]
        CanonicalInterface["CanonicalDataInterface<br/>- Type-safe access<br/>- Prevents direct backend access<br/>- Field mapping integration"]
    end

    subgraph "Transformation Layer"
        ModelTransformations["ModelTransformations<br/>- Node-to-bus mapping<br/>- Equipment bus number updates<br/>- Modeling approach conversion"]
        NodeMapping["Node Mapping Tables<br/>node_to_new_bus:<br/>(substation_id, node_id) → bus_number"]
    end

    subgraph "Export Layer"
        Writers["RawSectionWriter Classes<br/>- BusWriter, LoadWriter, etc.<br/>- Hierarchical writers<br/>- Canonical interface only"]
        ExportFunction["export_to_raw_format<br/>- Orchestrates export process<br/>- Handles modeling approach<br/>- Hierarchical vs flat logic"]
    end

    subgraph "Output Layer"
        BusBranch["Bus-Branch RAW Files<br/>- Traditional PSS/E format<br/>- Flat bus structure"]
        NodeBreaker["Node-Breaker RAW Files<br/>- Hierarchical substation format<br/>- Full topology detail"]
        Hybrid["Hybrid RAW Files<br/>- Mixed representation<br/>- Substation data + simplified buses"]
    end

    HDB --> HdbBackend
    RAWX --> RawxBackend
    JSON --> HdbBackend

    HdbBackend --> CanonicalData
    RawxBackend --> CanonicalData

    CanonicalData --> CanonicalInterface
    CanonicalInterface --> ModelTransformations

    ModelTransformations --> NodeMapping
    NodeMapping --> Writers

    Writers --> ExportFunction
    ExportFunction --> BusBranch
    ExportFunction --> NodeBreaker
    ExportFunction --> Hybrid
```

## Equipment Data Transformation

```mermaid
graph LR
    subgraph "Original Equipment Record"
        OriginalBus["bus_number: 0<br/>substation_id: 'SUB1'<br/>node_id: 'N1'"]
    end

    subgraph "Node Mapping Process"
        NodeKey["Node Key<br/>('SUB1', 'N1')"]
        BusAssignment["Bus Assignment<br/>new_bus_number: 1001"]
        MappingTable["node_to_new_bus<br/>('SUB1', 'N1') → 1001"]
    end

    subgraph "Transformed Equipment Record"
        TransformedBus["bus_number: 1001<br/>substation_id: 'SUB1'<br/>node_id: 'N1'"]
    end

    subgraph "Writer Access"
        WriterCall["record.get('bus_number', 0)<br/>returns: 1001"]
        WriterOutput["RAW Format Output<br/>1001, ..."]
    end

    OriginalBus --> NodeKey
    NodeKey --> BusAssignment
    BusAssignment --> MappingTable
    MappingTable --> TransformedBus
    TransformedBus --> WriterCall
    WriterCall --> WriterOutput
```

## Modeling Approach Comparison

```mermaid
graph TB
    subgraph "Bus-Branch Modeling"
        BusBranchData["Equipment Data<br/>- Direct bus references<br/>- No substation topology<br/>- Traditional PSS/E format"]
        BusBranchOutput["RAW Output<br/>- Flat bus structure<br/>- Standard equipment sections<br/>- Ends with 'Q'"]
    end

    subgraph "Node-Breaker Modeling"
        NodeBreakerData["Equipment Data<br/>- Substation topology<br/>- Node connections<br/>- Switching devices"]
        NodeBreakerOutput["RAW Output<br/>- Hierarchical substation blocks<br/>- Node/switching device sections<br/>- Ends with 'Q'"]
    end

    subgraph "Hybrid Modeling"
        HybridData["Equipment Data<br/>- Node info embedded<br/>- Mapped to bus numbers<br/>- Simplified topology"]
        HybridOutput["RAW Output<br/>- Simplified bus structure<br/>- Standard equipment sections<br/>- Optional substation data"]
    end

    BusBranchData --> BusBranchOutput
    NodeBreakerData --> NodeBreakerOutput
    HybridData --> HybridOutput
```

## Three-Phase Pipeline Architecture

```mermaid
graph TB
    subgraph "Phase 1: Loading & Conversion"
        Input["Input Files<br/>HDB/RAWX/JSON"]
        Backend["Backend Classes<br/>HdbBackend/RawxBackend"]
        Canonical["Canonical Format<br/>Always NODE_BREAKER<br/>Embedded node info"]
    end

    subgraph "Phase 2: Transformation"
        ModelDetection["Model Detection<br/>detect_model_type()"]
        Transformation["ModelTransformations<br/>Node-to-bus mapping<br/>Equipment updates"]
        TransformedData["Transformed Data<br/>Bus numbers updated<br/>Modeling approach applied"]
    end

    subgraph "Phase 3: Export"
        ExportDecision["Export Decision<br/>Hierarchical vs Flat"]
        Writers["Writer Classes<br/>Equipment-specific writers"]
        Output["RAW Format Output<br/>Version-specific format"]
    end

    Input --> Backend
    Backend --> Canonical
    Canonical --> ModelDetection
    ModelDetection --> Transformation
    Transformation --> TransformedData
    TransformedData --> ExportDecision
    ExportDecision --> Writers
    Writers --> Output
```

## Key Architectural Principles

### 1. Clean Separation of Concerns
- **Backends**: Handle format parsing only
- **Transformations**: Handle modeling conversions only
- **Writers**: Handle output formatting only

### 2. Immutable Canonical Data
- All data access through `CanonicalDataInterface`
- Records are frozen after creation
- Prevents accidental modifications

### 3. Embedded Node Information
- Equipment records contain `substation_id` and `node_id`
- Enables transparent node-to-bus conversion
- Writers remain unaware of transformations

### 4. Universal Transformation Logic
- `ModelTransformations` handles all conversions centrally
- Equipment writers never handle mapping logic
- Consistent transformation across all equipment types

## Bi-Directional Functions

### Functions That Can Be Called Both Ways

1. **Backend Methods**:
   ```python
   # Can be called to convert TO canonical
   canonical_data = backend.to_canonical()
   
   # Can be called to convert FROM canonical (planned)
   raw_data = backend.from_canonical(canonical_data)
   ```

2. **ModelTransformations Methods**:
   ```python
   # Can convert between any modeling approaches
   hybrid_result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
   node_breaker_result = ModelTransformations.convert_to_node_breaker(canonical_data)
   ```

3. **Writer Methods**:
   ```python
   # Can write from canonical format
   writer.write_section_canonical(canonical_section)
   
   # Can write from raw format (legacy)
   writer.write_section(raw_section)
   ```

### Functions That Are Unidirectional

1. **File Parsing**: Input files → Backend data (cannot reverse)
2. **Export Functions**: Canonical data → RAW file (cannot reverse)
3. **Node Mapping**: Nodes → Bus numbers (reverse requires additional logic)

## Node-to-Bus Conversion Details

### 1. Mapping Table Structure
```python
node_to_new_bus = {
    ('SUBSTATION_A', 'NODE_1'): 1001,
    ('SUBSTATION_A', 'NODE_2'): 1002,
    ('SUBSTATION_B', 'NODE_1'): 2001,
    ('SUBSTATION_B', 'NODE_2'): 2002,
    # ...
}
```

### 2. Equipment Processing Algorithm
```python
def update_equipment_bus_numbers(equipment_records, node_to_new_bus):
    for record in equipment_records:
        # Extract embedded node information
        substation_id = record.get('substation_id')
        node_id = record.get('node_id')
        
        # Create node key
        node_key = (substation_id, node_id)
        
        # Look up new bus number
        new_bus = node_to_new_bus.get(node_key)
        
        if new_bus:
            # Update bus number in record
            record['bus_number'] = new_bus
```

### 3. Multi-Terminal Equipment (Lines, Transformers)
```python
# Lines and transformers have FROM and TO connections
from_node_key = (record.get('from_substation_id'), record.get('from_node_id'))
to_node_key = (record.get('to_substation_id'), record.get('to_node_id'))

record['from_bus'] = node_to_new_bus.get(from_node_key, 0)
record['to_bus'] = node_to_new_bus.get(to_node_key, 0)
```

## Performance Considerations

### 1. Converter Performance Tracking
- Each converter is tracked individually
- Performance metrics saved to `performance_tracking.txt`
- Helps identify optimization opportunities

### 2. Memory Management
- Immutable records prevent memory leaks
- Canonical data is frozen after creation
- Large datasets handled efficiently

### 3. Optimization Strategies
- Bus conflict resolution optimized
- Node mapping creation optimized
- Writer operations parallelizable

## Error Handling and Quality Assurance

### 1. Data Quality Checker
- Integrated into all backends
- Automatic issue detection and fixing
- Configurable validation rules

### 2. Validation at Each Stage
- **Backend**: Format validation
- **Canonical**: Data consistency checks
- **Transformation**: Mapping validation
- **Export**: Output format validation

### 3. Comprehensive Logging
- Debug information for troubleshooting
- Performance tracking
- Error reporting with context

## Integration Points

### 1. Adding New Backends
1. Inherit from `BaseBackend`
2. Implement `to_canonical()` method
3. Add to backend factory

### 2. Adding New Equipment Types
1. Create converter class
2. Add to converter map in backends
3. Create writer class
4. Add to writer registry

### 3. Adding New Modeling Approaches
1. Add to `ModelingApproach` enum
2. Implement transformation methods
3. Update export logic
4. Add writer support

## Configuration and Customization

### 1. Backend Configuration
```python
backend = HdbBackend(
    enable_quality_check=True,
    auto_fix=True,
    system_state=SystemState.MAINTAIN_CURRENT
)
```

### 2. Export Configuration
```python
export_to_raw_format(
    data=canonical_data,
    output_path="output.raw",
    version=35,
    modeling="hybrid"
)
```

### 3. Transformation Configuration
```python
result = ModelTransformations.convert_to_hybrid_modeling(
    canonical_data=data
)
```

## Summary

This architecture provides a robust, extensible system for power system data conversion with clear separation of concerns, comprehensive error handling, and support for multiple modeling approaches. The key innovation is the embedded node information system that enables transparent node-to-bus conversion without requiring writers to understand the transformation process.

The pipeline is designed for both batch processing and interactive use, with comprehensive logging and performance tracking to ensure reliability and maintainability. 