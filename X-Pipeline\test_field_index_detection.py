#!/usr/bin/env python3
"""
Test to verify PureFieldMapper field index detection is working correctly.
"""

from hdb_to_raw_pipeline import Pure<PERSON>ieldMapper

def test_field_index_detection():
    """Test that PureFieldMapper can find field indices correctly."""
    print("Testing PureFieldMapper field index detection...")
    
    field_mapper = PureFieldMapper()
    
    # Test substation field detection
    print("\nTesting substation field detection:")
    substation_fields = ['isub', 'name', 'lati', 'long']
    sub_id_idx = field_mapper.find_field_index('substation', 'substation_number', substation_fields)
    sub_name_idx = field_mapper.find_field_index('substation', 'substation_name', substation_fields)
    print(f"substation_number index: {sub_id_idx} (expected: 0)")
    print(f"substation_name index: {sub_name_idx} (expected: 1)")
    
    # Test node field detection
    print("\nTesting node field detection:")
    node_fields = ['isub', 'inode', 'name', 'ibus', 'stat']
    substation_id_idx = field_mapper.find_field_index('node', 'substation_number', node_fields)
    node_id_idx = field_mapper.find_field_index('node', 'node_number', node_fields)
    node_name_idx = field_mapper.find_field_index('node', 'node_name', node_fields)
    bus_id_idx = field_mapper.find_field_index('node', 'bus_number', node_fields)
    status_idx = field_mapper.find_field_index('node', 'status', node_fields)
    print(f"substation_number index: {substation_id_idx} (expected: 0)")
    print(f"node_number index: {node_id_idx} (expected: 1)")
    print(f"node_name index: {node_name_idx} (expected: 2)")
    print(f"bus_number index: {bus_id_idx} (expected: 3)")
    print(f"status index: {status_idx} (expected: 4)")
    
    # Test switching device field detection
    print("\nTesting switching device field detection:")
    swd_fields = ['isub', 'inode', 'jnode', 'swdid', 'name', 'stat']
    sub_idx = field_mapper.find_field_index('switching_device', 'substation_number', swd_fields)
    from_node_idx = field_mapper.find_field_index('switching_device', 'from_node', swd_fields)
    to_node_idx = field_mapper.find_field_index('switching_device', 'to_node', swd_fields)
    ckt_idx = field_mapper.find_field_index('switching_device', 'device_id', swd_fields)
    swd_status_idx = field_mapper.find_field_index('switching_device', 'status', swd_fields)
    swd_name_idx = field_mapper.find_field_index('switching_device', 'device_name', swd_fields)
    print(f"substation_number index: {sub_idx} (expected: 0)")
    print(f"from_node index: {from_node_idx} (expected: 1)")
    print(f"to_node index: {to_node_idx} (expected: 2)")
    print(f"device_id index: {ckt_idx} (expected: 3)")
    print(f"status index: {swd_status_idx} (expected: 5)")
    print(f"device_name index: {swd_name_idx} (expected: 4)")
    
    # Test case-insensitive matching
    print("\nTesting case-insensitive matching:")
    mixed_case_fields = ['ISUB', 'Inode', 'JNODE', 'SWDID', 'Name', 'STAT']
    sub_idx_ci = field_mapper.find_field_index('switching_device', 'substation_number', mixed_case_fields)
    from_node_idx_ci = field_mapper.find_field_index('switching_device', 'from_node', mixed_case_fields)
    print(f"substation_number index (case-insensitive): {sub_idx_ci} (expected: 0)")
    print(f"from_node index (case-insensitive): {from_node_idx_ci} (expected: 1)")
    
    # Verify all indices are correct
    expected_indices = [
        (sub_id_idx, 0), (sub_name_idx, 1),
        (substation_id_idx, 0), (node_id_idx, 1), (node_name_idx, 2), (bus_id_idx, 3), (status_idx, 4),
        (sub_idx, 0), (from_node_idx, 1), (to_node_idx, 2), (ckt_idx, 3), (swd_status_idx, 5), (swd_name_idx, 4),
        (sub_idx_ci, 0), (from_node_idx_ci, 1)
    ]
    
    all_correct = True
    for actual, expected in expected_indices:
        if actual != expected:
            print(f"❌ Index mismatch: got {actual}, expected {expected}")
            all_correct = False
    
    if all_correct:
        print("\n✅ All field index detection tests passed!")
        return True
    else:
        print("\n❌ Some field index detection tests failed!")
        return False

if __name__ == "__main__":
    success = test_field_index_detection()
    if not success:
        exit(1) 