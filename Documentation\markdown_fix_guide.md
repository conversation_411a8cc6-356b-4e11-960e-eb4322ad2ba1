# Markdown Linting Fix Solutions

This folder contains two solutions for fixing common markdown linting issues that violate MD rules.

## The Problem

AI assistants (including <PERSON><PERSON><PERSON>) commonly create markdown files with these linting violations:

- **MD009**: Trailing spaces at end of lines
- **MD012**: Multiple consecutive blank lines
- **MD022**: Headings not surrounded by blank lines
- **MD031**: Fenced code blocks not surrounded by blank lines
- **MD032**: Lists not surrounded by blank lines
- **MD036**: Using emphasis (**bold**) instead of proper headings
- **MD038**: Spaces inside code span elements (like `code`instead of`code`)
- **MD040**: Fenced code blocks without language specification

## Solution 1: Cursor Rule (Preventive)

### Usage

Add this rule to your Cursor user-specific rules to prevent the AI from creating markdown violations:

```text
### ✅ Markdown Formatting Rule (MD Compliance)

When editing or creating .md (Markdown) files:

**MD009 - No trailing spaces:**
- Remove all trailing whitespace at the end of lines
- Do not leave spaces or tabs after the final character on any line
- Exception: Two spaces at end of line for explicit line break (rare usage)

**MD012 - No multiple consecutive blank lines:**
- Use only single blank lines to separate sections
- Never have more than one blank line in a row
- Remove any multiple consecutive blank lines

**MD022 - Headings surrounded by blank lines:**
- Always insert a blank line before each heading (e.g., before #, ##, ###, etc.)
- Always insert a blank line after each heading
- Exception: No blank line needed before the first heading in a file

**MD031 & MD032 - Code blocks and lists surrounded by blank lines:**
- Always surround fenced code blocks with a blank line above and below
- Always surround lists (both ordered and unordered) with a blank line above and below
- This includes both ``` code blocks and list items starting with - or 1.

**MD036 - Use proper headings instead of emphasis:**
- Use # ## ### headings instead of **bold text** for section titles
- Use **bold** and *italic* only for emphasis within paragraphs, not as heading substitutes
- Structure documents with proper heading hierarchy

**MD038 - No spaces inside code spans:**
- Remove spaces inside inline code spans: `code`not`code`or`code`
- Inline code should be tight against the backticks
- Use `code`not`code`or`code`or`code`

**MD040 - Fenced code blocks must specify language:**
- Always specify the language for syntax highlighting immediately after the opening triple backticks
- Use ```python,```bash,```json,```yaml,```javascript, etc.
- Never leave fenced code blocks blank (```language not just```)
- If no specific language, use ```text or```plaintext

**File ending:**
- Always ensure there is exactly one newline at the end of the file
- Do not leave trailing spaces or multiple blank lines at the end
- The file must end with a clean newline character
```text

### How to Add to Cursor

1. Open Cursor Settings
2. Go to "Rules" or "User Rules" section
3. Add the above text to your user-specific rules
4. Save the configuration

## Solution 2: Python Script (Corrective)

### Usage

Run the script to automatically fix existing markdown files:

```bash
python fix_markdown.py
```text

### What It Does

The script will:

1. **Find** all `.md` files in current directory and subdirectories
2. **Fix** the following issues automatically:
   - Remove trailing spaces (MD009)
   - Remove multiple consecutive blank lines (MD012)
   - Add blank lines around headings (MD022)
   - Add blank lines around code blocks (MD031)
   - Add blank lines around lists (MD032)
   - Convert standalone bold text to headings (MD036)
   - Remove spaces inside code spans (MD038)
   - Add language to bare fenced code blocks (MD040)
   - Ensure proper file ending

3. **Report** what was fixed

### Example Output

```text
Markdown Linter Fixer
========================================
Found 2 markdown file(s):
  - .\README.md
  - .\docs\api.md

Fixing .\README.md...
  ✓ Fixed .\README.md
Fixing .\docs\api.md...
  - No changes needed for .\docs\api.md

Summary: Fixed 1 out of 2 files.

Fixed the following markdown issues:
  ✓ MD009: Removed trailing spaces
  ✓ MD012: Removed multiple consecutive blank lines
  ✓ MD022: Added blank lines around headings
  ✓ MD031: Added blank lines around fenced code blocks
  ✓ MD032: Added blank lines around lists
  ✓ MD036: Converted standalone bold text to headings
  ✓ MD038: Removed spaces inside code spans
  ✓ MD040: Added language to fenced code blocks
```text

## Before and After Examples

### Before (Violations)

```markdown
##Problem Description
This is a problem.
Here's a list:
- Item 1
- Item 2
Here's some code:
```text

def hello():
    print("hello")

```text

## Another Section
Some `code` with spaces.
More content.
```text

### After (Fixed)

```markdown
## Problem Description

This is a problem.

Here's a list:

- Item 1
- Item 2

Here's some code:

```python

def hello():
    print("hello")

```text

## Another Section

Some `code` without spaces.
More content.
```text

## Recommendation

1. **First**: Add the Cursor rule to prevent future violations
2. **Then**: Run the Python script to fix existing files
3. **Verify**: Check that your markdown now passes linting tools

This dual approach ensures both prevention (Cursor rule) and correction (Python script) of markdown linting issues.
