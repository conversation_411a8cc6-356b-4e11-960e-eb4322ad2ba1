# Archive Managers System - AI Knowledge Transfer

## System Architecture

### Core Components

1. **Base ArchiveManager Class**
   - Abstract base class for all archive managers
   - Provides common file management functionality
   - Handles directory operations and result tracking
   - Implements auto-purge capabilities

2. **ExportArchiveManager**
   - Manages export data and files
   - Key properties:
     - `auto_purge_age`: From settings.EXPORT_RETENTION_POLICY
     - `directory`: From settings.EXPORT_DIR
     - `exports`: List of export directories
     - `index`: ArchiveIndex instance for metadata
   - Critical methods:
     - `is_export_complete()`: Validates export files
     - `get_unmodeled_exports()`: Finds incomplete exports
     - `execute_task_on_archive()`: Runs tasks on exports

3. **SftpArchiveManager**
   - Handles remote file synchronization
   - Key properties:
     - `remote_path`: From settings.EMS_SFTP_REMOTE_PATH
     - `connection_parameters`: From settings.EMS_SFTP_CONNECTION_PARAMS
     - `encryption_password`: From settings.EMS_SFTP_EXPORT_ENCRYPTION_PASSWORD
   - Critical methods:
     - `synchronize()`: Downloads missing files
     - `parallel_synchronize()`: Parallel file processing
     - `purge_old_downloads()`: Cleans up old files

4. **ArchiveIndex**
   - Manages export metadata
   - Key properties:
     - `data`: Dictionary of export metadata
     - `manager`: Associated archive manager
   - Critical methods:
     - `build()`: Creates index from exports
     - `update()`: Updates single export metadata
     - `validate()`: Verifies index accuracy

### Study-Specific Managers

1. **ContingencyAnalysisArchiveManager**
   - Manages contingency analysis results
   - Auto-purge age from settings.ca_archive_auto_purge_age_in_hours

2. **N11ArchiveManager**
   - Handles N-1-1 study results
   - Supports partition directories
   - Auto-purge age from settings.n_1_1_archive_auto_purge_age_in_hours

3. **Time-Based Study Managers**
   - NextDayStudyArchiveManager
   - TenDayStudyArchiveManager
   - VariableMidtermStudyArchiveManager
   - OneMonthStudyArchiveManager
   - TwoMonthStudyArchiveManager
   - SixMonthStudyArchiveManager
   - ProjectStudyArchiveManager
   - ThreeYearStudyArchiveManager

## Key Dependencies

1. **File Management**
   - `file_management`: Core file operations
   - `json_io`: Index serialization
   - `DiskManager`: Space management

2. **Parallel Processing**
   - `AnodeTaskManager`: Task execution
   - `AnodeTask`: Task definition

3. **Data Handling**
   - `ExportPackage`: Export data structure
   - `StudyPackage`: Study data structure
   - `LoadAreaRecordContext`: Loading data

## Critical Paths

1. **Export Processing**

```text
   ExportArchiveManager
   ├── is_export_complete()
   │   └── ExportPackage validation
   ├── get_unmodeled_exports()
   │   └── Index data filtering
   └── execute_task_on_archive()
       └── AnodeTask execution
```text

2. **SFTP Synchronization**

```text
   SftpArchiveManager
   ├── synchronize()
   │   ├── Connection setup
   │   ├── File download
   │   └── Index update
   └── parallel_synchronize()
       ├── Task creation
       └── Parallel execution
```text

3. **Index Management**

```text
   ArchiveIndex
   ├── build()
   │   ├── Export validation
   │   └── Metadata collection
   ├── update()
   │   └── Single export update
   └── validate()
       └── Index verification
```text

## Error Patterns

1. **File Operations**
   - Missing files
   - Permission issues
   - Disk space problems

2. **SFTP Operations**
   - Connection failures
   - Authentication errors
   - Transfer timeouts

3. **Index Operations**
   - Corrupted index
   - Missing metadata
   - Validation failures

## Recovery Strategies

1. **File Recovery**
   - Rebuild from source
   - Validate integrity
   - Update index

2. **SFTP Recovery**
   - Retry connection
   - Verify credentials
   - Resume transfers

3. **Index Recovery**
   - Rebuild index
   - Validate exports
   - Update metadata

## Configuration Dependencies

1. **Required Settings**
   ```python
   EXPORT_RETENTION_POLICY
   EXPORT_DIR
   EMS_SFTP_CONNECTION_PARAMS
   EMS_SFTP_EXPORT_ENCRYPTION_PASSWORD
   EMS_SFTP_MAX_DOWNLOAD_PROCESS_COUNT

```text

2. **Study Settings**
   ```python

   ca_archive_auto_purge_age_in_hours
   n_1_1_archive_auto_purge_age_in_hours

```text

## Common Operations

1. **Export Management**
   ```python

   # Initialize

   manager = ExportArchiveManager()

   # Validate

   is_complete = manager.is_export_complete(export)

   # Process

   manager.execute_task_on_archive(task)

```text

2. **SFTP Operations**
   ```python

   # Initialize

   manager = SftpArchiveManager()

   # Synchronize

   manager.synchronize()

   # Cleanup

   manager.purge_old_downloads(age)

```text

3. **Index Operations**
   ```python

   # Initialize

   index = ArchiveIndex.load(manager)

   # Update

   index.update(export)

   # Validate

   index.validate()

```text

## System Constraints

1. **Resource Limits**
   - Disk space
   - Network bandwidth
   - Process count

2. **Time Constraints**
   - Export intervals
   - Purge policies
   - Sync windows

3. **Security Constraints**
   - File permissions
   - Network access
   - Encryption requirements

## Integration Points

1. **File System**
   - Directory structure
   - File naming
   - Access patterns

2. **Network**
   - SFTP connections
   - Transfer protocols
   - Security requirements

3. **Data Processing**
   - Export packages
   - Study packages
   - Index management

## Maintenance Requirements

1. **Regular Tasks**
   - Index validation
   - File cleanup
   - Space management

2. **Monitoring**
   - Disk usage
   - Sync status
   - Error rates

3. **Updates**
   - Configuration
   - Dependencies
   - Security patches
