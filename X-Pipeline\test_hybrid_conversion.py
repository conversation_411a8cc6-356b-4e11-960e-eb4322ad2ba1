#!/usr/bin/env python3
"""
Simple test to verify hybrid conversion is working without mapped_record errors.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hdb_to_raw_pipeline import RawxBackend, ModelTransformations

def test_hybrid_conversion():
    """Test that hybrid conversion works without mapped_record errors."""
    print("🧪 Testing hybrid conversion...")
    
    try:
        # Load a RAWX file that has substation data (using manual loading approach)
        rawx_file = "savnw_nb.rawx"
        if not os.path.exists(rawx_file):
            rawx_file = "../savnw_nb.rawx"
        backend = RawxBackend()
        backend.load(rawx_file)
        canonical_data = backend.to_canonical()
        
        print(f"✅ Backend loaded successfully with {len(canonical_data)} sections")
        
        # Test hybrid conversion
        result = ModelTransformations.convert_to_hybrid_modeling(canonical_data)
        
        print(f"✅ Hybrid conversion completed successfully!")
        if result.canonical_data:
            print(f"📊 Hybrid data has {len(result.canonical_data)} sections")
        else:
            print("📊 No hybrid data returned")
        if result.warnings:
            print(f"⚠️  {len(result.warnings)} warnings generated")
        if result.errors:
            print(f"❌ {len(result.errors)} errors generated")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Hybrid conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hybrid_conversion()
    if success:
        print("🎉 Hybrid conversion test PASSED!")
        sys.exit(0)
    else:
        print("💥 Hybrid conversion test FAILED!")
        sys.exit(1) 