# Log files
*.log
*.log.*
*.log.*.*

# Environment files
*.env
*.env.*
*.env.*.*

# Python cache and compiled files
*.pyc
*.pyc.*
*.pyc.*.*
__pycache__/
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing and coverage
.coverage
.pytest_cache/
.coverage.*
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Data files (optional - remove if you want to track certain data files)
*.raw
*.json
*.hdb
*.csv
*.xlsx

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# Backup files
*.bak
*.backup
X-Pipeline/deprecated/
anode/
modeling/
X-Pipeline/out*/
X-Pipeline/test*/
X-Pipeline/out/
plans/