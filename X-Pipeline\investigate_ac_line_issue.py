#!/usr/bin/env python3
"""
Systematic investigation of AC line data mapping issue.
"""

def investigate_ac_line_issue():
    """Systematically investigate what's happening with AC line data."""
    
    print('=== SYSTEMATIC AC LINE INVESTIGATION ===')
    print()
    
    try:
        from hdb_to_raw_pipeline import HdbBackend
        
        # Step 1: Check what sections exist in the HDB data
        backend = HdbBackend()
        backend.load('hdbcontext_original.hdb')
        print('1. HDB Source Data Sections:')
        for section, data in backend.data.items():
            if isinstance(data, dict):
                count = len(data)
                print(f'   {section}: {count} records')
            elif isinstance(data, list):
                print(f'   {section}: {len(data)} records (list)')
        print()

        # Step 2: Look specifically at line data in HDB
        hdb_line_segments = backend.data.get('line_segment', {})
        print(f'2. HDB line_segment data: {len(hdb_line_segments)} records')
        if hdb_line_segments:
            first_key = list(hdb_line_segments.keys())[0]
            first_record = hdb_line_segments[first_key]
            if isinstance(first_record, dict):
                print(f'   Sample record keys: {list(first_record.keys())}')
                print(f'   Sample resistance: {first_record.get("Resistance", "N/A")}')
                print(f'   Sample reactance: {first_record.get("Reactance", "N/A")}')
                print(f'   Sample charging: {first_record.get("Charging", "N/A")}')
            else:
                print('   First record is not a dict')
        print()

        # Step 3: Check field mapping availability
        from hdb_to_raw_pipeline import FIELD_MAP
        print('3. Field Mapping Status:')
        print(f'   ac_line mapping exists: {"ac_line" in FIELD_MAP}')
        if 'ac_line' in FIELD_MAP:
            ac_line_fields = list(FIELD_MAP['ac_line'].keys())
            print(f'   ac_line mapped fields: {ac_line_fields}')
        print()
        
        # Step 4: Test field mapping directly
        if hdb_line_segments:
            print('4. Field Mapping Test:')
            test_record = list(hdb_line_segments.values())[0]
            mapped_record = backend.field_mapper.map_record('ac_line', test_record)
            print(f'   Original record type: {type(test_record)}')
            print(f'   Mapped record: {mapped_record}')
            print()

        # Step 5: Check what happens during conversion
        canonical_data = backend.to_canonical()
        print('5. Canonical Data Result:')
        for section, data in canonical_data.items():
            if isinstance(data, dict) and 'data' in data:
                count = len(data['data'])
                print(f'   {section}: {count} records')
        print()

        # Step 6: Detailed AC line analysis
        if 'ac_line' in canonical_data:
            ac_line_data = canonical_data['ac_line']['data']
            ac_line_fields = canonical_data['ac_line']['fields']
            print(f'6. Canonical ac_line Analysis:')
            print(f'   Record count: {len(ac_line_data)}')
            print(f'   Fields: {ac_line_fields}')
            if ac_line_data:
                first_record = ac_line_data[0]
                print(f'   First record length: {len(first_record)}')
                print(f'   First record: {first_record}')
                
                # Check specific field values
                if 'r' in ac_line_fields:
                    r_index = ac_line_fields.index('r')
                    if len(first_record) > r_index:
                        print(f'   Resistance value: {first_record[r_index]}')
                
                if 'x' in ac_line_fields:
                    x_index = ac_line_fields.index('x')
                    if len(first_record) > x_index:
                        print(f'   Reactance value: {first_record[x_index]}')
        else:
            print('6. ERROR: No ac_line section in canonical data!')
        
        print('\n=== INVESTIGATION COMPLETE ===')
        
    except Exception as e:
        print(f'Investigation failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_ac_line_issue() 